"""
查找与替换工具插件

提供在文件或项目中进行文本查找和批量替换操作的插件。
支持GUI界面操作，包含正则表达式、撤销功能、文件类型过滤等功能。
"""

import os
import sys
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from plugins.base import PluginBase
from find_and_replace.find_replace_gui import FindReplaceApp


class FindReplacePlugin(PluginBase):
    """查找与替换工具插件主类"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.find_replace_window = None
        self.main_window = None
        self.menu_action = None
        
    @property
    def name(self) -> str:
        """插件名称"""
        return "查找与替换工具"
    
    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """插件描述"""
        return "在文件或项目中进行文本查找和批量替换操作"
    
    def initialize(self, main_window):
        """初始化插件
        
        Args:
            main_window: 主窗口实例
        """
        try:
            self.main_window = main_window
            
            # 创建查找替换窗口
            self.find_replace_window = FindReplaceApp()
            
            # 添加菜单项
            self._add_menu_item()
            
            print(f"插件 {self.name} 初始化成功")
            
        except Exception as e:
            print(f"插件 {self.name} 初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def cleanup(self):
        """清理插件资源"""
        try:
            # 移除菜单项
            if self.menu_action and self.main_window:
                tools_menu = getattr(self.main_window, 'tools_menu', None)
                if tools_menu:
                    tools_menu.removeAction(self.menu_action)
            
            # 关闭查找替换窗口
            if self.find_replace_window:
                self.find_replace_window.close()
                self.find_replace_window = None
            
            self.main_window = None
            self.menu_action = None
            
            print(f"插件 {self.name} 清理完成")
            
        except Exception as e:
            print(f"插件 {self.name} 清理失败: {str(e)}")
    
    def _add_menu_item(self):
        """添加菜单项"""
        try:
            if not self.main_window:
                return
            
            # 获取工具菜单
            tools_menu = getattr(self.main_window, 'tools_menu', None)
            if not tools_menu:
                print("未找到工具菜单")
                return
            
            # 创建菜单动作
            self.menu_action = QAction("查找与替换工具", self.main_window)
            self.menu_action.setStatusTip("打开查找与替换工具")
            self.menu_action.triggered.connect(self._show_find_replace_window)
            
            # 添加到工具菜单
            tools_menu.addAction(self.menu_action)
            
        except Exception as e:
            print(f"添加菜单项失败: {str(e)}")
    
    def _show_find_replace_window(self):
        """显示查找替换窗口"""
        try:
            if self.find_replace_window:
                self.find_replace_window.show()
                self.find_replace_window.raise_()
                self.find_replace_window.activateWindow()
        except Exception as e:
            print(f"显示查找替换窗口失败: {str(e)}")
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开查找与替换工具: {str(e)}"
            )


# 插件实例
plugin_instance = FindReplacePlugin()
