from PyQt5.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
    QLineEdit, QPushButton, QProgressDialog,
    QFileDialog
)
from PyQt5.QtCore import Qt, pyqtSignal

class LabeledInput(QWidget):
    """带标签的输入框组件"""
    valueChanged = pyqtSignal(str)
    
    def __init__(self, label, placeholder="", validator=None):
        super().__init__()
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.label = QLabel(label)
        self.input = QLineEdit()
        self.input.setPlaceholderText(placeholder)
        if validator:
            self.input.setValidator(validator)
            
        self.input.textChanged.connect(self.valueChanged.emit)
        
        layout.addWidget(self.label)
        layout.addWidget(self.input)
        self.setLayout(layout)
        
    def setText(self, text):
        self.input.setText(text)
        
    def text(self):
        return self.input.text()
        
    def clear(self):
        self.input.clear()

class FileSelector(QWidget):
    """文件选择组件"""
    fileSelected = pyqtSignal(str)
    
    def __init__(self, label, file_types="All Files (*.*)", button_text="选择文件"):
        super().__init__()
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.label = QLabel(label)
        self.path_label = QLabel("未选择文件")
        self.path_label.setStyleSheet("color: #888; font-style: italic;")
        
        self.select_btn = QPushButton(button_text)
        self.clear_btn = QPushButton("清除")
        self.clear_btn.setEnabled(False)
        
        self.file_types = file_types
        
        layout.addWidget(self.label)
        layout.addWidget(self.path_label)
        layout.addWidget(self.select_btn)
        layout.addWidget(self.clear_btn)
        
        self.select_btn.clicked.connect(self.select_file)
        self.clear_btn.clicked.connect(self.clear_file)
        
        self.setLayout(layout)
        
    def select_file(self):
        path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "", self.file_types
        )
        if path:
            self.path_label.setText(path)
            self.clear_btn.setEnabled(True)
            self.fileSelected.emit(path)
            
    def clear_file(self):
        self.path_label.setText("未选择文件")
        self.clear_btn.setEnabled(False)
        self.fileSelected.emit("")
        
    def get_file_path(self):
        text = self.path_label.text()
        return "" if text == "未选择文件" else text

class ProgressWindow:
    """进度窗口"""
    def __init__(self, parent, title, message):
        self.progress = QProgressDialog(message, "取消", 0, 100, parent)
        self.progress.setWindowTitle(title)
        self.progress.setWindowModality(Qt.WindowModal)
        self.progress.setAutoClose(True)
        self.progress.setAutoReset(True)
        
    def update(self, value):
        self.progress.setValue(value)
        
    def close(self):
        self.progress.close()
