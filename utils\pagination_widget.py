"""
分页控件
提供数据分页显示功能
"""
from typing import Callable, Optional
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QPushButton, 
                           QLabel, QComboBox, QLineEdit, QSpinBox, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIntValidator


class PaginationWidget(QWidget):
    """分页控件"""
    
    # 信号定义
    page_changed = pyqtSignal(int)  # 页码变化
    page_size_changed = pyqtSignal(int)  # 每页大小变化
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 分页参数
        self.current_page = 1
        self.total_pages = 1
        self.total_records = 0
        self.page_size = 50
        self.page_sizes = [20, 50, 100, 200, 500]

        # 防重复点击
        self.is_changing_page = False

        # 创建UI
        self.init_ui()

        # 连接信号
        self.connect_signals()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 总记录数标签
        self.total_label = QLabel("总计: 0 条记录")
        layout.addWidget(self.total_label)
        
        # 弹性空间
        layout.addStretch()
        
        # 每页大小选择
        layout.addWidget(QLabel("每页:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems([str(size) for size in self.page_sizes])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.setFixedWidth(80)
        layout.addWidget(self.page_size_combo)
        layout.addWidget(QLabel("条"))
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)
        
        # 首页按钮
        self.first_btn = QPushButton("首页")
        self.first_btn.setFixedWidth(60)
        layout.addWidget(self.first_btn)
        
        # 上一页按钮
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.setFixedWidth(60)
        layout.addWidget(self.prev_btn)
        
        # 页码输入
        layout.addWidget(QLabel("第"))
        self.page_input = QLineEdit()
        self.page_input.setFixedWidth(60)
        self.page_input.setAlignment(Qt.AlignCenter)
        self.page_input.setValidator(QIntValidator(1, 999999))
        layout.addWidget(self.page_input)
        
        # 总页数标签
        self.page_label = QLabel("/ 1 页")
        layout.addWidget(self.page_label)
        
        # 下一页按钮
        self.next_btn = QPushButton("下一页")
        self.next_btn.setFixedWidth(60)
        layout.addWidget(self.next_btn)
        
        # 末页按钮
        self.last_btn = QPushButton("末页")
        self.last_btn.setFixedWidth(60)
        layout.addWidget(self.last_btn)
        
        # 跳转按钮
        self.goto_btn = QPushButton("跳转")
        self.goto_btn.setFixedWidth(50)
        layout.addWidget(self.goto_btn)
        
        self.setLayout(layout)
        
        # 更新按钮状态
        self.update_buttons()
    
    def connect_signals(self):
        """连接信号"""
        self.first_btn.clicked.connect(self.go_first_page)
        self.prev_btn.clicked.connect(self.go_prev_page)
        self.next_btn.clicked.connect(self.go_next_page)
        self.last_btn.clicked.connect(self.go_last_page)
        self.goto_btn.clicked.connect(self.go_to_page)
        self.page_input.returnPressed.connect(self.go_to_page)
        self.page_size_combo.currentTextChanged.connect(self.on_page_size_changed)
    
    def set_pagination_info(self, current_page: int, total_records: int, page_size: int):
        """设置分页信息
        
        Args:
            current_page (int): 当前页码
            total_records (int): 总记录数
            page_size (int): 每页大小
        """
        self.current_page = current_page
        self.total_records = total_records
        self.page_size = page_size
        
        # 计算总页数
        self.total_pages = max(1, (total_records + page_size - 1) // page_size)
        
        # 更新UI
        self.update_ui()
    
    def update_ui(self):
        """更新UI显示"""
        # 更新总记录数
        self.total_label.setText(f"总计: {self.total_records} 条记录")
        
        # 更新页码显示
        self.page_input.setText(str(self.current_page))
        self.page_label.setText(f"/ {self.total_pages} 页")
        
        # 更新每页大小
        self.page_size_combo.setCurrentText(str(self.page_size))
        
        # 更新按钮状态
        self.update_buttons()
    
    def update_buttons(self):
        """更新按钮状态"""
        # 首页和上一页按钮
        can_go_prev = self.current_page > 1
        self.first_btn.setEnabled(can_go_prev)
        self.prev_btn.setEnabled(can_go_prev)
        
        # 下一页和末页按钮
        can_go_next = self.current_page < self.total_pages
        self.next_btn.setEnabled(can_go_next)
        self.last_btn.setEnabled(can_go_next)
        
        # 跳转按钮
        self.goto_btn.setEnabled(self.total_pages > 1)
    
    def go_first_page(self):
        """跳转到首页"""
        if self.current_page != 1:
            self.current_page = 1
            self.page_changed.emit(self.current_page)
            self.update_ui()
    
    def go_prev_page(self):
        """跳转到上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.page_changed.emit(self.current_page)
            self.update_ui()
    
    def go_next_page(self):
        """跳转到下一页"""
        print(f"PaginationWidget点击下一页: 当前页={self.current_page}, 总页数={self.total_pages}, 正在切换={self.is_changing_page}")

        if self.is_changing_page or self.current_page >= self.total_pages:
            print("跳过下一页操作：正在切换或已是最后一页")
            return

        self.is_changing_page = True
        self.current_page += 1

        print(f"发送页码变化信号: {self.current_page}")

        # 临时禁用按钮，防止重复点击
        self.next_btn.setEnabled(False)

        self.page_changed.emit(self.current_page)
        self.update_ui()

        # 使用QTimer延迟重新启用按钮
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(200, self._enable_page_changing)
    
    def go_last_page(self):
        """跳转到末页"""
        if self.current_page != self.total_pages:
            self.current_page = self.total_pages
            self.page_changed.emit(self.current_page)
            self.update_ui()
    
    def go_to_page(self):
        """跳转到指定页"""
        try:
            page = int(self.page_input.text())
            if 1 <= page <= self.total_pages and page != self.current_page:
                self.current_page = page
                self.page_changed.emit(self.current_page)
                self.update_ui()
            else:
                # 恢复当前页码
                self.page_input.setText(str(self.current_page))
        except ValueError:
            # 输入无效，恢复当前页码
            self.page_input.setText(str(self.current_page))
    
    def on_page_size_changed(self, size_text: str):
        """页面大小变化处理"""
        try:
            new_size = int(size_text)
            if new_size != self.page_size:
                self.page_size = new_size
                
                # 重新计算当前页码（保持当前记录位置）
                current_record = (self.current_page - 1) * self.page_size + 1
                self.current_page = max(1, (current_record - 1) // new_size + 1)
                
                # 发送信号
                self.page_size_changed.emit(new_size)
                
                # 更新UI
                self.update_ui()
        except ValueError:
            pass
    
    def get_current_page(self) -> int:
        """获取当前页码"""
        return self.current_page
    
    def get_page_size(self) -> int:
        """获取每页大小"""
        return self.page_size
    
    def get_total_pages(self) -> int:
        """获取总页数"""
        return self.total_pages
    
    def get_total_records(self) -> int:
        """获取总记录数"""
        return self.total_records

    def _enable_page_changing(self):
        """重新启用分页操作"""
        self.is_changing_page = False
        self.update_buttons()


class FilterWidget(QWidget):
    """过滤控件"""
    
    # 信号定义
    filter_changed = pyqtSignal(str, str)  # status_filter, search_term
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 过滤参数
        self.status_filter = ""
        self.search_term = ""

        # 防抖定时器
        from PyQt5.QtCore import QTimer
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._emit_filter_changed)
        self.search_debounce_delay = 300  # 300ms防抖延迟

        # 创建UI
        self.init_ui()

        # 连接信号
        self.connect_signals()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 状态过滤
        layout.addWidget(QLabel("状态:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部", "Pending", "On-Going", "PASS", "FAIL"])
        self.status_combo.setFixedWidth(100)
        layout.addWidget(self.status_combo)
        
        # 搜索框
        layout.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入用例名称...")
        self.search_input.setFixedWidth(200)
        layout.addWidget(self.search_input)
        
        # 清除按钮
        self.clear_btn = QPushButton("清除")
        self.clear_btn.setFixedWidth(60)
        layout.addWidget(self.clear_btn)
        
        # 弹性空间
        layout.addStretch()
        
        self.setLayout(layout)
    
    def connect_signals(self):
        """连接信号"""
        self.status_combo.currentTextChanged.connect(self.on_status_filter_changed)
        self.search_input.textChanged.connect(self.on_search_text_changed)
        self.search_input.returnPressed.connect(self.on_search_enter_pressed)
        self.clear_btn.clicked.connect(self.clear_filters)
    
    def on_status_filter_changed(self):
        """状态过滤条件变化（立即响应）"""
        status = self.status_combo.currentText()
        status_filter = "" if status == "全部" else status

        print(f"FilterWidget状态筛选变化: '{self.status_filter}' -> '{status_filter}'")

        # 更新状态筛选值
        self.status_filter = status_filter
        # 总是发送信号，让接收方判断是否需要刷新
        self._emit_filter_changed()

    def on_search_text_changed(self):
        """搜索文本变化（防抖处理）"""
        # 停止之前的定时器
        if self.search_timer.isActive():
            self.search_timer.stop()

        # 启动防抖定时器
        self.search_timer.start(self.search_debounce_delay)

    def on_search_enter_pressed(self):
        """搜索框回车键按下（立即响应）"""
        # 停止防抖定时器，立即执行搜索
        if self.search_timer.isActive():
            self.search_timer.stop()
        self._emit_filter_changed()

    def _emit_filter_changed(self):
        """发送过滤条件变化信号"""
        search = self.search_input.text().strip()

        # 检查是否有任何过滤条件发生变化
        if search != self.search_term:
            self.search_term = search

        # 无论如何都发送信号，让接收方判断是否需要刷新
        self.filter_changed.emit(self.status_filter, self.search_term)
    
    def clear_filters(self):
        """清除所有过滤条件"""
        self.status_combo.setCurrentText("全部")
        self.search_input.clear()
    
    def get_filters(self) -> tuple:
        """获取当前过滤条件
        
        Returns:
            tuple: (status_filter, search_term)
        """
        return self.status_filter, self.search_term
