"""
BATCH RUN 工具模块
"""
import os
from utils.seed_generator import SeedGenerator


class BatchRunUtils:
    """BATCH RUN 相关工具类"""
    
    @staticmethod
    def check_base_case_directory_exists(base_case_name):
        """
        检查基础用例目录是否存在
        
        Args:
            base_case_name (str): 基础用例名
            
        Returns:
            tuple: (exists, directory_path) - 是否存在和目录路径
        """
        if not base_case_name:
            return False, None
            
        # 检查路径列表
        check_paths = []
        
        # 1. 检查当前工作目录
        current_dir = os.getcwd()
        check_paths.append(current_dir)
        
        # 2. 检查 $PROJ_WORK 环境变量目录
        proj_work = os.environ.get('PROJ_WORK')
        if proj_work and os.path.exists(proj_work):
            check_paths.append(proj_work)
        
        # 在每个路径下检查基础用例目录
        for check_path in check_paths:
            base_case_dir = os.path.join(check_path, base_case_name)
            if os.path.exists(base_case_dir) and os.path.isdir(base_case_dir):
                return True, base_case_dir
                
        return False, None
    
    @staticmethod
    def get_proj_work_path():
        """
        获取 $PROJ_WORK 环境变量路径
        
        Returns:
            str: $PROJ_WORK 路径，如果不存在则返回当前工作目录
        """
        proj_work = os.environ.get('PROJ_WORK')
        if proj_work and os.path.exists(proj_work):
            return proj_work
        return os.getcwd()
    
    @staticmethod
    def generate_batch_run_commands(original_command, base_case_name, force_update=False, execution_count=1, case_name=None):
        """
        生成 BATCH RUN 模式的命令序列

        Args:
            original_command (str): 原始命令
            base_case_name (str): 基础用例名
            force_update (bool): 是否强制更新编译基板
            execution_count (int): 执行次数，默认为1
            case_name (str): 当前用例名，用于生成工作目录

        Returns:
            list: 命令列表，每个元素是 (command, description) 元组
        """
        if not base_case_name:
            return [(original_command, "原始命令")]

        commands = []
        proj_work = BatchRunUtils.get_proj_work_path()
        base_case_dir = os.path.join(proj_work, base_case_name)

        # 检查基础用例目录是否存在
        dir_exists, _ = BatchRunUtils.check_base_case_directory_exists(base_case_name)

        # 如果执行次数大于1，需要生成多个仿真命令
        if execution_count > 1:
            return BatchRunUtils._generate_multiple_execution_commands(
                original_command, base_case_name, base_case_dir, force_update,
                dir_exists, execution_count, case_name
            )

        # 单次执行的原有逻辑
        if force_update or not dir_exists:
            # 情况B：强制更新编译基板 或 情况A：基础用例目录不存在
            # 第一条命令：编译基础用例
            compile_command = BatchRunUtils._modify_command_for_compile(original_command, base_case_name)
            commands.append((compile_command, f"编译基础用例: {base_case_name}"))

            # 第二条命令：使用编译结果运行仿真
            sim_command = BatchRunUtils._modify_command_for_simulation(original_command, base_case_dir)
            commands.append((sim_command, f"使用基础用例运行仿真"))
        else:
            # 情况A：基础用例目录存在，直接运行仿真
            sim_command = BatchRunUtils._modify_command_for_simulation(original_command, base_case_dir)
            commands.append((sim_command, f"使用现有基础用例运行仿真"))

        return commands

    @staticmethod
    def _generate_multiple_execution_commands(original_command, base_case_name, base_case_dir,
                                            force_update, dir_exists, execution_count, case_name):
        """
        生成多次执行的命令序列

        Args:
            original_command (str): 原始命令
            base_case_name (str): 基础用例名
            base_case_dir (str): 基础用例目录路径
            force_update (bool): 是否强制更新编译基板
            dir_exists (bool): 基础用例目录是否存在
            execution_count (int): 执行次数
            case_name (str): 当前用例名

        Returns:
            list: 命令列表，每个元素是 (command, description) 元组
        """
        commands = []

        # 生成唯一的随机种子
        seeds = SeedGenerator.generate_unique_seeds(execution_count)

        # 确保工作目录名称唯一
        if case_name:
            seeds = SeedGenerator.ensure_unique_rundir_names(case_name, seeds)

        # 检查是否有base_case（base_case_name != case_name表示有base_case）
        has_base_case = base_case_name != case_name

        # 如果有base_case且需要编译基板
        if has_base_case and (force_update or not dir_exists):
            # 第一条命令：编译基础用例
            compile_command = BatchRunUtils._modify_command_for_compile(original_command, base_case_name)
            commands.append((compile_command, f"编译基础用例: {base_case_name}"))

        # 为每个种子生成仿真命令
        for i, seed in enumerate(seeds, 1):
            # 生成工作目录名称
            rundir_name = SeedGenerator.generate_rundir_name(case_name or base_case_name, seed)

            # 根据是否有base_case选择不同的命令修改方式
            if has_base_case:
                # 有base_case：使用base_case目录的仿真命令
                sim_command = BatchRunUtils._modify_command_for_simulation_with_seed(
                    original_command, base_case_dir, seed, rundir_name
                )
            else:
                # 没有base_case：直接修改原始命令添加种子和工作目录
                sim_command = BatchRunUtils._modify_command_for_direct_simulation_with_seed(
                    original_command, seed, rundir_name
                )

            commands.append((sim_command, f"执行 {i}/{execution_count}: 种子={seed}, 目录={rundir_name}"))

        return commands

    @staticmethod
    def _modify_command_for_compile(original_command, base_case_name):
        """
        修改命令用于编译基础用例

        Args:
            original_command (str): 原始命令
            base_case_name (str): 基础用例名

        Returns:
            str: 修改后的编译命令
        """
        parts = original_command.split()
        new_parts = []

        i = 0
        case_replaced = False

        while i < len(parts):
            part = parts[i]

            if part == "-case" and i + 1 < len(parts):
                # 替换 -case 参数值为基础用例名
                new_parts.extend(["-case", base_case_name])
                case_replaced = True
                i += 2  # 跳过下一个参数
            elif part == "-R":
                # 跳过 -R 选项及其参数（编译命令不需要-R参数）
                if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                    i += 2  # 跳过 -R 和其路径参数
                else:
                    i += 1  # 只跳过 -R
            else:
                new_parts.append(part)
                i += 1

        # 如果没有找到 -case 参数，添加它
        if not case_replaced:
            new_parts.extend(["-case", base_case_name])

        # 添加 -C 选项（仅编译）
        if "-C" not in new_parts:
            new_parts.append("-C")

        return " ".join(new_parts)
    
    @staticmethod
    def _modify_command_for_simulation(original_command, base_case_dir):
        """
        修改命令用于仿真（使用编译结果）
        
        Args:
            original_command (str): 原始命令
            base_case_dir (str): 基础用例目录路径
            
        Returns:
            str: 修改后的仿真命令
        """
        parts = original_command.split()
        new_parts = []
        
        i = 0
        while i < len(parts):
            part = parts[i]
            
            # 跳过 -C 选项（如果存在）
            if part == "-C":
                i += 1
                continue
            
            new_parts.append(part)
            i += 1
        
        # 添加 -R 选项指向基础用例目录
        if "-R" not in new_parts:
            new_parts.extend(["-R", base_case_dir])
            
        return " ".join(new_parts)

    @staticmethod
    def _modify_command_for_simulation_with_seed(original_command, base_case_dir, seed, rundir_name):
        """
        修改命令用于带有种子和工作目录的仿真

        Args:
            original_command (str): 原始命令
            base_case_dir (str): 基础用例目录路径
            seed (int): 随机种子
            rundir_name (str): 工作目录名称

        Returns:
            str: 修改后的仿真命令
        """
        # 首先应用基本的仿真命令修改
        command = BatchRunUtils._modify_command_for_simulation(original_command, base_case_dir)

        parts = command.split()
        new_parts = []

        i = 0
        seed_added = False
        rundir_added = False

        while i < len(parts):
            part = parts[i]

            if part == "-seed" and i + 1 < len(parts):
                # 替换已有的种子参数
                new_parts.extend(["-seed", str(seed)])
                seed_added = True
                i += 2  # 跳过下一个参数
            elif part == "-rundir" and i + 1 < len(parts):
                # 替换已有的工作目录参数
                new_parts.extend(["-rundir", rundir_name])
                rundir_added = True
                i += 2  # 跳过下一个参数
            else:
                new_parts.append(part)
                i += 1

        # 如果命令中没有种子参数，添加它
        if not seed_added:
            new_parts.extend(["-seed", str(seed)])

        # 如果命令中没有工作目录参数，添加它
        if not rundir_added:
            new_parts.extend(["-rundir", rundir_name])

        return " ".join(new_parts)

    @staticmethod
    def _modify_command_for_direct_simulation_with_seed(original_command, seed, rundir_name):
        """
        修改命令用于没有base_case的直接仿真（多次执行）

        Args:
            original_command (str): 原始命令
            seed (int): 随机种子
            rundir_name (str): 工作目录名称

        Returns:
            str: 修改后的仿真命令
        """
        parts = original_command.split()
        new_parts = []

        i = 0
        seed_added = False
        rundir_added = False

        while i < len(parts):
            part = parts[i]

            if part == "-seed" and i + 1 < len(parts):
                # 替换已有的种子参数
                new_parts.extend(["-seed", str(seed)])
                seed_added = True
                i += 2  # 跳过下一个参数
            elif part == "-rundir" and i + 1 < len(parts):
                # 替换已有的工作目录参数
                new_parts.extend(["-rundir", rundir_name])
                rundir_added = True
                i += 2  # 跳过下一个参数
            else:
                new_parts.append(part)
                i += 1

        # 如果命令中没有种子参数，添加它
        if not seed_added:
            new_parts.extend(["-seed", str(seed)])

        # 如果命令中没有工作目录参数，添加它
        if not rundir_added:
            new_parts.extend(["-rundir", rundir_name])

        return " ".join(new_parts)
