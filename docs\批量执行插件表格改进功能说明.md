# 批量执行插件表格改进功能说明

## 概述

为了提升用户在查看和管理大量用例时的体验，批量执行插件的用例表格界面新增了两个重要的用户体验改进功能：

1. **可调整列宽功能**
2. **命令悬停提示功能**

## 功能详细说明

### 1. 可调整列宽功能

#### 功能描述
- 用户可以通过鼠标拖拽来调整表格各列的宽度
- 支持所有列的宽度调整（选择、用例名、状态、命令、开始时间、结束时间）
- 调整后的列宽会保持，直到用户重新调整

#### 使用方法
1. 将鼠标移动到表格列标题之间的分隔线上
2. 鼠标指针会变成双向箭头（↔）
3. 按住鼠标左键并拖拽来调整列宽
4. 释放鼠标完成调整

#### 初始列宽设置
- **选择列**：60像素
- **用例名列**：200像素
- **状态列**：100像素
- **命令列**：300像素
- **开始时间列**：100像素
- **结束时间列**：100像素（自动拉伸填充剩余空间）

#### 技术实现
- 使用`QHeaderView.Interactive`模式启用列宽调整
- 设置`setStretchLastSection(True)`使最后一列自动填充剩余空间
- 通过`setColumnWidth()`设置合理的初始列宽

### 2. 命令悬停提示功能

#### 功能描述
- 当runsim命令过长在表格中显示不完整时，提供鼠标悬停提示
- 悬停时显示完整的命令内容
- 长命令自动分行显示，提高可读性
- 工具提示有合适的样式和定位

#### 使用方法
1. 将鼠标悬停在"命令"列的任意单元格上
2. 等待约1秒钟，会自动显示工具提示
3. 工具提示显示完整的命令内容
4. 移开鼠标，工具提示自动消失

#### 命令格式化规则
- **短命令**（≤80字符）：直接显示原始命令
- **长命令**（>80字符）：自动在参数处分行显示
  ```
  原始命令：
  runsim -case very_long_case_name -block udtb/very_long_subsystem -base very_long_base -cfd_def VERY_LONG_CFD_DEF
  
  格式化显示：
  runsim -case very_long_case_name
    -block udtb/very_long_subsystem
    -base very_long_base
    -cfd_def VERY_LONG_CFD_DEF
  ```

#### 技术实现
- 创建自定义`CommandTableWidgetItem`类继承`QTableWidgetItem`
- 重写工具提示设置，提供格式化的命令显示
- 使用智能分行算法，在参数边界处分行

## 使用场景

### 可调整列宽功能适用场景
1. **用例名过长**：调整用例名列宽以完整显示长用例名
2. **命令参数复杂**：扩大命令列宽以显示更多命令内容
3. **时间信息重要**：调整时间列宽以更好地查看执行时间
4. **屏幕空间优化**：根据屏幕大小和个人偏好调整列宽比例

### 命令悬停提示功能适用场景
1. **复杂命令参数**：查看包含多个参数的完整runsim命令
2. **长路径参数**：查看完整的block路径和base参数
3. **调试和验证**：确认生成的命令参数是否正确
4. **命令复制**：通过悬停查看完整命令，便于手动复制

## 示例演示

### 示例1：短命令
```bash
# 表格显示：runsim -case simple_test -block udtb/simple
# 悬停提示：runsim -case simple_test -block udtb/simple
```

### 示例2：长命令
```bash
# 表格显示：runsim -case very_long_test_case_name_that_should_demonstrate_tooltip_functionality -block udtb/very_long_subsystem_name/very_long_environment_name -base very_long_base_name_for_testing_purposes -cfd_def VERY_LONG_CFD_DEFINITION_NAME_FOR_TESTING_TOOLTIP_DISPLAY

# 悬停提示：
runsim -case very_long_test_case_name_that_should_demonstrate_tooltip_functionality
  -block udtb/very_long_subsystem_name/very_long_environment_name
  -base very_long_base_name_for_testing_purposes
  -cfd_def VERY_LONG_CFD_DEFINITION_NAME_FOR_TESTING_TOOLTIP_DISPLAY
```

## 兼容性说明

- 这两个功能完全向后兼容，不影响现有功能
- 适用于所有支持的用例文件格式（.cfg、.txt、.list、.lst）
- 在不同操作系统（Windows、Linux）上表现一致
- 支持高DPI显示器的正确缩放

## 性能影响

- **可调整列宽功能**：无性能影响，仅改变UI行为
- **命令悬停提示功能**：极小的内存开销，每个命令项额外存储格式化的工具提示文本
- 两个功能都不影响命令执行性能

## 总结

这两个改进功能显著提升了批量执行插件的用户体验：

1. **可调整列宽功能**解决了表格列宽固定导致的显示问题
2. **命令悬停提示功能**解决了长命令显示不完整的问题

用户现在可以更灵活地查看和管理用例信息，特别是在处理大量复杂用例时，这些功能将大大提高工作效率。
