"""
任务队列管理器
负责任务队列的整体管理、调度和资源分配
"""
import os
import json
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
from PyQt5.QtCore import QObject, QTimer, pyqtSignal, QThread

from .task_queue_core import Task, TaskQueue, TaskStatus, TaskPriority, TaskMetadata
from .process_manager import ProcessManager
from .task_queue_config import TaskQueueConfig


class TaskScheduler(QObject):
    """任务调度器"""
    
    # 信号定义
    task_scheduled = pyqtSignal(object)  # 任务被调度
    scheduler_error = pyqtSignal(str)    # 调度器错误
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.max_concurrent_tasks = 5  # 最大并发任务数
        self.scheduling_interval = 1000  # 调度间隔（毫秒）
        self.is_running = False
        
        # 调度定时器
        self.schedule_timer = QTimer()
        self.schedule_timer.timeout.connect(self.schedule_tasks)
        
    def start_scheduling(self):
        """开始任务调度"""
        if not self.is_running:
            self.is_running = True
            print(f"启动调度器定时器，间隔: {self.scheduling_interval}ms")
            self.schedule_timer.start(self.scheduling_interval)
            print(f"调度器定时器状态: {'活跃' if self.schedule_timer.isActive() else '非活跃'}")
    
    def stop_scheduling(self):
        """停止任务调度"""
        if self.is_running:
            self.is_running = False
            self.schedule_timer.stop()
    
    def schedule_tasks(self):
        """调度任务（由定时器调用）"""
        # 这个方法会被TaskQueueManager重写
        pass
    
    def set_max_concurrent_tasks(self, max_tasks: int):
        """设置最大并发任务数"""
        self.max_concurrent_tasks = max(1, min(20, max_tasks))
    
    def set_scheduling_interval(self, interval_ms: int):
        """设置调度间隔"""
        self.scheduling_interval = max(100, interval_ms)
        if self.is_running:
            self.schedule_timer.setInterval(self.scheduling_interval)


class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        self.max_memory_mb = 4096  # 最大内存使用（MB）
        self.max_cpu_percent = 80  # 最大CPU使用率
        self.resource_lock = threading.Lock()
        self.allocated_resources = {}  # 已分配的资源
        
    def can_allocate_resources(self, task: Task) -> bool:
        """检查是否可以为任务分配资源"""
        with self.resource_lock:
            # 简单的资源检查逻辑
            # 实际实现中可以根据任务类型和系统资源情况进行更复杂的判断
            return len(self.allocated_resources) < 10
    
    def allocate_resources(self, task_id: str) -> bool:
        """为任务分配资源"""
        with self.resource_lock:
            if task_id not in self.allocated_resources:
                self.allocated_resources[task_id] = {
                    'allocated_time': datetime.now(),
                    'memory_mb': 512,  # 默认分配512MB
                    'cpu_percent': 10  # 默认分配10%CPU
                }
                return True
            return False
    
    def release_resources(self, task_id: str):
        """释放任务资源"""
        with self.resource_lock:
            if task_id in self.allocated_resources:
                del self.allocated_resources[task_id]
    
    def get_resource_usage(self) -> Dict[str, int]:
        """获取资源使用情况"""
        with self.resource_lock:
            total_memory = sum(res['memory_mb'] for res in self.allocated_resources.values())
            total_cpu = sum(res['cpu_percent'] for res in self.allocated_resources.values())
            
            return {
                'allocated_tasks': len(self.allocated_resources),
                'memory_mb': total_memory,
                'cpu_percent': total_cpu
            }


class TaskQueueManager(QObject):
    """任务队列管理器"""
    
    # 信号定义
    task_started = pyqtSignal(object)      # 任务开始执行
    task_finished = pyqtSignal(object, bool, int, str)  # 任务完成 (task, success, exit_code, error_msg)
    task_progress_updated = pyqtSignal(object, float)    # 任务进度更新
    queue_stats_changed = pyqtSignal(dict)               # 队列统计变化
    manager_error = pyqtSignal(str)                      # 管理器错误
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 核心组件
        self.task_queue = TaskQueue(self)
        self.scheduler = TaskScheduler(self)
        self.resource_manager = ResourceManager()
        
        # 执行管理
        self.running_processes: Dict[str, ProcessManager] = {}
        self.execution_callbacks: Dict[str, Callable] = {}
        
        # 配置管理器
        self.config_manager = TaskQueueConfig()
        self.config = self.config_manager.config
        
        # 连接信号
        self._connect_signals()

        # 连接调度器信号
        self.scheduler.schedule_timer.timeout.disconnect()  # 断开默认连接
        self.scheduler.schedule_timer.timeout.connect(self._schedule_tasks)  # 连接到我们的方法

        # 自动保存定时器
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self._auto_save_state)
        auto_save_interval = self.config.get('auto_save_interval', 30000)
        self.auto_save_timer.start(auto_save_interval)
        
    def _connect_signals(self):
        """连接信号"""
        self.task_queue.task_added.connect(self._on_task_added)
        self.task_queue.task_removed.connect(self._on_task_removed)
        self.task_queue.task_status_changed.connect(self._on_task_status_changed)
        self.task_queue.queue_changed.connect(self._on_queue_changed)
    
    def start(self):
        """启动任务队列管理器"""
        try:
            # 检查是否已经在运行
            if self.scheduler.is_running:
                print("任务队列管理器已经在运行中")
                # 立即执行一次调度，处理当前队列中的任务
                self._schedule_tasks()
                return

            # 只在首次启动时加载队列状态
            if self.config.get('save_queue_state', True) and not hasattr(self, '_state_loaded'):
                print("首次启动，加载队列状态...")
                self.load_queue_state()
                self._state_loaded = True
            else:
                print("队列管理器重启，使用当前内存中的任务状态")

            # 启动调度器
            self.scheduler.start_scheduling()

            # 立即执行一次调度，处理已有的任务
            self._schedule_tasks()

            # 显示当前队列状态
            stats = self.get_queue_stats()
            print(f"任务队列管理器已启动 (调度间隔: {self.scheduler.scheduling_interval}ms)")
            print(f"调度器状态: {'运行中' if self.scheduler.is_running else '已停止'}")
            print(f"当前队列状态: 总计={stats['total']}, 排队={stats['queued']}, 运行={stats['running']}")

        except Exception as e:
            error_msg = f"启动任务队列管理器失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)
    
    def stop(self):
        """停止任务队列管理器"""
        try:
            # 停止调度器
            self.scheduler.stop_scheduling()

            # 停止自动保存定时器
            if hasattr(self, 'auto_save_timer'):
                self.auto_save_timer.stop()

            # 停止所有正在运行的任务
            for task_id in list(self.running_processes.keys()):
                self.cancel_task(task_id)

            # 保存队列状态
            if self.config.get('save_queue_state', True):
                self.save_queue_state()

            print("任务队列管理器已停止")
            
        except Exception as e:
            error_msg = f"停止任务队列管理器失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)
    
    def add_task(self, name: str, command: str, 
                 priority: TaskPriority = TaskPriority.NORMAL,
                 dependencies: Optional[List[str]] = None,
                 metadata: Optional[TaskMetadata] = None,
                 execution_callback: Optional[Callable] = None) -> Optional[str]:
        """添加任务到队列"""
        try:
            task = Task(
                name=name,
                command=command,
                priority=priority,
                dependencies=dependencies,
                metadata=metadata
            )
            
            if self.task_queue.add_task(task):
                # 保存执行回调
                if execution_callback:
                    self.execution_callbacks[task.task_id] = execution_callback

                # 立即触发调度，尝试启动新添加的任务
                if self.scheduler.is_running:
                    self._schedule_tasks()

                return task.task_id
            
            return None
            
        except Exception as e:
            error_msg = f"添加任务失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)
            return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            # 如果任务正在运行，停止进程
            if task_id in self.running_processes:
                process_manager = self.running_processes[task_id]
                process_manager.stop()
                del self.running_processes[task_id]
                
                # 释放资源
                self.resource_manager.release_resources(task_id)
            
            # 从队列中取消任务
            return self.task_queue.cancel_task(task_id)
            
        except Exception as e:
            error_msg = f"取消任务失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)
            return False
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        return self.task_queue.pause_task(task_id)
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        return self.task_queue.resume_task(task_id)
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.task_queue.get_task(task_id)
    
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        return self.task_queue.get_all_tasks()
    
    def get_queue_stats(self) -> Dict[str, int]:
        """获取队列统计"""
        stats = self.task_queue.get_queue_stats()
        stats.update(self.resource_manager.get_resource_usage())
        return stats
    
    def clear_completed_tasks(self):
        """清除已完成的任务"""
        print("清除已完成的任务...")

        # 获取清除前的统计
        before_stats = self.get_queue_stats()
        completed_count = before_stats.get('completed', 0)
        failed_count = before_stats.get('failed', 0)
        cancelled_count = before_stats.get('cancelled', 0)
        total_to_clear = completed_count + failed_count + cancelled_count

        # 清除任务
        self.task_queue.clear_completed_tasks()

        # 立即保存状态到文件
        if self.config.get('save_queue_state', True):
            self.save_queue_state()
            print(f"已清除 {total_to_clear} 个已完成的任务并保存状态")
        else:
            print(f"已清除 {total_to_clear} 个已完成的任务")

        # 获取清除后的统计
        after_stats = self.get_queue_stats()
        print(f"清除后队列状态: 总计={after_stats['total']}, 排队={after_stats['queued']}, 运行={after_stats['running']}")

    def trigger_scheduling(self):
        """手动触发任务调度"""
        print("手动触发任务调度")
        self._schedule_tasks()
    
    def set_config(self, key: str, value):
        """设置配置"""
        if self.config_manager.set(key, value):
            self.config = self.config_manager.config

            # 应用配置变化
            if key == 'max_concurrent_tasks':
                self.scheduler.set_max_concurrent_tasks(value)
            elif key == 'scheduling_interval':
                self.scheduler.set_scheduling_interval(value)

            # 保存配置
            self.config_manager.save_config()

    def _schedule_tasks(self):
        """调度任务（核心调度逻辑）"""
        try:
            # 检查是否可以启动新任务
            current_running = self.task_queue.get_running_count()
            max_concurrent = self.scheduler.max_concurrent_tasks
            pending_count = self.task_queue.get_pending_count()

            # 添加调试信息
            if pending_count > 0:
                print(f"调度器运行: 当前运行={current_running}, 最大并发={max_concurrent}, 待执行={pending_count}")

            if current_running >= max_concurrent:
                return

            # 获取下一个可执行的任务
            available_slots = max_concurrent - current_running

            for _ in range(available_slots):
                task = self.task_queue.get_next_task()
                if not task:
                    break

                print(f"尝试启动任务: {task.name}")

                # 检查资源是否可用
                if not self.resource_manager.can_allocate_resources(task):
                    # 资源不足，将任务重新放回队列头部
                    import heapq
                    heapq.heappush(self.task_queue._pending_queue, task)
                    print(f"资源不足，任务 {task.name} 重新排队")
                    break

                # 启动任务
                print(f"启动任务: {task.name}")
                self._start_task_execution(task)

        except Exception as e:
            error_msg = f"任务调度失败: {str(e)}"
            print(error_msg)
            self.scheduler.scheduler_error.emit(error_msg)

    def _start_task_execution(self, task: Task):
        """启动任务执行"""
        try:
            # 分配资源
            if not self.resource_manager.allocate_resources(task.task_id):
                print(f"为任务 {task.name} 分配资源失败")
                return

            # 标记任务开始
            if not self.task_queue.start_task(task.task_id):
                self.resource_manager.release_resources(task.task_id)
                print(f"启动任务 {task.name} 失败")
                return

            # 设置进度回调
            task.on_progress_updated = lambda t, progress: self.task_progress_updated.emit(t, progress)

            # 发出任务开始信号
            self.task_started.emit(task)

            # 调用执行回调（这会创建LogPanel并启动执行）
            if task.task_id in self.execution_callbacks:
                try:
                    self.execution_callbacks[task.task_id](task)

                    # 检查是否成功创建了LogPanel
                    log_panel = task.metadata.user_data.get('log_panel')
                    if log_panel:
                        # 连接LogPanel的进程完成信号
                        def on_log_panel_finished(exit_code):
                            self._on_task_process_finished(task.task_id, exit_code)

                        def on_log_panel_error(error_msg):
                            self._on_task_process_error(task.task_id, error_msg)

                        # 连接信号
                        if hasattr(log_panel.process_manager, 'process_finished'):
                            log_panel.process_manager.process_finished.connect(on_log_panel_finished)
                        if hasattr(log_panel.process_manager, 'process_error'):
                            log_panel.process_manager.process_error.connect(on_log_panel_error)

                        # 将LogPanel的进程管理器保存到running_processes中
                        self.running_processes[task.task_id] = log_panel.process_manager

                        print(f"任务 {task.name} 开始执行（通过LogPanel）")
                    else:
                        # 如果没有创建LogPanel，说明可能是标签页已满
                        self._cleanup_failed_task(task.task_id, "无法创建执行标签页")

                except Exception as e:
                    print(f"执行回调失败: {e}")
                    self._cleanup_failed_task(task.task_id, f"执行回调失败: {e}")
            else:
                # 没有执行回调，使用原有的ProcessManager方式
                self._start_task_with_process_manager(task)

        except Exception as e:
            error_msg = f"启动任务执行失败: {str(e)}"
            print(error_msg)
            self._cleanup_failed_task(task.task_id, error_msg)

    def _start_task_with_process_manager(self, task: Task):
        """使用ProcessManager启动任务（备用方案）"""
        try:
            # 创建进程管理器
            process_manager = ProcessManager(
                command=task.command,
                working_directory=task.metadata.work_directory or os.getcwd(),
                parent=self
            )

            # 连接进程信号
            process_manager.process_finished.connect(
                lambda exit_code: self._on_task_process_finished(task.task_id, exit_code)
            )
            process_manager.process_error.connect(
                lambda error_msg: self._on_task_process_error(task.task_id, error_msg)
            )

            # 启动进程
            if process_manager.start():
                self.running_processes[task.task_id] = process_manager
                print(f"任务 {task.name} 开始执行（通过ProcessManager）")
            else:
                # 启动失败，清理资源
                self._cleanup_failed_task(task.task_id, "进程启动失败")

        except Exception as e:
            self._cleanup_failed_task(task.task_id, f"ProcessManager启动失败: {e}")

    def _on_task_process_finished(self, task_id: str, exit_code: int):
        """任务进程完成回调"""
        try:
            success = exit_code == 0
            error_message = "" if success else f"进程退出码: {exit_code}"

            # 标记任务完成
            self.task_queue.finish_task(task_id, success, exit_code, error_message)

            # 清理资源
            self._cleanup_task_resources(task_id)

            # 获取任务对象
            task = self.task_queue.get_task(task_id)
            if task:
                # 发出任务完成信号
                self.task_finished.emit(task, success, exit_code, error_message)
                print(f"任务 {task.name} 执行{'成功' if success else '失败'}")

        except Exception as e:
            error_msg = f"处理任务完成事件失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)

    def _on_task_process_error(self, task_id: str, error_message: str):
        """任务进程错误回调"""
        try:
            # 标记任务失败
            self.task_queue.finish_task(task_id, False, -1, error_message)

            # 清理资源
            self._cleanup_task_resources(task_id)

            # 获取任务对象
            task = self.task_queue.get_task(task_id)
            if task:
                # 发出任务完成信号
                self.task_finished.emit(task, False, -1, error_message)
                print(f"任务 {task.name} 执行出错: {error_message}")

        except Exception as e:
            error_msg = f"处理任务错误事件失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)

    def _cleanup_failed_task(self, task_id: str, error_message: str):
        """清理失败的任务"""
        # 标记任务失败
        self.task_queue.finish_task(task_id, False, -1, error_message)

        # 清理资源
        self._cleanup_task_resources(task_id)

    def _cleanup_task_resources(self, task_id: str):
        """清理任务资源"""
        # 移除进程管理器
        if task_id in self.running_processes:
            del self.running_processes[task_id]

        # 释放系统资源
        self.resource_manager.release_resources(task_id)

        # 移除执行回调
        if task_id in self.execution_callbacks:
            del self.execution_callbacks[task_id]

    def _on_task_added(self, task: Task):
        """任务添加回调"""
        pass

    def _on_task_removed(self, task_id: str):
        """任务移除回调"""
        pass

    def _on_task_status_changed(self, task: Task, old_status: str, new_status: str):
        """任务状态变化回调"""
        pass

    def _on_queue_changed(self):
        """队列变化回调"""
        # 发出统计信息变化信号
        stats = self.get_queue_stats()
        self.queue_stats_changed.emit(stats)

    def save_queue_state(self):
        """保存队列状态到文件"""
        try:
            state_file = self.config['queue_state_file']
            tasks_data = []

            # get_all_tasks() 返回字典 {task_id: Task}，需要获取values()
            all_tasks = self.task_queue.get_all_tasks()
            if isinstance(all_tasks, dict):
                tasks_to_process = all_tasks.values()
            else:
                tasks_to_process = all_tasks

            for task in tasks_to_process:
                # 确保task是Task对象
                if not hasattr(task, 'status') or not hasattr(task, 'to_dict'):
                    print(f"警告: 跳过无效的任务对象: {type(task)}")
                    continue

                # 只保存未完成的任务（排除已完成、失败、已取消的任务）
                try:
                    if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                        task_dict = task.to_dict()
                        tasks_data.append(task_dict)
                except Exception as e:
                    print(f"警告: 序列化任务失败 {task.name if hasattr(task, 'name') else 'Unknown'}: {e}")
                    continue

            print(f"保存 {len(tasks_data)} 个未完成的任务到状态文件")

            state_data = {
                'tasks': tasks_data,
                'config': self.config,
                'saved_time': datetime.now().isoformat()
            }

            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)

            print(f"队列状态已保存到 {state_file}")

        except Exception as e:
            error_msg = f"保存队列状态失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)

    def load_queue_state(self):
        """从文件加载队列状态"""
        try:
            state_file = self.config['queue_state_file']

            if not os.path.exists(state_file):
                return

            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 恢复配置
            if 'config' in state_data:
                self.config.update(state_data['config'])

            # 恢复任务
            if 'tasks' in state_data:
                for task_data in state_data['tasks']:
                    try:
                        task = Task.from_dict(task_data)
                        # 重置运行状态的任务为排队状态
                        if task.status == TaskStatus.RUNNING:
                            task.set_status(TaskStatus.QUEUED)

                        self.task_queue.add_task(task)
                    except Exception as e:
                        print(f"恢复任务失败: {e}")

            print(f"队列状态已从 {state_file} 加载")

        except Exception as e:
            error_msg = f"加载队列状态失败: {str(e)}"
            print(error_msg)
            self.manager_error.emit(error_msg)

    def _auto_save_state(self):
        """自动保存队列状态"""
        if self.config.get('save_queue_state', True):
            try:
                self.save_queue_state()
            except Exception as e:
                print(f"自动保存队列状态失败: {e}")

    def get_config_manager(self):
        """获取配置管理器"""
        return self.config_manager

    def update_config(self, config_dict: Dict[str, Any]):
        """批量更新配置"""
        if self.config_manager.update(config_dict):
            self.config = self.config_manager.config

            # 应用关键配置变化
            if 'max_concurrent_tasks' in config_dict:
                self.scheduler.set_max_concurrent_tasks(config_dict['max_concurrent_tasks'])
            if 'scheduling_interval' in config_dict:
                self.scheduler.set_scheduling_interval(config_dict['scheduling_interval'])
            if 'auto_save_interval' in config_dict:
                self.auto_save_timer.setInterval(config_dict['auto_save_interval'])

            # 保存配置
            self.config_manager.save_config()
            return True
        return False

    def clear_state_file(self):
        """清除状态文件"""
        try:
            state_file = self.config.get('queue_state_file', 'task_queue_state.json')
            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"已删除状态文件: {state_file}")
            else:
                print(f"状态文件不存在: {state_file}")
        except Exception as e:
            print(f"删除状态文件失败: {e}")

    def reset_queue(self):
        """重置队列（清除所有任务和状态文件）"""
        try:
            # 停止调度器
            was_running = self.scheduler.is_running
            if was_running:
                self.scheduler.stop_scheduling()

            # 清除所有任务
            all_task_ids = list(self.task_queue._tasks.keys())
            for task_id in all_task_ids:
                self.task_queue.remove_task(task_id)

            # 清除运行中的进程
            for task_id in list(self.running_processes.keys()):
                self.cancel_task(task_id)

            # 清除状态文件
            self.clear_state_file()

            # 重置状态加载标志
            if hasattr(self, '_state_loaded'):
                delattr(self, '_state_loaded')

            # 如果之前在运行，重新启动调度器
            if was_running:
                self.scheduler.start_scheduling()

            print("队列已重置")

        except Exception as e:
            print(f"重置队列失败: {e}")
