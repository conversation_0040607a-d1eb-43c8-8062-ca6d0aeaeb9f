"""
随机种子生成器模块
"""
import random
import time
import os


class SeedGenerator:
    """随机种子生成器类"""
    
    @staticmethod
    def generate_unique_seeds(count):
        """
        生成指定数量的唯一随机种子
        
        Args:
            count (int): 需要生成的种子数量
            
        Returns:
            list: 唯一的随机种子列表
        """
        if count <= 0:
            return []
            
        seeds = set()
        
        # 使用当前时间作为基础种子
        base_seed = int(time.time())
        random.seed(base_seed)
        
        # 生成唯一的种子
        while len(seeds) < count:
            # 生成一个较大范围的随机数作为种子
            seed = random.randint(1, 2**31 - 1)
            seeds.add(seed)
            
        return list(seeds)
    
    @staticmethod
    def generate_rundir_name(case_name, seed):
        """
        生成工作目录名称
        
        Args:
            case_name (str): 用例名称
            seed (int): 随机种子
            
        Returns:
            str: 工作目录名称，格式为 {case_name}_{seed}
        """
        return f"{case_name}_{seed}"
    
    @staticmethod
    def check_rundir_exists(rundir_name, base_path=None):
        """
        检查工作目录是否已存在
        
        Args:
            rundir_name (str): 工作目录名称
            base_path (str, optional): 基础路径，默认为当前工作目录
            
        Returns:
            bool: 目录是否存在
        """
        if base_path is None:
            base_path = os.getcwd()
            
        full_path = os.path.join(base_path, rundir_name)
        return os.path.exists(full_path)
    
    @staticmethod
    def ensure_unique_rundir_names(case_name, seeds, base_path=None):
        """
        确保生成的工作目录名称都是唯一的（不与现有目录冲突）
        
        Args:
            case_name (str): 用例名称
            seeds (list): 种子列表
            base_path (str, optional): 基础路径，默认为当前工作目录
            
        Returns:
            list: 调整后的种子列表，确保对应的工作目录名称唯一
        """
        if base_path is None:
            base_path = os.getcwd()
            
        unique_seeds = []
        
        for seed in seeds:
            original_seed = seed
            rundir_name = SeedGenerator.generate_rundir_name(case_name, seed)

            # 如果目录已存在，尝试生成新的种子
            attempt_count = 0
            while SeedGenerator.check_rundir_exists(rundir_name, base_path):
                seed = random.randint(1, 2**31 - 1)
                rundir_name = SeedGenerator.generate_rundir_name(case_name, seed)
                attempt_count += 1

                # 防止无限循环，最多尝试100次
                if attempt_count >= 100:
                    break

            unique_seeds.append(seed)
            
        return unique_seeds
