"""
历史记录解析器
从历史命令和回归列表文件中提取参数值
"""
import os
import re
import json
from typing import Dict, List, Set, Optional, Any
from collections import defaultdict


class HistoryParser:
    """历史记录解析器"""
    
    def __init__(self):
        """初始化解析器"""
        # 命令行参数模式
        self.command_patterns = {
            'base': [
                r'-base\s+([^\s]+)',
                r'--base\s+([^\s]+)',
                r'BASE=([^\s]+)'
            ],
            'block': [
                r'-block\s+([^\s]+)',
                r'--block\s+([^\s]+)',
                r'BLOCK=([^\s]+)'
            ],
            'case': [
                r'-case\s+([^\s]+)',
                r'--case\s+([^\s]+)',
                r'CASE=([^\s]+)'
            ],
            'bq_server': [
                r'-bq\s+([^\s]+)',
                r'--bq\s+([^\s]+)',
                r'BQ=([^\s]+)'
            ],
            'rundir': [
                r'-rundir\s+([^\s]+)',
                r'--rundir\s+([^\s]+)',
                r'RUNDIR=([^\s]+)'
            ],
            'cfg_def': [
                r'-cfg_def\s+([^\s]+)',
                r'-cfd_def\s+([^\s]+)',
                r'--cfg_def\s+([^\s]+)',
                r'CFG_DEF=([^\s]+)'
            ],
            'simarg': [
                r'-simarg\s+([^\s]+)',
                r'--simarg\s+([^\s]+)',
                r'SIMARG=([^\s]+)'
            ],
            'wdd': [
                r'-wdd\s+([^\s]+)',
                r'--wdd\s+([^\s]+)',
                r'WDD=([^\s]+)'
            ]
        }
    
    def parse_command_history(self, history_file: str) -> Dict[str, Set[str]]:
        """
        解析命令历史文件
        
        Args:
            history_file (str): 历史文件路径
            
        Returns:
            Dict[str, Set[str]]: 解析出的参数值
        """
        results = defaultdict(set)
        
        try:
            if not os.path.exists(history_file):
                return dict(results)
            
            with open(history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 处理历史记录
            if isinstance(data, list):
                for record in data:
                    if isinstance(record, dict) and 'command' in record:
                        command = record['command']
                        self._extract_from_command(command, results)
            
        except Exception as e:
            print(f"解析命令历史文件失败: {e}")
        
        return dict(results)
    
    def parse_config_file(self, config_file: str) -> Dict[str, Set[str]]:
        """
        解析配置文件
        
        Args:
            config_file (str): 配置文件路径
            
        Returns:
            Dict[str, Set[str]]: 解析出的参数值
        """
        results = defaultdict(set)
        
        try:
            if not os.path.exists(config_file):
                return dict(results)
            
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 直接从配置中提取参数
            field_mappings = {
                'base': 'base',
                'block': 'block', 
                'bq_server': 'bq_server',
                'rundir': 'rundir',
                'cfg_def': 'cfg_def',
                'simarg': 'simarg',
                'wdd': 'wdd',
                'other_options': 'other_options'
            }
            
            for field_type, config_key in field_mappings.items():
                if config_key in data and data[config_key]:
                    value = str(data[config_key]).strip()
                    if value:
                        results[field_type].add(value)
            
            # 从case_files中提取路径信息
            if 'case_files' in data and isinstance(data['case_files'], list):
                for case_file in data['case_files']:
                    if isinstance(case_file, str):
                        # 从路径中提取可能的block信息
                        path_parts = case_file.replace('\\', '/').split('/')
                        for part in path_parts:
                            if part and not part.endswith('.cfg') and len(part) > 2:
                                results['block'].add(part)
        
        except Exception as e:
            print(f"解析配置文件失败: {e}")
        
        return dict(results)
    
    def parse_regression_list_files(self, file_paths: List[str]) -> Dict[str, Set[str]]:
        """
        解析回归列表文件
        
        Args:
            file_paths (List[str]): 回归列表文件路径列表
            
        Returns:
            Dict[str, Set[str]]: 解析出的参数值
        """
        results = defaultdict(set)
        
        for file_path in file_paths:
            try:
                if not os.path.exists(file_path):
                    continue
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 按行解析
                lines = content.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    
                    # 跳过空行和注释行
                    if not line or line.startswith('//'):
                        continue
                    
                    # 解析CSV格式的行
                    self._parse_regression_line(line, results)
            
            except Exception as e:
                print(f"解析回归列表文件 {file_path} 失败: {e}")
        
        return dict(results)
    
    def _extract_from_command(self, command: str, results: Dict[str, Set[str]]):
        """从命令中提取参数"""
        if not command:
            return
        
        for field_type, patterns in self.command_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, command, re.IGNORECASE)
                for match in matches:
                    value = match.strip().strip('"\'')
                    if value and value.lower() != 'default':
                        results[field_type].add(value)
    
    def _parse_regression_line(self, line: str, results: Dict[str, Set[str]]):
        """解析回归列表文件的单行"""
        try:
            # 智能分割CSV行（处理方括号内的逗号）
            fields = self._split_csv_line(line)
            
            if len(fields) < 3:
                return
            
            # 解析字段
            status = fields[0].strip()
            block_path = fields[1].strip()
            case_name = fields[2].strip()
            
            # 只处理启用的用例
            if status.upper() != 'ON':
                return
            
            # 添加基本字段
            if block_path:
                results['block'].add(block_path)
            if case_name:
                results['case'].add(case_name)
            
            # 解析可选字段
            if len(fields) > 8:  # CFG_DEF字段
                cfg_def = self._clean_field_value(fields[8])
                if cfg_def and cfg_def.lower() != 'default':
                    results['cfg_def'].add(cfg_def)
            
            if len(fields) > 9:  # base字段
                base_value = self._clean_field_value(fields[9])
                if base_value and base_value.lower() != 'default':
                    results['base'].add(base_value)
        
        except Exception as e:
            print(f"解析回归列表行失败: {e}")
    
    def _split_csv_line(self, line: str) -> List[str]:
        """智能分割CSV行"""
        try:
            # 处理方括号内的逗号
            bracket_pattern = r'\[([^\]]*)\]'
            bracket_matches = re.findall(bracket_pattern, line)
            
            temp_line = line
            temp_replacements = {}
            
            for i, match in enumerate(bracket_matches):
                placeholder = f"__BRACKET_CONTENT_{i}__"
                bracket_content = match.replace(',', '__COMMA__')
                temp_replacements[placeholder] = f"[{bracket_content}]"
                temp_line = temp_line.replace(f"[{match}]", placeholder)
            
            # 分割字段
            fields = [field.strip() for field in temp_line.split(',')]
            
            # 恢复方括号内容
            for i, field in enumerate(fields):
                for placeholder, original in temp_replacements.items():
                    if placeholder in field:
                        restored = original.replace('__COMMA__', ',')
                        fields[i] = field.replace(placeholder, restored)
            
            return fields
        
        except Exception:
            # 回退到简单分割
            return [field.strip() for field in line.split(',')]
    
    def _clean_field_value(self, value: str) -> str:
        """清理字段值"""
        if not value:
            return ''
        
        value = value.strip()
        
        # 去除方括号
        if value.startswith('[') and value.endswith(']'):
            value = value[1:-1].strip()
        
        # 去除引号
        if value.startswith('"') and value.endswith('"'):
            value = value[1:-1].strip()
        elif value.startswith("'") and value.endswith("'"):
            value = value[1:-1].strip()
        
        return value
    
    def find_regression_list_files(self, search_dirs: List[str]) -> List[str]:
        """
        查找回归列表文件
        
        Args:
            search_dirs (List[str]): 搜索目录列表
            
        Returns:
            List[str]: 找到的回归列表文件路径
        """
        regression_files = []
        
        for search_dir in search_dirs:
            if not os.path.exists(search_dir):
                continue
            
            try:
                for root, dirs, files in os.walk(search_dir):
                    for file in files:
                        if file.endswith(('.list', '.lst')):
                            file_path = os.path.join(root, file)
                            regression_files.append(file_path)
            except Exception as e:
                print(f"搜索目录 {search_dir} 失败: {e}")
        
        return regression_files
    
    def parse_all_sources(self, history_file: str = None, 
                         config_file: str = None,
                         regression_dirs: List[str] = None) -> Dict[str, Set[str]]:
        """
        解析所有数据源
        
        Args:
            history_file (str): 历史文件路径
            config_file (str): 配置文件路径
            regression_dirs (List[str]): 回归列表文件搜索目录
            
        Returns:
            Dict[str, Set[str]]: 合并后的参数值
        """
        all_results = defaultdict(set)
        
        # 解析命令历史
        if history_file:
            history_results = self.parse_command_history(history_file)
            for field_type, values in history_results.items():
                all_results[field_type].update(values)
        
        # 解析配置文件
        if config_file:
            config_results = self.parse_config_file(config_file)
            for field_type, values in config_results.items():
                all_results[field_type].update(values)
        
        # 解析回归列表文件
        if regression_dirs:
            regression_files = self.find_regression_list_files(regression_dirs)
            if regression_files:
                regression_results = self.parse_regression_list_files(regression_files)
                for field_type, values in regression_results.items():
                    all_results[field_type].update(values)
        
        return dict(all_results)
