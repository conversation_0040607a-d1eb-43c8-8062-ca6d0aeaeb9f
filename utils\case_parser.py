"""
用例文件解析模块
"""
import os
import re

class CaseParser:
    """用例文件解析器"""

    @staticmethod
    def parse_single_file(case_file, pattern=r'\[case\s+([\w_]+)(?:\s*:\s*([\w_]+))?.*'):
        """
        解析单个用例文件

        Args:
            case_file (str): 用例文件路径
            pattern (str): 用例匹配的正则表达式

        Returns:
            dict: 解析结果，包含文件名、节点和子用例
        """
        file_data = {
            'name': os.path.basename(case_file),
            'nodes': {},
            'child_cases': []
        }

        # 使用生成器读取文件，减少内存使用
        def read_large_file(file_path, chunk_size=8192):
            with open(file_path, 'r') as f:
                buffer = ''
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        if buffer:
                            yield buffer
                        break
                    buffer += chunk
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        yield line

        # 解析文件内容
        for line in read_large_file(case_file):
            if match := re.match(pattern, line.strip()):
                case, base = match.groups()
                if base:
                    file_data['child_cases'].append((case, base))
                else:
                    file_data['nodes'][case] = []

        return file_data

    @staticmethod
    def parse_base_block_from_path(file_path):
        """
        根据用例配置文件路径解析 base 和 block 参数。

        Args:
            file_path (str): 用例配置文件的路径。

        Returns:
            tuple: (base, block) 参数，如果解析失败返回 (None, None)。

        规则：
        1. dv/{subsys}/bin/case_cfg/xxx_case.cfg: -base为空，-block为{subsys}名
        2. dv/udtb/{subsys}/子环境名/bin/xxx.cfg：-base为{subsys}名，-block为udtb/{subsys}/子环境名
        3. dv/udtb/usvp/bin/case_cfg/<sys>_subsys_case.cfg：-base为<sys>_sys，-block为udtb/usvp
        4. dv/udtb/usvp/bin/case_cfg/<sys>_top_case.cfg：-base为top，-block为udtb/usvp
        5. dv/udtb/usvp/bin/case_cfg/xxx.cfg：-base为top，-block为udtb/usvp
        """
        try:
            # 将路径转换为标准格式，统一路径分隔符
            path = os.path.normpath(file_path)
            parts = path.split(os.sep)

            # 查找 "dv" 目录位置，作为路径解析的起点
            if "dv" not in parts:
                return None, None

            dv_index = parts.index("dv")

            # 情况1: dv/{subsys}/bin/case_cfg/xxx_case.cfg 路径格式
            # -base为空，-block为{subsys}名
            if len(parts) > dv_index + 4 and parts[dv_index + 3] == "case_cfg" and "udtb" not in parts:
                subsys = parts[dv_index + 1]

                # 处理文件名中可能包含的额外信息，例如apcpu-sys_bus_case.cfg
                filename = os.path.basename(file_path)
                if "-sys" in filename:
                    # 尝试从文件名中提取更精确的subsys信息
                    name_parts = filename.split("_")[0].split("-")
                    if len(name_parts) >= 2 and name_parts[1] == "sys":
                        # 如果文件名格式为 apcpu-sys_xxx，则使用apcpu作为block
                        subsys = name_parts[0]

                # 返回结果：base为空，block为subsys
                result = ("", subsys)
                return result

            # 情况2: dv/udtb/{subsys}/子环境名/bin/xxx.cfg 路径格式
            # -base为{subsys}名，-block为udtb/{subsys}/子环境名
            if "udtb" in parts and "bin" in parts:
                udtb_index = parts.index("udtb")
                if len(parts) > udtb_index + 3 and parts[udtb_index + 1] != "usvp":
                    subsys = parts[udtb_index + 1]
                    subenv = parts[udtb_index + 2]

                    # 返回结果：base为subsys，block为udtb/{subsys}/{subenv}
                    result = (subsys, f"udtb/{subsys}/{subenv}")
                    return result

            # 情况3、4、5: dv/udtb/usvp/bin/case_cfg/ 相关路径格式
            if "udtb" in parts and "usvp" in parts:
                filename = os.path.basename(file_path)

                # 处理文件名中包含连字符的情况，例如apcpu-sys_bus_case.cfg
                if "-sys" in filename:
                    name_parts = filename.split("_")[0].split("-")
                    if len(name_parts) >= 2 and name_parts[1] == "sys":
                        sys_name = name_parts[0]
                        result = (f"{sys_name}_sys", "udtb/usvp")
                        return result

                # 情况3: dv/udtb/usvp/bin/case_cfg/<sys>_subsys_case.cfg
                # -base为<sys>_sys，-block为udtb/usvp
                subsys_match = re.match(r"^(\w+)_subsys(?:_case)?\.cfg$", filename, re.IGNORECASE)
                if subsys_match:
                    sys_name = subsys_match.group(1)
                    result = (f"{sys_name}_sys", "udtb/usvp")
                    return result

                # 情况4: dv/udtb/usvp/bin/case_cfg/<sys>_top_case.cfg
                # -base为top，-block为udtb/usvp
                top_match = re.match(r"^(\w+)_top(?:_case)?\.cfg$", filename, re.IGNORECASE)
                if top_match:
                    result = ("top", "udtb/usvp")
                    return result

                # 尝试匹配特定的系统名称格式，例如apcpu_bus_case.cfg
                # 只有当文件名明确包含已知系统名称时才使用特定的base
                known_systems = ['apcpu', 'ch', 'sp', 'aon', 'spch', 'ps_cp', 'phy_cp']
                parts_match = re.match(r"^(\w+)(?:_\w+)*(?:_case)?\.cfg$", filename, re.IGNORECASE)
                if parts_match:
                    sys_name = parts_match.group(1)
                    # 检查是否应该使用top作为base
                    if "top" in filename.lower():
                        result = ("top", "udtb/usvp")
                        return result
                    # 只有当系统名称在已知系统列表中时，才使用{sys_name}_sys
                    elif sys_name in known_systems:
                        result = (f"{sys_name}_sys", "udtb/usvp")
                        return result

                # 情况5: dv/udtb/usvp/bin/case_cfg/xxx.cfg (默认情况)
                # -base为top，-block为udtb/usvp
                result = ("top", "udtb/usvp")
                return result

            # 无法匹配任何规则，返回None, None
            return None, None
        except Exception as e:
            print(f"Debug - 解析异常: {str(e)}")
            return None, None
