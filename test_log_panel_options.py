#!/usr/bin/env python3
"""
测试LogPanel中的仿真选项控制功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from views.log_panel import LogPanel

class TestLogPanelWindow(QMainWindow):
    """测试LogPanel的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LogPanel 仿真选项控制测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加说明标签
        info_label = QLabel("测试LogPanel中的-fsdb和-R复选框功能")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14pt; font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)
        
        # 创建测试用的LogPanel
        test_command = "runsim -base test_base -block test_block -case test_case1 -rundir ./run1"
        self.log_panel = LogPanel("test_case1", test_command)
        layout.addWidget(self.log_panel)
        
        # 连接信号以便观察变化
        self.log_panel.fsdb_checkbox.stateChanged.connect(self.on_fsdb_changed)
        self.log_panel.r_checkbox.stateChanged.connect(self.on_r_changed)
        
        print(f"初始命令: {test_command}")
    
    def on_fsdb_changed(self, state):
        """处理-fsdb复选框变化"""
        enabled = state == Qt.Checked
        print(f"-fsdb选项变更: {'启用' if enabled else '禁用'}")
        print(f"当前命令: {self.log_panel.command}")
    
    def on_r_changed(self, state):
        """处理-R复选框变化"""
        enabled = state == Qt.Checked
        print(f"-R选项变更: {'启用' if enabled else '禁用'}")
        print(f"当前命令: {self.log_panel.command}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = TestLogPanelWindow()
    window.show()
    
    print("GUI测试启动成功！")
    print("请在界面中测试-fsdb和-R复选框的功能")
    print("观察控制台输出以查看命令变化")
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
