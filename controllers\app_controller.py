"""
应用程序控制器模块

该模块实现了应用程序的主控制器，负责协调各个子控制器、模型和视图之间的交互。
AppController 是应用程序的核心，管理整个应用程序的生命周期，包括初始化、
配置加载和保存、历史记录管理、插件系统集成以及资源优化。
"""
import os
import sys
import gc
from PyQt5.QtWidgets import QApplication, QFileDialog, QMessageBox, QTabWidget
from PyQt5.QtCore import QObject, pyqtSlot, QTimer

from utils.event_bus import EventBus
from utils.resource_optimizer import ResourceOptimizer

from models.config_model import ConfigModel
from models.history_model import HistoryModel
from views.main_window import MainWindow
from controllers.case_controller import CaseController
from controllers.config_controller import ConfigController
from controllers.execution_controller import ExecutionController
from controllers.dashboard_controller import DashboardController

class AppController(QObject):
    """
    应用程序控制器，负责协调各个控制器和视图

    AppController 是应用程序的主控制器，负责初始化和协调各个子控制器、
    模型和视图之间的交互。它管理应用程序的生命周期，包括配置加载和保存、
    历史记录管理、插件系统集成以及资源优化。

    主要职责：
    1. 初始化应用程序的模型、视图和控制器
    2. 管理配置的加载和保存
    3. 管理历史记录
    4. 集成插件系统
    5. 优化资源使用

    Attributes:
        config_model (ConfigModel): 配置数据模型
        history_model (HistoryModel): 历史记录模型
        main_window (MainWindow): 主窗口
        case_controller (CaseController): 用例控制器
        config_controller (ConfigController): 配置控制器
        execution_controller (ExecutionController): 执行控制器
        event_bus (EventBus): 事件总线
        plugin_manager (PluginManager): 插件管理器
        resource_optimizer (ResourceOptimizer): 资源优化器
    """

    def __init__(self):
        """
        初始化应用程序控制器

        创建并初始化应用程序的模型、视图和控制器，设置信号连接，
        加载配置和历史记录，初始化插件系统和资源优化器。
        """
        super().__init__()

        # 创建模型
        self.config_model = ConfigModel()
        self.history_model = HistoryModel()

        # 创建主窗口
        self.main_window = MainWindow()

        # 设置主窗口的app_controller引用
        self.main_window.app_controller = self

        # 创建右侧面板容器
        from PyQt5.QtWidgets import QWidget, QTabWidget

        # 创建子控制器
        self.case_controller = CaseController(self.main_window, self.config_model)

        # 创建配置控制器和执行控制器
        self.config_controller = ConfigController(
            self.main_window,
            self.config_model,
            self.history_model,
            self  # 传入app_controller引用
        )

        self.execution_controller = ExecutionController(
            self.main_window,
            self.config_model,
            self.history_model
        )

        # 创建数据看板控制器
        self.dashboard_controller = DashboardController(self.main_window)

        # 设置配置控制器的执行控制器引用
        self.config_controller.set_execution_controller(self.execution_controller)

        # 创建标签页控件作为右侧面板
        right_panel = QTabWidget()
        right_panel.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background: white;
                border-radius: 3px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover:!selected {
                background: #e6f3ff;
            }
        """)

        # 将配置面板和执行面板添加为标签页
        right_panel.addTab(self.config_controller.config_panel, "运行参数配置")
        right_panel.addTab(self.execution_controller.execution_panel, "执行日志")

        # 添加任务监控面板
        right_panel.addTab(self.execution_controller.get_task_monitor_panel(), "任务队列")

        # 设置默认显示"运行参数配置"标签页
        right_panel.setCurrentIndex(0)

        # 设置左侧和右侧面板
        self.main_window.set_left_panel(self.case_controller.case_panel)
        self.main_window.set_right_panel(right_panel)

        # 创建事件总线
        self.event_bus = EventBus.instance()

        # 连接信号
        self.connect_signals()

        # 使用事件总线连接控制器之间的信号
        try:
            # 用例选择信号
            self.event_bus.case_selected.connect(self.on_case_selected)

            # 命令执行信号（ExecutionController已经在自己的connect_signals中连接了，这里不需要重复连接）
            # self.event_bus.command_executed.connect(self.execution_controller.execute_command)  # 移除重复连接
            self.event_bus.command_executed.connect(self.dashboard_controller.on_simulation_command_executed)

            # 配置变更信号
            self.event_bus.config_changed.connect(self.config_controller.on_config_changed)

            # 历史记录更新信号
            self.event_bus.history_updated.connect(self.config_controller.update_history_view)
        except Exception as e:
            print(f"警告: 连接事件总线信号时出错: {str(e)}")

        # 加载配置和历史记录
        self.load_config()
        self.load_history()

        # 初始化插件系统
        self.init_plugins()

        # 初始化资源优化器
        self.init_resource_optimizer()

        # 初始化任务队列菜单
        self.init_queue_menu()

    def connect_signals(self):
        """
        连接信号和槽

        连接主窗口的信号到相应的槽函数，包括保存配置、加载配置和清除历史记录等。
        这些连接使得用户界面的操作能够触发相应的功能。
        """
        # 主窗口信号
        self.main_window.save_config_requested.connect(self.save_config)
        self.main_window.load_config_requested.connect(self.load_config)
        self.main_window.clear_history_requested.connect(self.clear_history)

    def show(self):
        """
        显示主窗口

        显示应用程序的主窗口，使其对用户可见。
        """
        self.main_window.show()

    @pyqtSlot()
    def save_config(self):
        """
        保存配置

        将当前配置保存到配置文件中，并在状态栏显示保存结果。
        如果保存失败，则显示错误对话框。

        Returns:
            bool: 保存是否成功
        """
        success = self.config_model.save_config()
        if success:
            self.main_window.show_message("配置已保存")
        else:
            self.main_window.show_error("保存失败", "无法保存配置文件")
        return success

    @pyqtSlot()
    def load_config(self):
        """
        加载配置

        从配置文件加载配置，更新窗口位置和大小，并通知子控制器配置已加载。
        确保种子号为空字符串，以避免使用旧的种子号。

        Returns:
            dict: 加载的配置
        """
        config = self.config_model.load_config()

        # 更新窗口位置和大小
        if "window" in config:
            window_config = config["window"]
            if not window_config.get("maximized", False):
                self.main_window.setGeometry(
                    window_config.get("x", 100),
                    window_config.get("y", 100),
                    window_config.get("width", 1400),
                    window_config.get("height", 900)
                )
            else:
                self.main_window.showMaximized()

        # 确保seed为空字符串
        config["seed"] = ""
        self.config_model.config["seed"] = ""

        # 通知子控制器配置已加载
        self.case_controller.on_config_loaded(config)
        self.config_controller.on_config_loaded(config)
        self.execution_controller.on_config_loaded(config)

        self.main_window.show_message("配置已加载")
        return config

    @pyqtSlot()
    def load_history(self):
        """
        加载历史记录

        从历史记录文件加载历史记录，并更新配置控制器的历史记录视图。

        Returns:
            list: 加载的历史记录列表
        """
        history = self.history_model.load_history()
        self.config_controller.update_history_view(history)
        return history

    @pyqtSlot()
    def clear_history(self):
        """
        清空历史记录

        显示确认对话框，询问用户是否确定要清除所有历史记录。
        如果用户确认，则清空历史记录并更新历史记录视图。

        Returns:
            bool: 是否清空了历史记录
        """
        reply = QMessageBox.question(
            self.main_window,
            "确认清除",
            "确定要清除所有历史记录吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.history_model.clear_history()
            self.config_controller.update_history_view([])
            self.main_window.show_message("历史记录已清除")
            return True
        return False

    def init_plugins(self):
        """
        初始化插件系统

        导入插件管理器，确保辅助模块可用，创建插件管理器实例，
        加载插件并设置插件菜单。如果插件系统不可用或初始化过程中
        出现错误，则打印错误信息并跳过插件加载。

        Returns:
            bool: 插件系统是否成功初始化
        """
        try:
            # 导入插件管理器
            from plugins.manager import PluginManager

            # 确保 utils 目录中的模块可用
            from utils import async_logger, async_task_manager, cache_manager, common_widgets

            # 创建插件管理器
            self.plugin_manager = PluginManager(self.main_window)

            # 加载插件
            self.plugin_manager.load_plugins()

            # 设置插件菜单
            self.plugin_manager.setup_plugin_menu()

            return True

        except ImportError:
            print("插件系统未找到，跳过插件加载")
            return False
        except Exception as e:
            print(f"初始化插件系统时出错: {str(e)}")
            return False

    @pyqtSlot(str)
    def on_case_selected(self, case_name):
        """
        处理用例选择事件

        当用户在用例树中选择用例时，更新配置面板中的用例名称，
        清空与具体用例相关的配置选项，以确保每次运行使用新的配置，
        并触发命令预览的更新。

        Args:
            case_name (str): 用例名称，即用例文件的路径或名称

        Returns:
            bool: 是否成功处理了用例选择事件
        """
        # 更新配置面板中的用例名称
        if hasattr(self.config_controller, 'config_panel') and hasattr(self.config_controller.config_panel, 'case_input'):
            # 使用程序化设置文本，避免触发自动补全
            if hasattr(self.config_controller.config_panel.case_input, 'set_text_programmatically'):
                self.config_controller.config_panel.case_input.set_text_programmatically(case_name)
            else:
                self.config_controller.config_panel.case_input.setText(case_name)

            # 更新配置模型中的用例名称
            self.config_model.update_config({"case": case_name})

            # 清空与具体用例相关的配置选项（不包括多选提示）
            if case_name and not case_name.startswith("已选择"):
                self._clear_case_specific_options()

            # 强制立即触发命令预览更新
            if hasattr(self.config_controller, 'generate_command_preview'):
                # 确保命令预览立即更新，不受延迟影响
                self.config_controller.config_panel.config_changed_flag = True
                self.config_controller.config_panel.last_user_input = 0  # 设置为0以确保立即更新
                self.config_controller.generate_command_preview()

            return True
        return False

    def _clear_case_specific_options(self):
        """
        清空与具体用例相关的配置选项

        当选择新用例时，清空一些与具体用例相关的配置，
        包括：rundir、seed、其他选项、simarg、cfg_def、dump_sva、cov、upf、
        执行模式选项、内存dump相关选项、回归相关选项、回归列表文件等
        注意：后仿参数(post)会被保留，因为后仿阶段通常连续运行多个用例
        注意：BASE和BLOCK参数不清除，保留CaseController解析的值
        """
        config_panel = self.config_controller.config_panel

        # 保存当前的BASE和BLOCK值
        current_base = self.config_model.config.get("base", "")
        current_block = self.config_model.config.get("block", "")

        # 暂停定时器，防止清空过程中触发预览更新
        timer_was_active = False
        if hasattr(config_panel, 'preview_timer') and config_panel.preview_timer.isActive():
            timer_was_active = True
            config_panel.preview_timer.stop()

        try:
            # 设置更新状态标记，防止信号触发
            config_panel._updating_config = True

            # 清空文本输入框
            if hasattr(config_panel, 'rundir_input'):
                config_panel.rundir_input.clear()
            if hasattr(config_panel, 'seed_input'):
                config_panel.seed_input.clear()
            if hasattr(config_panel, 'other_options_input'):
                config_panel.other_options_input.clear()
            if hasattr(config_panel, 'simarg_input'):
                config_panel.simarg_input.clear()
            if hasattr(config_panel, 'cfg_def_input'):
                config_panel.cfg_def_input.clear()

            # 清空内存dump相关选项
            if hasattr(config_panel, 'wdd_input'):
                config_panel.wdd_input.clear()
            if hasattr(config_panel, 'dump_mem_input'):
                config_panel.dump_mem_input.clear_selection()

            # 注意：后仿参数(post)保留不清空，因为后仿阶段通常连续运行多个用例

            # 清空回归相关选项
            if hasattr(config_panel, 'tag_input'):
                config_panel.tag_input.clear()
            if hasattr(config_panel, 'nt_input'):
                config_panel.nt_input.clear()
            if hasattr(config_panel, 'dashboard_input'):
                config_panel.dashboard_input.clear()

            # 清空回归列表文件
            if hasattr(config_panel, 'regr_label'):
                config_panel.regr_label.setText("未选择回归文件")
            if hasattr(config_panel, 'clear_regr_btn'):
                config_panel.clear_regr_btn.setEnabled(False)

            # 取消相关复选框
            if hasattr(config_panel, 'sva_check'):
                config_panel.sva_check.setChecked(False)
            if hasattr(config_panel, 'cov_check'):
                config_panel.cov_check.setChecked(False)
            if hasattr(config_panel, 'upf_check'):
                config_panel.upf_check.setChecked(False)

            # 取消执行模式选项
            if hasattr(config_panel, 'sim_only_check'):
                config_panel.sim_only_check.setChecked(False)
            if hasattr(config_panel, 'compile_only_check'):
                config_panel.compile_only_check.setChecked(False)

            # 取消回归相关复选框
            if hasattr(config_panel, 'fm_check'):
                config_panel.fm_check.setChecked(False)

        finally:
            # 恢复状态标记
            config_panel._updating_config = False

            # 恢复定时器（如果之前是活跃的）
            if timer_was_active and hasattr(config_panel, 'preview_timer'):
                config_panel.preview_timer.start(2000)

        # 更新配置模型中的清空的选项，但保留BASE和BLOCK
        cleared_config = {
            "seed": "",
            "rundir": "",
            "other_options": "",
            "simarg": "",
            "cfg_def": "",
            "dump_sva": False,
            "cov": False,
            "upf": False,
            "wdd": "",
            "dump_mem": "",
            # 注意：post参数保留不清空
            "sim_only": False,
            "compile_only": False,
            "fm_checked": False,
            "tag": "",
            "nt": "",
            "dashboard": "",
            "regr_file": "",
            # 恢复先前的BASE和BLOCK值
            "base": current_base,
            "block": current_block
        }
        self.config_model.update_config(cleared_config)
        
        # 同时更新UI组件显示BASE和BLOCK值
        if hasattr(config_panel, 'base_input') and current_base is not None:
            config_panel.base_input.setText(current_base)
        if hasattr(config_panel, 'block_input') and current_block is not None:
            config_panel.block_input.setText(current_block)

    def save_window_state(self):
        """
        保存窗口状态

        保存主窗口的位置、大小和最大化状态到配置模型中，
        并将配置保存到文件。这样在下次启动应用程序时，
        可以恢复窗口的位置和大小。

        Returns:
            bool: 保存是否成功
        """
        if self.main_window.isMaximized():
            self.config_model.update_config({
                "window": {
                    "maximized": True,
                    "x": 100,
                    "y": 100,
                    "width": 1400,
                    "height": 900
                }
            })
        else:
            geometry = self.main_window.geometry()
            self.config_model.update_config({
                "window": {
                    "maximized": False,
                    "x": geometry.x(),
                    "y": geometry.y(),
                    "width": geometry.width(),
                    "height": geometry.height()
                }
            })

        return self.config_model.save_config()

    def init_resource_optimizer(self):
        """
        初始化资源优化器

        创建资源优化器实例，设置优化参数，连接信号，并启动优化。
        资源优化器用于监控和优化应用程序的资源使用，减少内存和CPU占用。

        Returns:
            bool: 资源优化器是否成功初始化
        """
        try:
            # 创建资源优化器
            self.resource_optimizer = ResourceOptimizer()

            # 连接信号
            self.resource_optimizer.optimization_performed.connect(self.on_optimization_performed)

            # 设置优化参数
            self.resource_optimizer.set_thresholds(memory_threshold=500, cpu_threshold=50)
            self.resource_optimizer.set_log_parameters(cache_size=10000, refresh_interval=500)

            # 启动优化
            self.resource_optimizer.start_optimization(interval=60000)  # 每分钟优化一次

            print("资源优化器已启动")
            return True

        except Exception as e:
            print(f"初始化资源优化器时出错: {str(e)}")
            return False

    @pyqtSlot(dict)
    def on_optimization_performed(self, result):
        """
        处理优化执行事件

        当资源优化器执行优化后，处理优化结果，打印优化信息，
        并在优化效果显著时在状态栏显示提示信息。

        Args:
            result (dict): 优化结果，包含优化前后的内存和CPU使用情况
                - memory_before (float): 优化前的内存使用量（MB）
                - memory_after (float): 优化后的内存使用量（MB）
                - cpu_before (float): 优化前的CPU使用率（%）
                - cpu_after (float): 优化后的CPU使用率（%）
        """
        try:
            # 打印优化结果
            print(f"资源优化已执行: 内存使用从 {result['memory_before']:.1f}MB 减少到 {result['memory_after']:.1f}MB")

            # 如果优化效果显著，显示提示
            if result['memory_before'] - result['memory_after'] > 50:  # 内存减少超过50MB
                self.main_window.show_message(f"已优化资源使用，释放了 {result['memory_before'] - result['memory_after']:.1f}MB 内存")
        except OSError as e:
            # 捕获I/O错误，记录但不中断程序
            import logging
            logging.error(f"打印优化结果时发生I/O错误: {e}")
        except Exception as e:
            # 捕获其他可能的异常
            import logging
            logging.error(f"处理优化结果时发生错误: {e}")

    def cleanup(self):
        """
        清理应用程序资源

        在应用程序关闭时调用，确保所有资源都被正确清理，
        包括停止资源优化器、清理控制器、断开事件总线连接等。
        """
        try:
            print("开始清理应用程序资源...")

            # 停止所有可能的异步任务和定时器
            try:
                # 停止快速执行路径中的任务
                if hasattr(self, 'fast_execution_path') and self.fast_execution_path:
                    self.fast_execution_path.cleanup()
                    print("快速执行路径已清理")
            except Exception as e:
                print(f"清理快速执行路径时出错: {str(e)}")

            # 停止资源优化器
            if hasattr(self, 'resource_optimizer') and self.resource_optimizer:
                try:
                    self.resource_optimizer.stop_optimization()
                    self.resource_optimizer = None
                    print("资源优化器已停止")
                except Exception as e:
                    print(f"停止资源优化器时出错: {str(e)}")

            # 清理执行控制器
            if hasattr(self, 'execution_controller') and self.execution_controller:
                try:
                    # 停止任务队列管理器
                    if hasattr(self.execution_controller, 'task_queue_manager'):
                        self.execution_controller.task_queue_manager.stop()
                        print("任务队列管理器已停止")

                    # 关闭所有执行标签页
                    if hasattr(self.execution_controller, 'execution_panel'):
                        self.execution_controller.execution_panel.close_all_tabs()
                    print("执行控制器已清理")
                except Exception as e:
                    print(f"清理执行控制器时出错: {str(e)}")

            # 清理配置控制器
            if hasattr(self, 'config_controller') and self.config_controller:
                try:
                    if hasattr(self.config_controller, 'cleanup'):
                        self.config_controller.cleanup()
                    print("配置控制器已清理")
                except Exception as e:
                    print(f"清理配置控制器时出错: {str(e)}")

            # 清理用例控制器
            if hasattr(self, 'case_controller') and self.case_controller:
                try:
                    if hasattr(self.case_controller, 'cleanup'):
                        self.case_controller.cleanup()
                    print("用例控制器已清理")
                except Exception as e:
                    print(f"清理用例控制器时出错: {str(e)}")

            # 清理数据看板控制器
            if hasattr(self, 'dashboard_controller') and self.dashboard_controller:
                try:
                    if hasattr(self.dashboard_controller, 'cleanup'):
                        self.dashboard_controller.cleanup()
                    print("数据看板控制器已清理")
                except Exception as e:
                    print(f"清理数据看板控制器时出错: {str(e)}")

            # 断开事件总线连接
            if hasattr(self, 'event_bus') and self.event_bus:
                try:
                    # 安全地断开所有连接
                    signals_to_disconnect = [
                        (self.event_bus.case_selected, self.on_case_selected),
                        (self.event_bus.command_executed, self.execution_controller.execute_command if hasattr(self, 'execution_controller') and self.execution_controller else None),
                        (self.event_bus.command_executed, self.dashboard_controller.on_simulation_command_executed if hasattr(self, 'dashboard_controller') and self.dashboard_controller else None),
                        (self.event_bus.config_changed, self.config_controller.on_config_changed if hasattr(self, 'config_controller') and self.config_controller else None),
                        (self.event_bus.history_updated, self.config_controller.update_history_view if hasattr(self, 'config_controller') and self.config_controller else None),
                    ]

                    for signal, slot in signals_to_disconnect:
                        if slot:
                            try:
                                signal.disconnect(slot)
                            except (TypeError, RuntimeError) as e:
                                # 连接可能已经不存在，忽略这个错误
                                pass

                    print("事件总线连接已断开")
                except Exception as e:
                    print(f"断开事件总线连接时出错: {str(e)}")

            # 清理插件管理器
            if hasattr(self, 'plugin_manager') and self.plugin_manager:
                try:
                    if hasattr(self.plugin_manager, 'cleanup'):
                        self.plugin_manager.cleanup()
                    self.plugin_manager = None
                    print("插件管理器已清理")
                except Exception as e:
                    print(f"清理插件管理器时出错: {str(e)}")

            # 保存窗口状态
            try:
                self.save_window_state()
                print("窗口状态已保存")
            except Exception as e:
                print(f"保存窗口状态时出错: {str(e)}")

            # 保存配置
            try:
                self.save_config()
                print("配置已保存")
            except Exception as e:
                print(f"保存配置时出错: {str(e)}")

            # 清理所有活跃的线程
            try:
                from PyQt5.QtCore import QThread
                import time

                # 查找所有活跃的QThread
                active_threads = []
                for obj in [self.main_window, self.config_controller, self.case_controller,
                           self.execution_controller, self.dashboard_controller]:
                    if obj:
                        for child in obj.findChildren(QThread):
                            if child.isRunning():
                                active_threads.append(child)

                if active_threads:
                    print(f"发现 {len(active_threads)} 个活跃线程，正在清理...")

                    # 尝试优雅地停止线程
                    for thread in active_threads:
                        if hasattr(thread, 'stop'):
                            thread.stop()
                        elif hasattr(thread, 'cancel'):
                            thread.cancel()

                    # 等待线程完成
                    start_time = time.time()
                    while time.time() - start_time < 2.0:  # 最多等待2秒
                        running_threads = [t for t in active_threads if t.isRunning()]
                        if not running_threads:
                            break
                        time.sleep(0.1)

                    # 强制终止仍在运行的线程
                    for thread in active_threads:
                        if thread.isRunning():
                            print(f"强制终止线程: {thread}")
                            thread.terminate()
                            thread.wait(500)  # 等待500ms

                    print("线程清理完成")

            except Exception as e:
                print(f"清理线程时出错: {str(e)}")

            print("应用程序资源清理完成")

        except Exception as e:
            print(f"清理应用程序资源时出错: {str(e)}")

    def init_queue_menu(self):
        """初始化任务队列菜单"""
        try:
            from PyQt5.QtWidgets import QAction
            from PyQt5.QtCore import Qt

            # 启用队列模式
            self.enable_queue_action = QAction("启用队列模式", self.main_window)
            self.enable_queue_action.setCheckable(True)
            self.enable_queue_action.setChecked(False)
            self.enable_queue_action.triggered.connect(self.toggle_queue_mode)
            self.main_window.queue_menu.addAction(self.enable_queue_action)

            self.main_window.queue_menu.addSeparator()

            # 启动队列
            start_queue_action = QAction("启动队列", self.main_window)
            start_queue_action.triggered.connect(self.start_queue)
            self.main_window.queue_menu.addAction(start_queue_action)

            # 停止队列
            stop_queue_action = QAction("停止队列", self.main_window)
            stop_queue_action.triggered.connect(self.stop_queue)
            self.main_window.queue_menu.addAction(stop_queue_action)

            self.main_window.queue_menu.addSeparator()

            # 清除已完成任务
            clear_completed_action = QAction("清除已完成任务", self.main_window)
            clear_completed_action.triggered.connect(self.clear_completed_tasks)
            self.main_window.queue_menu.addAction(clear_completed_action)

            # 重置队列
            reset_queue_action = QAction("重置队列", self.main_window)
            reset_queue_action.triggered.connect(self.reset_queue)
            self.main_window.queue_menu.addAction(reset_queue_action)

            self.main_window.queue_menu.addSeparator()

            # 队列设置
            queue_settings_action = QAction("队列设置", self.main_window)
            queue_settings_action.triggered.connect(self.show_queue_settings)
            self.main_window.queue_menu.addAction(queue_settings_action)

        except Exception as e:
            print(f"初始化任务队列菜单时出错: {str(e)}")

    def toggle_queue_mode(self, enabled):
        """切换队列模式"""
        try:
            self.execution_controller.enable_queue_mode(enabled)

            if enabled:
                self.main_window.show_message("任务队列模式已启用")
            else:
                self.main_window.show_message("任务队列模式已禁用")

        except Exception as e:
            self.main_window.show_error("队列模式切换失败", f"无法切换队列模式: {str(e)}")

    def start_queue(self):
        """启动任务队列"""
        try:
            task_manager = self.execution_controller.get_task_queue_manager()
            task_manager.start()
            # 手动触发一次调度，立即处理排队的任务
            task_manager.trigger_scheduling()
            self.main_window.show_message("任务队列已启动并开始处理任务")
        except Exception as e:
            self.main_window.show_error("启动队列失败", f"无法启动任务队列: {str(e)}")

    def stop_queue(self):
        """停止任务队列"""
        try:
            task_manager = self.execution_controller.get_task_queue_manager()
            task_manager.stop()
            self.main_window.show_message("任务队列已停止")
        except Exception as e:
            self.main_window.show_error("停止队列失败", f"无法停止任务队列: {str(e)}")

    def clear_completed_tasks(self):
        """清除已完成的任务"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                self.main_window, "确认", "确定要清除所有已完成的任务吗？",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                task_manager = self.execution_controller.get_task_queue_manager()
                task_manager.clear_completed_tasks()
                self.main_window.show_message("已完成的任务已清除")

        except Exception as e:
            self.main_window.show_error("清除任务失败", f"无法清除已完成的任务: {str(e)}")

    def reset_queue(self):
        """重置队列"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                self.main_window, "确认",
                "确定要重置队列吗？这将清除所有任务和状态文件。",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                task_manager = self.execution_controller.get_task_queue_manager()
                task_manager.reset_queue()
                self.main_window.show_message("队列已重置")

        except Exception as e:
            self.main_window.show_error("重置队列失败", f"无法重置队列: {str(e)}")

    def show_queue_settings(self):
        """显示队列设置对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QPushButton

            dialog = QDialog(self.main_window)
            dialog.setWindowTitle("任务队列设置")
            dialog.setModal(True)
            dialog.resize(300, 150)

            layout = QVBoxLayout(dialog)

            # 最大并发数设置
            concurrent_layout = QHBoxLayout()
            concurrent_layout.addWidget(QLabel("最大并发任务数:"))

            concurrent_spin = QSpinBox()
            concurrent_spin.setRange(1, 20)
            task_manager = self.execution_controller.get_task_queue_manager()
            current_max = task_manager.config.get('max_concurrent_tasks', 5)
            concurrent_spin.setValue(current_max)
            concurrent_layout.addWidget(concurrent_spin)

            layout.addLayout(concurrent_layout)

            # 按钮
            button_layout = QHBoxLayout()

            ok_btn = QPushButton("确定")
            ok_btn.clicked.connect(lambda: self.apply_queue_settings(dialog, concurrent_spin.value()))
            button_layout.addWidget(ok_btn)

            cancel_btn = QPushButton("取消")
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            dialog.exec_()

        except Exception as e:
            self.main_window.show_error("设置失败", f"无法显示队列设置: {str(e)}")

    def apply_queue_settings(self, dialog, max_concurrent):
        """应用队列设置"""
        try:
            task_manager = self.execution_controller.get_task_queue_manager()
            task_manager.set_config('max_concurrent_tasks', max_concurrent)

            dialog.accept()
            self.main_window.show_message(f"队列设置已更新：最大并发数 {max_concurrent}")

        except Exception as e:
            self.main_window.show_error("应用设置失败", f"无法应用队列设置: {str(e)}")
