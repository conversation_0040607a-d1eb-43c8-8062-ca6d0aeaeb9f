"""
任务监控面板
提供任务队列的实时状态显示、进度监控和操作控制
"""
import time
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar,
    QComboBox, QSpinBox, QGroupBox, QSplitter, QTextEdit,
    QFrame, QCheckBox, QMessageBox, QMenu, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, QMutex, QWaitCondition
from PyQt5.QtGui import QColor, QFont, QIcon, QPixmap, QPainter

from utils.task_queue_core import Task, TaskStatus, TaskPriority
from utils.task_queue_manager import TaskQueueManager


class SignalBatcher:
    """信号批处理器 - 将短时间内的多个信号合并处理"""

    def __init__(self, batch_interval=100):  # 100ms批处理间隔
        self.batch_interval = batch_interval
        self.pending_signals = {}
        self.batch_timer = QTimer()
        self.batch_timer.setSingleShot(True)
        self.batch_timer.timeout.connect(self._process_batch)

    def add_signal(self, signal_type: str, data: any, callback: callable):
        """添加信号到批处理队列"""
        if signal_type not in self.pending_signals:
            self.pending_signals[signal_type] = []

        self.pending_signals[signal_type].append({
            'data': data,
            'callback': callback,
            'timestamp': time.time() * 1000
        })

        # 启动或重启批处理定时器
        if not self.batch_timer.isActive():
            self.batch_timer.start(self.batch_interval)

    def _process_batch(self):
        """处理批处理的信号"""
        if not self.pending_signals:
            return

        # 按信号类型分组处理
        for signal_type, signals in self.pending_signals.items():
            if signal_type == 'task_progress':
                self._process_progress_batch(signals)
            elif signal_type == 'task_status':
                self._process_status_batch(signals)
            elif signal_type == 'stats_update':
                self._process_stats_batch(signals)
            else:
                # 其他信号类型逐个处理
                for signal in signals:
                    signal['callback'](signal['data'])

        # 清空待处理信号
        self.pending_signals.clear()

    def _process_progress_batch(self, signals):
        """批处理进度更新信号"""
        # 对于进度更新，只保留每个任务的最新进度
        latest_progress = {}
        for signal in signals:
            task, progress = signal['data']
            if task.task_id not in latest_progress or signal['timestamp'] > latest_progress[task.task_id]['timestamp']:
                latest_progress[task.task_id] = signal

        # 执行最新的进度更新
        for signal in latest_progress.values():
            signal['callback'](signal['data'])

    def _process_status_batch(self, signals):
        """批处理状态变化信号"""
        # 对于状态变化，只保留每个任务的最新状态
        latest_status = {}
        for signal in signals:
            task = signal['data']
            # 确保task是Task对象而不是字符串
            if hasattr(task, 'task_id'):
                task_id = task.task_id
                if task_id not in latest_status or signal['timestamp'] > latest_status[task_id]['timestamp']:
                    latest_status[task_id] = signal
            else:
                # 如果不是Task对象，跳过这个信号
                print(f"警告: 收到非Task对象的状态更新信号: {type(task)}")
                continue

        # 执行最新的状态更新
        for signal in latest_status.values():
            signal['callback'](signal['data'])

    def _process_stats_batch(self, signals):
        """批处理统计信息更新信号"""
        # 对于统计信息，只执行最新的更新
        if signals:
            latest_signal = max(signals, key=lambda s: s['timestamp'])
            latest_signal['callback'](latest_signal['data'])


class UIUpdateWorker(QThread):
    """UI更新工作线程 - 在后台处理耗时的UI更新操作"""

    # 信号定义
    update_stats_signal = pyqtSignal(dict)
    update_task_signal = pyqtSignal(object)
    update_progress_signal = pyqtSignal(object, float)
    refresh_table_signal = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        self.update_queue = []
        self.running = False

    def start_worker(self):
        """启动工作线程"""
        self.running = True
        self.start()

    def stop_worker(self):
        """停止工作线程"""
        self.running = False
        self.condition.wakeAll()
        self.wait()

    def stop(self):
        """停止线程（别名方法，用于统一的清理接口）"""
        self.stop_worker()

    def add_update_task(self, update_type: str, data: any):
        """添加更新任务到队列"""
        self.mutex.lock()
        try:
            self.update_queue.append({
                'type': update_type,
                'data': data,
                'timestamp': time.time() * 1000
            })
            self.condition.wakeAll()
        finally:
            self.mutex.unlock()

    def run(self):
        """工作线程主循环"""
        while self.running:
            self.mutex.lock()
            try:
                if not self.update_queue:
                    # 等待新的更新任务
                    self.condition.wait(self.mutex, 100)  # 100ms超时
                    continue

                # 获取并处理更新任务
                updates = self.update_queue.copy()
                self.update_queue.clear()

            finally:
                self.mutex.unlock()

            # 在线程中处理更新任务
            self._process_updates(updates)

            # 短暂休眠，避免过度占用CPU
            self.msleep(10)

    def _process_updates(self, updates):
        """处理更新任务"""
        # 按类型分组并去重
        grouped_updates = {}
        for update in updates:
            update_type = update['type']
            if update_type not in grouped_updates:
                grouped_updates[update_type] = []
            grouped_updates[update_type].append(update)

        # 处理每种类型的更新
        for update_type, type_updates in grouped_updates.items():
            if update_type == 'stats':
                # 统计更新只保留最新的
                latest_update = max(type_updates, key=lambda u: u['timestamp'])
                self.update_stats_signal.emit(latest_update['data'])

            elif update_type == 'task':
                # 任务更新按任务ID去重，保留最新的
                task_updates = {}
                for update in type_updates:
                    task = update['data']
                    # 确保task是Task对象
                    if hasattr(task, 'task_id'):
                        task_id = task.task_id
                        if task_id not in task_updates or update['timestamp'] > task_updates[task_id]['timestamp']:
                            task_updates[task_id] = update
                    else:
                        print(f"警告: UIUpdateWorker收到非Task对象: {type(task)}")
                        continue

                for update in task_updates.values():
                    self.update_task_signal.emit(update['data'])

            elif update_type == 'progress':
                # 进度更新按任务ID去重，保留最新的
                progress_updates = {}
                for update in type_updates:
                    task, progress = update['data']
                    task_id = task.task_id
                    if task_id not in progress_updates or update['timestamp'] > progress_updates[task_id]['timestamp']:
                        progress_updates[task_id] = update

                for update in progress_updates.values():
                    task, progress = update['data']
                    self.update_progress_signal.emit(task, progress)

            elif update_type == 'refresh':
                # 表格刷新只需要一次
                self.refresh_table_signal.emit()


class VirtualScrollManager:
    """虚拟滚动管理器 - 只渲染可见区域的行"""

    def __init__(self, table_widget):
        self.table = table_widget
        self.visible_start = 0
        self.visible_end = 0
        self.row_height = 30  # 默认行高
        self.buffer_size = 5  # 缓冲区大小（上下各5行）
        self.total_rows = 0
        self.viewport_height = 0

    def update_viewport(self):
        """更新视口信息"""
        if not self.table:
            return

        self.viewport_height = self.table.viewport().height()
        scroll_value = self.table.verticalScrollBar().value()

        # 计算可见区域
        visible_rows = self.viewport_height // self.row_height + 1
        start_row = max(0, scroll_value // self.row_height - self.buffer_size)
        end_row = min(self.total_rows, start_row + visible_rows + 2 * self.buffer_size)

        # 检查是否需要更新
        if start_row != self.visible_start or end_row != self.visible_end:
            self.visible_start = start_row
            self.visible_end = end_row
            return True
        return False

    def get_visible_range(self):
        """获取可见范围"""
        return self.visible_start, self.visible_end

    def set_total_rows(self, total_rows):
        """设置总行数"""
        self.total_rows = total_rows


class LazyLoadManager:
    """延迟加载管理器"""

    def __init__(self):
        self.load_timer = QTimer()
        self.load_timer.setSingleShot(True)
        self.load_timer.timeout.connect(self._perform_load)
        self.pending_loads = set()
        self.load_delay = 100  # 100ms延迟

    def request_load(self, row_range, callback):
        """请求加载指定范围的行"""
        self.pending_loads.add((row_range, callback))
        if not self.load_timer.isActive():
            self.load_timer.start(self.load_delay)

    def _perform_load(self):
        """执行待处理的加载请求"""
        if not self.pending_loads:
            return

        # 合并相邻的加载范围
        loads = list(self.pending_loads)
        self.pending_loads.clear()

        # 执行加载
        for row_range, callback in loads:
            callback(row_range)


class TaskStatusIndicator(QLabel):
    """任务状态指示器"""
    
    STATUS_COLORS = {
        TaskStatus.PENDING: QColor(128, 128, 128),    # 灰色
        TaskStatus.QUEUED: QColor(255, 165, 0),       # 橙色
        TaskStatus.RUNNING: QColor(0, 128, 255),      # 蓝色
        TaskStatus.COMPLETED: QColor(0, 128, 0),      # 绿色
        TaskStatus.FAILED: QColor(255, 0, 0),         # 红色
        TaskStatus.CANCELLED: QColor(128, 0, 128),    # 紫色
        TaskStatus.PAUSED: QColor(255, 255, 0)        # 黄色
    }
    
    def __init__(self, status: TaskStatus, parent=None):
        super().__init__(parent)
        self.setFixedSize(16, 16)
        self.set_status(status)
    
    def set_status(self, status):
        """设置状态"""
        # 如果传入的是字符串，尝试转换为TaskStatus枚举
        if isinstance(status, str):
            try:
                from utils.task_queue_core import TaskStatus
                # 尝试通过值查找枚举
                for task_status in TaskStatus:
                    if task_status.value == status:
                        status = task_status
                        break
                else:
                    # 如果找不到匹配的枚举值，使用默认状态
                    print(f"警告: 未知的任务状态字符串: {status}")
                    status = TaskStatus.PENDING
            except Exception as e:
                print(f"错误: 转换任务状态失败: {e}")
                status = TaskStatus.PENDING

        self.status = status
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        color = self.STATUS_COLORS.get(self.status, QColor(128, 128, 128))
        
        # 创建状态图标
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(color)
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(2, 2, 12, 12)
        painter.end()
        
        self.setPixmap(pixmap)
        # 安全地获取状态值
        status_text = getattr(self.status, 'value', str(self.status))
        self.setToolTip(f"状态: {status_text}")


class TaskStatsWidget(QWidget):
    """任务统计小部件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 统计标签
        self.stats_labels = {}
        stats_items = [
            ('total', '总计', QColor(100, 100, 100)),
            ('queued', '排队', QColor(255, 165, 0)),
            ('running', '运行', QColor(0, 128, 255)),
            ('completed', '完成', QColor(0, 128, 0)),
            ('failed', '失败', QColor(255, 0, 0)),
            ('paused', '暂停', QColor(255, 255, 0))
        ]
        
        for key, name, color in stats_items:
            # 创建统计项
            frame = QFrame()
            frame.setFrameStyle(QFrame.StyledPanel)
            frame.setStyleSheet(f"""
                QFrame {{
                    border: 1px solid {color.name()};
                    border-radius: 4px;
                    background-color: rgba({color.red()}, {color.green()}, {color.blue()}, 30);
                }}
            """)
            
            item_layout = QVBoxLayout(frame)
            item_layout.setContentsMargins(8, 4, 8, 4)
            
            # 数值标签
            value_label = QLabel("0")
            value_label.setAlignment(Qt.AlignCenter)
            value_label.setFont(QFont("Arial", 12, QFont.Bold))
            value_label.setStyleSheet(f"color: {color.name()};")
            
            # 名称标签
            name_label = QLabel(name)
            name_label.setAlignment(Qt.AlignCenter)
            name_label.setFont(QFont("Arial", 8))
            
            item_layout.addWidget(value_label)
            item_layout.addWidget(name_label)
            
            self.stats_labels[key] = value_label
            layout.addWidget(frame)
        
        layout.addStretch()
    
    def update_stats(self, stats: dict):
        """更新统计信息"""
        for key, label in self.stats_labels.items():
            value = stats.get(key, 0)
            label.setText(str(value))


class TaskTableWidget(QTableWidget):
    """任务表格小部件"""
    
    # 信号定义
    task_action_requested = pyqtSignal(str, str)  # task_id, action
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.tasks = {}  # 存储任务对象
        self.task_rows = {}  # 任务ID到行号的映射
        self.row_tasks = {}  # 行号到任务ID的映射
        self.last_task_states = {}  # 上次任务状态的缓存
        self.needs_full_refresh = True  # 是否需要完全刷新

        # 虚拟滚动和延迟加载
        self.virtual_scroll = VirtualScrollManager(self)
        self.lazy_loader = LazyLoadManager()
        self.enable_virtual_scroll = True  # 是否启用虚拟滚动
        self.virtual_scroll_threshold = 100  # 超过100行时启用虚拟滚动

        # 连接滚动事件
        self.verticalScrollBar().valueChanged.connect(self._on_scroll_changed)
        
    def init_ui(self):
        """初始化界面"""
        # 设置列
        headers = ['状态', '名称', '优先级', '进度', '创建时间', '开始时间', '持续时间', '操作']
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        # 设置列宽
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 名称
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 优先级
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 进度
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 创建时间
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 开始时间
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 持续时间
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 操作
        
        self.setColumnWidth(0, 60)   # 状态
        self.setColumnWidth(2, 80)   # 优先级
        self.setColumnWidth(3, 100)  # 进度
        self.setColumnWidth(7, 140)  # 操作（增加宽度以适应更大的按钮）
        
        # 设置右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def add_task(self, task: Task):
        """添加任务到表格"""
        self.tasks[task.task_id] = task
        self.needs_full_refresh = True  # 新增任务需要完全刷新
        self.refresh_table()

    def update_task(self, task: Task):
        """更新任务信息"""
        # 确保task是Task对象
        if not hasattr(task, 'task_id'):
            print(f"错误: update_task收到非Task对象: {type(task)}")
            return

        if task.task_id in self.tasks:
            old_task = self.tasks[task.task_id]
            self.tasks[task.task_id] = task

            # 检查是否需要完全刷新（任务顺序可能改变）
            if self._task_order_changed(old_task, task):
                self.needs_full_refresh = True
                self.refresh_table()
            else:
                # 只更新单个任务行
                self._update_single_task_row(task)

    def remove_task(self, task_id: str):
        """从表格移除任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            if task_id in self.last_task_states:
                del self.last_task_states[task_id]
            self.needs_full_refresh = True  # 删除任务需要完全刷新
            self.refresh_table()
    
    def refresh_table(self):
        """刷新表格显示 - 支持增量更新和虚拟滚动"""
        if self.needs_full_refresh or not self.task_rows:
            self._full_refresh()
            self.needs_full_refresh = False
        else:
            self._incremental_refresh()

    def _should_use_virtual_scroll(self):
        """判断是否应该使用虚拟滚动"""
        return (self.enable_virtual_scroll and
                len(self.tasks) > self.virtual_scroll_threshold)

    def _full_refresh(self):
        """完全刷新表格"""
        # 清空表格和映射
        self.setRowCount(0)
        self.task_rows.clear()
        self.row_tasks.clear()

        # 按创建时间排序任务
        sorted_tasks = sorted(self.tasks.values(), key=lambda t: t.created_time, reverse=True)

        if self._should_use_virtual_scroll():
            self._virtual_refresh(sorted_tasks)
        else:
            self._standard_refresh(sorted_tasks)

        # 更新状态缓存
        self.last_task_states = {task.task_id: self._get_task_state_hash(task)
                                for task in self.tasks.values()}

    def _standard_refresh(self, sorted_tasks):
        """标准刷新模式"""
        for row, task in enumerate(sorted_tasks):
            self.insertRow(row)
            self._populate_task_row(row, task)

            # 更新映射
            self.task_rows[task.task_id] = row
            self.row_tasks[row] = task.task_id

    def _virtual_refresh(self, sorted_tasks):
        """虚拟滚动刷新模式"""
        total_rows = len(sorted_tasks)
        self.virtual_scroll.set_total_rows(total_rows)

        # 设置表格行数但不填充内容
        self.setRowCount(total_rows)

        # 更新任务映射
        for row, task in enumerate(sorted_tasks):
            self.task_rows[task.task_id] = row
            self.row_tasks[row] = task.task_id

        # 只渲染可见区域
        self._render_visible_rows(sorted_tasks)

    def _incremental_refresh(self):
        """增量刷新 - 只更新变化的任务"""
        for task_id, task in self.tasks.items():
            current_state = self._get_task_state_hash(task)
            last_state = self.last_task_states.get(task_id)

            if current_state != last_state:
                self._update_single_task_row(task)
                self.last_task_states[task_id] = current_state

    def _populate_task_row(self, row: int, task: Task):
        """填充任务行数据"""
        # 状态指示器
        status_indicator = TaskStatusIndicator(task.status)
        self.setCellWidget(row, 0, status_indicator)

        # 任务名称
        name_item = QTableWidgetItem(task.name)
        name_item.setData(Qt.UserRole, task.task_id)
        self.setItem(row, 1, name_item)

        # 优先级
        priority_item = QTableWidgetItem(task.priority.name)
        priority_color = self._get_priority_color(task.priority)
        priority_item.setForeground(priority_color)
        self.setItem(row, 2, priority_item)

        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 100)

        # 根据任务状态设置正确的进度值
        progress_value = int(task.progress * 100)
        if task.status == TaskStatus.COMPLETED and progress_value < 100:
            # 如果任务已完成但进度不是100%，显示100%
            progress_value = 100
        elif task.status == TaskStatus.FAILED and progress_value == 0:
            # 如果任务失败且进度为0，保持0%
            progress_value = 0

        progress_bar.setValue(progress_value)
        progress_bar.setTextVisible(True)
        self.setCellWidget(row, 3, progress_bar)
            
        # 创建时间
        created_time = task.created_time.strftime("%m-%d %H:%M")
        self.setItem(row, 4, QTableWidgetItem(created_time))
        
        # 开始时间
        start_time = task.start_time.strftime("%m-%d %H:%M") if task.start_time else "-"
        self.setItem(row, 5, QTableWidgetItem(start_time))
        
        # 持续时间
        duration = self._format_duration(task.get_duration())
        self.setItem(row, 6, QTableWidgetItem(duration))
        
        # 操作按钮
        action_widget = self._create_action_widget(task)
        self.setCellWidget(row, 7, action_widget)
    
    def _get_priority_color(self, priority: TaskPriority) -> QColor:
        """获取优先级颜色"""
        colors = {
            TaskPriority.URGENT: QColor(255, 0, 0),      # 红色
            TaskPriority.HIGH: QColor(255, 165, 0),      # 橙色
            TaskPriority.NORMAL: QColor(0, 0, 0),        # 黑色
            TaskPriority.LOW: QColor(128, 128, 128),     # 灰色
            TaskPriority.BACKGROUND: QColor(200, 200, 200)  # 浅灰色
        }
        return colors.get(priority, QColor(0, 0, 0))
    
    def _format_duration(self, duration: float) -> str:
        """格式化持续时间"""
        if duration is None:
            return "-"
        
        if duration < 60:
            return f"{duration:.1f}s"
        elif duration < 3600:
            return f"{duration/60:.1f}m"
        else:
            return f"{duration/3600:.1f}h"
    
    def _create_action_widget(self, task: Task) -> QWidget:
        """创建操作按钮小部件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # 根据任务状态创建相应的按钮
        if task.status == TaskStatus.QUEUED:
            pause_btn = QPushButton("暂停")
            pause_btn.setFixedSize(50, 26)  # 增加按钮尺寸
            pause_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF8C00;  /* 橙色 */
                    color: white;
                    border: 1px solid #FF7F00;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #FF7F00;
                }
                QPushButton:pressed {
                    background-color: #FF6500;
                }
            """)
            pause_btn.clicked.connect(lambda: self.task_action_requested.emit(task.task_id, "pause"))
            layout.addWidget(pause_btn)

        elif task.status == TaskStatus.PAUSED:
            resume_btn = QPushButton("恢复")
            resume_btn.setFixedSize(50, 26)  # 增加按钮尺寸
            resume_btn.setStyleSheet("""
                QPushButton {
                    background-color: #32CD32;  /* 绿色 */
                    color: white;
                    border: 1px solid #228B22;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #228B22;
                }
                QPushButton:pressed {
                    background-color: #006400;
                }
            """)
            resume_btn.clicked.connect(lambda: self.task_action_requested.emit(task.task_id, "resume"))
            layout.addWidget(resume_btn)

        # 取消按钮（除了已完成的任务）
        if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            cancel_btn = QPushButton("取消")
            cancel_btn.setFixedSize(50, 26)  # 增加按钮尺寸
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #DC143C;  /* 红色 */
                    color: white;
                    border: 1px solid #B22222;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #B22222;
                }
                QPushButton:pressed {
                    background-color: #8B0000;
                }
            """)
            cancel_btn.clicked.connect(lambda: self.task_action_requested.emit(task.task_id, "cancel"))
            layout.addWidget(cancel_btn)

        # 删除按钮（已完成的任务）
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            delete_btn = QPushButton("删除")
            delete_btn.setFixedSize(50, 26)  # 增加按钮尺寸
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #696969;  /* 灰色 */
                    color: white;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #555555;
                }
                QPushButton:pressed {
                    background-color: #404040;
                }
            """)
            delete_btn.clicked.connect(lambda: self.task_action_requested.emit(task.task_id, "delete"))
            layout.addWidget(delete_btn)

        layout.addStretch()
        return widget

    def _update_single_task_row(self, task: Task):
        """更新单个任务行"""
        if task.task_id not in self.task_rows:
            return

        row = self.task_rows[task.task_id]
        if row >= self.rowCount():
            return

        # 更新状态指示器
        status_indicator = TaskStatusIndicator(task.status)
        self.setCellWidget(row, 0, status_indicator)

        # 更新任务名称（通常不变，但为了完整性）
        name_item = self.item(row, 1)
        if name_item:
            name_item.setText(task.name)

        # 更新优先级
        priority_item = self.item(row, 2)
        if priority_item:
            priority_item.setText(task.priority.name)
            priority_color = self._get_priority_color(task.priority)
            priority_item.setForeground(priority_color)

        # 更新进度条
        progress_widget = self.cellWidget(row, 3)
        if isinstance(progress_widget, QProgressBar):
            progress_value = int(task.progress * 100)
            progress_widget.setValue(progress_value)

            # 根据任务状态调整进度条显示
            if task.status == TaskStatus.COMPLETED and progress_value < 100:
                # 如果任务已完成但进度不是100%，强制设置为100%
                progress_widget.setValue(100)
                # 同时更新任务对象的进度
                task.progress = 1.0
            elif task.status == TaskStatus.FAILED and progress_value == 0:
                # 如果任务失败且进度为0，可能需要显示一些进度表示任务至少开始了
                pass  # 保持0%，表示任务启动就失败了

        # 更新时间相关列
        self._update_time_columns(row, task)

        # 更新操作按钮
        action_widget = self._create_action_widget(task)
        self.setCellWidget(row, 7, action_widget)

    def _update_time_columns(self, row: int, task: Task):
        """更新时间相关列"""
        # 创建时间
        created_item = self.item(row, 4)
        if created_item:
            created_item.setText(task.created_time.strftime("%H:%M:%S"))

        # 开始时间
        start_item = self.item(row, 5)
        if start_item:
            start_text = task.start_time.strftime("%H:%M:%S") if task.start_time else "-"
            start_item.setText(start_text)

        # 持续时间
        duration_item = self.item(row, 6)
        if duration_item:
            if task.start_time:
                if task.end_time:
                    duration = task.end_time - task.start_time
                else:
                    from datetime import datetime
                    duration = datetime.now() - task.start_time
                duration_text = str(duration).split('.')[0]  # 去掉微秒
            else:
                duration_text = "-"
            duration_item.setText(duration_text)

    def _get_task_state_hash(self, task: Task) -> str:
        """获取任务状态哈希，用于检测变化"""
        return f"{task.status.value}_{task.progress}_{task.priority.value}_{task.start_time}_{task.end_time}"

    def _task_order_changed(self, old_task: Task, new_task: Task) -> bool:
        """检查任务顺序是否可能改变"""
        # 如果创建时间、优先级等影响排序的属性发生变化，则需要完全刷新
        return (old_task.created_time != new_task.created_time or
                old_task.priority != new_task.priority)

    def _render_visible_rows(self, sorted_tasks):
        """渲染可见区域的行"""
        if not self.virtual_scroll.update_viewport():
            return

        start, end = self.virtual_scroll.get_visible_range()

        # 使用延迟加载
        self.lazy_loader.request_load(
            (start, end),
            lambda range_tuple: self._load_row_range(range_tuple, sorted_tasks)
        )

    def _load_row_range(self, range_tuple, sorted_tasks):
        """加载指定范围的行"""
        start, end = range_tuple

        for row in range(start, min(end, len(sorted_tasks))):
            if row < len(sorted_tasks):
                task = sorted_tasks[row]
                self._populate_task_row(row, task)

    def _on_scroll_changed(self, value):
        """滚动条变化事件"""
        if self._should_use_virtual_scroll():
            sorted_tasks = sorted(self.tasks.values(), key=lambda t: t.created_time, reverse=True)
            self._render_visible_rows(sorted_tasks)

    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        if self._should_use_virtual_scroll():
            # 重新计算可见区域
            sorted_tasks = sorted(self.tasks.values(), key=lambda t: t.created_time, reverse=True)
            self._render_visible_rows(sorted_tasks)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        if not item:
            return
        
        task_id = item.data(Qt.UserRole)
        if not task_id or task_id not in self.tasks:
            return
        
        task = self.tasks[task_id]
        
        menu = QMenu(self)
        
        # 查看详情
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(lambda: self.task_action_requested.emit(task_id, "view"))
        menu.addAction(view_action)
        
        menu.addSeparator()
        
        # 根据状态添加操作
        if task.status == TaskStatus.QUEUED:
            pause_action = QAction("暂停任务", self)
            pause_action.triggered.connect(lambda: self.task_action_requested.emit(task_id, "pause"))
            menu.addAction(pause_action)
            
        elif task.status == TaskStatus.PAUSED:
            resume_action = QAction("恢复任务", self)
            resume_action.triggered.connect(lambda: self.task_action_requested.emit(task_id, "resume"))
            menu.addAction(resume_action)
        
        if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            cancel_action = QAction("取消任务", self)
            cancel_action.triggered.connect(lambda: self.task_action_requested.emit(task_id, "cancel"))
            menu.addAction(cancel_action)
        
        menu.exec_(self.mapToGlobal(position))


class TaskMonitorPanel(QWidget):
    """任务监控面板主界面"""

    # 信号定义
    add_task_requested = pyqtSignal(str, str, object, list)  # name, command, priority, dependencies

    def __init__(self, task_manager: TaskQueueManager, parent=None):
        super().__init__(parent)
        self.task_manager = task_manager

        # 性能优化相关属性
        self.last_refresh_time = 0
        self.refresh_throttle_interval = 1000  # 节流间隔1秒
        self.pending_refresh = False

        # 多级缓存系统
        self.stats_cache = {}
        self.stats_cache_time = 0
        self.stats_cache_ttl = 2000  # 统计缓存2秒有效期

        self.ui_state_cache = {}  # UI状态缓存
        self.ui_cache_time = 0
        self.ui_cache_ttl = 5000  # UI缓存5秒有效期

        self.task_list_cache = []  # 任务列表缓存
        self.task_list_cache_time = 0
        self.task_list_cache_ttl = 3000  # 任务列表缓存3秒有效期

        # 信号批处理器
        self.signal_batcher = SignalBatcher(batch_interval=150)  # 150ms批处理间隔

        # 先初始化UI组件
        self.init_ui()
        self.connect_signals()

        # 然后创建并连接UI更新工作线程
        self.ui_worker = UIUpdateWorker(self)
        self.ui_worker.update_stats_signal.connect(self.stats_widget.update_stats)
        self.ui_worker.update_task_signal.connect(self.task_table.update_task)
        self.ui_worker.update_progress_signal.connect(self._handle_worker_progress_update)
        self.ui_worker.refresh_table_signal.connect(self.task_table.refresh_table)
        self.ui_worker.start_worker()

        # 优化后的定时刷新 - 从2秒调整到5秒
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.smart_refresh_display)
        self.refresh_timer.start(5000)  # 每5秒刷新一次

        # 节流定时器
        self.throttle_timer = QTimer()
        self.throttle_timer.setSingleShot(True)
        self.throttle_timer.timeout.connect(self.execute_pending_refresh)

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 标题
        title_label = QLabel("任务队列监控")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title_label)

        # 统计信息
        self.stats_widget = TaskStatsWidget()
        layout.addWidget(self.stats_widget)

        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)

        # 分割器
        splitter = QSplitter(Qt.Vertical)

        # 任务表格
        self.task_table = TaskTableWidget()
        splitter.addWidget(self.task_table)

        # 详情面板
        details_panel = self.create_details_panel()
        splitter.addWidget(details_panel)

        # 设置分割器比例
        splitter.setSizes([400, 200])
        layout.addWidget(splitter)

    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QGroupBox("队列控制")
        layout = QHBoxLayout(panel)

        # 并发数设置
        layout.addWidget(QLabel("最大并发数:"))
        self.concurrent_spin = QSpinBox()
        self.concurrent_spin.setRange(1, 20)
        self.concurrent_spin.setValue(5)
        self.concurrent_spin.valueChanged.connect(self.on_concurrent_changed)
        layout.addWidget(self.concurrent_spin)

        layout.addWidget(QLabel("  "))

        # 状态筛选
        layout.addWidget(QLabel("状态筛选:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(['全部', '排队中', '运行中', '已完成', '失败', '已取消', '已暂停'])
        self.status_filter.currentTextChanged.connect(self.on_filter_changed)
        layout.addWidget(self.status_filter)

        layout.addWidget(QLabel("  "))

        # 操作按钮
        self.start_btn = QPushButton("启动队列")
        self.start_btn.clicked.connect(self.start_queue)
        layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止队列")
        self.stop_btn.clicked.connect(self.stop_queue)
        layout.addWidget(self.stop_btn)

        self.clear_btn = QPushButton("清除已完成")
        self.clear_btn.clicked.connect(self.clear_completed)
        layout.addWidget(self.clear_btn)

        layout.addStretch()

        # 自动刷新
        self.auto_refresh_check = QCheckBox("自动刷新")
        self.auto_refresh_check.setChecked(True)
        self.auto_refresh_check.toggled.connect(self.toggle_auto_refresh)
        layout.addWidget(self.auto_refresh_check)

        return panel

    def create_details_panel(self) -> QWidget:
        """创建详情面板"""
        panel = QGroupBox("任务详情")
        layout = QVBoxLayout(panel)

        # 详情文本
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setReadOnly(True)
        layout.addWidget(self.details_text)

        return panel

    def connect_signals(self):
        """连接信号"""
        # 任务管理器信号
        self.task_manager.task_started.connect(self.on_task_started)
        self.task_manager.task_finished.connect(self.on_task_finished)
        self.task_manager.task_progress_updated.connect(self.on_task_progress_updated)
        self.task_manager.queue_stats_changed.connect(self.on_stats_changed)
        self.task_manager.manager_error.connect(self.on_manager_error)

        # 任务队列信号
        self.task_manager.task_queue.task_added.connect(self.on_task_added)
        self.task_manager.task_queue.task_removed.connect(self.on_task_removed)
        self.task_manager.task_queue.task_status_changed.connect(self.on_task_status_changed)

        # 表格信号
        self.task_table.task_action_requested.connect(self.on_task_action)
        self.task_table.itemSelectionChanged.connect(self.on_selection_changed)

    def start_queue(self):
        """启动队列"""
        try:
            self.task_manager.start()
            # 手动触发一次调度，立即处理排队的任务
            self.task_manager.trigger_scheduling()
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.show_message("任务队列已启动并开始处理任务")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"启动队列失败: {str(e)}")

    def stop_queue(self):
        """停止队列"""
        try:
            self.task_manager.stop()
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"停止队列失败: {str(e)}")

    def clear_completed(self):
        """清除已完成的任务"""
        reply = QMessageBox.question(
            self, "确认", "确定要清除所有已完成的任务吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.task_manager.clear_completed_tasks()

    def on_concurrent_changed(self, value):
        """并发数变化"""
        self.task_manager.set_config('max_concurrent_tasks', value)

    def on_filter_changed(self, filter_text):
        """筛选条件变化"""
        # TODO: 实现任务筛选功能
        pass

    def toggle_auto_refresh(self, enabled):
        """切换自动刷新"""
        if enabled:
            self.refresh_timer.start(5000)  # 使用优化后的5秒间隔
        else:
            self.refresh_timer.stop()

    def smart_refresh_display(self):
        """智能刷新显示 - 只在需要时刷新"""
        current_time = time.time() * 1000  # 转换为毫秒

        # 检查是否需要刷新（基于缓存有效期）
        if (current_time - self.stats_cache_time) < self.stats_cache_ttl and self.stats_cache:
            return

        self.request_refresh()

    def request_refresh(self):
        """请求刷新 - 带节流机制"""
        current_time = time.time() * 1000

        if current_time - self.last_refresh_time < self.refresh_throttle_interval:
            # 在节流期间，标记有待处理的刷新
            if not self.pending_refresh:
                self.pending_refresh = True
                remaining_time = self.refresh_throttle_interval - (current_time - self.last_refresh_time)
                self.throttle_timer.start(int(remaining_time))
            return

        self.execute_refresh()

    def execute_pending_refresh(self):
        """执行待处理的刷新"""
        if self.pending_refresh:
            self.pending_refresh = False
            self.execute_refresh()

    def execute_refresh(self):
        """执行实际的刷新操作"""
        current_time = time.time() * 1000
        self.last_refresh_time = current_time

        # 更新统计信息（使用缓存）
        stats = self.get_cached_stats()
        self.stats_widget.update_stats(stats)

        # 刷新任务表格
        self.task_table.refresh_table()

    def get_cached_stats(self):
        """获取缓存的统计信息"""
        current_time = time.time() * 1000

        # 检查缓存是否有效
        if (current_time - self.stats_cache_time) < self.stats_cache_ttl and self.stats_cache:
            return self.stats_cache

        # 更新缓存
        self.stats_cache = self.task_manager.get_queue_stats()
        self.stats_cache_time = current_time
        return self.stats_cache

    def get_cached_task_list(self):
        """获取缓存的任务列表"""
        current_time = time.time() * 1000

        # 检查缓存是否有效
        if (current_time - self.task_list_cache_time) < self.task_list_cache_ttl and self.task_list_cache:
            return self.task_list_cache

        # 更新缓存
        self.task_list_cache = list(self.task_manager.task_queue.get_all_tasks().values())
        self.task_list_cache_time = current_time
        return self.task_list_cache

    def get_cached_ui_state(self):
        """获取缓存的UI状态"""
        current_time = time.time() * 1000

        # 检查缓存是否有效
        if (current_time - self.ui_cache_time) < self.ui_cache_ttl and self.ui_state_cache:
            return self.ui_state_cache

        # 更新缓存
        self.ui_state_cache = {
            'concurrent_value': self.concurrent_spin.value(),
            'filter_text': self.status_filter.currentText(),
            'auto_refresh_enabled': self.auto_refresh_check.isChecked(),
            'queue_running': self.task_manager.scheduler.is_running
        }
        self.ui_cache_time = current_time
        return self.ui_state_cache

    def invalidate_all_caches(self):
        """使所有缓存失效"""
        self.stats_cache_time = 0
        self.ui_cache_time = 0
        self.task_list_cache_time = 0

    def invalidate_task_list_cache(self):
        """使任务列表缓存失效"""
        self.task_list_cache_time = 0

    def _handle_progress_update_batch(self, data):
        """处理批处理的进度更新 - 使用后台线程"""
        task, progress = data
        # 将进度更新任务添加到后台线程
        self.ui_worker.add_update_task('progress', (task, progress))

    def _handle_status_update_batch(self, task):
        """处理批处理的状态更新 - 使用后台线程"""
        # 确保task是Task对象
        if not hasattr(task, 'task_id'):
            print(f"错误: _handle_status_update_batch收到非Task对象: {type(task)}")
            return

        # 将任务更新添加到后台线程
        self.ui_worker.add_update_task('task', task)
        self.invalidate_stats_cache()
        self.invalidate_task_list_cache()

    def _handle_stats_update_batch(self, stats):
        """处理批处理的统计更新 - 使用后台线程"""
        self.stats_cache = stats
        self.stats_cache_time = time.time() * 1000
        # 将更新任务添加到后台线程
        self.ui_worker.add_update_task('stats', stats)

    def _handle_worker_progress_update(self, task, progress):
        """处理工作线程的进度更新"""
        print(f"TaskMonitorPanel后台线程进度更新: {task.name} -> {progress:.1%}")
        # 直接更新任务表格，因为这已经在主线程中了
        self.task_table.update_task(task)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止后台工作线程
        if hasattr(self, 'ui_worker'):
            self.ui_worker.stop_worker()
        super().closeEvent(event)

    def refresh_display(self):
        """刷新显示 - 保持向后兼容"""
        self.request_refresh()

    def on_task_started(self, task: Task):
        """任务开始回调"""
        self.task_table.update_task(task)
        self.show_message(f"任务 '{task.name}' 开始执行")
        # 状态变化时触发智能刷新
        self.invalidate_stats_cache()
        self.request_refresh()

    def on_task_finished(self, task: Task, success: bool, exit_code: int, error_msg: str):
        """任务完成回调"""
        self.task_table.update_task(task)
        status = "成功" if success else "失败"
        self.show_message(f"任务 '{task.name}' 执行{status}")
        # 状态变化时触发智能刷新
        self.invalidate_stats_cache()
        self.request_refresh()

    def on_task_progress_updated(self, task: Task, progress: float):
        """任务进度更新回调 - 使用批处理"""
        # 将进度更新添加到批处理队列
        self.signal_batcher.add_signal(
            'task_progress',
            (task, progress),
            self._handle_progress_update_batch
        )

    def on_stats_changed(self, stats: dict):
        """统计信息变化回调 - 使用批处理"""
        # 将统计更新添加到批处理队列
        self.signal_batcher.add_signal(
            'stats_update',
            stats,
            self._handle_stats_update_batch
        )

    def invalidate_stats_cache(self):
        """使统计缓存失效"""
        self.stats_cache_time = 0

    def on_manager_error(self, error_msg: str):
        """管理器错误回调"""
        QMessageBox.critical(self, "队列管理器错误", error_msg)

    def on_task_added(self, task: Task):
        """任务添加回调"""
        self.task_table.add_task(task)
        # 任务添加时使相关缓存失效
        self.invalidate_stats_cache()
        self.invalidate_task_list_cache()
        self.request_refresh()

    def on_task_removed(self, task_id: str):
        """任务移除回调"""
        self.task_table.remove_task(task_id)
        # 任务移除时使相关缓存失效
        self.invalidate_stats_cache()
        self.invalidate_task_list_cache()
        self.request_refresh()

    def on_task_status_changed(self, task: Task, old_status: str, new_status: str):
        """任务状态变化回调 - 使用批处理"""
        # 将状态变化添加到批处理队列
        self.signal_batcher.add_signal(
            'task_status',
            task,
            self._handle_status_update_batch
        )

    def on_task_action(self, task_id: str, action: str):
        """任务操作回调"""
        try:
            if action == "pause":
                self.task_manager.pause_task(task_id)
            elif action == "resume":
                self.task_manager.resume_task(task_id)
            elif action == "cancel":
                reply = QMessageBox.question(
                    self, "确认", "确定要取消这个任务吗？",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    self.task_manager.cancel_task(task_id)
            elif action == "delete":
                self.task_manager.task_queue.remove_task(task_id)
            elif action == "view":
                self.show_task_details(task_id)

        except Exception as e:
            QMessageBox.warning(self, "操作失败", f"执行操作失败: {str(e)}")

    def on_selection_changed(self):
        """选择变化回调"""
        selected_items = self.task_table.selectedItems()
        if selected_items:
            task_id = selected_items[0].data(Qt.UserRole)
            if task_id:
                self.show_task_details(task_id)

    def show_task_details(self, task_id: str):
        """显示任务详情"""
        task = self.task_manager.get_task(task_id)
        if not task:
            return

        details = f"""任务ID: {task.task_id}
任务名称: {task.name}
执行命令: {task.command}
优先级: {task.priority.name}
状态: {task.status.value}
创建时间: {task.created_time.strftime('%Y-%m-%d %H:%M:%S')}
开始时间: {task.start_time.strftime('%Y-%m-%d %H:%M:%S') if task.start_time else '未开始'}
结束时间: {task.end_time.strftime('%Y-%m-%d %H:%M:%S') if task.end_time else '未结束'}
执行时长: {self.task_table._format_duration(task.get_duration())}
重试次数: {task.retry_count}/{task.max_retries}
退出码: {task.exit_code if task.exit_code is not None else '未知'}
错误信息: {task.error_message or '无'}
工作目录: {task.metadata.work_directory or '默认'}
"""

        self.details_text.setText(details)

    def show_message(self, message: str):
        """显示消息"""
        # 可以在状态栏或其他地方显示消息
        print(f"[任务监控] {message}")

    def add_task(self, name: str, command: str, priority: TaskPriority = TaskPriority.NORMAL,
                 dependencies: list = None, metadata: dict = None):
        """添加任务到队列"""
        from utils.task_queue_core import TaskMetadata

        task_metadata = TaskMetadata()
        if metadata:
            task_metadata.case_name = metadata.get('case_name', '')
            task_metadata.work_directory = metadata.get('work_directory', '')
            task_metadata.tags = metadata.get('tags', [])
            task_metadata.user_data = metadata.get('user_data', {})

        task_id = self.task_manager.add_task(
            name=name,
            command=command,
            priority=priority,
            dependencies=dependencies,
            metadata=task_metadata
        )

        return task_id
