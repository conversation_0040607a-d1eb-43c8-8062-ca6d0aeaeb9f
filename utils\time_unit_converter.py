"""
时间单位转换工具类
支持分钟、小时、天之间的转换，包括精度控制和格式化显示
"""

from enum import Enum
from typing import Optional, Union


class TimeUnit(Enum):
    """时间单位枚举"""
    MINUTES = "minutes"
    HOURS = "hours"
    DAYS = "days"


class TimeUnitConverter:
    """时间单位转换器"""
    
    # 时间单位转换系数（以分钟为基准）
    CONVERSION_FACTORS = {
        TimeUnit.MINUTES: 1.0,
        TimeUnit.HOURS: 60.0,
        TimeUnit.DAYS: 1440.0  # 24 * 60
    }
    
    # 时间单位显示名称
    UNIT_DISPLAY_NAMES = {
        TimeUnit.MINUTES: "分钟",
        TimeUnit.HOURS: "小时", 
        TimeUnit.DAYS: "天"
    }
    
    # 时间单位精度设置
    UNIT_PRECISION = {
        TimeUnit.MINUTES: 2,  # 分钟显示2位小数
        TimeUnit.HOURS: 2,    # 小时显示2位小数
        TimeUnit.DAYS: 3      # 天显示3位小数
    }
    
    @classmethod
    def convert_time(cls, time_value: Optional[float], 
                    from_unit: TimeUnit = TimeUnit.MINUTES,
                    to_unit: TimeUnit = TimeUnit.MINUTES) -> Optional[float]:
        """
        转换时间单位
        
        Args:
            time_value: 时间值，如果为None则返回None
            from_unit: 源时间单位
            to_unit: 目标时间单位
            
        Returns:
            转换后的时间值，如果输入为None则返回None
        """
        if time_value is None:
            return None
            
        if time_value == 0:
            return 0.0
            
        # 先转换为分钟（基准单位）
        minutes = time_value * cls.CONVERSION_FACTORS[from_unit]
        
        # 再转换为目标单位
        result = minutes / cls.CONVERSION_FACTORS[to_unit]
        
        return result
    
    @classmethod
    def format_time(cls, time_value: Optional[float], 
                   unit: TimeUnit = TimeUnit.MINUTES,
                   show_unit: bool = True) -> str:
        """
        格式化时间显示
        
        Args:
            time_value: 时间值
            unit: 时间单位
            show_unit: 是否显示单位
            
        Returns:
            格式化后的时间字符串
        """
        if time_value is None:
            return "-"
            
        if time_value == 0:
            return "0"
            
        # 获取精度
        precision = cls.UNIT_PRECISION[unit]
        
        # 格式化数值
        formatted_value = f"{time_value:.{precision}f}"
        
        # 移除末尾的0
        if '.' in formatted_value:
            formatted_value = formatted_value.rstrip('0').rstrip('.')
        
        # 添加单位
        if show_unit:
            unit_name = cls.UNIT_DISPLAY_NAMES[unit]
            return f"{formatted_value}"
        else:
            return formatted_value
    
    @classmethod
    def get_unit_display_name(cls, unit: TimeUnit) -> str:
        """获取时间单位的显示名称"""
        return cls.UNIT_DISPLAY_NAMES[unit]
    
    @classmethod
    def get_all_units(cls) -> list:
        """获取所有支持的时间单位"""
        return list(TimeUnit)
    
    @classmethod
    def convert_and_format(cls, time_value: Optional[float],
                          from_unit: TimeUnit = TimeUnit.MINUTES,
                          to_unit: TimeUnit = TimeUnit.MINUTES,
                          show_unit: bool = True) -> str:
        """
        转换时间单位并格式化显示
        
        Args:
            time_value: 时间值
            from_unit: 源时间单位
            to_unit: 目标时间单位
            show_unit: 是否显示单位
            
        Returns:
            格式化后的时间字符串
        """
        converted_value = cls.convert_time(time_value, from_unit, to_unit)
        return cls.format_time(converted_value, to_unit, show_unit)
    
    @classmethod
    def get_table_header(cls, base_name: str, unit: TimeUnit) -> str:
        """
        获取表格列头
        
        Args:
            base_name: 基础名称（如"编译时间"）
            unit: 时间单位
            
        Returns:
            完整的列头字符串
        """
        unit_name = cls.get_unit_display_name(unit)
        return f"{base_name}({unit_name})"


# 便捷函数
def convert_minutes_to_unit(minutes: Optional[float], unit: TimeUnit) -> Optional[float]:
    """将分钟转换为指定单位"""
    return TimeUnitConverter.convert_time(minutes, TimeUnit.MINUTES, unit)


def format_time_with_unit(time_value: Optional[float], unit: TimeUnit) -> str:
    """格式化时间并显示单位"""
    return TimeUnitConverter.format_time(time_value, unit, True)


def get_time_unit_choices() -> list:
    """获取时间单位选择列表（用于下拉菜单）"""
    return [(unit, TimeUnitConverter.get_unit_display_name(unit)) 
            for unit in TimeUnitConverter.get_all_units()]
