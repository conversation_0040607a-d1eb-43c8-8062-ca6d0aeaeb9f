# Git文件版本比对插件

Git文件版本比对插件为RunSim GUI提供了强大的Git文件版本比对功能，支持可视化显示文件在不同Git提交之间的差异。

## 功能特性

### 🔍 文件选择
- **单文件模式**：直接选择具体文件进行比对
- **目录模式**：浏览目录中的Git跟踪文件
- **智能检测**：自动检测Git仓库和跟踪文件
- **文件过滤**：支持按文件类型和名称过滤

### 📚 版本管理
- **提交历史**：显示文件的完整Git提交历史
- **版本选择**：选择任意历史版本进行比对
- **提交信息**：显示提交哈希、作者、日期、消息
- **快速比对**：双击提交即可快速比对

### 👀 差异显示
- **并排视图**：左右分栏显示新旧版本差异
- **统一视图**：传统的unified diff格式显示
- **语法高亮**：支持多种编程语言的语法高亮
- **差异标记**：清晰的颜色标记添加、删除、修改的行

### 🛠️ 实用工具
- **搜索功能**：在差异内容中搜索特定文本
- **导出功能**：将差异结果导出为文件
- **同步滚动**：并排视图支持同步滚动
- **统计信息**：显示添加、删除、修改的行数统计

## 支持的文件类型

### 编程语言
- **Python** (.py, .pyw)
- **C/C++** (.c, .cpp, .cc, .cxx, .h, .hpp)
- **SystemVerilog/Verilog** (.sv, .v, .vh, .svh)
- **JavaScript/TypeScript** (.js, .jsx, .ts, .tsx)
- **HTML/CSS** (.html, .htm, .css, .scss, .sass, .less)
- **其他语言** (Java, Go, Rust, PHP, Ruby等)

### 文档格式
- **文本文件** (.txt, .log)
- **Markdown** (.md, .rst)
- **配置文件** (.json, .yaml, .yml, .toml, .ini, .cfg)
- **脚本文件** (.sh, .bat, .ps1)

## 安装和使用

### 1. 依赖安装

运行依赖安装脚本：
```bash
python plugins/user/git_diff/install_git_diff_dependencies.py
```

或手动安装依赖：
```bash
pip install GitPython chardet
# 可选：语法高亮支持
pip install Pygments
```

### 2. 启动插件

1. 启动RunSim GUI应用程序
2. 在菜单栏选择 **"工具"** → **"Git文件版本比对工具"**
3. Git文件版本比对窗口将会打开

### 3. 使用步骤

#### 单文件模式
1. 选择 **"单文件模式"**
2. 点击 **"浏览..."** 按钮选择文件，或直接输入文件路径
3. 按回车键确认选择
4. 在版本选择区查看文件的提交历史
5. 选择要比对的历史版本
6. 点击 **"与当前版本比对"** 按钮

#### 目录模式
1. 选择 **"目录模式"**
2. 点击 **"浏览..."** 按钮选择Git仓库目录
3. 在文件树中选择要比对的文件
4. 在版本选择区选择历史版本
5. 点击 **"与当前版本比对"** 按钮

#### 查看差异
- **切换视图**：使用工具栏的视图选择器切换并排/统一视图
- **搜索内容**：在工具栏搜索框中输入要查找的文本
- **导出差异**：点击工具栏的 **"导出"** 按钮保存差异到文件
- **刷新数据**：点击工具栏的 **"刷新"** 按钮更新文件列表和提交历史

## 界面说明

### 主窗口布局
```
┌─────────────────────────────────────────────────────────────────┐
│ [刷新] [导出] [搜索: ______] [视图: 并排视图▼]          工具栏    │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   文件选择区    │   版本选择区    │       差异显示区            │
│                 │                 │                             │
│ ○ 单文件模式    │ 提交历史列表    │ ┌─────────┬─────────────────┐ │
│ ○ 目录模式      │                 │ │历史版本 │   当前版本      │ │
│                 │ abc123f         │ │         │                 │ │
│ [浏览文件...]   │ 2024-01-15      │ │ -删除行 │   +新增行       │ │
│                 │ 修复bug         │ │         │                 │ │
│ 文件树:         │                 │ └─────────┴─────────────────┘ │
│ ├─src/          │ [与当前版本比对] │                             │
│ │ ├─main.py     │                 │                             │
│ │ └─utils.py    │                 │                             │
│ └─tests/        │                 │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
│ 状态栏: 就绪 | 文件: main.py | 差异: +5 -3                     │
└─────────────────────────────────────────────────────────────────┘
```

### 颜色说明
- **红色背景**：删除的行
- **绿色背景**：新增的行
- **白色背景**：未修改的上下文行
- **灰色文字**：行号和注释

## 快捷键

- **Ctrl+R**：刷新
- **Ctrl+E**：导出差异
- **Ctrl+F**：搜索（焦点在搜索框时）
- **Enter**：确认文件路径输入
- **双击**：快速比对选中的提交

## 故障排除

### 常见问题

1. **"GitPython未安装"错误**
   - 运行 `pip install GitPython` 安装依赖

2. **"文件不在Git仓库中"警告**
   - 确保选择的文件在Git仓库目录下
   - 检查目录是否已初始化为Git仓库

3. **"该文件没有提交历史"提示**
   - 文件可能是新创建的，尚未提交到Git
   - 使用 `git add` 和 `git commit` 提交文件

4. **语法高亮不工作**
   - 安装Pygments库：`pip install Pygments`
   - 检查文件扩展名是否被支持

5. **中文字符显示乱码**
   - 插件会自动检测文件编码
   - 如果仍有问题，请确保文件使用UTF-8编码

### 性能优化

- 对于大文件，比对可能需要较长时间
- 建议限制提交历史数量（默认100个）
- 大型仓库建议使用单文件模式而非目录模式

## 技术架构

### 目录结构
```
git_diff/
├── __init__.py                    # 插件初始化
├── models/                        # 数据模型层
│   ├── git_repository.py         # Git仓库操作
│   ├── file_diff.py              # 文件差异计算
│   └── syntax_highlighter.py     # 语法高亮
├── views/                         # 视图层
│   ├── main_window.py            # 主窗口
│   ├── file_selector.py          # 文件选择器
│   ├── version_selector.py       # 版本选择器
│   ├── diff_viewer.py            # 差异显示器
│   └── toolbar.py                # 工具栏
├── utils/                         # 工具类
│   └── file_utils.py             # 文件处理工具
└── README.md                      # 说明文档
```

### 核心依赖
- **PyQt5**：GUI框架
- **GitPython**：Git操作库
- **chardet**：字符编码检测（可选）
- **Pygments**：语法高亮（可选）

## 版本历史

### v1.0.0
- 初始版本发布
- 支持基本的Git文件版本比对功能
- 实现并排和统一两种差异显示模式
- 支持多种编程语言的语法高亮
- 提供搜索和导出功能

## 贡献

欢迎提交问题报告和功能建议！

## 许可证

本插件遵循与RunSim GUI相同的许可证。
