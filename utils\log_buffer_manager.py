"""
日志缓冲管理器 - 专门负责日志的缓冲、批处理和编码转换
从LogPanel中提取的日志处理逻辑
集成字符串池优化内存使用
集成自适应批处理优化UI响应性
"""
import sys
import time
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from .encoding_detector import EncodingDetector
from .string_pool import intern_string
from .adaptive_batch_processor import AdaptiveBatchProcessor

# 导入sip用于检查Qt对象状态
try:
    import sip
except ImportError:
    # 如果没有sip模块，创建一个简单的替代
    class MockSip:
        @staticmethod
        def isdeleted(obj):
            return False
    sip = MockSip()


class LogBufferManager(QObject):
    """
    日志缓冲管理器，负责日志的缓冲、批处理和编码转换
    
    职责：
    1. 原始数据的编码检测和转换
    2. 日志数据的缓冲和批处理
    3. 智能刷新策略
    4. 内存使用优化
    """
    
    # 信号定义
    log_data_ready = pyqtSignal(str)  # 有日志数据准备好显示
    buffer_flushed = pyqtSignal(str)  # 缓冲区已刷新
    
    # 常量定义 - 优化实时性
    MIN_BATCH_SIZE = 4096   # 优化：减少最小批处理大小到4KB，提高响应速度
    FORCE_FLUSH_SIZE = 16384  # 优化：减少强制刷新大小到16KB
    MIN_FLUSH_INTERVAL = 0.05  # 优化：减少最小刷新间隔到50ms
    
    def __init__(self, parent=None):
        """
        初始化日志缓冲管理器
        
        Args:
            parent (QObject): 父对象
        """
        super().__init__(parent)
        
        # 缓冲区
        self._main_buffer = []  # 主缓冲区
        self._batch_buffer = []  # 批处理缓冲区
        
        # 时间戳
        self._last_flush_time = time.time()
        self._last_output_time = time.time()
        
        # 状态标志
        self._pending_flush = False
        self._flush_lock = False
        self._force_flush_needed = False
        self._is_cleaned_up = False  # 添加清理状态标志
        self._is_paused = False  # 添加暂停状态标志
        
        # 编码检测器
        self._encoding_detector = EncodingDetector()
        
        # 延迟刷新定时器
        self._pending_flush_timer = None
        
        # 统计信息
        self._stats = {
            'total_bytes_processed': 0,
            'total_flushes': 0,
            'encoding_errors': 0,
            'avg_batch_size': 0
        }
    
    def append_raw_data(self, raw_data):
        """
        添加原始字节数据（优化版本）

        Args:
            raw_data (bytes): 原始字节数据
        """
        if not raw_data or self._is_cleaned_up or self._is_paused:
            return

        try:
            # 检查编码检测器是否可用
            if self._encoding_detector is None:
                return

            # 编码检测和转换
            text = self._encoding_detector.decode(raw_data)
            if not text:
                return

            # 使用字符串池优化内存
            text = intern_string(text)

            # 检查批处理缓冲区是否可用
            if self._batch_buffer is None:
                return

            # 添加到批处理缓冲区
            self._batch_buffer.append(text)

            # 使用快速刷新条件检查
            self._check_flush_conditions_fast()

        except Exception as e:
            print(f"处理原始数据时出错: {str(e)}")
    
    def append_text(self, text):
        """
        直接添加文本数据（优化版本）

        Args:
            text (str): 文本数据
        """
        if not text or self._is_cleaned_up or self._is_paused:
            return

        # 检查批处理缓冲区是否可用
        if self._batch_buffer is None:
            return

        # 使用字符串池优化内存（已在intern_string中优化）
        text = intern_string(text)

        # 添加到批处理缓冲区
        self._batch_buffer.append(text)

        # 简化刷新条件检查
        self._check_flush_conditions_fast()
    
    def _check_flush_conditions_fast(self):
        """优化的刷新条件检查（减少时间计算）"""
        buffer_count = len(self._batch_buffer)

        # 快速检查：基于条目数量而不是字节大小，优化实时性
        if buffer_count >= 25:  # 优化：减少到约4KB的文本
            self._move_batch_to_main_buffer()
            self._schedule_immediate_flush()
        elif buffer_count >= 5:  # 优化：更小的批次，更快响应
            if not self._pending_flush:
                self._pending_flush = True
                self._schedule_delayed_flush()

    def _check_flush_conditions(self, current_time):
        """原始的刷新条件检查（保留兼容性）"""
        # 计算当前批处理缓冲区大小
        batch_size = sum(len(text) for text in self._batch_buffer)

        should_flush_immediately = False
        should_schedule_flush = False

        # 1. 立即刷新条件
        if batch_size >= self.FORCE_FLUSH_SIZE:
            should_flush_immediately = True
            self._force_flush_needed = True
        elif batch_size >= self.MIN_BATCH_SIZE:
            time_since_last_flush = current_time - self._last_flush_time
            if time_since_last_flush >= self.MIN_FLUSH_INTERVAL:
                should_flush_immediately = True
            else:
                should_schedule_flush = True

        # 2. 强制刷新：长时间没有刷新
        elif batch_size > 0 and (current_time - self._last_flush_time) > 1.0:
            should_flush_immediately = True
            self._force_flush_needed = True

        # 执行刷新
        if should_flush_immediately:
            self._move_batch_to_main_buffer()
            self._schedule_immediate_flush()
        elif should_schedule_flush:
            self._pending_flush = True
            self._schedule_delayed_flush()
    
    def _move_batch_to_main_buffer(self):
        """将批处理内容移动到主缓冲区"""
        if self._batch_buffer:
            combined_text = ''.join(self._batch_buffer)
            self._main_buffer.append(combined_text)
            self._batch_buffer.clear()
            
            # 更新统计
            self._update_batch_stats(len(combined_text))
    
    def _schedule_immediate_flush(self):
        """安排立即刷新"""
        if not self._flush_lock:
            self._flush_main_buffer()
    
    def _schedule_delayed_flush(self):
        """安排延迟刷新"""
        # 取消之前的延迟刷新
        if self._pending_flush_timer:
            self._pending_flush_timer.stop()
            self._pending_flush_timer = None
        
        # 创建新的延迟刷新定时器
        self._pending_flush_timer = QTimer()
        self._pending_flush_timer.setSingleShot(True)
        self._pending_flush_timer.timeout.connect(self._handle_delayed_flush)
        self._pending_flush_timer.start(20)  # 优化：减少到20ms延迟，提高实时性
    
    def _handle_delayed_flush(self):
        """处理延迟刷新"""
        self._pending_flush_timer = None
        if self._pending_flush and self._batch_buffer:
            self._move_batch_to_main_buffer()
            self._pending_flush = False
            self._flush_main_buffer()
    
    def _flush_main_buffer(self):
        """刷新主缓冲区"""
        if self._flush_lock and not self._force_flush_needed:
            return
        
        try:
            self._flush_lock = True
            current_time = time.time()
            
            # 检查是否有内容需要刷新
            if not self._main_buffer:
                return
            
            # 合并所有缓冲内容
            log_text = ''.join(self._main_buffer)
            self._main_buffer.clear()
            
            # 发送数据
            if log_text:
                self.log_data_ready.emit(log_text)
                self.buffer_flushed.emit(log_text)
            
            # 更新时间戳和统计
            self._last_flush_time = current_time
            self._stats['total_flushes'] += 1
            self._force_flush_needed = False
            
        except Exception as e:
            print(f"刷新缓冲区时出错: {str(e)}")
        finally:
            self._flush_lock = False
    
    def force_flush(self):
        """强制刷新所有缓冲区"""
        if self._is_cleaned_up:
            return

        try:
            # 移动批处理内容到主缓冲区
            if self._batch_buffer is not None and self._batch_buffer:
                self._move_batch_to_main_buffer()
                self._pending_flush = False

            # 强制刷新主缓冲区
            self._force_flush_needed = True
            self._flush_main_buffer()

        except Exception as e:
            print(f"强制刷新时出错: {str(e)}")
    
    def clear_buffers(self):
        """清空所有缓冲区"""
        # 安全地清空缓冲区，检查是否为None
        if self._main_buffer is not None:
            self._main_buffer.clear()
        if self._batch_buffer is not None:
            self._batch_buffer.clear()
        self._pending_flush = False

        # 停止延迟刷新定时器
        if self._pending_flush_timer:
            self._pending_flush_timer.stop()
            self._pending_flush_timer = None
    
    def get_buffer_stats(self):
        """获取缓冲区统计信息"""
        return {
            'main_buffer_size': len(self._main_buffer),
            'batch_buffer_size': len(self._batch_buffer),
            'pending_flush': self._pending_flush,
            'total_bytes_processed': self._stats['total_bytes_processed'],
            'total_flushes': self._stats['total_flushes'],
            'encoding_errors': self._stats['encoding_errors'],
            'avg_batch_size': self._stats['avg_batch_size']
        }
    
    def _update_batch_stats(self, batch_size):
        """更新批处理统计"""
        if self._stats['total_flushes'] == 0:
            self._stats['avg_batch_size'] = batch_size
        else:
            # 计算移动平均
            self._stats['avg_batch_size'] = (
                (self._stats['avg_batch_size'] * self._stats['total_flushes'] + batch_size) /
                (self._stats['total_flushes'] + 1)
            )

    def pause_processing(self):
        """暂停日志处理"""
        self._is_paused = True

        # 停止延迟刷新定时器
        if self._pending_flush_timer:
            self._pending_flush_timer.stop()
            self._pending_flush_timer = None

        # 强制刷新当前缓冲区内容，确保暂停前的日志都显示出来
        self.force_flush()

    def resume_processing(self):
        """恢复日志处理"""
        self._is_paused = False

        # 如果有待处理的内容，重新安排刷新
        if self._batch_buffer and len(self._batch_buffer) > 0:
            self._check_flush_conditions_fast()

    def is_paused(self):
        """检查是否已暂停"""
        return self._is_paused

    def cleanup(self):
        """清理资源"""
        # 防止重复清理
        if self._is_cleaned_up:
            return

        try:
            # 设置清理标志
            self._is_cleaned_up = True

            # 强制刷新剩余数据
            self.force_flush()

            # 停止并清理定时器 - 更安全的方式
            if self._pending_flush_timer is not None:
                try:
                    # 检查Qt对象是否仍然有效
                    if not sip.isdeleted(self._pending_flush_timer):
                        try:
                            self._pending_flush_timer.timeout.disconnect()
                        except (RuntimeError, TypeError):
                            pass
                        self._pending_flush_timer.stop()
                        self._pending_flush_timer.deleteLater()
                except (RuntimeError, TypeError, AttributeError):
                    # Qt对象可能已被删除，忽略错误
                    pass
                finally:
                    self._pending_flush_timer = None

            # 断开信号连接
            try:
                self.log_data_ready.disconnect()
            except Exception:
                pass

            # 清空缓冲区
            self.clear_buffers()

            # 清理编码检测器
            if hasattr(self, '_encoding_detector') and self._encoding_detector is not None:
                self._encoding_detector.cleanup()
                self._encoding_detector = None

            # 清理其他属性
            self._main_buffer = None
            self._batch_buffer = None

        except Exception as e:
            print(f"清理日志缓冲管理器时出错: {str(e)}")


# EncodingDetector现在从独立模块导入
