"""
字符串池 - 减少重复字符串的内存占用
通过字符串驻留技术优化内存使用
"""
import sys
import weakref
from collections import defaultdict, OrderedDict
from typing import Dict, Optional, Set
import threading


class StringPool:
    """
    字符串池类，用于减少重复字符串的内存占用
    
    特性：
    1. 字符串驻留 - 相同字符串只存储一份
    2. LRU缓存 - 自动清理最少使用的字符串
    3. 使用计数 - 跟踪字符串使用频率
    4. 线程安全 - 支持多线程环境
    5. 内存监控 - 提供内存使用统计
    """
    
    def __init__(self, max_size: int = 10000, enable_stats: bool = True):
        """
        初始化字符串池
        
        Args:
            max_size: 最大缓存字符串数量
            enable_stats: 是否启用统计功能
        """
        self.max_size = max_size
        self.enable_stats = enable_stats
        
        # 主要存储结构
        self._pool: OrderedDict[str, str] = OrderedDict()
        self._usage_count: Dict[str, int] = defaultdict(int)
        self._access_order: OrderedDict[str, None] = OrderedDict()
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 统计信息
        if enable_stats:
            self._stats = {
                'total_requests': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'evictions': 0,
                'memory_saved_bytes': 0,
                'unique_strings': 0
            }
        else:
            self._stats = {}
        
        # 预定义常用字符串
        self._preload_common_strings()
    
    def _preload_common_strings(self):
        """预加载常用字符串"""
        common_strings = [
            # 状态相关
            "状态: 准备执行", "状态: 执行中...", "状态: 执行完成", 
            "状态: 已终止", "状态: 执行失败",
            
            # 执行状态
            "ready", "running", "finished", "stopped", "error",
            
            # 日志消息
            "[执行完成] 可以使用工具栏按钮查看波形或日志\n",
            "[执行失败] 退出码: ",
            "[进程错误] ",
            "[错误] 处理进程结束时发生错误: ",
            
            # 命令相关
            "执行命令:\n",
            "echo test",
            
            # 编码相关
            "utf-8", "gbk", "latin1", "ascii", "cp1252",
            
            # 常用分隔符和格式
            "\n", "\r\n", "\t", " ", "",
            
            # 错误消息模板
            "时出错: ", "失败: ", "错误: ",
            
            # 文件路径常用部分
            "utils/", "views/", "tests/", ".py", ".log", ".txt"
        ]
        
        for s in common_strings:
            self._pool[s] = s
            self._usage_count[s] = 1
            self._access_order[s] = None
    
    def intern(self, text: str) -> str:
        """
        字符串驻留 - 返回池中的字符串实例（优化版本）

        Args:
            text: 要驻留的字符串

        Returns:
            池中的字符串实例
        """
        if not isinstance(text, str) or not text:
            return text

        # 快速检查（无锁）
        if text in self._pool:
            with self._lock:
                if text in self._pool:  # 双重检查
                    # 简化统计收集
                    if self.enable_stats:
                        self._stats['cache_hits'] += 1

                    # 简化使用计数更新
                    self._usage_count[text] += 1

                    return self._pool[text]

        # 缓存未命中，需要添加新字符串
        with self._lock:
            # 再次检查（可能在等待锁期间被其他线程添加）
            if text in self._pool:
                if self.enable_stats:
                    self._stats['cache_hits'] += 1
                self._usage_count[text] += 1
                return self._pool[text]

            # 简化统计收集
            if self.enable_stats:
                self._stats['cache_misses'] += 1
                self._stats['total_requests'] += 1

            # 检查是否需要清理空间
            if len(self._pool) >= self.max_size:
                self._evict_least_used()

            # 添加新字符串
            self._pool[text] = text
            self._usage_count[text] = 1
            self._access_order[text] = None

            return text
    
    def _evict_least_used(self):
        """清理最少使用的字符串"""
        if not self._pool:
            return
        
        # 找到最少使用的字符串
        min_usage = min(self._usage_count.values())
        candidates = [s for s, count in self._usage_count.items() if count == min_usage]
        
        # 在候选中选择最久未访问的
        to_evict = None
        for s in self._access_order:
            if s in candidates:
                to_evict = s
                break
        
        if to_evict:
            del self._pool[to_evict]
            del self._usage_count[to_evict]
            del self._access_order[to_evict]
            
            if self.enable_stats:
                self._stats['evictions'] += 1
                self._stats['unique_strings'] = len(self._pool)
    
    def get_usage_count(self, text: str) -> int:
        """获取字符串的使用次数"""
        with self._lock:
            return self._usage_count.get(text, 0)
    
    def get_stats(self):
        """获取统计信息"""
        if not self.enable_stats:
            return {}
        
        with self._lock:
            stats = self._stats.copy()
            stats['current_size'] = len(self._pool)
            stats['max_size'] = self.max_size
            stats['hit_ratio'] = (
                stats['cache_hits'] / stats['total_requests'] 
                if stats['total_requests'] > 0 else 0.0
            )
            return stats
    
    def clear(self):
        """清空字符串池"""
        with self._lock:
            self._pool.clear()
            self._usage_count.clear()
            self._access_order.clear()
            
            if self.enable_stats:
                self._stats = {
                    'total_requests': 0,
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'evictions': 0,
                    'memory_saved_bytes': 0,
                    'unique_strings': 0
                }
            
            # 重新加载常用字符串
            self._preload_common_strings()
    
    def get_top_used_strings(self, limit: int = 10) -> list:
        """获取使用最频繁的字符串"""
        with self._lock:
            sorted_strings = sorted(
                self._usage_count.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            return sorted_strings[:limit]
    
    def optimize(self):
        """优化字符串池 - 清理使用次数为1的字符串"""
        with self._lock:
            to_remove = [s for s, count in self._usage_count.items() if count == 1]
            
            for s in to_remove:
                if s in self._pool:
                    del self._pool[s]
                    del self._usage_count[s]
                    if s in self._access_order:
                        del self._access_order[s]
            
            if self.enable_stats:
                self._stats['unique_strings'] = len(self._pool)
                self._stats['evictions'] += len(to_remove)
    
    def __len__(self) -> int:
        """返回池中字符串数量"""
        return len(self._pool)
    
    def __contains__(self, text: str) -> bool:
        """检查字符串是否在池中"""
        return text in self._pool


# 全局字符串池实例
_global_string_pool = None
_pool_lock = threading.Lock()


def get_global_string_pool() -> StringPool:
    """获取全局字符串池实例（单例模式）"""
    global _global_string_pool
    
    if _global_string_pool is None:
        with _pool_lock:
            if _global_string_pool is None:
                _global_string_pool = StringPool(max_size=15000, enable_stats=True)
    
    return _global_string_pool


def intern_string(text: str) -> str:
    """
    便捷函数：使用全局字符串池驻留字符串

    Args:
        text: 要驻留的字符串

    Returns:
        池中的字符串实例
    """
    # 快速路径：跳过短字符串和空字符串
    if not text or len(text) < 10:
        return text

    # 快速路径：跳过过长的字符串（避免池化大文本）
    if len(text) > 500:
        return text

    return get_global_string_pool().intern(text)


def get_string_pool_stats():
    """获取全局字符串池统计信息"""
    return get_global_string_pool().get_stats()


def optimize_string_pool():
    """优化全局字符串池"""
    get_global_string_pool().optimize()
