"""
自动补全管理器
提供补全历史的清除、导入导出和数量限制功能
"""
import os
import json
from typing import Dict, List, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
    QPushButton, QLabel, QSpinBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QFileDialog, QProgressBar, QTextEdit
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal

from models.autocomplete_model import AutoCompleteModel


class AutoCompleteStatsDialog(QDialog):
    """自动补全统计信息对话框"""
    
    def __init__(self, model: AutoCompleteModel, parent=None):
        super().__init__(parent)
        self.model = model
        self.setWindowTitle("自动补全统计信息")
        self.setModal(True)
        self.resize(600, 400)
        
        self.init_ui()
        self.load_stats()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 统计表格
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(5)
        self.stats_table.setHorizontalHeaderLabels([
            "字段类型", "项目数量", "总使用次数", "最常用项", "最后更新"
        ])
        
        # 设置表格属性
        header = self.stats_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.stats_table.setAlternatingRowColors(True)
        self.stats_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.stats_table)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_stats)
        
        self.clear_btn = QPushButton("清除选中字段")
        self.clear_btn.clicked.connect(self.clear_selected_field)
        
        self.clear_all_btn = QPushButton("清除所有数据")
        self.clear_all_btn.clicked.connect(self.clear_all_data)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.clear_all_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def load_stats(self):
        """加载统计信息"""
        self.stats_table.setRowCount(0)
        
        for field_type, field_name in self.model.FIELD_TYPES.items():
            stats = self.model.get_field_stats(field_type)
            
            row = self.stats_table.rowCount()
            self.stats_table.insertRow(row)
            
            # 字段类型
            self.stats_table.setItem(row, 0, QTableWidgetItem(f"{field_name} ({field_type})"))
            
            # 项目数量
            self.stats_table.setItem(row, 1, QTableWidgetItem(str(stats.get('total_items', 0))))
            
            # 总使用次数
            self.stats_table.setItem(row, 2, QTableWidgetItem(str(stats.get('total_usage', 0))))
            
            # 最常用项
            most_used = stats.get('most_used', '')
            most_used_count = stats.get('most_used_count', 0)
            most_used_text = f"{most_used} ({most_used_count}次)" if most_used else "无"
            self.stats_table.setItem(row, 3, QTableWidgetItem(most_used_text))
            
            # 最后更新
            last_updated = stats.get('last_updated', '')
            if last_updated:
                # 格式化时间显示
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(last_updated)
                    formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    formatted_time = last_updated
            else:
                formatted_time = "从未使用"
            self.stats_table.setItem(row, 4, QTableWidgetItem(formatted_time))
    
    def clear_selected_field(self):
        """清除选中字段的数据"""
        current_row = self.stats_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要清除的字段")
            return
        
        # 获取字段类型
        field_item = self.stats_table.item(current_row, 0)
        if not field_item:
            return
        
        field_text = field_item.text()
        # 从文本中提取字段类型（括号内的内容）
        if '(' in field_text and ')' in field_text:
            field_type = field_text.split('(')[1].split(')')[0]
        else:
            return
        
        # 确认对话框
        reply = QMessageBox.question(
            self, "确认清除",
            f"确定要清除字段 '{self.model.FIELD_TYPES.get(field_type, field_type)}' 的所有数据吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.model.clear_field_data(field_type)
            if success:
                QMessageBox.information(self, "成功", "字段数据已清除")
                self.load_stats()
            else:
                QMessageBox.warning(self, "失败", "清除字段数据失败")
    
    def clear_all_data(self):
        """清除所有数据"""
        reply = QMessageBox.question(
            self, "确认清除",
            "确定要清除所有自动补全数据吗？此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.model.clear_all_data()
            QMessageBox.information(self, "成功", "所有数据已清除")
            self.load_stats()


class DataImportExportDialog(QDialog):
    """数据导入导出对话框"""
    
    def __init__(self, model: AutoCompleteModel, parent=None):
        super().__init__(parent)
        self.model = model
        self.setWindowTitle("数据导入导出")
        self.setModal(True)
        self.resize(500, 300)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 导出区域
        export_group = QGroupBox("导出数据")
        export_layout = QVBoxLayout()
        
        export_info = QLabel("将当前的自动补全数据导出到文件")
        export_layout.addWidget(export_info)
        
        export_btn_layout = QHBoxLayout()
        self.export_btn = QPushButton("选择导出位置")
        self.export_btn.clicked.connect(self.export_data)
        export_btn_layout.addWidget(self.export_btn)
        export_btn_layout.addStretch()
        
        export_layout.addLayout(export_btn_layout)
        export_group.setLayout(export_layout)
        layout.addWidget(export_group)
        
        # 导入区域
        import_group = QGroupBox("导入数据")
        import_layout = QVBoxLayout()
        
        import_info = QLabel("从文件导入自动补全数据")
        import_layout.addWidget(import_info)
        
        import_btn_layout = QHBoxLayout()
        self.import_merge_btn = QPushButton("导入并合并")
        self.import_merge_btn.clicked.connect(lambda: self.import_data(merge=True))
        
        self.import_replace_btn = QPushButton("导入并替换")
        self.import_replace_btn.clicked.connect(lambda: self.import_data(merge=False))
        
        import_btn_layout.addWidget(self.import_merge_btn)
        import_btn_layout.addWidget(self.import_replace_btn)
        import_btn_layout.addStretch()
        
        import_layout.addLayout(import_btn_layout)
        import_group.setLayout(import_layout)
        layout.addWidget(import_group)
        
        # 关闭按钮
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        close_layout.addWidget(self.close_btn)
        
        layout.addLayout(close_layout)
        self.setLayout(layout)
    
    def export_data(self):
        """导出数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出自动补全数据",
            "autocomplete_backup.json",
            "JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if file_path:
            success = self.model.export_data(file_path)
            if success:
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            else:
                QMessageBox.warning(self, "失败", "导出数据失败")
    
    def import_data(self, merge: bool):
        """导入数据"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入自动补全数据",
            "",
            "JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if file_path:
            action = "合并" if merge else "替换"
            reply = QMessageBox.question(
                self, "确认导入",
                f"确定要{action}导入数据吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success = self.model.import_data(file_path, merge)
                if success:
                    QMessageBox.information(self, "成功", f"数据已{action}导入")
                else:
                    QMessageBox.warning(self, "失败", "导入数据失败")


class AutoCompleteManager:
    """自动补全管理器"""
    
    def __init__(self, model: AutoCompleteModel):
        self.model = model
    
    def show_stats_dialog(self, parent=None):
        """显示统计信息对话框"""
        dialog = AutoCompleteStatsDialog(self.model, parent)
        dialog.exec_()
    
    def show_import_export_dialog(self, parent=None):
        """显示导入导出对话框"""
        dialog = DataImportExportDialog(self.model, parent)
        dialog.exec_()
    
    def get_summary_info(self) -> Dict:
        """获取摘要信息"""
        total_items = 0
        total_usage = 0
        
        for field_type in self.model.FIELD_TYPES.keys():
            stats = self.model.get_field_stats(field_type)
            total_items += stats.get('total_items', 0)
            total_usage += stats.get('total_usage', 0)
        
        return {
            'total_fields': len(self.model.FIELD_TYPES),
            'total_items': total_items,
            'total_usage': total_usage,
            'data_loaded': self.model.is_loaded()
        }
