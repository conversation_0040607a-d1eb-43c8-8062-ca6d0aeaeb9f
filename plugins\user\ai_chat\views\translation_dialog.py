"""
翻译对话框

提供专用的翻译界面，支持中英文双向翻译和文档翻译
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton, 
    QLabel, QComboBox, QFileDialog, QMessageBox, QSplitter,
    QGroupBox, QProgressBar, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont


class TranslationDialog(QDialog):
    """翻译对话框"""
    
    # 信号
    translation_requested = pyqtSignal(str, str)  # text, target_lang
    document_translation_requested = pyqtSignal(str, str)  # file_path, target_lang
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("AI翻译助手")
        self.setModal(True)
        self.resize(800, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("AI翻译助手")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("", 16, QFont.Bold))
        main_layout.addWidget(title_label)
        
        # 翻译方向选择
        direction_layout = QHBoxLayout()
        direction_layout.addWidget(QLabel("翻译方向:"))
        
        self.direction_combo = QComboBox()
        self.direction_combo.addItems([
            "中文 → 英文",
            "英文 → 中文",
            "自动检测"
        ])
        self.direction_combo.setCurrentIndex(2)  # 默认自动检测
        direction_layout.addWidget(self.direction_combo)
        direction_layout.addStretch()
        
        main_layout.addLayout(direction_layout)
        
        # 翻译区域
        translation_splitter = QSplitter(Qt.Horizontal)
        
        # 输入区域
        input_group = QGroupBox("原文")
        input_layout = QVBoxLayout(input_group)
        
        self.input_text = QTextEdit()
        self.input_text.setPlaceholderText("请输入要翻译的文本...")
        self.input_text.setFont(QFont("", 12))
        input_layout.addWidget(self.input_text)
        
        # 输入区域按钮
        input_buttons = QHBoxLayout()
        
        self.clear_input_btn = QPushButton("清空")
        self.clear_input_btn.clicked.connect(self.clear_input)
        input_buttons.addWidget(self.clear_input_btn)
        
        self.upload_doc_btn = QPushButton("上传文档")
        self.upload_doc_btn.clicked.connect(self.upload_document)
        input_buttons.addWidget(self.upload_doc_btn)
        
        input_buttons.addStretch()
        input_layout.addLayout(input_buttons)
        
        translation_splitter.addWidget(input_group)
        
        # 输出区域
        output_group = QGroupBox("译文")
        output_layout = QVBoxLayout(output_group)
        
        self.output_text = QTextEdit()
        self.output_text.setPlaceholderText("翻译结果将显示在这里...")
        self.output_text.setFont(QFont("", 12))
        self.output_text.setReadOnly(True)
        output_layout.addWidget(self.output_text)
        
        # 输出区域按钮
        output_buttons = QHBoxLayout()
        
        self.copy_output_btn = QPushButton("复制译文")
        self.copy_output_btn.clicked.connect(self.copy_output)
        output_buttons.addWidget(self.copy_output_btn)
        
        self.save_output_btn = QPushButton("保存译文")
        self.save_output_btn.clicked.connect(self.save_output)
        output_buttons.addWidget(self.save_output_btn)
        
        output_buttons.addStretch()
        output_layout.addLayout(output_buttons)
        
        translation_splitter.addWidget(output_group)
        translation_splitter.setSizes([400, 400])
        
        main_layout.addWidget(translation_splitter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 选项
        options_layout = QHBoxLayout()
        
        self.preserve_format_cb = QCheckBox("保持格式")
        self.preserve_format_cb.setChecked(True)
        options_layout.addWidget(self.preserve_format_cb)
        
        options_layout.addStretch()
        main_layout.addLayout(options_layout)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.translate_btn = QPushButton("开始翻译")
        self.translate_btn.clicked.connect(self.start_translation)
        self.translate_btn.setDefault(True)
        button_layout.addWidget(self.translate_btn)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
        
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #FFFFFF;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }
            QLabel {
                color: #2C3E50;
                font-weight: 600;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #1DA1F2;
            }
            QTextEdit {
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                padding: 12px;
                background-color: #FAFBFC;
                selection-background-color: #1DA1F2;
                selection-color: white;
            }
            QTextEdit:focus {
                border-color: #1DA1F2;
                background-color: #FFFFFF;
            }
            QPushButton {
                background-color: #1DA1F2;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 13px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1991DB;
            }
            QPushButton:pressed {
                background-color: #1681C7;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                color: #7F8C8D;
            }
            QComboBox {
                border: 2px solid #E1E8ED;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #1DA1F2;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7F8C8D;
                margin-right: 5px;
            }
            QCheckBox {
                color: #2C3E50;
                font-weight: 500;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #BDC3C7;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #1DA1F2;
                border-color: #1DA1F2;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QProgressBar {
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                text-align: center;
                background-color: #F8F9FA;
            }
            QProgressBar::chunk {
                background-color: #1DA1F2;
                border-radius: 6px;
            }
        """)
    
    def clear_input(self):
        """清空输入"""
        self.input_text.clear()
        
    def upload_document(self):
        """上传文档"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择要翻译的文档",
            "",
            "支持的文档 (*.txt *.md *.docx *.pdf);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 读取文档内容
                if file_path.endswith('.txt') or file_path.endswith('.md'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    self.input_text.setPlainText(content)
                else:
                    # 对于其他格式，发送文档翻译请求
                    target_lang = self.get_target_language()
                    self.document_translation_requested.emit(file_path, target_lang)
                    
            except Exception as e:
                QMessageBox.warning(self, "错误", f"读取文档失败: {str(e)}")
    
    def copy_output(self):
        """复制输出"""
        text = self.output_text.toPlainText()
        if text:
            clipboard = self.parent().clipboard() if self.parent() else None
            if clipboard:
                clipboard.setText(text)
                QMessageBox.information(self, "提示", "译文已复制到剪贴板")
    
    def save_output(self):
        """保存输出"""
        text = self.output_text.toPlainText()
        if not text:
            QMessageBox.warning(self, "警告", "没有可保存的译文")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存译文",
            "translation.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                QMessageBox.information(self, "提示", "译文已保存")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存失败: {str(e)}")
    
    def get_target_language(self):
        """获取目标语言"""
        direction = self.direction_combo.currentText()
        if "英文" in direction and "中文" in direction:
            if direction.startswith("中文"):
                return "en"
            else:
                return "zh"
        return "auto"  # 自动检测
    
    def start_translation(self):
        """开始翻译"""
        text = self.input_text.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入要翻译的文本")
            return
        
        target_lang = self.get_target_language()
        self.set_translating(True)
        self.translation_requested.emit(text, target_lang)
    
    def set_translating(self, translating):
        """设置翻译状态"""
        self.translate_btn.setEnabled(not translating)
        self.progress_bar.setVisible(translating)
        
        if translating:
            self.progress_bar.setRange(0, 0)  # 无限进度条
            self.translate_btn.setText("翻译中...")
        else:
            self.progress_bar.setVisible(False)
            self.translate_btn.setText("开始翻译")
    
    def show_translation_result(self, original, translated, target_lang):
        """显示翻译结果"""
        self.output_text.setPlainText(translated)
        self.set_translating(False)
    
    def show_translation_error(self, error):
        """显示翻译错误"""
        self.set_translating(False)
        QMessageBox.warning(self, "翻译失败", f"翻译过程中发生错误:\n{error}")
