"""
版本选择器组件

提供Git提交历史选择功能，支持：
- 显示文件的提交历史
- 选择要比对的版本
- 显示提交详细信息
"""

import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QListWidget, 
    QListWidgetItem, QPushButton, QLabel, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QDateTime
from PyQt5.QtGui import QFont

from ..models.git_repository import GitRepository, CommitInfo


class CommitListItem(QListWidgetItem):
    """提交列表项"""
    
    def __init__(self, commit_info: CommitInfo):
        super().__init__()
        self.commit_info = commit_info
        self._setup_display()
    
    def _setup_display(self):
        """设置显示内容"""
        # 主要显示内容
        main_text = f"{self.commit_info.short_sha} - {self.commit_info.summary}"
        
        # 详细信息
        detail_text = f"{self.commit_info.author} • {self.commit_info.date.strftime('%Y-%m-%d %H:%M')}"
        
        # 组合显示文本
        display_text = f"{main_text}\n{detail_text}"
        self.setText(display_text)
        
        # 设置工具提示
        tooltip = f"提交: {self.commit_info.sha}\n"
        tooltip += f"作者: {self.commit_info.author} <{self.commit_info.author_email}>\n"
        tooltip += f"日期: {self.commit_info.date.strftime('%Y-%m-%d %H:%M:%S')}\n"
        tooltip += f"消息: {self.commit_info.message}"
        self.setToolTip(tooltip)
        
        # 设置数据
        self.setData(Qt.UserRole, self.commit_info)


class VersionSelector(QWidget):
    """版本选择器组件"""
    
    # 信号定义
    version_selected = pyqtSignal(object)  # 版本选择信号 (CommitInfo)
    compare_requested = pyqtSignal(str, str, str)  # 比对请求信号 (file_path, old_commit, new_commit)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.git_repo = None
        self.current_file_path = ""
        self.selected_commit = None
        self.commits = []
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建分组框
        group_box = QGroupBox("版本选择")
        layout.addWidget(group_box)
        
        group_layout = QVBoxLayout(group_box)
        group_layout.setSpacing(10)
        
        # 文件信息标签
        self.file_info_label = QLabel("请先选择文件")
        self.file_info_label.setStyleSheet("color: #666; font-style: italic;")
        group_layout.addWidget(self.file_info_label)
        
        # 提交历史列表
        self.commit_list = QListWidget()
        self.commit_list.setAlternatingRowColors(True)
        
        # 设置列表项的字体
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(9)
        self.commit_list.setFont(font)
        
        group_layout.addWidget(self.commit_list)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("刷新历史")
        self.refresh_button.setEnabled(False)
        
        self.compare_button = QPushButton("与当前版本比对")
        self.compare_button.setEnabled(False)
        
        button_layout.addWidget(self.refresh_button)
        button_layout.addWidget(self.compare_button)
        
        group_layout.addLayout(button_layout)
        
        # 状态标签
        self.status_label = QLabel("等待文件选择...")
        self.status_label.setStyleSheet("color: #666; font-size: 11px;")
        group_layout.addWidget(self.status_label)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.commit_list.itemClicked.connect(self._on_commit_selected)
        self.commit_list.itemDoubleClicked.connect(self._on_commit_double_clicked)
        self.refresh_button.clicked.connect(self._on_refresh_clicked)
        self.compare_button.clicked.connect(self._on_compare_clicked)
    
    def set_file_path(self, file_path: str):
        """设置文件路径并加载提交历史"""
        try:
            self.current_file_path = file_path
            
            # 更新文件信息显示
            filename = os.path.basename(file_path)
            self.file_info_label.setText(f"文件: {filename}")
            
            # 初始化Git仓库
            file_dir = os.path.dirname(file_path)
            self.git_repo = GitRepository(file_dir)
            
            if not self.git_repo.is_valid_repo():
                self.status_label.setText("文件不在Git仓库中")
                self.refresh_button.setEnabled(False)
                return
            
            # 加载提交历史
            self._load_commit_history()
            
            # 启用刷新按钮
            self.refresh_button.setEnabled(True)
            
        except Exception as e:
            self.status_label.setText(f"设置文件路径失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"设置文件路径失败: {str(e)}")
    
    def _load_commit_history(self):
        """加载提交历史"""
        try:
            if not self.git_repo or not self.current_file_path:
                return
            
            self.status_label.setText("正在加载提交历史...")
            
            # 获取文件的提交历史（直接传递绝对路径，让Git仓库类处理路径转换）
            self.commits = self.git_repo.get_file_commits(self.current_file_path, max_count=100)
            
            # 清空列表
            self.commit_list.clear()
            self.selected_commit = None
            self.compare_button.setEnabled(False)
            
            if not self.commits:
                self.status_label.setText("该文件没有提交历史")
                return
            
            # 添加提交到列表
            for commit in self.commits:
                item = CommitListItem(commit)
                self.commit_list.addItem(item)
            
            # 默认选择第一个提交（最新的）
            if self.commits:
                self.commit_list.setCurrentRow(0)
                self._on_commit_selected(self.commit_list.item(0))
            
            self.status_label.setText(f"已加载 {len(self.commits)} 个提交")
            
        except Exception as e:
            self.status_label.setText("加载提交历史失败")
            QMessageBox.warning(self, "错误", f"加载提交历史失败: {str(e)}")
    
    def _on_commit_selected(self, item):
        """提交选择事件处理"""
        if not item:
            return
        
        try:
            commit_info = item.data(Qt.UserRole)
            if commit_info:
                self.selected_commit = commit_info
                self.compare_button.setEnabled(True)
                
                # 发送版本选择信号
                self.version_selected.emit(commit_info)
                
                # 更新状态
                self.status_label.setText(f"已选择: {commit_info.short_sha}")
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"选择提交失败: {str(e)}")
    
    def _on_commit_double_clicked(self, item):
        """提交双击事件处理 - 直接进行比对"""
        self._on_commit_selected(item)
        if self.selected_commit:
            self._on_compare_clicked()
    
    def _on_refresh_clicked(self):
        """刷新按钮点击事件处理"""
        if self.current_file_path:
            self._load_commit_history()
    
    def _on_compare_clicked(self):
        """比对按钮点击事件处理"""
        try:
            if not self.selected_commit or not self.current_file_path:
                QMessageBox.warning(self, "警告", "请先选择要比对的版本")
                return
            
            # 检查文件在选中提交中是否存在（直接传递绝对路径）
            if not self.git_repo.file_exists_in_commit(self.current_file_path, self.selected_commit.sha):
                QMessageBox.warning(
                    self, "警告", 
                    f"文件在提交 {self.selected_commit.short_sha} 中不存在"
                )
                return
            
            # 发送比对请求信号
            # 参数：文件路径，旧版本（选中的提交），新版本（当前工作目录）
            self.compare_requested.emit(
                self.current_file_path,
                self.selected_commit.sha,
                "current"  # 表示当前工作目录版本
            )
            
            self.status_label.setText(f"正在比对 {self.selected_commit.short_sha} 与当前版本...")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"比对失败: {str(e)}")
    
    def refresh(self):
        """刷新提交历史"""
        if self.current_file_path:
            self._load_commit_history()
    
    def get_selected_commit(self) -> CommitInfo:
        """获取当前选中的提交"""
        return self.selected_commit
    
    def get_commits(self) -> list:
        """获取所有提交列表"""
        return self.commits
    
    def clear(self):
        """清空选择器"""
        self.commit_list.clear()
        self.selected_commit = None
        self.commits = []
        self.current_file_path = ""
        self.file_info_label.setText("请先选择文件")
        self.status_label.setText("等待文件选择...")
        self.refresh_button.setEnabled(False)
        self.compare_button.setEnabled(False)
