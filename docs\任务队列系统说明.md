# RunSim GUI 任务队列管理系统

## 📋 系统概述

任务队列管理系统是对RunSim GUI的重大升级，彻底解决了原有的单任务执行限制，提供了企业级的任务队列管理能力。

### 🎯 解决的问题

**原有问题**：
- ❌ 只能单个任务执行，达到10个标签页上限后无法创建新任务
- ❌ 无法批量提交任务进行排队执行
- ❌ 缺乏任务优先级管理
- ❌ 没有统一的任务状态监控

**解决方案**：
- ✅ 支持最多20个任务同时执行（可配置）
- ✅ 智能任务调度，基于优先级和依赖关系
- ✅ 实时任务状态监控和管理界面
- ✅ 完善的配置管理和状态持久化

## 🏗️ 系统架构

### 核心组件

1. **TaskQueue** - 任务队列核心类
   - 任务添加、移除、状态管理
   - 优先级排序和依赖关系处理
   - 线程安全的队列操作

2. **TaskQueueManager** - 任务队列管理器
   - 整体队列管理和任务调度
   - 并发控制和资源分配
   - 进程管理和状态监控

3. **TaskMonitorPanel** - 任务监控面板
   - 实时状态显示和进度监控
   - 任务操作控制（暂停、恢复、取消）
   - 队列统计和性能指标

4. **TaskQueueConfig** - 配置管理器
   - 配置加载、保存和验证
   - 默认配置和用户自定义配置
   - 配置导入导出功能

### 数据结构

#### 任务优先级
```python
class TaskPriority(Enum):
    URGENT = 1      # 紧急任务
    HIGH = 2        # 高优先级  
    NORMAL = 3      # 普通优先级
    LOW = 4         # 低优先级
    BACKGROUND = 5  # 后台任务
```

#### 任务状态
```python
class TaskStatus(Enum):
    PENDING = "pending"         # 等待执行
    QUEUED = "queued"          # 已排队
    RUNNING = "running"        # 正在执行
    COMPLETED = "completed"    # 执行完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"    # 已取消
    PAUSED = "paused"          # 已暂停
```

## 🚀 主要功能

### 1. 任务队列管理
- **批量任务提交**：支持一次性提交多个任务到队列
- **优先级调度**：按优先级自动调度任务执行顺序
- **依赖关系处理**：支持任务间的依赖关系管理
- **并发控制**：可配置的最大并发任务数（1-20）

### 2. 任务状态监控
- **实时状态显示**：任务列表实时更新状态和进度
- **统计信息面板**：显示队列总体统计信息
- **进度跟踪**：任务执行进度的可视化显示
- **日志集成**：与现有日志系统无缝集成

### 3. 任务操作控制
- **暂停/恢复**：可以暂停排队中的任务，稍后恢复
- **取消任务**：取消排队或正在执行的任务
- **重试机制**：失败任务的自动重试功能
- **批量操作**：支持批量取消、暂停等操作

### 4. 配置和持久化
- **配置管理**：完整的配置系统，支持各种参数调整
- **状态持久化**：系统重启后自动恢复任务状态
- **自动保存**：定期自动保存队列状态
- **配置导入导出**：支持配置的备份和共享

## 🎛️ 用户界面

### 主界面集成
- 在主窗口右侧添加了"任务队列"标签页
- 菜单栏新增"任务队列"菜单，提供队列控制选项
- 与现有"执行日志"标签页完美集成

### 任务监控面板
- **统计概览**：显示总计、排队、运行、完成、失败等统计
- **任务列表**：表格形式显示所有任务的详细信息
- **控制面板**：队列启停、并发数设置、状态筛选等
- **详情面板**：选中任务的详细信息显示

### 操作方式
1. **队列模式切换**：通过菜单"任务队列" → "启用队列模式"
2. **任务提交**：启用队列模式后，执行命令自动进入队列
3. **状态监控**：在"任务队列"标签页实时查看任务状态
4. **队列控制**：通过菜单或控制面板管理队列

## ⚙️ 配置选项

### 基本设置
- **最大并发任务数**：1-20（默认5）
- **调度间隔**：100-60000毫秒（默认1000）
- **自动重试失败任务**：是/否（默认是）
- **最大重试次数**：0-10（默认3）

### 持久化设置
- **保存队列状态**：是/否（默认是）
- **状态文件路径**：task_queue_state.json
- **自动保存间隔**：30秒

### 资源管理
- **最大内存限制**：512-32768MB（默认4096）
- **CPU使用率限制**：10-100%（默认80）
- **资源检查间隔**：5秒

### 界面设置
- **自动刷新间隔**：2秒
- **显示已完成任务**：是/否（默认是）
- **最大显示任务数**：1000

## 📊 性能优势

### 执行效率提升
- **并发执行**：最多20个任务同时执行，相比原来的单任务执行，效率提升高达20倍
- **智能调度**：优先级调度确保重要任务优先执行
- **资源优化**：智能资源分配，避免系统过载

### 用户体验改善
- **无需等待**：提交任务后立即返回，无需等待当前任务完成
- **状态透明**：实时查看所有任务的执行状态和进度
- **操作灵活**：可以随时暂停、恢复、取消任务

### 系统稳定性
- **错误隔离**：单个任务失败不影响其他任务执行
- **状态恢复**：系统重启后自动恢复任务状态
- **资源保护**：防止系统资源过度使用

## 🔧 技术实现

### 核心技术栈
- **PyQt5**：GUI框架和信号槽机制
- **Python Threading**：线程安全的队列操作
- **JSON**：配置和状态数据序列化
- **heapq**：优先级队列实现

### 设计模式
- **观察者模式**：任务状态变化通知
- **单例模式**：配置管理器
- **工厂模式**：任务对象创建
- **策略模式**：任务调度算法

### 关键特性
- **线程安全**：所有队列操作都是线程安全的
- **信号驱动**：基于PyQt信号槽的事件驱动架构
- **模块化设计**：各组件职责清晰，易于维护和扩展
- **向后兼容**：完全兼容现有的执行方式

## 📝 使用示例

### 基本使用流程
```python
# 1. 启用队列模式
execution_controller.enable_queue_mode(True)

# 2. 添加任务到队列
task_id = task_manager.add_task(
    name="测试任务",
    command="runsim -case test_case",
    priority=TaskPriority.HIGH
)

# 3. 监控任务状态
stats = task_manager.get_queue_stats()
print(f"队列状态: {stats}")

# 4. 操作任务
task_manager.pause_task(task_id)
task_manager.resume_task(task_id)
task_manager.cancel_task(task_id)
```

### 配置管理示例
```python
# 获取配置管理器
config = task_manager.get_config_manager()

# 修改配置
config.set('max_concurrent_tasks', 8)
config.update({
    'scheduling_interval': 500,
    'auto_retry_failed': True
})

# 保存配置
config.save_config()
```

## 🧪 测试验证

系统包含完整的测试套件：

1. **simple_test.py** - 基本功能测试
2. **demo_task_queue.py** - 完整功能演示
3. **test_task_queue.py** - 综合测试套件

测试覆盖：
- ✅ 任务创建和队列操作
- ✅ 优先级调度验证
- ✅ 并发执行测试
- ✅ 配置管理测试
- ✅ 状态持久化测试
- ✅ GUI集成测试

## 🔮 未来扩展

### 计划功能
1. **任务模板**：保存常用任务配置为模板
2. **任务分组**：支持任务的分类管理
3. **执行统计**：详细的执行时间和成功率统计
4. **通知系统**：任务完成的邮件或桌面通知
5. **远程队列**：支持分布式任务执行

### 性能优化
1. **动态调度**：根据系统负载动态调整并发数
2. **智能重试**：基于失败原因的智能重试策略
3. **资源预测**：基于历史数据预测任务资源需求

---

**版本**：1.0.0  
**更新时间**：2024年12月  
**作者**：RunSim GUI开发团队
