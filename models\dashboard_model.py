"""
数据看板数据模型
负责管理仿真记录、统计数据和数据库操作
"""
import os
import sqlite3
import threading
import queue
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QTimer


class DatabaseConnectionPool:
    """数据库连接池"""

    def __init__(self, db_path: str, max_connections: int = 5):
        self.db_path = db_path
        self.max_connections = max_connections
        self._pool = queue.Queue(maxsize=max_connections)
        self._lock = threading.Lock()
        self._created_connections = 0

        # 预创建一些连接
        for _ in range(min(2, max_connections)):
            self._create_connection()

    def _create_connection(self):
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            conn.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式提高并发性能
            conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全性
            conn.execute("PRAGMA cache_size=10000")  # 增加缓存大小
            conn.execute("PRAGMA temp_store=MEMORY")  # 临时表存储在内存中
            self._pool.put(conn)
            self._created_connections += 1
        except Exception as e:
            print(f"创建数据库连接失败: {e}")

    def get_connection(self, timeout: float = 5.0):
        """获取数据库连接"""
        try:
            # 尝试从池中获取连接
            conn = self._pool.get(timeout=timeout)
            return conn
        except queue.Empty:
            # 如果池为空且未达到最大连接数，创建新连接
            with self._lock:
                if self._created_connections < self.max_connections:
                    self._create_connection()
                    return self._pool.get(timeout=1.0)
            raise Exception("无法获取数据库连接：连接池已满")

    def return_connection(self, conn):
        """归还数据库连接到池中"""
        try:
            # 检查连接是否仍然有效
            conn.execute("SELECT 1")
            self._pool.put(conn, timeout=1.0)
        except (sqlite3.Error, queue.Full):
            # 连接无效或池已满，关闭连接
            try:
                conn.close()
            except:
                pass
            with self._lock:
                self._created_connections -= 1

    def close_all(self):
        """关闭所有连接"""
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except (queue.Empty, sqlite3.Error):
                break
        self._created_connections = 0


class AsyncDatabaseWorker(QThread):
    """异步数据库操作工作线程"""

    # 信号定义
    record_added = pyqtSignal(dict)
    record_updated = pyqtSignal(dict)
    operation_completed = pyqtSignal(str, bool, object)  # operation_type, success, result

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path
        self.operations_queue = []
        self.queue_lock = threading.Lock()
        self.running = True

    def add_operation(self, operation_type, **kwargs):
        """添加数据库操作到队列"""
        with self.queue_lock:
            self.operations_queue.append({
                'type': operation_type,
                'params': kwargs
            })

    def run(self):
        """工作线程主循环"""
        while self.running:
            operations_to_process = []

            # 获取待处理的操作
            with self.queue_lock:
                if self.operations_queue:
                    operations_to_process = self.operations_queue.copy()
                    self.operations_queue.clear()

            # 处理操作
            for operation in operations_to_process:
                try:
                    self.process_operation(operation)
                except Exception as e:
                    print(f"异步数据库操作失败: {str(e)}")

            # 短暂休眠，避免CPU占用过高
            self.msleep(100)

    def process_operation(self, operation):
        """处理单个数据库操作"""
        op_type = operation['type']
        params = operation['params']

        try:
            if op_type == 'add_record':
                self._add_record_sync(**params)
            elif op_type == 'update_status':
                self._update_status_sync(**params)
            # 可以添加更多操作类型

        except Exception as e:
            print(f"处理数据库操作失败 {op_type}: {str(e)}")

    def _add_record_sync(self, case_name, command_line="", simulation_stage="DVR1"):
        """同步添加记录（在工作线程中执行）"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.cursor()

            # 检查是否已存在相同用例的记录（任何状态）
            cursor.execute('''
                SELECT id, status, start_time FROM simulation_records
                WHERE case_name = ?
                ORDER BY updated_at DESC LIMIT 1
            ''', (case_name,))

            existing_record = cursor.fetchone()
            if existing_record:
                record_id, current_status, start_time = existing_record
                # 如果存在记录，重置为Pending状态并更新相关信息
                # 注意：保留原有的start_time，不更新开始时间
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute('''
                    UPDATE simulation_records
                    SET status = 'Pending', command_line = ?, simulation_stage = ?,
                        updated_at = ?, compile_time = NULL,
                        simulation_time = NULL, log_path = NULL
                    WHERE id = ?
                ''', (command_line, simulation_stage, now, record_id))

                conn.commit()

                # 构建更新后的记录信息
                record = {
                    'id': record_id,
                    'case_name': case_name,
                    'status': 'Pending',
                    'command_line': command_line,
                    'simulation_stage': simulation_stage,
                    'updated_at': now
                }

                # 发送更新信号而不是添加信号
                self.record_updated.emit(record)
                self.operation_completed.emit('add_record', True, record_id)
                return record_id

            # 如果不存在记录，插入新记录（首次执行）
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute('''
                INSERT INTO simulation_records
                (case_name, status, command_line, simulation_stage, created_at, updated_at)
                VALUES (?, 'Pending', ?, ?, ?, ?)
            ''', (case_name, command_line, simulation_stage, now, now))

            record_id = cursor.lastrowid
            conn.commit()

            # 构建记录信息
            record = {
                'id': record_id,
                'case_name': case_name,
                'status': 'Pending',
                'command_line': command_line,
                'simulation_stage': simulation_stage,
                'created_at': now,
                'updated_at': now
            }

            # 发送信号
            self.record_added.emit(record)
            self.operation_completed.emit('add_record', True, record_id)

        except Exception as e:
            print(f"异步添加记录失败: {str(e)}")
            self.operation_completed.emit('add_record', False, None)
        finally:
            conn.close()

    def _update_status_sync(self, case_name, status=None, start_time=None, end_time=None,
                           compile_time=None, simulation_time=None, log_path=None, simulation_stage=None):
        """同步更新状态（在工作线程中执行）"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.cursor()

            # 首先获取当前记录的状态信息
            cursor.execute('''
                SELECT start_time, end_time, status FROM simulation_records
                WHERE case_name = ?
                ORDER BY updated_at DESC LIMIT 1
            ''', (case_name,))

            current_record = cursor.fetchone()
            current_start_time, current_end_time, current_status = current_record if current_record else (None, None, None)

            # 构建更新SQL - 使用标准化时间格式
            update_fields = ["updated_at = ?"]
            update_values = [datetime.now().strftime('%Y-%m-%d %H:%M:%S')]

            if status is not None:
                update_fields.append("status = ?")
                update_values.append(status)

                # 结束时间更新逻辑：只在状态变为PASS且之前没有设置过结束时间时才更新
                if status == 'PASS' and current_end_time is None and end_time is None:
                    update_fields.append("end_time = ?")
                    update_values.append(datetime.now())

            # 开始时间更新逻辑：只在明确指定start_time且当前没有开始时间时才更新
            if start_time is not None and current_start_time is None:
                update_fields.append("start_time = ?")
                update_values.append(start_time)

            # 结束时间更新逻辑：只在明确指定end_time且当前没有结束时间时才更新
            if end_time is not None and current_end_time is None:
                update_fields.append("end_time = ?")
                update_values.append(end_time)

            if compile_time is not None:
                update_fields.append("compile_time = ?")
                update_values.append(compile_time)

            if simulation_time is not None:
                update_fields.append("simulation_time = ?")
                update_values.append(simulation_time)

            if log_path is not None:
                update_fields.append("log_path = ?")
                update_values.append(log_path)

            if simulation_stage is not None:
                update_fields.append("simulation_stage = ?")
                update_values.append(simulation_stage)

            update_values.append(case_name)

            # 执行更新
            cursor.execute(f'''
                UPDATE simulation_records
                SET {", ".join(update_fields)}
                WHERE case_name = ?
            ''', update_values)

            if cursor.rowcount > 0:
                conn.commit()

                # 获取更新后的记录
                cursor.execute('''
                    SELECT * FROM simulation_records
                    WHERE case_name = ?
                    ORDER BY updated_at DESC LIMIT 1
                ''', (case_name,))

                row = cursor.fetchone()
                if row:
                    columns = [description[0] for description in cursor.description]
                    record = dict(zip(columns, row))
                    self.record_updated.emit(record)

                self.operation_completed.emit('update_status', True, True)
            else:
                self.operation_completed.emit('update_status', False, False)

        except Exception as e:
            print(f"异步更新状态失败: {str(e)}")
            self.operation_completed.emit('update_status', False, False)
        finally:
            conn.close()

    def stop(self):
        """停止工作线程"""
        self.running = False

        # 安全断开信号连接，避免在Linux下出现重复断开连接的错误
        try:
            # 断开所有信号连接
            self.record_added.disconnect()
            self.record_updated.disconnect()
            self.operation_completed.disconnect()
        except (TypeError, RuntimeError):
            # 信号可能已经断开或对象已被销毁，忽略错误
            pass

        if self.isRunning():
            self.wait(5000)  # 等待最多5秒


class DashboardModel(QObject):
    """数据看板数据模型"""
    
    # 信号定义
    record_added = pyqtSignal(dict)  # 新增记录
    record_updated = pyqtSignal(dict)  # 更新记录
    statistics_updated = pyqtSignal(dict)  # 统计数据更新
    
    def __init__(self, db_path=None):
        """初始化数据看板模型

        Args:
            db_path (str): 数据库文件路径，如果为None则使用当前工作目录下的runsim_dashboard.db
        """
        super().__init__()

        # 如果没有指定数据库路径，使用当前工作目录
        if db_path is None:
            db_path = os.path.join(os.getcwd(), "runsim_dashboard.db")

        self.db_path = db_path
        self._lock = threading.Lock()
        self._cleaned_up = False  # 清理状态标志

        # 确保数据库目录存在
        db_dir = os.path.dirname(db_path)
        if db_dir:  # 如果有目录部分
            os.makedirs(db_dir, exist_ok=True)

        # 初始化数据库连接池
        self.connection_pool = DatabaseConnectionPool(db_path, max_connections=5)

        # 初始化数据库
        self.init_database()

        # 创建异步数据库工作线程（仅在有Qt应用程序时）
        try:
            from PyQt5.QtWidgets import QApplication
            if QApplication.instance() is not None:
                self.async_worker = AsyncDatabaseWorker(db_path)
                self.async_worker.record_added.connect(self.record_added.emit)
                self.async_worker.record_updated.connect(self.record_updated.emit)
                self.async_worker.start()
            else:
                # 在测试环境中不启动异步工作线程
                self.async_worker = None
                print("警告：在非Qt环境中运行，异步工作线程已禁用")
        except Exception as e:
            print(f"异步工作线程启动失败: {e}")
            self.async_worker = None
    
    def init_database(self):
        """初始化数据库表结构"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 获取SQLite版本信息
                cursor.execute("SELECT sqlite_version()")
                sqlite_version = cursor.fetchone()[0]
                print(f"SQLite版本: {sqlite_version}")

                # 创建仿真记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS simulation_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        case_name TEXT NOT NULL,
                        status TEXT DEFAULT 'Pending',
                        start_time TIMESTAMP,
                        end_time TIMESTAMP,
                        compile_time REAL,
                        simulation_time REAL,
                        simulation_stage TEXT,
                        log_path TEXT,
                        command_line TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建每日统计表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE NOT NULL UNIQUE,
                        total_cases INTEGER DEFAULT 0,
                        pass_cases INTEGER DEFAULT 0,
                        fail_cases INTEGER DEFAULT 0,
                        ongoing_cases INTEGER DEFAULT 0,
                        total_compile_time REAL DEFAULT 0,
                        total_simulation_time REAL DEFAULT 0,
                        avg_compile_time REAL DEFAULT 0,
                        avg_simulation_time REAL DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建索引（使用兼容性更好的语法）
                index_statements = [
                    ('idx_simulation_records_case', 'CREATE INDEX IF NOT EXISTS idx_simulation_records_case ON simulation_records(case_name)'),
                    ('idx_simulation_records_status', 'CREATE INDEX IF NOT EXISTS idx_simulation_records_status ON simulation_records(status)'),
                    ('idx_simulation_records_created_at', 'CREATE INDEX IF NOT EXISTS idx_simulation_records_created_at ON simulation_records(created_at)'),
                    ('idx_simulation_records_updated_at', 'CREATE INDEX IF NOT EXISTS idx_simulation_records_updated_at ON simulation_records(updated_at)'),
                    ('idx_simulation_records_status_updated', 'CREATE INDEX IF NOT EXISTS idx_simulation_records_status_updated ON simulation_records(status, updated_at)'),
                    ('idx_simulation_records_case_status', 'CREATE INDEX IF NOT EXISTS idx_simulation_records_case_status ON simulation_records(case_name, status)'),
                    ('idx_daily_statistics_date', 'CREATE INDEX IF NOT EXISTS idx_daily_statistics_date ON daily_statistics(date)')
                ]

                for index_name, sql in index_statements:
                    try:
                        cursor.execute(sql)
                    except Exception as e:
                        print(f"索引 {index_name} 创建失败: {e}")
                        # 索引创建失败不影响整体功能，继续执行

                conn.commit()
                print("数据看板数据库初始化成功")

            except Exception as e:
                print(f"数据库初始化失败: {str(e)}")
                print(f"错误详情: {type(e).__name__}")
                import traceback
                traceback.print_exc()
                conn.rollback()
                raise  # 重新抛出异常，让调用者知道初始化失败
            finally:
                conn.close()
    
    def add_simulation_record(self, case_name: str, command_line: str = "",
                            simulation_stage: str = "DVR1") -> bool:
        """添加仿真记录

        Args:
            case_name (str): 用例名称
            command_line (str): 执行命令行
            simulation_stage (str): 仿真阶段

        Returns:
            bool: 是否成功添加记录
        """
        try:
            if self.async_worker is not None:
                # 异步模式：将操作添加到异步队列
                self.async_worker.add_operation(
                    'add_record',
                    case_name=case_name,
                    command_line=command_line,
                    simulation_stage=simulation_stage
                )
                return True
            else:
                # 同步模式：直接执行数据库操作
                return self._add_simulation_record_sync(case_name, command_line, simulation_stage)
        except Exception as e:
            print(f"添加仿真记录失败: {str(e)}")
            return False

    def _add_simulation_record_sync(self, case_name: str, command_line: str = "",
                                   simulation_stage: str = "DVR1") -> bool:
        """同步添加仿真记录

        Args:
            case_name (str): 用例名称
            command_line (str): 执行命令行
            simulation_stage (str): 仿真阶段

        Returns:
            bool: 是否成功添加
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()

            # 检查记录是否已存在
            cursor.execute('SELECT id FROM simulation_records WHERE case_name = ?', (case_name,))
            if cursor.fetchone():
                print(f"记录已存在: {case_name}")
                return False

            # 插入新记录 - 使用标准化时间格式确保跨平台兼容性
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute('''
                INSERT INTO simulation_records
                (case_name, command_line, simulation_stage, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (case_name, command_line, simulation_stage, 'Pending', now, now))

            conn.commit()

            # 发送信号（如果在Qt环境中）
            try:
                record_data = {
                    'case_name': case_name,
                    'command_line': command_line,
                    'simulation_stage': simulation_stage,
                    'status': 'Pending',
                    'created_at': now,
                    'updated_at': now
                }
                self.record_added.emit(record_data)
            except:
                pass  # 在非Qt环境中忽略信号发送

            return True

        except Exception as e:
            print(f"同步添加记录失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def update_simulation_status(self, case_name: str, status: str = None,
                               start_time: datetime = None, end_time: datetime = None,
                               compile_time: float = None, simulation_time: float = None,
                               log_path: str = None, simulation_stage: str = None) -> bool:
        """更新仿真状态

        Args:
            case_name (str): 用例名称
            status (str): 新状态 (Pending/On-Going/PASS/FAIL)，可选
            start_time (datetime): 开始时间
            end_time (datetime): 结束时间
            compile_time (float): 编译时间(分钟)
            simulation_time (float): 仿真时间(分钟)
            log_path (str): 日志路径
            simulation_stage (str): 仿真阶段

        Returns:
            bool: 是否成功更新状态
        """
        try:
            if self.async_worker is not None:
                # 异步模式：将操作添加到异步队列
                self.async_worker.add_operation(
                    'update_status',
                    case_name=case_name,
                    status=status,
                    start_time=start_time,
                    end_time=end_time,
                    compile_time=compile_time,
                    simulation_time=simulation_time,
                    log_path=log_path,
                    simulation_stage=simulation_stage
                )

                # 异步更新每日统计（仅在Qt环境中）
                try:
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(1000, self._update_daily_statistics_async)
                except:
                    pass  # 在非Qt环境中忽略

                return True
            else:
                # 同步模式：直接执行数据库操作
                return self._update_simulation_status_sync(
                    case_name, status, start_time, end_time,
                    compile_time, simulation_time, log_path, simulation_stage
                )
        except Exception as e:
            print(f"更新仿真状态失败: {str(e)}")
            return False

    def _update_simulation_status_sync(self, case_name: str, status: str = None,
                                      start_time: datetime = None, end_time: datetime = None,
                                      compile_time: float = None, simulation_time: float = None,
                                      log_path: str = None, simulation_stage: str = None) -> bool:
        """同步更新仿真状态

        Args:
            case_name (str): 用例名称
            status (str): 新状态
            start_time (datetime): 开始时间
            end_time (datetime): 结束时间
            compile_time (float): 编译时间
            simulation_time (float): 仿真时间
            log_path (str): 日志路径
            simulation_stage (str): 仿真阶段

        Returns:
            bool: 是否成功更新
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()

            # 构建更新语句
            update_fields = []
            params = []

            if status is not None:
                update_fields.append("status = ?")
                params.append(status)

            if start_time is not None:
                update_fields.append("start_time = ?")
                params.append(start_time.isoformat())

            if end_time is not None:
                update_fields.append("end_time = ?")
                params.append(end_time.isoformat())

            if compile_time is not None:
                update_fields.append("compile_time = ?")
                params.append(compile_time)

            if simulation_time is not None:
                update_fields.append("simulation_time = ?")
                params.append(simulation_time)

            if log_path is not None:
                update_fields.append("log_path = ?")
                params.append(log_path)

            if simulation_stage is not None:
                update_fields.append("simulation_stage = ?")
                params.append(simulation_stage)

            if not update_fields:
                return True  # 没有需要更新的字段

            # 添加更新时间 - 使用标准化时间格式
            update_fields.append("updated_at = ?")
            params.append(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

            # 添加WHERE条件
            params.append(case_name)

            # 执行更新
            sql = f"UPDATE simulation_records SET {', '.join(update_fields)} WHERE case_name = ?"
            cursor.execute(sql, params)

            if cursor.rowcount == 0:
                print(f"未找到要更新的记录: {case_name}")
                return False

            conn.commit()

            # 发送信号（如果在Qt环境中）
            try:
                record_data = {
                    'case_name': case_name,
                    'status': status,
                    'start_time': start_time.isoformat() if start_time else None,
                    'end_time': end_time.isoformat() if end_time else None,
                    'compile_time': compile_time,
                    'simulation_time': simulation_time,
                    'log_path': log_path,
                    'simulation_stage': simulation_stage
                }
                self.record_updated.emit(record_data)
            except:
                pass  # 在非Qt环境中忽略信号发送

            return True

        except Exception as e:
            print(f"同步更新状态失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def batch_add_simulation_records(self, cases: List[Dict]) -> int:
        """批量添加仿真记录（同步执行，用于导入场景）

        Args:
            cases (List[Dict]): 用例信息列表，每个字典包含case_name和simulation_stage

        Returns:
            int: 成功添加的记录数量
        """
        if not cases:
            return 0

        success_count = 0

        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 开始事务
                conn.execute('BEGIN TRANSACTION')

                for case_info in cases:
                    try:
                        case_name = case_info.get('case_name', '')
                        simulation_stage = case_info.get('simulation_stage', 'DVR1')
                        command_line = case_info.get('command_line', '')

                        if not case_name:
                            continue

                        # 检查是否已存在相同用例的记录
                        cursor.execute('''
                            SELECT id, start_time FROM simulation_records
                            WHERE case_name = ?
                            ORDER BY updated_at DESC LIMIT 1
                        ''', (case_name,))

                        existing_record = cursor.fetchone()
                        if existing_record:
                            # 如果存在记录，重置为Pending状态并更新相关信息
                            record_id, existing_start_time = existing_record
                            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            cursor.execute('''
                                UPDATE simulation_records
                                SET status = 'Pending', command_line = ?, simulation_stage = ?,
                                    updated_at = ?, compile_time = NULL,
                                    simulation_time = NULL, log_path = NULL
                                WHERE id = ?
                            ''', (command_line, simulation_stage, now, record_id))
                        else:
                            # 如果不存在记录，插入新记录
                            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            cursor.execute('''
                                INSERT INTO simulation_records
                                (case_name, status, command_line, simulation_stage, created_at, updated_at)
                                VALUES (?, 'Pending', ?, ?, ?, ?)
                            ''', (case_name, command_line, simulation_stage, now, now))

                        success_count += 1

                    except Exception as e:
                        print(f"批量添加用例 {case_info.get('case_name', 'Unknown')} 失败: {str(e)}")
                        continue

                # 提交事务
                conn.commit()

            except Exception as e:
                # 回滚事务
                conn.rollback()
                print(f"批量添加仿真记录失败: {str(e)}")
                success_count = 0
            finally:
                conn.close()

        return success_count

    def get_simulation_record(self, record_id: int) -> Optional[Dict]:
        """获取仿真记录
        
        Args:
            record_id (int): 记录ID
            
        Returns:
            Optional[Dict]: 记录信息，失败返回None
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM simulation_records WHERE id = ?', (record_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_dict(cursor, row)
                return None
                
            except Exception as e:
                print(f"获取仿真记录失败: {str(e)}")
                return None
            finally:
                conn.close()
    
    def get_all_simulation_records(self, limit: int = 1000) -> List[Dict]:
        """获取所有仿真记录

        Args:
            limit (int): 限制返回数量

        Returns:
            List[Dict]: 记录列表
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM simulation_records
                ORDER BY created_at DESC
                LIMIT ?
            ''', (limit,))

            rows = cursor.fetchall()
            return [self._row_to_dict(cursor, row) for row in rows]

        except Exception as e:
            print(f"获取仿真记录失败: {str(e)}")
            return []
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def get_simulation_records_by_status(self, status: str, limit: int = 1000) -> List[Dict]:
        """根据状态获取仿真记录（使用索引优化）

        Args:
            status (str): 状态过滤条件
            limit (int): 限制返回数量

        Returns:
            List[Dict]: 记录列表
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM simulation_records
                WHERE status = ?
                ORDER BY updated_at DESC
                LIMIT ?
            ''', (status, limit))

            rows = cursor.fetchall()
            return [self._row_to_dict(cursor, row) for row in rows]

        except Exception as e:
            print(f"根据状态获取仿真记录失败: {str(e)}")
            return []
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def get_simulation_records_incremental(self, last_update_time: str = None, limit: int = 1000) -> List[Dict]:
        """增量获取仿真记录（只获取更新时间晚于指定时间的记录）

        Args:
            last_update_time (str): 上次更新时间，ISO格式
            limit (int): 限制返回数量

        Returns:
            List[Dict]: 记录列表
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()

            if last_update_time:
                cursor.execute('''
                    SELECT * FROM simulation_records
                    WHERE updated_at > ?
                    ORDER BY updated_at DESC
                    LIMIT ?
                ''', (last_update_time, limit))
            else:
                # 如果没有指定时间，返回最近的记录
                cursor.execute('''
                    SELECT * FROM simulation_records
                    ORDER BY updated_at DESC
                    LIMIT ?
                ''', (limit,))

            rows = cursor.fetchall()
            return [self._row_to_dict(cursor, row) for row in rows]

        except Exception as e:
            print(f"增量获取仿真记录失败: {str(e)}")
            return []
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def get_simulation_records_paginated(self, page: int = 1, page_size: int = 50,
                                       status_filter: str = None, search_term: str = None) -> Tuple[List[Dict], int]:
        """分页获取仿真记录

        Args:
            page (int): 页码（从1开始）
            page_size (int): 每页记录数
            status_filter (str): 状态过滤条件
            search_term (str): 搜索关键词（用例名称）

        Returns:
            Tuple[List[Dict], int]: (记录列表, 总记录数)
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()

            # 构建查询条件
            where_conditions = []
            params = []

            if status_filter and status_filter.strip():
                where_conditions.append("status = ?")
                params.append(str(status_filter))

            if search_term and search_term.strip():
                where_conditions.append("case_name LIKE ?")
                params.append(f"%{str(search_term)}%")

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # 获取总记录数
            count_query = f"SELECT COUNT(*) FROM simulation_records {where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]

            # 获取分页数据
            offset = (int(page) - 1) * int(page_size)
            data_query = f'''
                SELECT * FROM simulation_records
                {where_clause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            '''
            # 确保所有参数都是正确的类型
            data_params = params + [int(page_size), int(offset)]
            cursor.execute(data_query, data_params)

            rows = cursor.fetchall()
            records = [self._row_to_dict(cursor, row) for row in rows]

            return records, total_count

        except Exception as e:
            print(f"分页获取仿真记录失败: {str(e)}")
            print(f"参数详情: page={page}, page_size={page_size}, status_filter={status_filter}, search_term={search_term}")
            import traceback
            traceback.print_exc()
            return [], 0
        finally:
            if conn:
                self.connection_pool.return_connection(conn)
    
    def get_daily_statistics(self, days: int = 30) -> List[Dict]:
        """获取每日统计数据

        Args:
            days (int): 获取最近多少天的数据

        Returns:
            List[Dict]: 统计数据列表
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 修复跨平台兼容性和时间统计逻辑：
                # 关键修复：通过用例数应该按结束时间统计，而不是创建时间
                # 这样可以确保用例在实际完成的日期被正确统计
                cursor.execute('''
                    SELECT
                        -- 使用创建时间作为主要日期维度
                        date(CASE
                            WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                            ELSE created_at
                        END) as date,
                        COUNT(*) as total_cases,
                        -- 通过用例数：按结束时间统计，只统计在当前日期完成的PASS用例
                        (SELECT COUNT(*)
                         FROM simulation_records sr2
                         WHERE sr2.status = 'PASS'
                           AND sr2.end_time IS NOT NULL
                           AND date(CASE
                               WHEN sr2.end_time LIKE '%T%' THEN replace(sr2.end_time, 'T', ' ')
                               ELSE sr2.end_time
                           END) = date(CASE
                               WHEN sr1.created_at LIKE '%T%' THEN replace(sr1.created_at, 'T', ' ')
                               ELSE sr1.created_at
                           END)
                        ) as pass_cases,
                        SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) as fail_cases,
                        SUM(CASE WHEN status = 'On-Going' THEN 1 ELSE 0 END) as ongoing_cases,
                        SUM(COALESCE(compile_time, 0)) as total_compile_time,
                        SUM(COALESCE(simulation_time, 0)) as total_simulation_time,
                        AVG(CASE WHEN compile_time IS NOT NULL THEN compile_time END) as avg_compile_time,
                        AVG(CASE WHEN simulation_time IS NOT NULL THEN simulation_time END) as avg_simulation_time
                    FROM simulation_records sr1
                    WHERE date(CASE
                        WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                        ELSE created_at
                    END) >= date('now', '-{} days')
                    GROUP BY date(CASE
                        WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                        ELSE created_at
                    END)

                    UNION

                    -- 添加只有通过记录但没有创建记录的日期
                    SELECT
                        date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END) as date,
                        0 as total_cases,
                        COUNT(*) as pass_cases,
                        0 as fail_cases,
                        0 as ongoing_cases,
                        0 as total_compile_time,
                        0 as total_simulation_time,
                        NULL as avg_compile_time,
                        NULL as avg_simulation_time
                    FROM simulation_records
                    WHERE status = 'PASS'
                        AND end_time IS NOT NULL
                        AND date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END) >= date('now', '-{} days')
                        AND date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END) NOT IN (
                            SELECT DISTINCT date(CASE
                                WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                                ELSE created_at
                            END)
                            FROM simulation_records
                            WHERE date(CASE
                                WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                                ELSE created_at
                            END) >= date('now', '-{} days')
                        )
                    GROUP BY date(CASE
                        WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                        ELSE end_time
                    END)

                    ORDER BY date DESC
                '''.format(days, days, days))

                rows = cursor.fetchall()
                return [self._row_to_dict(cursor, row) for row in rows]

            except Exception as e:
                print(f"获取每日统计失败: {str(e)}")
                print(f"错误详情: {type(e).__name__}")
                import traceback
                traceback.print_exc()
                return []
            finally:
                conn.close()

    def get_weekly_statistics(self, weeks: int = 12) -> List[Dict]:
        """获取每周统计数据

        Args:
            weeks (int): 获取最近多少周的数据

        Returns:
            List[Dict]: 周统计数据列表，包含week_label, pass_cases等字段
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 修复每周统计逻辑：通过用例数按结束时间统计
                # 使用ISO 8601标准计算周数，周一为一周的开始
                # 兼容新旧时间格式
                cursor.execute('''
                    SELECT
                        strftime('%Y', CASE
                            WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                            ELSE created_at
                        END) as year,
                        -- 使用ISO 8601标准周数计算（周一开始）
                        -- 将日期调整到周一，然后使用%W计算，最后+1得到ISO周数
                        (strftime('%W', date(CASE
                            WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                            ELSE created_at
                        END, 'weekday 1', '-6 days')) + 1) as week_num,
                        COUNT(*) as total_cases,
                        -- 通过用例数：按结束时间统计，确保在正确的完成周统计
                        (SELECT COUNT(*)
                         FROM simulation_records sr2
                         WHERE sr2.status = 'PASS'
                           AND sr2.end_time IS NOT NULL
                           AND strftime('%Y', CASE
                               WHEN sr2.end_time LIKE '%T%' THEN replace(sr2.end_time, 'T', ' ')
                               ELSE sr2.end_time
                           END) = strftime('%Y', CASE
                               WHEN sr1.created_at LIKE '%T%' THEN replace(sr1.created_at, 'T', ' ')
                               ELSE sr1.created_at
                           END)
                           AND (strftime('%W', date(CASE
                               WHEN sr2.end_time LIKE '%T%' THEN replace(sr2.end_time, 'T', ' ')
                               ELSE sr2.end_time
                           END, 'weekday 1', '-6 days')) + 1) = (strftime('%W', date(CASE
                               WHEN sr1.created_at LIKE '%T%' THEN replace(sr1.created_at, 'T', ' ')
                               ELSE sr1.created_at
                           END, 'weekday 1', '-6 days')) + 1)
                        ) as pass_cases,
                        SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) as fail_cases,
                        SUM(CASE WHEN status = 'On-Going' THEN 1 ELSE 0 END) as ongoing_cases,
                        AVG(CASE WHEN compile_time IS NOT NULL THEN compile_time END) as avg_compile_time,
                        AVG(CASE WHEN simulation_time IS NOT NULL THEN simulation_time END) as avg_simulation_time,
                        MIN(date(CASE
                            WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                            ELSE created_at
                        END)) as week_start_date,
                        MAX(date(CASE
                            WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                            ELSE created_at
                        END)) as week_end_date
                    FROM simulation_records sr1
                    WHERE date(CASE
                        WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                        ELSE created_at
                    END) >= date('now', '-{} days')
                    GROUP BY year, week_num

                    UNION

                    -- 添加只有通过记录但没有创建记录的周
                    SELECT
                        strftime('%Y', CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END) as year,
                        (strftime('%W', date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END, 'weekday 1', '-6 days')) + 1) as week_num,
                        0 as total_cases,
                        COUNT(*) as pass_cases,
                        0 as fail_cases,
                        0 as ongoing_cases,
                        NULL as avg_compile_time,
                        NULL as avg_simulation_time,
                        MIN(date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END)) as week_start_date,
                        MAX(date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END)) as week_end_date
                    FROM simulation_records
                    WHERE status = 'PASS'
                        AND end_time IS NOT NULL
                        AND date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END) >= date('now', '-{} days')
                        AND (strftime('%Y', CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END) || '-' || (strftime('%W', date(CASE
                            WHEN end_time LIKE '%T%' THEN replace(end_time, 'T', ' ')
                            ELSE end_time
                        END, 'weekday 1', '-6 days')) + 1)) NOT IN (
                            SELECT DISTINCT strftime('%Y', CASE
                                WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                                ELSE created_at
                            END) || '-' || (strftime('%W', date(CASE
                                WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                                ELSE created_at
                            END, 'weekday 1', '-6 days')) + 1)
                            FROM simulation_records
                            WHERE date(CASE
                                WHEN created_at LIKE '%T%' THEN replace(created_at, 'T', ' ')
                                ELSE created_at
                            END) >= date('now', '-{} days')
                        )
                    GROUP BY year, week_num

                    ORDER BY year DESC, week_num DESC
                '''.format(weeks * 7, weeks * 7, weeks * 7))

                rows = cursor.fetchall()

                # 转换为字典格式并添加周标签
                weekly_stats = []
                for row in rows:
                    year, week_num, total_cases, pass_cases, fail_cases, ongoing_cases, \
                    avg_compile_time, avg_simulation_time, week_start_date, week_end_date = row

                    # 计算周标签 (W1, W2, ...)
                    # 注意：现在使用ISO周数，已经是正确的周数，不需要再+1
                    week_label = f"W{int(week_num)}"

                    weekly_stats.append({
                        'week_label': week_label,
                        'year': int(year),
                        'week_num': int(week_num),
                        'total_cases': total_cases or 0,
                        'pass_cases': pass_cases or 0,
                        'fail_cases': fail_cases or 0,
                        'ongoing_cases': ongoing_cases or 0,
                        'avg_compile_time': avg_compile_time,
                        'avg_simulation_time': avg_simulation_time,
                        'week_start_date': week_start_date,
                        'week_end_date': week_end_date
                    })

                return weekly_stats

            except Exception as e:
                print(f"获取每周统计失败: {str(e)}")
                return []
            finally:
                conn.close()
    
    def _update_daily_statistics_async(self):
        """异步更新每日统计数据"""
        QTimer.singleShot(0, self._update_daily_statistics)

    def _update_daily_statistics(self):
        """更新每日统计数据"""
        try:
            today = date.today()

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 计算今日统计
            cursor.execute('''
                SELECT
                    COUNT(*) as total_cases,
                    SUM(CASE WHEN status = 'PASS' THEN 1 ELSE 0 END) as pass_cases,
                    SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) as fail_cases,
                    SUM(CASE WHEN status = 'On-Going' THEN 1 ELSE 0 END) as ongoing_cases,
                    SUM(COALESCE(compile_time, 0)) as total_compile_time,
                    SUM(COALESCE(simulation_time, 0)) as total_simulation_time,
                    AVG(COALESCE(compile_time, 0)) as avg_compile_time,
                    AVG(COALESCE(simulation_time, 0)) as avg_simulation_time
                FROM simulation_records
                WHERE date(created_at) = ?
            ''', (today,))

            stats = cursor.fetchone()

            if stats:
                # 插入或更新统计数据
                cursor.execute('''
                    INSERT OR REPLACE INTO daily_statistics
                    (date, total_cases, pass_cases, fail_cases, ongoing_cases,
                     total_compile_time, total_simulation_time,
                     avg_compile_time, avg_simulation_time, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (today, *stats, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

                conn.commit()

                # 发送统计更新信号
                stats_dict = {
                    'date': today,
                    'total_cases': stats[0],
                    'pass_cases': stats[1],
                    'fail_cases': stats[2],
                    'ongoing_cases': stats[3],
                    'total_compile_time': stats[4],
                    'total_simulation_time': stats[5],
                    'avg_compile_time': stats[6],
                    'avg_simulation_time': stats[7]
                }
                self.statistics_updated.emit(stats_dict)

            conn.close()

        except Exception as e:
            print(f"更新每日统计失败: {str(e)}")

    def get_total_records_count(self) -> int:
        """获取总记录数

        Returns:
            int: 总记录数
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM simulation_records')
            count = cursor.fetchone()[0]

            return count

        except Exception as e:
            print(f"获取总记录数失败: {e}")
            return 0
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def clear_all_historical_data(self) -> bool:
        """清除所有历史数据

        Returns:
            bool: 是否成功清除
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()

            # 开始事务
            cursor.execute('BEGIN TRANSACTION')

            # 清除仿真记录表
            cursor.execute('DELETE FROM simulation_records')
            records_deleted = cursor.rowcount

            # 清除每日统计表
            cursor.execute('DELETE FROM daily_statistics')
            stats_deleted = cursor.rowcount

            # 重置自增ID
            cursor.execute('DELETE FROM sqlite_sequence WHERE name="simulation_records"')
            cursor.execute('DELETE FROM sqlite_sequence WHERE name="daily_statistics"')

            # 提交事务
            cursor.execute('COMMIT')

            print(f"成功清除 {records_deleted} 条仿真记录和 {stats_deleted} 条统计记录")

            # 发送清除完成信号（如果在Qt环境中）
            try:
                # 发送一个特殊的记录更新信号表示数据已清除
                self.record_updated.emit({'action': 'clear_all', 'records_deleted': records_deleted})
            except:
                pass  # 在非Qt环境中忽略信号发送

            return True

        except Exception as e:
            print(f"清除历史数据失败: {e}")
            if conn:
                try:
                    cursor.execute('ROLLBACK')
                except:
                    pass
            return False
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def cleanup(self):
        """清理资源"""
        # 防止重复清理
        if hasattr(self, '_cleaned_up') and self._cleaned_up:
            return

        try:
            # 标记为已清理，防止重复调用
            self._cleaned_up = True

            # 先停止异步工作线程
            if hasattr(self, 'async_worker') and self.async_worker is not None:
                try:
                    if self.async_worker.isRunning():
                        self.async_worker.stop()
                        self.async_worker.wait(5000)  # 等待最多5秒
                except Exception as e:
                    print(f"停止异步工作线程失败: {e}")
                finally:
                    self.async_worker = None

            # 然后关闭连接池
            if hasattr(self, 'connection_pool') and self.connection_pool is not None:
                try:
                    self.connection_pool.close_all()
                except Exception as e:
                    print(f"关闭连接池失败: {e}")
                finally:
                    self.connection_pool = None

            print("数据看板模型清理完成")
        except Exception as e:
            print(f"数据看板模型清理失败: {str(e)}")

    def __del__(self):
        """析构函数"""
        try:
            # 只在未清理时才调用cleanup
            if not hasattr(self, '_cleaned_up') or not self._cleaned_up:
                self.cleanup()
        except:
            pass  # 忽略析构函数中的异常
    
    def _row_to_dict(self, cursor, row) -> Dict:
        """将数据库行转换为字典

        Args:
            cursor: 数据库游标
            row: 数据库行

        Returns:
            Dict: 字典格式的数据
        """
        columns = [description[0] for description in cursor.description]
        result = dict(zip(columns, row))

        # 处理可能的None值，特别是数值字段
        numeric_fields = ['pass_cases', 'fail_cases', 'total_cases', 'ongoing_cases',
                         'compile_time', 'simulation_time', 'avg_compile_time', 'avg_simulation_time',
                         'total_compile_time', 'total_simulation_time']

        for field in numeric_fields:
            if field in result and result[field] is None:
                result[field] = 0

        return result
