"""
Git差异比对工具栏组件

提供常用操作按钮和设置选项，包括：
- 刷新、导出、搜索功能
- 视图模式切换
- 设置选项
"""

from PyQt5.QtWidgets import (
    QToolBar, QAction, QComboBox, QLineEdit, QPushButton,
    QLabel, QWidget, QHBoxLayout, QSizePolicy, QMenu
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QKeySequence

from ..models.external_tools import external_tools_manager


class GitDiffToolbar(QToolBar):
    """Git差异比对工具栏"""
    
    # 信号定义
    refresh_requested = pyqtSignal()
    export_requested = pyqtSignal()
    search_requested = pyqtSignal(str)
    view_mode_changed = pyqtSignal(str)
    settings_requested = pyqtSignal()
    external_tool_requested = pyqtSignal(str)  # 外部工具请求信号

    # 差异导航信号
    previous_diff_requested = pyqtSignal()
    next_diff_requested = pyqtSignal()
    toggle_navigation_bar = pyqtSignal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.setMovable(False)
        
        self._init_actions()
        self._init_widgets()
        self._setup_toolbar()
        self._connect_signals()
    
    def _init_actions(self):
        """初始化动作"""
        # 刷新动作
        self.refresh_action = QAction("刷新", self)
        self.refresh_action.setToolTip("刷新文件列表和提交历史")
        self.refresh_action.setShortcut(QKeySequence.Refresh)
        
        # 导出动作
        self.export_action = QAction("导出", self)
        self.export_action.setToolTip("导出差异结果到文件")
        self.export_action.setShortcut(QKeySequence("Ctrl+E"))
        
        # 设置动作
        self.settings_action = QAction("设置", self)
        self.settings_action.setToolTip("打开设置对话框")

        # 外部工具动作
        self.external_tools_action = QAction("外部工具", self)
        self.external_tools_action.setToolTip("使用外部比较工具")

        # 差异导航动作
        self.prev_diff_action = QAction("上一个差异", self)
        self.prev_diff_action.setToolTip("上一个差异 (F7)")
        self.prev_diff_action.setShortcut("F7")

        self.next_diff_action = QAction("下一个差异", self)
        self.next_diff_action.setToolTip("下一个差异 (F8)")
        self.next_diff_action.setShortcut("F8")

        # 导航栏切换动作
        self.toggle_nav_action = QAction("导航栏", self)
        self.toggle_nav_action.setToolTip("显示/隐藏差异导航栏")
        self.toggle_nav_action.setCheckable(True)
        self.toggle_nav_action.setChecked(True)  # 默认选中（显示导航栏）
    
    def _init_widgets(self):
        """初始化控件"""
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索差异内容...")
        self.search_edit.setMaximumWidth(200)
        self.search_edit.setMinimumWidth(150)
        
        # 搜索按钮
        self.search_button = QPushButton("搜索")
        self.search_button.setMaximumWidth(60)
        
        # 视图模式选择
        self.view_mode_combo = QComboBox()
        self.view_mode_combo.addItems(["并排视图", "统一视图"])
        self.view_mode_combo.setCurrentText("并排视图")
        self.view_mode_combo.setToolTip("选择差异显示模式")
        self.view_mode_combo.setMaximumWidth(100)
        
        # 创建搜索容器
        search_widget = QWidget()
        search_layout = QHBoxLayout(search_widget)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(5)
        
        search_layout.addWidget(QLabel("搜索:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(self.search_button)
        
        self.search_widget = search_widget
        
        # 创建视图模式容器
        view_widget = QWidget()
        view_layout = QHBoxLayout(view_widget)
        view_layout.setContentsMargins(0, 0, 0, 0)
        view_layout.setSpacing(5)
        
        view_layout.addWidget(QLabel("视图:"))
        view_layout.addWidget(self.view_mode_combo)
        
        self.view_widget = view_widget

        # 创建外部工具按钮和菜单
        self.external_tools_button = QPushButton("外部工具")
        self.external_tools_button.setToolTip("使用外部比较工具")
        self.external_tools_button.setEnabled(False)  # 默认禁用

        # 创建外部工具菜单
        self.external_tools_menu = QMenu()
        self.external_tools_button.setMenu(self.external_tools_menu)

        # 更新外部工具菜单
        self._update_external_tools_menu()
    
    def _setup_toolbar(self):
        """设置工具栏布局"""
        # 添加基本动作
        self.addAction(self.refresh_action)
        self.addAction(self.export_action)
        
        # 添加分隔符
        self.addSeparator()
        
        # 添加搜索控件
        self.addWidget(self.search_widget)
        
        # 添加分隔符
        self.addSeparator()
        
        # 添加视图模式选择
        self.addWidget(self.view_widget)

        # 添加分隔符
        self.addSeparator()

        # 添加差异导航按钮
        self.addAction(self.prev_diff_action)
        self.addAction(self.next_diff_action)
        self.addAction(self.toggle_nav_action)

        # 添加分隔符
        self.addSeparator()

        # 添加外部工具按钮
        self.addWidget(self.external_tools_button)

        # 添加弹性空间
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.addWidget(spacer)

        # 添加设置动作
        self.addAction(self.settings_action)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.refresh_action.triggered.connect(self.refresh_requested.emit)
        self.export_action.triggered.connect(self.export_requested.emit)
        self.settings_action.triggered.connect(self.settings_requested.emit)
        
        self.search_button.clicked.connect(self._on_search_clicked)
        self.search_edit.returnPressed.connect(self._on_search_clicked)
        
        self.view_mode_combo.currentTextChanged.connect(self._on_view_mode_changed)

        # 差异导航信号
        self.prev_diff_action.triggered.connect(self.previous_diff_requested.emit)
        self.next_diff_action.triggered.connect(self.next_diff_requested.emit)
        self.toggle_nav_action.toggled.connect(self.toggle_navigation_bar.emit)
    
    def _on_search_clicked(self):
        """搜索按钮点击事件处理"""
        search_text = self.search_edit.text().strip()
        self.search_requested.emit(search_text)
    
    def _on_view_mode_changed(self, mode_text):
        """视图模式变更事件处理"""
        # 将中文模式名转换为英文标识
        mode_map = {
            "并排视图": "side-by-side",
            "统一视图": "unified"
        }
        mode = mode_map.get(mode_text, "side-by-side")
        self.view_mode_changed.emit(mode)
    
    def set_search_text(self, text: str):
        """设置搜索文本"""
        self.search_edit.setText(text)
    
    def get_search_text(self) -> str:
        """获取搜索文本"""
        return self.search_edit.text().strip()
    
    def set_view_mode(self, mode: str):
        """设置视图模式"""
        mode_map = {
            "side-by-side": "并排视图",
            "unified": "统一视图"
        }
        mode_text = mode_map.get(mode, "并排视图")
        self.view_mode_combo.setCurrentText(mode_text)
    
    def get_view_mode(self) -> str:
        """获取当前视图模式"""
        mode_text = self.view_mode_combo.currentText()
        mode_map = {
            "并排视图": "side-by-side",
            "统一视图": "unified"
        }
        return mode_map.get(mode_text, "side-by-side")
    
    def enable_export(self, enabled: bool):
        """启用/禁用导出功能"""
        self.export_action.setEnabled(enabled)
    
    def enable_search(self, enabled: bool):
        """启用/禁用搜索功能"""
        self.search_edit.setEnabled(enabled)
        self.search_button.setEnabled(enabled)
    
    def clear_search(self):
        """清空搜索框"""
        self.search_edit.clear()

    def _update_external_tools_menu(self):
        """更新外部工具菜单"""
        self.external_tools_menu.clear()

        # 获取可用的外部工具
        available_tools = external_tools_manager.get_available_tools()

        if not available_tools:
            # 没有可用工具时显示提示
            no_tools_action = self.external_tools_menu.addAction("未检测到外部工具")
            no_tools_action.setEnabled(False)
            return

        # 添加可用工具到菜单
        for tool in available_tools:
            action = self.external_tools_menu.addAction(tool.display_name)
            action.setData(tool.name)  # 存储工具名称
            action.triggered.connect(lambda checked, name=tool.name: self._on_external_tool_selected(name))

    def _on_external_tool_selected(self, tool_name: str):
        """外部工具选择事件处理"""
        self.external_tool_requested.emit(tool_name)

    def enable_external_tools(self, enabled: bool):
        """启用/禁用外部工具按钮"""
        self.external_tools_button.setEnabled(enabled)

    def refresh_external_tools(self):
        """刷新外部工具列表"""
        external_tools_manager._detect_available_tools()
        self._update_external_tools_menu()
