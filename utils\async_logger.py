from threading import Thread
from queue import Queue
from PyQt5.QtCore import Qt, QMetaObject, Q_ARG
import time

class AsyncLogger:
    """异步日志记录器"""
    def __init__(self):
        self.queue = Queue()
        self.running = True
        self.thread = Thread(target=self._process_queue, daemon=True)
        self.thread.start()

    def write(self, text, widget, auto_scroll=True):
        """写入日志文本到队列"""
        self.queue.put((text, widget, auto_scroll))

    def _process_queue(self):
        """处理队列中的日志"""
        while self.running:
            try:
                if not self.queue.empty():
                    text, widget, auto_scroll = self.queue.get()
                    
                    # 使用 moveToThread 确保在主线程中更新 UI
                    QMetaObject.invokeMethod(
                        widget,
                        "append",
                        Qt.ConnectionType.QueuedConnection,
                        Q_ARG(str, text)
                    )
                    
                    # 如果需要自动滚动，滚动到底部
                    if auto_scroll:
                        scrollbar = widget.verticalScrollBar()
                        QMetaObject.invokeMethod(
                            scrollbar,
                            "setValue",
                            Qt.ConnectionType.QueuedConnection,
                            Q_ARG(int, scrollbar.maximum())
                        )
                    
                time.sleep(0.01)  # 避免过度占用 CPU
            except Exception as e:
                print(f"处理日志队列时出错: {str(e)}")
                continue

    def stop(self):
        """停止日志处理线程"""
        self.running = False
        if self.thread.is_alive():
            self.thread.join()
        self.queue = Queue()  # 清空队列
