from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QLabel, QProgressBar,
                           QLineEdit, QMessageBox, QHeaderView, QComboBox, QTreeWidget,
                           QTreeWidgetItem, QSplitter, QFrame, QGroupBox, QTabWidget,
                           QFileDialog, QApplication, QToolButton)
from PyQt5.QtCore import Qt, QProcess, QSize, pyqtSignal
from PyQt5.QtGui import QColor, QFont, QIcon
import os
import re
import glob
import shutil
import time
from datetime import datetime
from plugins.base import PluginBase
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from utils.time_unit_converter import TimeUnitConverter, TimeUnit



class RegressionResultAnalyzerPlugin(PluginBase):
    """回归结果解析插件"""

    # 定义信号
    task_progress = pyqtSignal(str)
    task_completed = pyqtSignal()

    @property
    def name(self):
        return "回归结果解析器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "解析回归结果文件，展示PASS和FAIL用例"

    def __init__(self):
        super().__init__()
        # 设置为管理员控制
        self.admin_controlled = False
        # 设置默认为禁用状态
        self.default_enabled = True
        # 对话框引用
        self.current_dialog = None

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_analyzer)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            # 创建状态指示器标签
            self.status_label = QLabel("回归结果解析器运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.current_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.current_dialog.isVisible():
                self.current_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.current_dialog.isMinimized():
                self.current_dialog.showNormal()
            # 将对话框置于前台
            self.current_dialog.raise_()
            self.current_dialog.activateWindow()

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("回归结果解析器正在后台运行", 5000)

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 隐藏状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(False)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(False)

        # 清理对话框引用
        self.current_dialog = None

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        # 关闭对话框
        if hasattr(self, 'current_dialog') and self.current_dialog:
            try:
                self.current_dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

    def show_analyzer(self):
        """显示回归结果解析器"""
        dialog = RegressionResultDialog(self.main_window)

        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 保存对话框引用
        self.current_dialog = dialog

        # 连接对话框关闭事件
        dialog.finished.connect(self.on_dialog_closed)

        # 显示状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(True)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(True)

        # 非模态显示对话框
        dialog.show()

class RegressionResultDialog(QDialog):
    """回归结果解析对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("回归结果解析器")
        self.resize(1200, 800)
        self.regression_data = {}  # 存储回归数据
        self.current_timestamp = None  # 当前选中的时间戳
        self.current_group = None  # 当前选中的用例组
        self.parent_plugin = None  # 父插件引用，用于后台运行

        # 解析环境变量
        self.proj_dir = self.get_proj_dir()

        # 添加缓存机制
        self.log_time_cache = {}  # 缓存日志文件的时间信息 {log_path: (compile_time, sim_time)}
        self.log_mtime_cache = {}  # 缓存日志文件的修改时间 {log_path: mtime}
        self.max_cache_size = 1000  # 最大缓存条目数

        self.init_ui()
        self.scan_regression_files()

    def run_in_background(self):
        """将窗口隐藏到后台运行"""
        # 隐藏窗口但不关闭
        self.hide()

        # 如果有父插件，通知它窗口已隐藏
        if isinstance(self.parent(), QWidget) and hasattr(self.parent(), 'run_in_background'):
            self.parent().run_in_background(self)

    def closeEvent(self, event):
        """
        关闭事件处理

        Args:
            event (QCloseEvent): 关闭事件
        """
        # 发出finished信号，通知插件对话框已关闭
        self.finished.emit(0)
        event.accept()

    def get_proj_dir(self):
        """获取PROJ_DIR环境变量"""
        proj_dir = os.environ.get("PROJ_DIR", "")
        if not proj_dir:
            # 如果环境变量不存在，使用当前目录
            proj_dir = os.getcwd()
        return proj_dir

    def get_cached_time(self, log_path, is_sim_log=False):
        """从缓存中获取时间信息，如果缓存中没有则返回None

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志
        """
        if not log_path or not os.path.exists(log_path):
            return None

        cache_key = f"{log_path}_{is_sim_log}"
        current_mtime = os.path.getmtime(log_path)

        # 检查缓存
        if (cache_key in self.log_time_cache and
            cache_key in self.log_mtime_cache and
            self.log_mtime_cache[cache_key] == current_mtime):

            return self.log_time_cache[cache_key]

        return None

    def update_time_cache(self, log_path, is_sim_log, time_value):
        """更新时间缓存

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志
            time_value: 时间值
        """
        if not log_path or not os.path.exists(log_path):
            return

        cache_key = f"{log_path}_{is_sim_log}"
        current_mtime = os.path.getmtime(log_path)

        self.log_time_cache[cache_key] = time_value
        self.log_mtime_cache[cache_key] = current_mtime

        # 如果缓存过大，删除最早的条目
        if len(self.log_time_cache) > self.max_cache_size:
            # 删除最早的10%条目
            keys_to_remove = list(self.log_time_cache.keys())[:int(self.max_cache_size * 0.1)]
            for key in keys_to_remove:
                del self.log_time_cache[key]
                if key in self.log_mtime_cache:
                    del self.log_mtime_cache[key]

    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)

        # 顶部区域 - 回归目录选择
        top_layout = QHBoxLayout()
        dir_label = QLabel("回归目录:")

        # 设置默认回归目录路径
        default_regression_dir = os.path.join(self.proj_dir, "work/regression")
        self.dir_input = QLineEdit(default_regression_dir)
        self.dir_input.setPlaceholderText("输入回归结果目录路径...")

        # 添加浏览按钮
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_regression_dir)

        # 添加扫描按钮
        scan_btn = QPushButton("扫描")
        scan_btn.clicked.connect(self.scan_regression_files)

        # 添加解析时间按钮
        self.parse_time_btn = QPushButton("解析时间")
        self.parse_time_btn.setToolTip("解析编译时间和仿真时间（可能会导致界面短暂卡顿）")
        self.parse_time_btn.clicked.connect(self.parse_all_times)
        self.parse_time_btn.setEnabled(False)  # 初始禁用，直到扫描完成

        # 添加时间单位选择控件
        time_unit_label = QLabel("时间单位:")
        time_unit_label.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.time_unit_combo = QComboBox()
        for unit in TimeUnitConverter.get_all_units():
            display_name = TimeUnitConverter.get_unit_display_name(unit)
            self.time_unit_combo.addItem(display_name, unit)

        # 默认选择分钟
        self.time_unit_combo.setCurrentIndex(0)
        self.time_unit_combo.currentIndexChanged.connect(self.on_time_unit_changed)

        top_layout.addWidget(dir_label)
        top_layout.addWidget(self.dir_input, stretch=1)
        top_layout.addWidget(browse_btn)
        top_layout.addWidget(scan_btn)
        top_layout.addWidget(self.parse_time_btn)
        top_layout.addWidget(time_unit_label)
        top_layout.addWidget(self.time_unit_combo)

        # 中间区域 - 分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧 - 时间戳和用例组树
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)

        # 时间戳树
        timestamp_group = QGroupBox("回归时间戳")
        timestamp_layout = QVBoxLayout()

        # 添加"显示全部"按钮
        show_all_btn = QPushButton("显示全部时间戳总览")
        show_all_btn.setToolTip("点击显示所有时间戳的用例总览")
        show_all_btn.clicked.connect(self.show_all_timestamps_overview)
        timestamp_layout.addWidget(show_all_btn)

        self.timestamp_tree = QTreeWidget()
        self.timestamp_tree.setHeaderLabels(["时间戳", "PASS", "FAIL"])
        self.timestamp_tree.setColumnWidth(0, 200)
        self.timestamp_tree.setColumnWidth(1, 80)
        self.timestamp_tree.setColumnWidth(2, 80)
        self.timestamp_tree.itemClicked.connect(self.on_timestamp_clicked)
        self.timestamp_tree.itemDoubleClicked.connect(self.on_timestamp_double_clicked)
        timestamp_layout.addWidget(self.timestamp_tree)
        timestamp_group.setLayout(timestamp_layout)

        # 用例组树
        group_group = QGroupBox("用例组")
        group_layout = QVBoxLayout()
        self.group_tree = QTreeWidget()
        self.group_tree.setHeaderLabels(["用例组", "PASS", "FAIL"])
        self.group_tree.setColumnWidth(0, 200)
        self.group_tree.setColumnWidth(1, 80)
        self.group_tree.setColumnWidth(2, 80)
        self.group_tree.itemClicked.connect(self.on_group_clicked)
        group_layout.addWidget(self.group_tree)
        group_group.setLayout(group_layout)

        left_layout.addWidget(timestamp_group, stretch=1)
        left_layout.addWidget(group_group, stretch=1)

        # 右侧 - 用例表格
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)

        # 表格标签页
        self.tab_widget = QTabWidget()

        # 总览表
        self.overview_table = QTableWidget()
        self.setup_overview_table(self.overview_table)

        # PASS表格
        self.pass_table = QTableWidget()
        self.setup_table(self.pass_table)

        # FAIL表格
        self.fail_table = QTableWidget()
        self.setup_table(self.fail_table)

        self.tab_widget.addTab(self.overview_table, "总览表")
        self.tab_widget.addTab(self.pass_table, "PASS用例")
        self.tab_widget.addTab(self.fail_table, "FAIL用例")

        # 搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键字搜索...")
        self.search_input.textChanged.connect(self.apply_filter)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, stretch=1)

        right_layout.addLayout(search_layout)
        right_layout.addWidget(self.tab_widget, stretch=1)

        # 添加到分割器
        splitter.addWidget(left_frame)
        splitter.addWidget(right_frame)
        splitter.setSizes([300, 900])  # 设置初始分割比例

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 底部按钮区域
        button_layout = QHBoxLayout()

        # 添加导出Excel按钮
        export_button = QPushButton("导出Excel")
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        export_button.clicked.connect(self.export_to_excel)
        button_layout.addWidget(export_button)

        # 添加最小化按钮
        minimize_button = QPushButton("最小化窗口")
        minimize_button.clicked.connect(self.showMinimized)
        button_layout.addWidget(minimize_button)

        # 添加后台运行按钮
        background_button = QPushButton("后台运行")
        background_button.clicked.connect(self.run_in_background)
        button_layout.addWidget(background_button)

        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        # 添加到主布局
        main_layout.addLayout(top_layout)
        main_layout.addWidget(splitter, stretch=1)
        main_layout.addWidget(self.progress_bar)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def on_time_unit_changed(self):
        """时间单位改变时的处理"""
        try:
            # 更新表头
            self.update_table_headers()

            # 重新显示当前数据
            if self.current_timestamp:
                self.update_overview_table(self.current_timestamp)
                self.update_detail_tables(self.current_timestamp, self.current_group)
            else:
                self.update_overview_table()

        except Exception as e:
            print(f"时间单位切换失败: {str(e)}")

    def update_table_headers(self):
        """更新表格表头（根据当前时间单位）"""
        try:
            current_unit = self.time_unit_combo.currentData()
            if current_unit is None:
                current_unit = TimeUnit.MINUTES

            # 更新总览表表头
            overview_headers = [
                "用例名", "最终状态", "执行次数",
                TimeUnitConverter.get_table_header("平均仿真时间", current_unit),
                TimeUnitConverter.get_table_header("最新编译时间", current_unit),
                "所有种子", "最新日志", "最新命令"
            ]
            self.overview_table.setHorizontalHeaderLabels(overview_headers)

            # 更新详细表表头
            detail_headers = [
                "用例名", "仿真状态", "种子",
                TimeUnitConverter.get_table_header("编译时间", current_unit),
                TimeUnitConverter.get_table_header("仿真时间", current_unit),
                "结果日志", "仿真命令"
            ]
            self.pass_table.setHorizontalHeaderLabels(detail_headers)
            self.fail_table.setHorizontalHeaderLabels(detail_headers)

        except Exception as e:
            print(f"更新表头失败: {str(e)}")

    def setup_overview_table(self, table):
        """设置总览表属性"""
        table.setColumnCount(8)
        # 初始表头（使用分钟单位）
        table.setHorizontalHeaderLabels([
            "用例名", "最终状态", "执行次数", "平均仿真时间(分钟)",
            "最新编译时间(分钟)", "所有种子", "最新日志", "最新命令"
        ])

        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #cccccc;
                border-radius: 3px;
                selection-background-color: #e8f0fe;
                selection-color: #000000;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f0f0f0;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 5px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        # 设置表头和列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # 用例名
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # 最终状态
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # 执行次数
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # 平均仿真时间
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # 最新编译时间
        header.setSectionResizeMode(5, QHeaderView.Interactive)  # 所有种子
        header.setSectionResizeMode(6, QHeaderView.Interactive)  # 最新日志
        header.setSectionResizeMode(7, QHeaderView.Stretch)      # 最新命令

        # 设置初始列宽
        table.setColumnWidth(0, 180)  # 用例名
        table.setColumnWidth(1, 80)   # 最终状态
        table.setColumnWidth(2, 80)   # 执行次数
        table.setColumnWidth(3, 120)  # 平均仿真时间
        table.setColumnWidth(4, 120)  # 最新编译时间
        table.setColumnWidth(5, 150)  # 所有种子
        table.setColumnWidth(6, 250)  # 最新日志

        # 设置表格属性
        table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        table.setSelectionMode(QTableWidget.SingleSelection)  # 单行选择
        table.setAlternatingRowColors(True)  # 交替行颜色
        table.setSortingEnabled(True)  # 允许排序
        table.cellDoubleClicked.connect(self.on_cell_clicked)  # 双击事件

    def setup_table(self, table):
        """设置表格属性"""
        table.setColumnCount(7)
        # 初始表头（使用分钟单位）
        table.setHorizontalHeaderLabels(["用例名", "仿真状态", "种子", "编译时间(分钟)", "仿真时间(分钟)", "结果日志", "仿真命令"])

        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #cccccc;
                border-radius: 3px;
                selection-background-color: #e8f0fe;
                selection-color: #000000;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f0f0f0;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 5px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        # 设置表头和列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # 用例名
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # 仿真状态
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # 种子
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # 编译时间
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # 仿真时间
        header.setSectionResizeMode(5, QHeaderView.Interactive)  # 结果日志
        header.setSectionResizeMode(6, QHeaderView.Stretch)      # 仿真命令

        # 设置初始列宽
        table.setColumnWidth(0, 180)  # 用例名
        table.setColumnWidth(1, 80)   # 仿真状态
        table.setColumnWidth(2, 80)   # 种子
        table.setColumnWidth(3, 100)  # 编译时间
        table.setColumnWidth(4, 100)  # 仿真时间
        table.setColumnWidth(5, 250)  # 结果日志

        # 设置表格属性
        table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        table.setSelectionMode(QTableWidget.SingleSelection)  # 单行选择
        table.setAlternatingRowColors(True)  # 交替行颜色
        table.setSortingEnabled(True)  # 允许排序
        table.cellDoubleClicked.connect(self.on_cell_clicked)  # 双击事件

    def browse_regression_dir(self):
        """浏览选择回归目录"""
        # 获取当前目录作为起始目录
        current_dir = self.dir_input.text().strip()
        if not current_dir or not os.path.exists(current_dir):
            current_dir = self.proj_dir

        # 打开目录选择对话框
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择回归结果目录",
            current_dir,
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if dir_path:
            self.dir_input.setText(dir_path)
            # 自动扫描新选择的目录
            self.scan_regression_files()

    def scan_regression_files(self):
        """扫描回归结果文件"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            QApplication.processEvents()  # 更新UI

            # 获取回归目录
            regression_dir = self.dir_input.text().strip()
            if not regression_dir:
                # 使用默认路径
                regression_dir = os.path.join(self.proj_dir, "work/regression")
                self.dir_input.setText(regression_dir)

            # 确保目录存在
            if not os.path.exists(regression_dir):
                reply = QMessageBox.question(
                    self,
                    "目录不存在",
                    f"回归目录 {regression_dir} 不存在，是否创建该目录？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    try:
                        os.makedirs(regression_dir, exist_ok=True)
                        QMessageBox.information(self, "成功", f"已创建目录: {regression_dir}")
                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"创建目录失败: {str(e)}")
                        self.progress_bar.setVisible(False)
                        return
                else:
                    self.progress_bar.setVisible(False)
                    return

            # 清空现有数据
            self.regression_data = {}
            self.timestamp_tree.clear()
            self.group_tree.clear()
            self.pass_table.setRowCount(0)
            self.fail_table.setRowCount(0)

            # 查找所有回归结果文件
            pass_files = glob.glob(os.path.join(regression_dir, "regr_pass_list_regr_*.lst"))
            fail_files = glob.glob(os.path.join(regression_dir, "regr_fail_list_regr_*.lst"))

            total_files = len(pass_files) + len(fail_files)
            if total_files == 0:
                QMessageBox.warning(self, "警告", f"在 {regression_dir} 中未找到回归结果文件")
                self.progress_bar.setVisible(False)
                return

            # 按时间戳排序文件（从新到旧）
            pass_files.sort(key=os.path.getmtime, reverse=True)
            fail_files.sort(key=os.path.getmtime, reverse=True)

            # 创建文件映射 {timestamp: (pass_file, fail_file)}
            file_map = {}

            # 处理PASS文件
            for pass_file in pass_files:
                timestamp = self.extract_timestamp(pass_file)
                if timestamp not in file_map:
                    file_map[timestamp] = [None, None]
                file_map[timestamp][0] = pass_file

            # 处理FAIL文件
            for fail_file in fail_files:
                timestamp = self.extract_timestamp(fail_file)
                if timestamp not in file_map:
                    file_map[timestamp] = [None, None]
                file_map[timestamp][1] = fail_file

            # 按时间戳排序（从新到旧）
            sorted_timestamps = sorted(file_map.keys(), reverse=True)

            # 解析所有文件
            processed_files = 0

            for timestamp in sorted_timestamps:
                pass_file, fail_file = file_map[timestamp]

                # 初始化数据结构
                if timestamp not in self.regression_data:
                    self.regression_data[timestamp] = {"pass": {}, "fail": {}}

                # 处理PASS文件
                if pass_file:
                    self.parse_regression_file(pass_file, timestamp, "pass")
                    processed_files += 1
                    progress = int(processed_files * 100 / total_files)
                    self.progress_bar.setValue(progress)
                    QApplication.processEvents()  # 更新UI

                # 处理FAIL文件
                if fail_file:
                    self.parse_regression_file(fail_file, timestamp, "fail")
                    processed_files += 1
                    progress = int(processed_files * 100 / total_files)
                    self.progress_bar.setValue(progress)
                    QApplication.processEvents()  # 更新UI

            # 更新时间戳树
            self.update_timestamp_tree()

            # 更新总览表（根据当前选中的时间戳）
            if self.current_timestamp:
                self.update_overview_table(self.current_timestamp)
            else:
                self.update_overview_table()

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 启用解析时间按钮
            self.parse_time_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"扫描回归文件失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.parse_time_btn.setEnabled(False)

    def extract_timestamp(self, file_path):
        """从文件名中提取时间戳"""
        match = re.search(r'regr_(?:pass|fail)_list_regr_(\d{8}_\d{6})\.lst', os.path.basename(file_path))
        if match:
            return match.group(1)
        return None

    def parse_regression_file(self, file_path, timestamp, result_type):
        """解析回归结果文件（不包括时间信息）"""
        try:
            current_group = None
            current_log = None

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()

                    # 跳过空行和注释行
                    if not line or line.startswith('//') and not line.startswith('//group:') and not line.startswith('//log:'):
                        continue

                    # 解析用例组
                    if line.startswith('//group:'):
                        current_group = line.replace('//group:', '').strip()
                        if current_group not in self.regression_data[timestamp][result_type]:
                            self.regression_data[timestamp][result_type][current_group] = []

                    # 解析日志路径
                    elif line.startswith('//log:'):
                        current_log = line.replace('//log:', '').strip()

                    # 解析用例行
                    elif current_group and ',' in line:
                        parts = [p.strip() for p in line.split(',')]
                        if len(parts) >= 6:
                            # 获取仿真状态
                            tag = parts[5].strip('[]') if len(parts) > 5 else ""

                            # 创建用例信息（不包括时间信息）
                            case_info = {
                                'status': parts[0],  # ON/OFF
                                'block': parts[1],   # block选项
                                'case': parts[2],    # case选项
                                'seed': parts[3],    # seed选项
                                'iterative': parts[4],  # 回归次数
                                'tag': parts[5],     # 仿真状态
                                'log': current_log,  # 日志路径
                                'compile_time': None,  # 编译时间（初始为None）
                                'sim_time': None,  # 仿真时间（初始为None）
                                'command': self.generate_command(parts)  # 生成仿真命令
                            }

                            # 添加到数据结构
                            self.regression_data[timestamp][result_type][current_group].append(case_info)

        except Exception as e:
            print(f"解析文件 {file_path} 失败: {str(e)}")

    def parse_time_from_log(self, log_path, is_sim_log=False):
        """从日志文件中解析时间信息

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志
        """
        try:
            if not log_path or not os.path.exists(log_path):
                return None

            # 只读取文件的最后60行以提高解析速度
            last_lines = []

            # 使用更高效的方式读取最后60行
            with open(log_path, 'rb') as f:
                # 移动到文件末尾
                f.seek(0, os.SEEK_END)
                file_size = f.tell()

                # 如果文件太小，直接读取整个文件
                if file_size < 10000:  # 10KB
                    f.seek(0)
                    content = f.read().decode('utf-8', errors='ignore')
                    last_lines = content.splitlines()[-60:]
                else:
                    # 从文件末尾开始读取
                    block_size = 1024  # 1KB
                    blocks = []
                    lines_count = 0

                    # 从文件末尾开始，每次读取一个块，直到读取了足够的行
                    while lines_count < 60 and f.tell() > 0:
                        # 移动到前一个块
                        pos = max(f.tell() - block_size, 0)
                        f.seek(pos)
                        block = f.read(min(block_size, f.tell())).decode('utf-8', errors='ignore')
                        lines = block.splitlines()

                        # 如果不是第一个块，并且最后一行不完整，则忽略它
                        if blocks and not block.endswith('\n'):
                            lines = lines[:-1]

                        lines_count += len(lines)
                        blocks.insert(0, block)

                        # 如果已经到达文件开头，则退出循环
                        if f.tell() == 0:
                            break

                    # 合并所有块并获取最后60行
                    content = ''.join(blocks)
                    last_lines = content.splitlines()[-60:]

            if not last_lines:
                return None

            content = '\n'.join(last_lines)

            # 尝试匹配xrun格式
            xrun_pattern = r'xrun: Time - (\d+\.?\d*)s'
            if match := re.search(xrun_pattern, content):
                seconds = float(match.group(1))
                return round(seconds / 60, 2)

            # 尝试匹配VCS格式
            if "Compilation Performance Summary" in content:
                if is_sim_log:
                    # 对于仿真日志，先定位到仿真部分
                    if "SimuLation Performance Summary" in content:
                        # 分割内容，只保留仿真部分
                        sim_part = content.split("SimuLation Performance Summary")[-1]
                        # 在仿真部分中查找Elapsed Time
                        sim_pattern = r'Elapsed Time\s+:\s+(\d+)\s+sec'
                        if sim_match := re.search(sim_pattern, sim_part):
                            sim_time = float(sim_match.group(1))
                            return round(sim_time / 60, 2)
                else:
                    # 编译日志，查找第一个Elapsed time
                    compile_pattern = r'Elapsed time\s+:\s+(\d+)\s+sec'
                    if match := re.search(compile_pattern, content):
                        compile_time = float(match.group(1))
                        return round(compile_time / 60, 2)

        except Exception as e:
            print(f"解析日志 {log_path} 时间失败: {str(e)}")
        return None

    def generate_command(self, parts):
        """根据用例信息生成仿真命令"""
        try:
            # 确保有足够的部分
            if len(parts) < 10:
                return "无法生成命令：数据不完整"

            # 提取各个部分
            status = parts[0].strip()  # ON/OFF
            block = parts[1].strip()   # block选项
            case = parts[2].strip()    # case选项
            seed = parts[3].strip()    # seed选项，格式为[123456]
            tag = parts[5].strip()     # 仿真状态，格式为[PASS]
            priority = parts[6].strip() if len(parts) > 6 else ""  # 优先级
            config = parts[7].strip() if len(parts) > 7 else ""    # config文件
            cfg_def = parts[8].strip() if len(parts) > 8 else ""   # cfg_def选项
            base = parts[9].strip() if len(parts) > 9 else ""      # base选项
            plusarg = parts[10].strip() if len(parts) > 10 else "" # simarg选项

            # 构建命令
            cmd = ["runsim"]

            # 添加block选项
            if block:
                cmd.append(f"-block {block}")

            # 添加case选项
            if case:
                cmd.append(f"-case {case}")

            # 添加seed选项，去掉中括号
            if seed and '[' in seed and ']' in seed:
                seed_value = seed.strip('[]')
                cmd.append(f"-seed {seed_value}")

            # 添加config选项
            if config and config.lower() != "default":
                cmd.append(f"-config {config}")

            # 添加cfg_def选项，去掉中括号
            if cfg_def and '[' in cfg_def and ']' in cfg_def:
                cfg_def_value = cfg_def.strip('[]')
                if cfg_def_value.lower() != "default":
                    cmd.append(f"-cfg_def {cfg_def_value}")

            # 添加base选项
            if base:
                cmd.append(f"-base {base}")

            # 添加simarg选项，去掉括号
            if plusarg and '(' in plusarg and ')' in plusarg:
                plusarg_value = plusarg.strip('()')
                # 处理多个simarg参数
                if plusarg_value:
                    for arg in plusarg_value.split(','):
                        if '=' in arg:
                            key, value = arg.split('=', 1)
                            # 处理值中的中括号
                            if '[' in value and ']' in value:
                                value = value.strip('[]')
                            cmd.append(f"-simarg +{key}={value}")
                        else:
                            cmd.append(f"-simarg +{arg}")

            return " ".join(cmd)

        except Exception as e:
            print(f"生成命令失败: {str(e)}")
            return "命令生成失败"

    def parse_all_times(self):
        """解析所有用例的编译时间和仿真时间"""
        try:
            # 确认是否解析时间
            reply = QMessageBox.question(
                self,
                "确认解析时间",
                "解析编译时间和仿真时间可能需要较长时间，并可能导致界面短暂卡顿。确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            QApplication.processEvents()  # 更新UI

            # 禁用解析时间按钮
            self.parse_time_btn.setEnabled(False)

            # 创建日志路径到时间的映射，用于避免重复解析相同的日志
            log_time_map = {}  # {log_path: (compile_time, sim_time)}

            # 收集所有需要解析时间的用例
            cases_to_process = []

            for timestamp, data in self.regression_data.items():
                for result_type in ["pass", "fail"]:
                    for group, cases in data[result_type].items():
                        for i, case_info in enumerate(cases):
                            tag = case_info["tag"].strip('[]')
                            log_path = case_info["log"]

                            # 只有PASS、RSF或RSP状态才解析时间
                            if tag in ["PASS", "RSF", "RSP"] and log_path and os.path.exists(log_path):
                                cases_to_process.append((timestamp, result_type, group, i, log_path))

            # 计算总数
            total_cases = len(cases_to_process)
            if total_cases == 0:
                self.progress_bar.setVisible(False)
                self.parse_time_btn.setEnabled(True)
                QMessageBox.information(self, "信息", "没有找到需要解析时间的用例")
                return

            # 解析所有用例的时间
            for i, (timestamp, result_type, group, case_index, log_path) in enumerate(cases_to_process):
                # 更新进度条
                progress = int((i + 1) * 100 / total_cases)
                self.progress_bar.setValue(progress)

                # 每处理10个用例更新一次UI
                if (i + 1) % 10 == 0:
                    QApplication.processEvents()

                # 解析仿真时间
                if log_path not in log_time_map:
                    # 先检查缓存
                    sim_time = self.get_cached_time(log_path, True)
                    if sim_time is None:
                        # 解析仿真时间
                        sim_time = self.parse_time_from_log(log_path, True)
                        # 更新缓存
                        if sim_time is not None:
                            self.update_time_cache(log_path, True, sim_time)

                    # 存储结果以避免重复解析
                    log_time_map[log_path] = (None, sim_time)

                # 更新用例的仿真时间
                self.regression_data[timestamp][result_type][group][case_index]['sim_time'] = log_time_map[log_path][1]

                # 解析编译时间
                log_dir = os.path.dirname(log_path)
                compile_log = os.path.join(log_dir, "irun_compile.log")

                if os.path.exists(compile_log) and compile_log not in log_time_map:
                    # 先检查缓存
                    compile_time = self.get_cached_time(compile_log, False)
                    if compile_time is None:
                        # 解析编译时间
                        compile_time = self.parse_time_from_log(compile_log, False)
                        # 更新缓存
                        if compile_time is not None:
                            self.update_time_cache(compile_log, False, compile_time)

                    # 存储结果以避免重复解析
                    log_time_map[compile_log] = (compile_time, None)

                # 更新用例的编译时间
                if os.path.exists(compile_log):
                    self.regression_data[timestamp][result_type][group][case_index]['compile_time'] = log_time_map[compile_log][0]

            # 更新当前显示的表格
            if self.current_timestamp and self.current_group:
                self.update_tables(self.current_timestamp, self.current_group)

            # 更新总览表（根据当前选中的时间戳）
            if self.current_timestamp:
                self.update_overview_table(self.current_timestamp)
            else:
                self.update_overview_table()

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 启用解析时间按钮
            self.parse_time_btn.setEnabled(True)

            QMessageBox.information(self, "完成", f"已成功解析 {total_cases} 个用例的时间信息")

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.parse_time_btn.setEnabled(True)
            QMessageBox.critical(self, "错误", f"解析时间失败: {str(e)}")

    def update_timestamp_tree(self):
        """更新时间戳树"""
        self.timestamp_tree.clear()

        # 按时间戳排序（从新到旧）
        sorted_timestamps = sorted(self.regression_data.keys(), reverse=True)

        for timestamp in sorted_timestamps:
            data = self.regression_data[timestamp]

            # 计算PASS和FAIL用例总数
            pass_count = sum(len(cases) for cases in data["pass"].values())
            fail_count = sum(len(cases) for cases in data["fail"].values())

            # 创建时间戳项
            item = QTreeWidgetItem([
                timestamp,
                str(pass_count),
                str(fail_count)
            ])

            # 设置颜色
            if fail_count > 0:
                item.setForeground(2, QColor(255, 0, 0))  # 红色显示FAIL数量

            self.timestamp_tree.addTopLevelItem(item)

    def on_timestamp_clicked(self, item):
        """处理时间戳点击事件"""
        timestamp = item.text(0)
        self.current_timestamp = timestamp
        self.current_group = None

        # 更新用例组树
        self.update_group_tree(timestamp)

        # 更新总览表，只显示选中时间戳的用例
        self.update_overview_table(timestamp)

        # 显示该时间戳下所有用例组的PASS和FAIL用例
        self.update_all_tables(timestamp)

    def on_timestamp_double_clicked(self, item):
        """处理时间戳双击事件 - 清除选择，显示全部时间戳总览"""
        self.show_all_timestamps_overview()

    def show_all_timestamps_overview(self):
        """显示所有时间戳的用例总览"""
        # 清除当前选择
        self.current_timestamp = None
        self.current_group = None

        # 清除时间戳树的选择
        self.timestamp_tree.clearSelection()

        # 清空用例组树
        self.group_tree.clear()

        # 更新总览表，显示所有时间戳的用例
        self.update_overview_table()

        # 清空PASS和FAIL表格
        self.pass_table.setRowCount(0)
        self.fail_table.setRowCount(0)

    def update_group_tree(self, timestamp):
        """更新用例组树"""
        self.group_tree.clear()

        if timestamp not in self.regression_data:
            return

        data = self.regression_data[timestamp]

        # 获取所有组
        all_groups = set(list(data["pass"].keys()) + list(data["fail"].keys()))

        for group in sorted(all_groups):
            # 计算PASS和FAIL用例数
            pass_count = len(data["pass"].get(group, []))
            fail_count = len(data["fail"].get(group, []))

            # 创建组项
            item = QTreeWidgetItem([
                group,
                str(pass_count),
                str(fail_count)
            ])

            # 设置颜色
            if fail_count > 0:
                item.setForeground(2, QColor(255, 0, 0))  # 红色显示FAIL数量

            self.group_tree.addTopLevelItem(item)

    def on_group_clicked(self, item):
        """处理用例组点击事件"""
        if not self.current_timestamp:
            # 如果没有选中时间戳，提示用户先选择时间戳
            QMessageBox.information(
                self,
                "提示",
                "请先选择一个时间戳，然后再选择用例组查看详细的PASS和FAIL用例。\n\n"
                "或者直接在总览表中查看所有用例的汇总信息。"
            )
            return

        group = item.text(0)
        self.current_group = group

        # 更新表格，只显示选中用例组的用例
        self.update_tables(self.current_timestamp, group)

    def update_tables(self, timestamp, group):
        """更新表格内容（显示特定用例组的用例）"""
        if timestamp not in self.regression_data:
            return

        data = self.regression_data[timestamp]

        # 更新PASS表格
        self.pass_table.setRowCount(0)
        if group in data["pass"]:
            for case_info in data["pass"][group]:
                self.add_case_to_table(self.pass_table, case_info)

        # 更新FAIL表格
        self.fail_table.setRowCount(0)
        if group in data["fail"]:
            for case_info in data["fail"][group]:
                self.add_case_to_table(self.fail_table, case_info)

        # 应用过滤器
        self.apply_filter()

    def update_all_tables(self, timestamp):
        """更新表格内容（显示所有用例组的用例）"""
        if timestamp not in self.regression_data:
            return

        data = self.regression_data[timestamp]

        # 清空表格
        self.pass_table.setRowCount(0)
        self.fail_table.setRowCount(0)

        # 添加所有PASS用例
        for group, cases in data["pass"].items():
            for case_info in cases:
                self.add_case_to_table(self.pass_table, case_info)

        # 添加所有FAIL用例
        for group, cases in data["fail"].items():
            for case_info in cases:
                self.add_case_to_table(self.fail_table, case_info)

        # 应用过滤器
        self.apply_filter()

    def add_case_to_table(self, table, case_info):
        """添加用例到表格"""
        row = table.rowCount()
        table.insertRow(row)

        # 获取当前时间单位
        current_unit = self.time_unit_combo.currentData()
        if current_unit is None:
            current_unit = TimeUnit.MINUTES

        # 设置用例名
        case_item = QTableWidgetItem(case_info["case"])
        table.setItem(row, 0, case_item)

        # 设置仿真状态
        tag = case_info["tag"].strip('[]')
        status_item = QTableWidgetItem(tag)
        if tag == "PASS":
            status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
        else:
            status_item.setBackground(QColor(255, 200, 200))  # 浅红色
        table.setItem(row, 1, status_item)

        # 设置种子
        seed = case_info["seed"].strip('[]')
        table.setItem(row, 2, QTableWidgetItem(seed))

        # 设置编译时间
        compile_time = case_info.get("compile_time")
        compile_item = QTableWidgetItem("")
        if compile_time is not None:
            # 转换时间单位
            converted_compile_time = TimeUnitConverter.convert_time(compile_time, TimeUnit.MINUTES, current_unit)
            formatted_time = TimeUnitConverter.format_time(converted_compile_time, current_unit, False)
            compile_item = QTableWidgetItem(formatted_time)
            compile_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            # 设置为数字类型以便正确排序
            if converted_compile_time is not None:
                compile_item.setData(Qt.DisplayRole, converted_compile_time)
        table.setItem(row, 3, compile_item)

        # 设置仿真时间
        sim_time = case_info.get("sim_time")
        sim_item = QTableWidgetItem("")
        if sim_time is not None:
            # 转换时间单位
            converted_sim_time = TimeUnitConverter.convert_time(sim_time, TimeUnit.MINUTES, current_unit)
            formatted_time = TimeUnitConverter.format_time(converted_sim_time, current_unit, False)
            sim_item = QTableWidgetItem(formatted_time)
            sim_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            # 设置为数字类型以便正确排序
            if converted_sim_time is not None:
                sim_item.setData(Qt.DisplayRole, converted_sim_time)
        table.setItem(row, 4, sim_item)

        # 设置日志路径
        log_item = QTableWidgetItem(case_info["log"])
        log_item.setToolTip("双击打开日志文件")
        log_item.setForeground(QColor(0, 0, 255))  # 蓝色
        table.setItem(row, 5, log_item)

        # 设置仿真命令
        cmd_item = QTableWidgetItem(case_info["command"])
        cmd_item.setToolTip("双击执行命令")
        cmd_item.setForeground(QColor(0, 128, 0))  # 绿色
        table.setItem(row, 6, cmd_item)

    def apply_filter(self):
        """应用搜索过滤器"""
        search_text = self.search_input.text().lower()

        # 过滤总览表
        for row in range(self.overview_table.rowCount()):
            visible = True
            if search_text:
                visible = False
                # 检查所有列
                for col in range(self.overview_table.columnCount()):
                    item = self.overview_table.item(row, col)
                    if item and search_text in item.text().lower():
                        visible = True
                        break
            self.overview_table.setRowHidden(row, not visible)

        # 过滤PASS表格
        for row in range(self.pass_table.rowCount()):
            visible = True
            if search_text:
                visible = False
                # 检查所有列
                for col in range(self.pass_table.columnCount()):
                    item = self.pass_table.item(row, col)
                    if item and search_text in item.text().lower():
                        visible = True
                        break
            self.pass_table.setRowHidden(row, not visible)

        # 过滤FAIL表格
        for row in range(self.fail_table.rowCount()):
            visible = True
            if search_text:
                visible = False
                # 检查所有列
                for col in range(self.fail_table.columnCount()):
                    item = self.fail_table.item(row, col)
                    if item and search_text in item.text().lower():
                        visible = True
                        break
            self.fail_table.setRowHidden(row, not visible)

    def on_cell_clicked(self, row, col):
        """处理单元格双击事件"""
        table = self.sender()

        # 判断是否为总览表
        if table == self.overview_table:
            # 总览表的列布局：用例名、最终状态、执行次数、平均仿真时间、最新编译时间、所有种子、最新日志、最新命令
            if col == 6:  # 最新日志列
                log_path = table.item(row, col).text()
                if log_path and os.path.exists(log_path):
                    self.open_log_file(log_path)
                else:
                    QMessageBox.warning(self, "警告", f"日志文件不存在: {log_path}")
            elif col == 7:  # 最新命令列
                command = table.item(row, col).text()
                if command:
                    self.execute_command(command)
        else:
            # PASS/FAIL表格的列布局：用例名、仿真状态、种子、编译时间、仿真时间、结果日志、仿真命令
            if col == 5:  # 日志路径列
                log_path = table.item(row, col).text()
                if log_path and os.path.exists(log_path):
                    self.open_log_file(log_path)
                else:
                    QMessageBox.warning(self, "警告", f"日志文件不存在: {log_path}")
            elif col == 6:  # 仿真命令列
                command = table.item(row, col).text()
                if command:
                    self.execute_command(command)

    def open_log_file(self, log_path):
        """使用gvim打开日志文件"""
        try:
            process = QProcess()
            process.startDetached("gvim", [log_path])
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开日志文件失败: {str(e)}")

    def execute_command(self, command):
        """执行仿真命令"""
        try:
            # 确认是否执行命令
            reply = QMessageBox.question(
                self,
                "确认执行",
                f"确定要执行以下命令吗？\n{command}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 获取父窗口（主窗口）
                main_window = self.parent()

                # 如果是对话框，则获取其父窗口
                if isinstance(main_window, QDialog):
                    main_window = main_window.parent()

                # 检查是否有主窗口引用
                if main_window is None:
                    QMessageBox.warning(self, "警告", "无法获取主窗口引用，将在独立进程中执行命令")
                    process = QProcess()
                    process.startDetached(command)
                    return

                # 从命令中提取用例名称
                case_name = None
                for part in command.split():
                    if part.startswith("-case"):
                        parts = command.split()
                        idx = parts.index(part)
                        if idx + 1 < len(parts):
                            case_name = parts[idx + 1]
                            break

                # 如果没有找到用例名称，使用命令的一部分作为标签名
                if not case_name:
                    # 使用命令的前30个字符作为标签名
                    case_name = command[:30] + "..." if len(command) > 30 else command

                # 检查主窗口是否有创建标签页的方法
                if hasattr(main_window, 'case_tabs') and hasattr(main_window, 'tab_widget'):
                    # 创建新标签页并执行命令
                    from runsim_gui import CaseTab

                    # 如果已有同名标签页，先关闭
                    if case_name in main_window.case_tabs:
                        tab_index = main_window.tab_widget.indexOf(main_window.case_tabs[case_name])
                        # 检查是否有关闭标签页的方法
                        if hasattr(main_window, 'close_case_tab'):
                            main_window.close_case_tab(tab_index)
                        else:
                            main_window.tab_widget.removeTab(tab_index)
                            del main_window.case_tabs[case_name]

                    # 创建新标签页
                    case_tab = CaseTab(case_name, command, main_window)
                    main_window.case_tabs[case_name] = case_tab
                    main_window.tab_widget.addTab(case_tab, case_name)
                    main_window.tab_widget.setCurrentWidget(case_tab)

                    # 执行命令
                    case_tab.start_execution()

                    # 保存命令到历史记录（如果主窗口有此方法）
                    if hasattr(main_window, 'save_history'):
                        main_window.save_history(command)

                    # 显示成功消息
                    QMessageBox.information(self, "成功", f"命令已在主窗口的'{case_name}'标签页中启动执行")
                else:
                    # 如果主窗口没有必要的方法，回退到独立进程执行
                    QMessageBox.warning(self, "警告", "主窗口不支持创建标签页，将在独立进程中执行命令")
                    process = QProcess()
                    process.startDetached(command)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"执行命令失败: {str(e)}")
            # 出错时回退到独立进程执行
            try:
                process = QProcess()
                process.startDetached(command)
            except Exception:
                pass

    def aggregate_case_data(self, specific_timestamp=None):
        """聚合用例数据，处理重复执行的用例

        Args:
            specific_timestamp: 如果指定，只聚合该时间戳的数据；如果为None，聚合所有时间戳的数据
        """
        aggregated_data = {}

        # 确定要处理的时间戳
        if specific_timestamp:
            timestamps_to_process = [specific_timestamp] if specific_timestamp in self.regression_data else []
        else:
            timestamps_to_process = sorted(self.regression_data.keys(), reverse=True)  # 从新到旧

        # 如果是特定时间戳，直接显示该时间戳的用例状态，不需要复杂的聚合逻辑
        if specific_timestamp and specific_timestamp in self.regression_data:
            data = self.regression_data[specific_timestamp]

            # 处理PASS和FAIL用例
            for result_type in ["pass", "fail"]:
                for group, cases in data[result_type].items():
                    for case_info in cases:
                        case_name = case_info["case"]

                        if case_name not in aggregated_data:
                            # 创建用例数据
                            aggregated_data[case_name] = {
                                'case_name': case_name,
                                'final_status': case_info["tag"].strip('[]'),
                                'execution_count': 1,
                                'sim_times': [],
                                'latest_compile_time': case_info.get("compile_time"),
                                'seeds': [case_info["seed"].strip('[]')],
                                'latest_log': case_info["log"],
                                'latest_command': case_info["command"],
                                'latest_timestamp': specific_timestamp,
                                'has_fail': result_type == "fail"
                            }

                            # 添加仿真时间（如果存在）
                            if case_info.get("sim_time") is not None:
                                aggregated_data[case_name]['sim_times'].append(case_info["sim_time"])
                        else:
                            # 同一时间戳下的重复用例（不同种子），更新数据
                            agg_case = aggregated_data[case_name]
                            agg_case['execution_count'] += 1

                            # 如果是FAIL状态，优先显示FAIL
                            if result_type == "fail":
                                agg_case['final_status'] = case_info["tag"].strip('[]')
                                agg_case['has_fail'] = True
                                agg_case['latest_log'] = case_info["log"]
                                agg_case['latest_command'] = case_info["command"]

                            # 添加仿真时间
                            if case_info.get("sim_time") is not None:
                                agg_case['sim_times'].append(case_info["sim_time"])

                            # 添加种子（避免重复）
                            seed = case_info["seed"].strip('[]')
                            if seed not in agg_case['seeds']:
                                agg_case['seeds'].append(seed)

            return aggregated_data

        # 处理多个时间戳的聚合（显示所有时间戳总览时）
        for timestamp in timestamps_to_process:
            data = self.regression_data[timestamp]

            # 处理PASS和FAIL用例
            for result_type in ["pass", "fail"]:
                for group, cases in data[result_type].items():
                    for case_info in cases:
                        case_name = case_info["case"]

                        if case_name not in aggregated_data:
                            # 首次遇到该用例，初始化数据
                            aggregated_data[case_name] = {
                                'case_name': case_name,
                                'final_status': case_info["tag"].strip('[]'),
                                'execution_count': 1,
                                'sim_times': [],
                                'latest_compile_time': case_info.get("compile_time"),
                                'seeds': [case_info["seed"].strip('[]')],
                                'latest_log': case_info["log"],
                                'latest_command': case_info["command"],
                                'latest_timestamp': timestamp,
                                'has_fail': result_type == "fail"
                            }

                            # 添加仿真时间（如果存在）
                            if case_info.get("sim_time") is not None:
                                aggregated_data[case_name]['sim_times'].append(case_info["sim_time"])
                        else:
                            # 已存在该用例，更新数据
                            agg_case = aggregated_data[case_name]
                            agg_case['execution_count'] += 1

                            # 更新状态（显示最新时间戳的状态）
                            if timestamp >= agg_case['latest_timestamp']:
                                agg_case['final_status'] = case_info["tag"].strip('[]')
                                agg_case['has_fail'] = result_type == "fail"
                                agg_case['latest_compile_time'] = case_info.get("compile_time")
                                agg_case['latest_log'] = case_info["log"]
                                agg_case['latest_command'] = case_info["command"]
                                agg_case['latest_timestamp'] = timestamp

                            # 添加仿真时间
                            if case_info.get("sim_time") is not None:
                                agg_case['sim_times'].append(case_info["sim_time"])

                            # 添加种子（避免重复）
                            seed = case_info["seed"].strip('[]')
                            if seed not in agg_case['seeds']:
                                agg_case['seeds'].append(seed)

        return aggregated_data

    def update_overview_table(self, specific_timestamp=None):
        """更新总览表

        Args:
            specific_timestamp: 如果指定，只显示该时间戳的用例总览；如果为None，显示所有时间戳的总览
        """
        try:
            # 聚合数据
            aggregated_data = self.aggregate_case_data(specific_timestamp)

            # 清空表格
            self.overview_table.setRowCount(0)

            # 按用例名排序
            sorted_cases = sorted(aggregated_data.values(), key=lambda x: x['case_name'])

            for case_data in sorted_cases:
                self.add_case_to_overview_table(case_data)

            # 应用过滤器
            self.apply_filter()

        except Exception as e:
            print(f"更新总览表失败: {str(e)}")

    def add_case_to_overview_table(self, case_data):
        """添加用例到总览表"""
        row = self.overview_table.rowCount()
        self.overview_table.insertRow(row)

        # 获取当前时间单位
        current_unit = self.time_unit_combo.currentData()
        if current_unit is None:
            current_unit = TimeUnit.MINUTES

        # 用例名
        case_item = QTableWidgetItem(case_data['case_name'])
        self.overview_table.setItem(row, 0, case_item)

        # 最终状态
        status_item = QTableWidgetItem(case_data['final_status'])
        if case_data['final_status'] == "PASS":
            status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
        else:
            status_item.setBackground(QColor(255, 200, 200))  # 浅红色
        self.overview_table.setItem(row, 1, status_item)

        # 执行次数
        count_item = QTableWidgetItem(str(case_data['execution_count']))
        count_item.setTextAlignment(Qt.AlignCenter)
        self.overview_table.setItem(row, 2, count_item)

        # 平均仿真时间
        avg_sim_time_item = QTableWidgetItem("")
        if case_data['sim_times']:
            avg_sim_time = sum(case_data['sim_times']) / len(case_data['sim_times'])
            # 转换时间单位
            converted_avg_sim_time = TimeUnitConverter.convert_time(avg_sim_time, TimeUnit.MINUTES, current_unit)
            formatted_time = TimeUnitConverter.format_time(converted_avg_sim_time, current_unit, False)
            avg_sim_time_item = QTableWidgetItem(formatted_time)
            avg_sim_time_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            if converted_avg_sim_time is not None:
                avg_sim_time_item.setData(Qt.DisplayRole, converted_avg_sim_time)
        self.overview_table.setItem(row, 3, avg_sim_time_item)

        # 最新编译时间
        compile_time_item = QTableWidgetItem("")
        if case_data['latest_compile_time'] is not None:
            # 转换时间单位
            converted_compile_time = TimeUnitConverter.convert_time(case_data['latest_compile_time'], TimeUnit.MINUTES, current_unit)
            formatted_time = TimeUnitConverter.format_time(converted_compile_time, current_unit, False)
            compile_time_item = QTableWidgetItem(formatted_time)
            compile_time_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            if converted_compile_time is not None:
                compile_time_item.setData(Qt.DisplayRole, converted_compile_time)
        self.overview_table.setItem(row, 4, compile_time_item)

        # 所有种子
        seeds_text = ", ".join(case_data['seeds'])
        seeds_item = QTableWidgetItem(seeds_text)
        seeds_item.setToolTip(f"所有种子: {seeds_text}")
        self.overview_table.setItem(row, 5, seeds_item)

        # 最新日志
        log_item = QTableWidgetItem(case_data['latest_log'])
        log_item.setToolTip("双击打开日志文件")
        log_item.setForeground(QColor(0, 0, 255))  # 蓝色
        self.overview_table.setItem(row, 6, log_item)

        # 最新命令
        cmd_item = QTableWidgetItem(case_data['latest_command'])
        cmd_item.setToolTip("双击执行命令")
        cmd_item.setForeground(QColor(0, 128, 0))  # 绿色
        self.overview_table.setItem(row, 7, cmd_item)

    def export_to_excel(self):
        """导出回归测试报告到Excel"""
        try:
            # 确定导出范围并提示用户
            if self.current_timestamp:
                export_scope = f"选中时间戳 {self.current_timestamp} 的数据"
                default_suffix = f"_{self.current_timestamp}"
            else:
                export_scope = "所有时间戳的数据"
                default_suffix = "_all"

            # 确认导出范围
            reply = QMessageBox.question(
                self,
                "确认导出范围",
                f"将导出 {export_scope}，确定继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply != QMessageBox.Yes:
                return

            # 获取保存路径
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"regression_report{default_suffix}_{current_time}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出回归测试报告",
                default_name,
                "Excel文件 (*.xlsx)"
            )

            if not file_path:
                return

            # 创建工作簿
            wb = openpyxl.Workbook()

            # 删除默认工作表
            wb.remove(wb.active)

            # 创建总览表工作表
            self.create_overview_sheet(wb)

            # 创建详细数据工作表
            self.create_detailed_sheets(wb)

            # 保存文件
            wb.save(file_path)

            # 显示成功消息，包含导出范围信息
            success_msg = f"回归测试报告已成功导出到:\n{file_path}\n\n导出范围: {export_scope}"
            QMessageBox.information(self, "成功", success_msg)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出Excel失败: {str(e)}")

    def create_overview_sheet(self, workbook):
        """创建总览表工作表"""
        ws = workbook.create_sheet("总览表", 0)

        # 设置表头
        headers = [
            "用例名", "最终状态", "执行次数", "平均仿真时间(分钟)",
            "最新编译时间(分钟)", "所有种子", "最新日志", "最新命令"
        ]

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="f8f9fa", end_color="f8f9fa", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")

        # 获取聚合数据（根据当前选中的时间戳）
        aggregated_data = self.aggregate_case_data(self.current_timestamp)
        sorted_cases = sorted(aggregated_data.values(), key=lambda x: x['case_name'])

        # 写入数据
        for row_idx, case_data in enumerate(sorted_cases, 2):
            # 用例名
            ws.cell(row=row_idx, column=1).value = case_data['case_name']

            # 最终状态
            status_cell = ws.cell(row=row_idx, column=2)
            status_cell.value = case_data['final_status']
            if case_data['final_status'] == "PASS":
                status_cell.fill = PatternFill(start_color="c8e6c9", end_color="c8e6c9", fill_type="solid")
            else:
                status_cell.fill = PatternFill(start_color="ffcdd2", end_color="ffcdd2", fill_type="solid")

            # 执行次数
            ws.cell(row=row_idx, column=3).value = case_data['execution_count']

            # 平均仿真时间
            if case_data['sim_times']:
                avg_sim_time = sum(case_data['sim_times']) / len(case_data['sim_times'])
                ws.cell(row=row_idx, column=4).value = round(avg_sim_time, 2)

            # 最新编译时间
            if case_data['latest_compile_time'] is not None:
                ws.cell(row=row_idx, column=5).value = round(case_data['latest_compile_time'], 2)

            # 所有种子
            ws.cell(row=row_idx, column=6).value = ", ".join(case_data['seeds'])

            # 最新日志
            ws.cell(row=row_idx, column=7).value = case_data['latest_log']

            # 最新命令
            ws.cell(row=row_idx, column=8).value = case_data['latest_command']

        # 调整列宽
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
            ws.column_dimensions[column].width = adjusted_width

    def create_detailed_sheets(self, workbook):
        """创建详细数据工作表"""
        # 根据当前选择状态决定要导出的时间戳
        if self.current_timestamp:
            # 如果选中了特定时间戳，只导出该时间戳的详细数据
            timestamps_to_export = [self.current_timestamp]
        else:
            # 如果没有选中时间戳，导出所有时间戳的详细数据
            timestamps_to_export = sorted(self.regression_data.keys(), reverse=True)

        # 按时间戳创建工作表
        for timestamp in timestamps_to_export:
            # 限制工作表名称长度
            sheet_name = f"详细_{timestamp}"[:31]  # Excel工作表名称限制31个字符
            ws = workbook.create_sheet(sheet_name)

            # 设置表头
            headers = ["用例组", "用例名", "仿真状态", "种子", "编译时间(分钟)", "仿真时间(分钟)", "结果日志", "仿真命令"]

            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col)
                cell.value = header
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="f8f9fa", end_color="f8f9fa", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")

            row_idx = 2
            data = self.regression_data[timestamp]

            # 写入PASS和FAIL数据
            for result_type in ["pass", "fail"]:
                for group, cases in data[result_type].items():
                    for case_info in cases:
                        ws.cell(row=row_idx, column=1).value = group
                        ws.cell(row=row_idx, column=2).value = case_info["case"]

                        # 仿真状态
                        status_cell = ws.cell(row=row_idx, column=3)
                        status = case_info["tag"].strip('[]')
                        status_cell.value = status
                        if status == "PASS":
                            status_cell.fill = PatternFill(start_color="c8e6c9", end_color="c8e6c9", fill_type="solid")
                        else:
                            status_cell.fill = PatternFill(start_color="ffcdd2", end_color="ffcdd2", fill_type="solid")

                        ws.cell(row=row_idx, column=4).value = case_info["seed"].strip('[]')

                        # 编译时间
                        if case_info.get("compile_time") is not None:
                            ws.cell(row=row_idx, column=5).value = round(case_info["compile_time"], 2)

                        # 仿真时间
                        if case_info.get("sim_time") is not None:
                            ws.cell(row=row_idx, column=6).value = round(case_info["sim_time"], 2)

                        ws.cell(row=row_idx, column=7).value = case_info["log"]
                        ws.cell(row=row_idx, column=8).value = case_info["command"]

                        row_idx += 1

            # 调整列宽
            for col in ws.columns:
                max_length = 0
                column = col[0].column_letter
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                ws.column_dimensions[column].width = adjusted_width
