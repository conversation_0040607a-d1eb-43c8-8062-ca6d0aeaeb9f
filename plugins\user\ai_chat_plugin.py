"""
AI聊天插件

提供AI聊天对话、文档上传解析、中英文互译等功能的插件。
支持多种AI模型（DeepSeek、通义千问等），兼容OpenAI API格式。
"""

import os
import sys
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from plugins.base import PluginBase
from ai_chat.views.chat_window import ChatWindow
from ai_chat.controllers.chat_controller import ChatController


class AIChatPlugin(PluginBase):
    """AI聊天插件主类"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.chat_window = None
        self.chat_controller = None
        self.main_window = None
        self.menu_action = None
        
    @property
    def name(self) -> str:
        """插件名称"""
        return "AI聊天助手"
    
    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """插件描述"""
        return "提供AI聊天对话、文档上传解析、中英文互译等功能"
    
    def initialize(self, main_window):
        """初始化插件
        
        Args:
            main_window: 主窗口实例
        """
        try:
            self.main_window = main_window
            
            # 创建聊天窗口和控制器
            self.chat_window = ChatWindow()
            self.chat_controller = ChatController(self.chat_window)
            
            # 添加菜单项
            self._add_menu_item()
            
            print(f"插件 {self.name} 初始化成功")
            
        except Exception as e:
            print(f"插件 {self.name} 初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def cleanup(self):
        """清理插件资源"""
        try:
            # 移除菜单项
            if self.menu_action and self.main_window:
                tools_menu = getattr(self.main_window, 'tools_menu', None)
                if tools_menu:
                    tools_menu.removeAction(self.menu_action)
            
            # 关闭聊天窗口
            if self.chat_window:
                self.chat_window.close()
                self.chat_window = None
            
            # 清理控制器
            if self.chat_controller:
                self.chat_controller.cleanup()
                self.chat_controller = None
            
            self.main_window = None
            self.menu_action = None
            
            print(f"插件 {self.name} 清理完成")
            
        except Exception as e:
            print(f"插件 {self.name} 清理失败: {str(e)}")
    
    def _add_menu_item(self):
        """添加菜单项"""
        try:
            if not self.main_window:
                return
            
            # 获取工具菜单
            tools_menu = getattr(self.main_window, 'tools_menu', None)
            if not tools_menu:
                print("未找到工具菜单")
                return
            
            # 创建菜单动作
            self.menu_action = QAction("AI聊天助手", self.main_window)
            self.menu_action.setStatusTip("打开AI聊天助手")
            self.menu_action.triggered.connect(self._show_chat_window)
            
            # 添加到工具菜单
            tools_menu.addAction(self.menu_action)
            
        except Exception as e:
            print(f"添加菜单项失败: {str(e)}")
    
    def _show_chat_window(self):
        """显示聊天窗口"""
        try:
            if self.chat_window:
                self.chat_window.show()
                self.chat_window.raise_()
                self.chat_window.activateWindow()
        except Exception as e:
            print(f"显示聊天窗口失败: {str(e)}")
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开AI聊天助手: {str(e)}"
            )


# 插件实例
plugin_instance = AIChatPlugin()
