import os
import re
import sys

# 直接导入需要的模块，不再使用LazyLoader
import jinja2
import pandas as pd

def clean_column_name(col):
    """清理列名，移除空格、下划线和斜杠"""
    return re.sub('[ _/]', '', str(col)).lower()

def normalize_rw(rw_value):
    """标准化读写属性值，将 'R/W' 转换为 'RW' 格式"""
    if pd.isna(rw_value):
        return ''
    return str(rw_value).strip().upper().replace('/', '')

class TemplateManager:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TemplateManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 使用单例模式，避免重复初始化
        if TemplateManager._initialized:
            return
            
        template_path = os.path.join(os.path.dirname(__file__), 'templates')
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(template_path),
            trim_blocks=True,
            lstrip_blocks=True,
            autoescape=True,
            extensions=['jinja2.ext.loopcontrols']
        )
        
        # 添加C语言转义过滤器
        self.env.filters['c_escape'] = lambda s: str(s).replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
        
        # 添加左移运算过滤器
        self.env.filters['left_shift'] = lambda x: (1 << int(x)) - 1
        
        TemplateManager._initialized = True

    def render_template(self, template_name, context):
        """渲染模板"""
        template = self.env.get_template(template_name)
        return template.render(context)

def escape_operators(text):
    if isinstance(text, str):
        return text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
    return text

def parse_register_file(excel_path):
    """解析寄存器文件"""
    # 缓存结果的静态变量
    if not hasattr(parse_register_file, '_cache'):
        parse_register_file._cache = {}
    
    # 检查缓存中是否有结果
    if excel_path in parse_register_file._cache:
        print(f"使用缓存的解析结果: {excel_path}")
        return parse_register_file._cache[excel_path]
    
    # 扫描Excel表格获取Module Name和表头信息
    header_df = pd.read_excel(excel_path, header=None)
    module_cell = header_df[header_df.apply(lambda row: row.astype(str).str.contains('Module Name').any(), axis=1)]
    base_addr_cell = header_df[header_df.apply(lambda row: row.astype(str).str.contains('BASE ADDR').any(), axis=1)]
    offset_cell = header_df[header_df.apply(lambda row: row.astype(str).str.contains('Offset').any(), axis=1)]

    # 确定Module Name
    if not module_cell.empty:
        module_row, module_col = module_cell.index[0], module_cell.columns[module_cell.iloc[0].astype(str).str.contains('Module Name').argmax()]
        module_name = str(header_df.iloc[module_row, module_col+1]).strip()
    else:
        module_name = 'module'

    # 确定BASE ADDR
    if not base_addr_cell.empty:
        base_addr_row, base_addr_col = base_addr_cell.index[0], base_addr_cell.columns[base_addr_cell.iloc[0].astype(str).str.contains('BASE ADDR').argmax()]
        base_addr = str(header_df.iloc[base_addr_row, base_addr_col+1]).strip()
        if base_addr.startswith('0x'):
            base_addr = int(base_addr[2:].strip().replace(' ', '').replace('_', '').lower(), 16)
        else:
            base_addr = int(base_addr.strip().replace('[ _]', '', regex=True).str.lower(), 16)
    else:
        base_addr = int('0x0', 16)

    # 自适应确定表头行和数据起始行
    # 首先尝试查找包含必要列名的行
    required_columns_keywords = ['offset', 'regname', 'bit', 'fieldname', 'rw', 'resetvalue', 'description']
    header_row = None

    # 遍历前20行，查找包含必要列名的行
    for row_idx in range(min(20, len(header_df))):
        row_values = [str(val).lower() for val in header_df.iloc[row_idx].values if pd.notna(val)]
        row_text = ' '.join(row_values)

        # 计算该行包含的必要列名关键字数量
        keyword_count = sum(1 for keyword in required_columns_keywords if keyword in row_text)

        # 如果该行包含至少4个必要列名关键字，认为它是表头行
        if keyword_count >= 4:
            header_row = row_idx
            break

    # 如果找到了表头行，则数据从下一行开始
    if header_row is not None:
        skip_rows = header_row
        print(f"找到表头行：第{header_row+1}行，包含必要列名")
    else:
        # 如果没有找到表头行，使用其他方法推断
        skip_rows = 9  # 默认跳过9行

        # 方法1：根据Module Name所在行推断
        if not module_cell.empty:
            # 通常offset所在行在Module Name所在行后7行
            skip_rows = module_row + 7

        # 方法2：直接查找包含"Offset"的行
        if not offset_cell.empty:
            offset_row = offset_cell.index[0]
            # offset所在行就是包含"Offset"的行
            skip_rows = offset_row

    print(f"自适应识别：跳过前{skip_rows}行，从第{skip_rows+1}行开始解析数据")

    # 读取Excel文件，使用第skip_rows行作为表头
    df = pd.read_excel(excel_path, header=skip_rows)

    # 打印原始列名
    print("原始列名:", list(df.columns))

    # 清理列名
    df.columns = df.columns.map(clean_column_name)
    print("清理后列名:", list(df.columns))

    # 检查必要列是否存在
    required_columns = ['regname', 'offset', 'bit', 'fieldname', 'rw', 'resetvalue', 'description']

    # 尝试映射列名
    column_mapping = {}
    for req_col in required_columns:
        # 尝试找到匹配的列
        for col in df.columns:
            if req_col in col.lower():
                column_mapping[col] = req_col  # 注意：这里是将原列名映射到所需列名
                break

    # 如果找到了映射关系，重命名列
    if column_mapping:
        print("列名映射:", column_mapping)
        df = df.rename(columns=column_mapping)
        print("映射后列名:", list(df.columns))

    # 确保不会有重复的列名
    if 'description' in df.columns and df.columns.tolist().count('description') > 1:
        # 如果有多个description列，保留第一个，重命名其他的
        dup_indices = [i for i, col in enumerate(df.columns) if col == 'description']
        for i in dup_indices[1:]:
            df.columns.values[i] = f'description_{i}'
        print("修正后列名:", list(df.columns))

    # 再次检查必要列
    missing_cols = [col for col in required_columns if col not in df.columns]
    if missing_cols:
        # 尝试使用更宽松的匹配
        for req_col in missing_cols[:]:
            for col in df.columns:
                # 使用更宽松的匹配规则
                if any(keyword in col.lower() for keyword in [req_col[:4], req_col[-4:]]):
                    df = df.rename(columns={col: req_col})
                    missing_cols.remove(req_col)
                    break

    # 最终检查
    if missing_cols:
        raise ValueError(f"缺失必要列: {missing_cols}，请检查Excel表格格式")

    # 确保width列存在，如果不存在则添加默认值32
    if 'width' not in df.columns:
        df['width'] = 32
        print("未找到width列，使用默认值32")
    else:
        df['width'] = df['width'].fillna(0)
        df['width'] = df['width'].ffill()

    # 向前填充合并单元格数据
    df['regname'] = df['regname'].ffill()
    df['offset'] = df['offset'].ffill()

    # 确保description列存在
    if 'description' not in df.columns:
        df['description'] = ''
        print("未找到description列，使用空字符串")
    else:
        df['description'] = df['description'].ffill()
        df['description'] = df['description'].fillna('')

    valid_rows = []
    for idx, row in df.iterrows():
        try:
            offset_str = str(row['offset']).strip()
            if offset_str.lower().startswith('0x'):
                int(offset_str, 16)
            else:
                int(offset_str)
            valid_rows.append(idx)
        except ValueError:
            pass

    df = df.loc[valid_rows].reset_index(drop=True)
    df['offset'] = df['offset'].ffill()
    
    # 处理寄存器和字段
    registers = []
    current_reg = {}
    prev_offset = None
    
    for _, row in df.iterrows():
        current_offset_str = str(row['offset']).strip()
        try:
            if current_offset_str.lower().startswith('0x'):
                current_offset = int(current_offset_str, 16)
            else:
                current_offset = int(current_offset_str)
        except ValueError as e:
            raise ValueError(f"第{_+1}行偏移量格式错误: '{current_offset_str}'，请检查Excel表格数据")

        # 通过offset判断寄存器边界
        if current_offset != prev_offset:
            if current_reg:
                registers.append(current_reg)

            # 获取寄存器描述信息
            reg_desc = ''
            if 'shortdescription' in row and pd.notna(row['shortdescription']):
                reg_desc = str(row['shortdescription']).strip()

            current_reg = {
                'offset': current_offset,
                'name': row['regname'],
                'width': int(row['width']),
                'short_desc': reg_desc,  # 添加寄存器描述
                'fields': []
            }
            prev_offset = current_offset

        # 处理合并单元格中的空值
        if pd.notna(row['bit']) and pd.notna(row.get('fieldname')) and pd.notna(row.get('rw')):
            bit_str = str(row['bit']).strip()
            # 修正位域解析逻辑
            if match := re.match(r'\[(\d+)(?::(\d+))?\]', bit_str):
                bit_start = int(match.group(1))
                bit_end = int(match.group(2)) if match.group(2) else bit_start
                if bit_start < bit_end:
                    bit_start, bit_end = bit_end, bit_start  # 确保bit_start始终是高位
                bit_width = bit_start - bit_end + 1
            else:
                try:
                    bit_start = int(bit_str)
                    bit_end = bit_start
                    bit_width = 1
                except ValueError:
                    raise ValueError(f"无效的位域格式: {bit_str}")

            # 添加字段信息，使用安全的字符串处理
            if 'reserved' in str(row['fieldname']).lower():
                continue

            # 获取描述信息，优先使用'description'列，如果不存在则尝试使用其他可能的描述列
            desc_value = ''
            if 'description' in row and pd.notna(row['description']):
                desc_value = str(row['description']).strip()
            elif 'shortdescription' in row and pd.notna(row['shortdescription']):
                desc_value = str(row['shortdescription']).strip()

            current_reg['fields'].append({
                'bit': escape_operators(str(bit_str) if bit_start == bit_end else f"{bit_start}:{bit_end}"),  # 使用标准格式并转义操作符
                'bit_start': bit_start,
                'bit_end': bit_end,
                'bit_width': int(bit_width),
                'name': ''.join(c if c.isalnum() or c == '_' else '' for c in str(row['fieldname']).strip()),
                'rw': normalize_rw(row['rw']),  # 使用normalize_rw函数
                'reset': escape_operators(str(row['resetvalue']).strip()),  # 转义操作符
                'desc': escape_operators(desc_value)  # 使用安全处理的描述信息
            })

    if current_reg:
        registers.append(current_reg)
    
    # 创建返回结果
    result = {
        'module_name': module_name,
        'base_addr': base_addr,
        'registers': registers
    }
    
    # 缓存结果
    parse_register_file._cache[excel_path] = result
    
    return result

# 保留原有导出函数供GUI调用
generate_c_header = lambda reg_data: TemplateManager().render_template(
    'c_struct.j2', {'registers': reg_data, 'base_addr': 0x64951000}
)
