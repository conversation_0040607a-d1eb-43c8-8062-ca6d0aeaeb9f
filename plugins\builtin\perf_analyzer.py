from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTextEdit, QMessageBox, QFileDialog, QLabel, QProgressBar,
                           QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt
import re
import os
from plugins.base import PluginBase

class PerfAnalyzerPlugin(PluginBase):
    """性能分析插件，分析仿真性能瓶颈"""

    @property
    def name(self):
        return "性能分析器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "分析仿真性能瓶颈"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_analyzer)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

        except Exception as e:
            print(f"初始化性能分析插件失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理性能分析插件失败: {str(e)}")

    def show_analyzer(self):
        """显示性能分析器对话框"""
        dialog = PerfAnalyzerDialog(self.main_window)
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
        # 非模态显示对话框
        dialog.show()

class PerfAnalyzerDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("仿真性能分析")
        self.resize(800, 600)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 添加文件选择区域
        file_layout = QHBoxLayout()
        select_btn = QPushButton("选择日志文件")
        select_btn.clicked.connect(self.select_log_file)
        self.file_label = QLabel("未选择文件")
        file_layout.addWidget(select_btn)
        file_layout.addWidget(self.file_label, stretch=1)

        # 添加性能树
        self.perf_tree = QTreeWidget()
        self.perf_tree.setHeaderLabels(["模块/操作", "耗时(秒)", "占比"])
        self.perf_tree.setAlternatingRowColors(True)

        # 添加分析结果文本框
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)

        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        layout.addLayout(file_layout)
        layout.addWidget(self.perf_tree)
        layout.addWidget(self.result_text)
        layout.addWidget(self.progress_bar)

        self.setLayout(layout)

    def select_log_file(self):
        """选择日志文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择日志文件",
            "",
            "日志文件 (*.log);;所有文件 (*.*)"
        )
        if file_path:
            self.file_label.setText(file_path)
            self.analyze_performance(file_path)

    def analyze_performance(self, log_file):
        """分析性能数据"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.perf_tree.clear()
            self.result_text.clear()

            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            self.progress_bar.setValue(30)

            # 分析总仿真时间
            total_time = 0
            if match := re.search(r'Elapsed time\s*:\s*(\d+)\s*sec', content):
                total_time = int(match.group(1))

            # 分析各模块耗时
            module_times = {}
            module_pattern = r'(\w+)\s+module\s+simulation\s+time:\s+(\d+)\s*sec'
            for match in re.finditer(module_pattern, content):
                module = match.group(1)
                time = int(match.group(2))
                module_times[module] = time

            self.progress_bar.setValue(60)

            # 分析关键操作耗时
            operation_times = {}
            operation_pattern = r'(\w+)\s+operation\s+time:\s+(\d+)\s*sec'
            for match in re.finditer(operation_pattern, content):
                operation = match.group(1)
                time = int(match.group(2))
                operation_times[operation] = time

            self.progress_bar.setValue(80)

            # 更新性能树
            if total_time > 0:
                # 添加模块耗时
                modules_item = QTreeWidgetItem(["模块耗时统计"])
                self.perf_tree.addTopLevelItem(modules_item)
                for module, time in sorted(module_times.items(), key=lambda x: x[1], reverse=True):
                    percent = (time / total_time) * 100
                    item = QTreeWidgetItem([
                        module,
                        f"{time}",
                        f"{percent:.1f}%"
                    ])
                    modules_item.addChild(item)

                # 添加操作耗时
                operations_item = QTreeWidgetItem(["操作耗时统计"])
                self.perf_tree.addTopLevelItem(operations_item)
                for operation, time in sorted(operation_times.items(), key=lambda x: x[1], reverse=True):
                    percent = (time / total_time) * 100
                    item = QTreeWidgetItem([
                        operation,
                        f"{time}",
                        f"{percent:.1f}%"
                    ])
                    operations_item.addChild(item)

            # 生成分析报告
            self.generate_report(total_time, module_times, operation_times)

            self.progress_bar.setValue(100)
            self.progress_bar.setVisible(False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"分析性能数据失败: {str(e)}")
            self.progress_bar.setVisible(False)

    def generate_report(self, total_time, module_times, operation_times):
        """生成性能分析报告"""
        report = []
        report.append("性能分析报告")
        report.append("=" * 40)
        report.append(f"\n总仿真时间: {total_time} 秒")

        # 添加模块耗时分析
        if module_times:
            report.append("\n模块耗时分析:")
            report.append("-" * 20)
            for module, time in sorted(module_times.items(), key=lambda x: x[1], reverse=True):
                percent = (time / total_time) * 100
                report.append(f"{module}: {time}秒 ({percent:.1f}%)")

        # 添加操作耗时分析
        if operation_times:
            report.append("\n操作耗时分析:")
            report.append("-" * 20)
            for operation, time in sorted(operation_times.items(), key=lambda x: x[1], reverse=True):
                percent = (time / total_time) * 100
                report.append(f"{operation}: {time}秒 ({percent:.1f}%)")

        # 性能建议
        report.append("\n性能优化建议:")
        report.append("-" * 20)

        # 根据耗时分析生成建议
        if module_times:
            max_module = max(module_times.items(), key=lambda x: x[1])
            if (max_module[1] / total_time) > 0.5:
                report.append(f"- 模块 {max_module[0]} 耗时占比过高，建议优化其性能")

        report.append("- 考虑使用并行仿真以提高性能")
        report.append("- 检查是否有不必要的信号dump")
        report.append("- 优化仿真步长设置")

        self.result_text.setText("\n".join(report))