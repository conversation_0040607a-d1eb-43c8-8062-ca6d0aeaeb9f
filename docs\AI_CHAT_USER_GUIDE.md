# AI聊天助手使用指南

## 启动应用

```bash
python start_with_ai_chat.py
```

## 主要功能

### 1. 多会话管理 ✨

**创建新对话：**
- 点击"新建聊天"按钮
- 每次都会创建一个全新的独立会话
- 之前的聊天记录不会丢失

**切换会话：**
- 在左侧会话列表中点击任意会话
- 自动加载该会话的完整聊天记录
- 当前会话会高亮显示

**删除会话：**
- 右键点击会话或使用删除功能
- 删除后自动切换到其他会话
- 确保始终有活跃的会话

### 2. 智能对话 🤖

**发送消息：**
- 在输入框中输入消息
- 按回车键发送（Shift+回车换行）
- 支持多行文本输入

**模型配置：**
- 点击"设置"按钮
- 选择AI模型提供商（DeepSeek、Open Router等）
- 配置API Key和相关参数
- 模型切换立即生效

**当前配置：**
- 模型：`deepseek/deepseek-r1-0528-qwen3-8b:free`
- 提供商：Open Router
- 支持免费使用

### 3. 翻译助手 🌐

**打开翻译功能：**
- 点击"翻译助手"按钮
- 打开专用翻译对话框

**翻译选项：**
- 中文 → 英文
- 英文 → 中文  
- 自动检测（推荐）

**使用方法：**
1. 在左侧输入框输入要翻译的文本
2. 选择翻译方向
3. 点击"开始翻译"
4. 右侧显示翻译结果

**高级功能：**
- 📄 上传文档翻译（支持txt、md、docx、pdf）
- 📋 一键复制译文
- 💾 保存翻译结果
- ⚙️ 保持格式选项

### 4. 文档处理 📄

**支持格式：**
- PDF文档
- Word文档（.docx/.doc）
- 文本文件（.txt）
- Markdown文件（.md）

**上传方式：**
- 拖拽文件到文档区域
- 点击"上传文档"按钮选择文件
- 通过翻译助手上传

**文档功能：**
- 自动解析文档内容
- 生成文档摘要
- AI可基于文档内容回答问题
- 支持多文档同时处理

## 界面说明

### 主界面布局

```
┌─────────────────────────────────────────────────┐
│ 工具栏：设置 | 清空聊天                          │
├─────────────┬───────────────────────────────────┤
│             │                                   │
│  会话列表   │        聊天消息区域                │
│             │                                   │
│  - 新对话   │  [用户消息气泡]                   │
│  - 历史1    │  [AI回复气泡]                     │
│  - 历史2    │  [用户消息气泡]                   │
│             │  [AI回复气泡]                     │
├─────────────┼───────────────────────────────────┤
│             │  输入框                           │
│  文档列表   │  [翻译助手] [停止] [发送]         │
│             │                                   │
└─────────────┴───────────────────────────────────┘
```

### 翻译助手界面

```
┌─────────────────────────────────────────────────┐
│              AI翻译助手                          │
├─────────────────────────────────────────────────┤
│ 翻译方向: [中文→英文 ▼]                         │
├─────────────────┬───────────────────────────────┤
│     原文        │           译文                │
│                 │                               │
│ [输入文本...]   │ [翻译结果显示...]             │
│                 │                               │
│ [清空][上传文档] │ [复制译文][保存译文]          │
├─────────────────┴───────────────────────────────┤
│ ☑ 保持格式                                      │
│ [开始翻译]                           [关闭]     │
└─────────────────────────────────────────────────┘
```

## 快速开始

### 第一次使用

1. **配置API**
   ```
   设置 → 选择模型提供商 → 输入API Key → 保存
   ```

2. **开始对话**
   ```
   输入消息 → 按回车发送 → 查看AI回复
   ```

3. **尝试翻译**
   ```
   翻译助手 → 输入文本 → 开始翻译 → 查看结果
   ```

### 常用操作

- **新建对话**: 随时点击"新建聊天"创建独立会话
- **切换会话**: 点击左侧会话列表中的任意会话
- **上传文档**: 拖拽文件或点击上传按钮
- **翻译文本**: 使用专用翻译助手界面
- **清空聊天**: 工具栏 → 清空聊天（仅清空当前会话）

## 故障排除

### 常见问题

**Q: AI不回复或回复错误？**
A: 检查设置中的API配置，确保API Key有效且模型可用

**Q: 历史记录丢失？**
A: 检查`ai_chat_history.json`文件是否存在，重启应用尝试恢复

**Q: 翻译功能不工作？**
A: 确保网络连接正常，API配置正确

**Q: 文档上传失败？**
A: 检查文件格式是否支持，文件大小是否超过50MB限制

### 调试信息

应用运行时会在控制台输出调试信息：
- `[DEBUG]` 开头的是调试信息
- `[ERROR]` 开头的是错误信息
- `[WARNING]` 开头的是警告信息

## 技术支持

如遇到问题，请：
1. 查看控制台输出的错误信息
2. 检查配置文件是否正确
3. 尝试重启应用
4. 联系技术支持并提供错误日志

---

**版本**: v1.1.0  
**更新日期**: 2025-01-12  
**修复内容**: 历史记录显示、模型切换、多会话管理、翻译功能
