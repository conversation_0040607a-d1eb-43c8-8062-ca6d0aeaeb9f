"""
AI模型性能监控器

监控模型的响应时间、内存使用、GPU使用率等性能指标
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import json
import os


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    model_name: str
    response_time: float  # 响应时间（秒）
    tokens_per_second: float  # 每秒生成的token数
    memory_usage: float  # 内存使用量（MB）
    cpu_usage: float  # CPU使用率（%）
    gpu_usage: Optional[float] = None  # GPU使用率（%）
    gpu_memory: Optional[float] = None  # GPU内存使用量（MB）
    request_count: int = 1  # 请求数量
    error_count: int = 0  # 错误数量
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PerformanceMetrics':
        """从字典创建"""
        return cls(**data)


class ModelPerformanceMonitor(QObject):
    """模型性能监控器"""
    
    # 信号
    metrics_updated = pyqtSignal(object)  # PerformanceMetrics
    alert_triggered = pyqtSignal(str, str)  # alert_type, message
    
    def __init__(self):
        super().__init__()
        self.metrics_history: List[PerformanceMetrics] = []
        self.current_metrics: Dict[str, PerformanceMetrics] = {}
        self.monitoring_active = False
        self.monitor_timer = None  # 延迟创建定时器
        self.monitor_interval = 5000  # 5秒

        # 性能阈值
        self.thresholds = {
            'response_time': 10.0,  # 响应时间超过10秒
            'memory_usage': 8192,   # 内存使用超过8GB
            'cpu_usage': 90.0,      # CPU使用率超过90%
            'gpu_usage': 95.0,      # GPU使用率超过95%
            'error_rate': 0.1       # 错误率超过10%
        }

        # 数据文件
        self.metrics_file = "model_performance_metrics.json"
        self.load_metrics_history()
    
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring_active:
            try:
                # 确保在主线程中创建定时器
                if self.monitor_timer is None:
                    self.monitor_timer = QTimer()
                    self.monitor_timer.timeout.connect(self._collect_system_metrics)

                self.monitoring_active = True
                self.monitor_timer.start(self.monitor_interval)
                print("[监控] 性能监控已启动")
            except Exception as e:
                print(f"[ERROR] 启动性能监控失败: {e}")
                self.monitoring_active = False
    
    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring_active:
            try:
                self.monitoring_active = False
                if self.monitor_timer and self.monitor_timer.isActive():
                    self.monitor_timer.stop()
                self.save_metrics_history()
                print("[监控] 性能监控已停止")
            except Exception as e:
                print(f"[ERROR] 停止性能监控失败: {e}")
                self.monitoring_active = False
    
    def record_request_start(self, model_name: str) -> str:
        """记录请求开始"""
        request_id = f"{model_name}_{time.time()}"
        self.current_metrics[request_id] = PerformanceMetrics(
            timestamp=time.time(),
            model_name=model_name,
            response_time=0.0,
            tokens_per_second=0.0,
            memory_usage=self._get_memory_usage(),
            cpu_usage=self._get_cpu_usage()
        )
        return request_id
    
    def record_request_end(self, request_id: str, token_count: int = 0, error: bool = False):
        """记录请求结束"""
        if request_id not in self.current_metrics:
            return
        
        metrics = self.current_metrics[request_id]
        end_time = time.time()
        response_time = end_time - metrics.timestamp
        
        # 更新指标
        metrics.response_time = response_time
        if token_count > 0 and response_time > 0:
            metrics.tokens_per_second = token_count / response_time
        metrics.memory_usage = self._get_memory_usage()
        metrics.cpu_usage = self._get_cpu_usage()
        metrics.gpu_usage = self._get_gpu_usage()
        metrics.gpu_memory = self._get_gpu_memory()
        
        if error:
            metrics.error_count = 1
        
        # 添加到历史记录
        self.metrics_history.append(metrics)
        
        # 检查性能阈值
        self._check_thresholds(metrics)
        
        # 发送信号
        self.metrics_updated.emit(metrics)
        
        # 清理当前指标
        del self.current_metrics[request_id]
        
        # 限制历史记录数量
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
    
    def _collect_system_metrics(self):
        """收集系统性能指标"""
        if not self.monitoring_active:
            return
        
        # 创建系统指标记录
        system_metrics = PerformanceMetrics(
            timestamp=time.time(),
            model_name="system",
            response_time=0.0,
            tokens_per_second=0.0,
            memory_usage=self._get_memory_usage(),
            cpu_usage=self._get_cpu_usage(),
            gpu_usage=self._get_gpu_usage(),
            gpu_memory=self._get_gpu_memory(),
            request_count=0
        )
        
        # 检查系统性能阈值
        self._check_thresholds(system_metrics)
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            return psutil.cpu_percent(interval=0.1)
        except:
            return 0.0
    
    def _get_gpu_usage(self) -> Optional[float]:
        """获取GPU使用率"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                return gpus[0].load * 100
        except:
            pass
        return None
    
    def _get_gpu_memory(self) -> Optional[float]:
        """获取GPU内存使用量（MB）"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                return gpus[0].memoryUsed
        except:
            pass
        return None
    
    def _check_thresholds(self, metrics: PerformanceMetrics):
        """检查性能阈值"""
        alerts = []
        
        # 检查响应时间
        if metrics.response_time > self.thresholds['response_time']:
            alerts.append(f"响应时间过长: {metrics.response_time:.2f}秒")
        
        # 检查内存使用
        if metrics.memory_usage > self.thresholds['memory_usage']:
            alerts.append(f"内存使用过高: {metrics.memory_usage:.1f}MB")
        
        # 检查CPU使用率
        if metrics.cpu_usage > self.thresholds['cpu_usage']:
            alerts.append(f"CPU使用率过高: {metrics.cpu_usage:.1f}%")
        
        # 检查GPU使用率
        if metrics.gpu_usage and metrics.gpu_usage > self.thresholds['gpu_usage']:
            alerts.append(f"GPU使用率过高: {metrics.gpu_usage:.1f}%")
        
        # 发送警报
        for alert in alerts:
            self.alert_triggered.emit("performance", f"[{metrics.model_name}] {alert}")
    
    def get_model_statistics(self, model_name: str, hours: int = 24) -> Dict[str, Any]:
        """获取模型统计信息"""
        cutoff_time = time.time() - (hours * 3600)
        model_metrics = [
            m for m in self.metrics_history 
            if m.model_name == model_name and m.timestamp > cutoff_time
        ]
        
        if not model_metrics:
            return {}
        
        # 计算统计信息
        response_times = [m.response_time for m in model_metrics if m.response_time > 0]
        token_rates = [m.tokens_per_second for m in model_metrics if m.tokens_per_second > 0]
        memory_usage = [m.memory_usage for m in model_metrics]
        cpu_usage = [m.cpu_usage for m in model_metrics]
        
        total_requests = sum(m.request_count for m in model_metrics)
        total_errors = sum(m.error_count for m in model_metrics)
        error_rate = total_errors / total_requests if total_requests > 0 else 0
        
        return {
            'model_name': model_name,
            'time_period_hours': hours,
            'total_requests': total_requests,
            'total_errors': total_errors,
            'error_rate': error_rate,
            'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
            'max_response_time': max(response_times) if response_times else 0,
            'min_response_time': min(response_times) if response_times else 0,
            'avg_tokens_per_second': sum(token_rates) / len(token_rates) if token_rates else 0,
            'avg_memory_usage': sum(memory_usage) / len(memory_usage) if memory_usage else 0,
            'max_memory_usage': max(memory_usage) if memory_usage else 0,
            'avg_cpu_usage': sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0,
            'max_cpu_usage': max(cpu_usage) if cpu_usage else 0
        }
    
    def get_all_models_statistics(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取所有模型的统计信息"""
        cutoff_time = time.time() - (hours * 3600)
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_time]
        
        model_names = set(m.model_name for m in recent_metrics if m.model_name != "system")
        
        return [self.get_model_statistics(name, hours) for name in model_names]
    
    def save_metrics_history(self):
        """保存性能指标历史"""
        try:
            data = {
                'metrics': [m.to_dict() for m in self.metrics_history[-1000:]],  # 只保存最近1000条
                'thresholds': self.thresholds
            }
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存性能指标失败: {e}")
    
    def load_metrics_history(self):
        """加载性能指标历史"""
        try:
            if os.path.exists(self.metrics_file):
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.metrics_history = [
                    PerformanceMetrics.from_dict(m) for m in data.get('metrics', [])
                ]
                
                # 加载阈值设置
                saved_thresholds = data.get('thresholds', {})
                self.thresholds.update(saved_thresholds)
                
        except Exception as e:
            print(f"加载性能指标失败: {e}")
    
    def set_threshold(self, metric_name: str, value: float):
        """设置性能阈值"""
        if metric_name in self.thresholds:
            self.thresholds[metric_name] = value
            self.save_metrics_history()
    
    def clear_history(self):
        """清空历史记录"""
        self.metrics_history.clear()
        if os.path.exists(self.metrics_file):
            os.remove(self.metrics_file)
