"""
通用对话框基类
提供统一的对话框关闭事件处理
"""

from PyQt5.QtWidgets import QDialog
from PyQt5.QtCore import Qt


class PluginDialogBase(QDialog):
    """
    插件对话框基类
    
    自动处理关闭事件，确保finished信号正确发出
    """
    
    def __init__(self, parent=None):
        """初始化对话框"""
        super().__init__(parent)
        
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
    
    def closeEvent(self, event):
        """
        关闭事件处理
        
        Args:
            event (QCloseEvent): 关闭事件
        """
        # 发出finished信号，通知插件对话框已关闭
        self.finished.emit(0)
        event.accept()
