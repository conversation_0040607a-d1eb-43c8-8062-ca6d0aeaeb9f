"""
文件差异计算模型

提供文件差异计算和数据结构定义，包括：
- 差异行数据结构
- 统一格式差异计算
- 并排格式差异计算
- 差异统计信息
"""

import difflib
from typing import List, Optional, NamedTuple, Tuple
from enum import Enum


class DiffLineType(Enum):
    """差异行类型枚举"""
    CONTEXT = "context"      # 上下文行（无变化）
    ADD = "add"             # 新增行
    DELETE = "delete"       # 删除行
    MODIFY = "modify"       # 修改行（用于并排显示）


class DiffLine(NamedTuple):
    """差异行数据结构"""
    line_type: DiffLineType
    old_line_no: Optional[int]  # 旧文件行号
    new_line_no: Optional[int]  # 新文件行号
    content: str                # 行内容
    
    def __str__(self):
        """字符串表示"""
        type_char = {
            DiffLineType.CONTEXT: " ",
            DiffLineType.ADD: "+",
            DiffLineType.DELETE: "-",
            DiffLineType.MODIFY: "~"
        }
        return f"{type_char[self.line_type]} {self.content}"


class DiffStats:
    """差异统计信息"""
    
    def __init__(self):
        self.added_lines = 0      # 新增行数
        self.deleted_lines = 0    # 删除行数
        self.modified_lines = 0   # 修改行数
        self.context_lines = 0    # 上下文行数
        self.total_changes = 0    # 总变更数
    
    def update_from_diff_lines(self, diff_lines: List[DiffLine]):
        """从差异行列表更新统计信息"""
        self.added_lines = 0
        self.deleted_lines = 0
        self.modified_lines = 0
        self.context_lines = 0
        
        for line in diff_lines:
            if line.line_type == DiffLineType.ADD:
                self.added_lines += 1
            elif line.line_type == DiffLineType.DELETE:
                self.deleted_lines += 1
            elif line.line_type == DiffLineType.MODIFY:
                self.modified_lines += 1
            elif line.line_type == DiffLineType.CONTEXT:
                self.context_lines += 1
        
        self.total_changes = self.added_lines + self.deleted_lines + self.modified_lines
    
    def __str__(self):
        """字符串表示"""
        return f"+{self.added_lines} -{self.deleted_lines} ~{self.modified_lines}"


class FileDiff:
    """文件差异计算类"""
    
    def __init__(self, old_content: str = "", new_content: str = "", 
                 old_filename: str = "旧版本", new_filename: str = "新版本"):
        """初始化文件差异计算器
        
        Args:
            old_content: 旧文件内容
            new_content: 新文件内容
            old_filename: 旧文件名
            new_filename: 新文件名
        """
        self.old_content = old_content
        self.new_content = new_content
        self.old_filename = old_filename
        self.new_filename = new_filename
        
        self.old_lines = old_content.splitlines() if old_content else []
        self.new_lines = new_content.splitlines() if new_content else []
        
        self.stats = DiffStats()
    
    def calculate_unified_diff(self, context_lines: int = 3) -> List[str]:
        """计算统一格式差异
        
        Args:
            context_lines: 上下文行数
            
        Returns:
            统一格式差异行列表
        """
        try:
            # 为行添加换行符以便difflib处理
            old_lines_with_newline = [line + '\n' for line in self.old_lines]
            new_lines_with_newline = [line + '\n' for line in self.new_lines]
            
            diff = difflib.unified_diff(
                old_lines_with_newline,
                new_lines_with_newline,
                fromfile=self.old_filename,
                tofile=self.new_filename,
                n=context_lines,
                lineterm=''
            )
            
            return list(diff)
            
        except Exception as e:
            print(f"计算统一格式差异失败: {e}")
            return []
    
    def calculate_side_by_side_diff(self) -> List[DiffLine]:
        """计算并排格式差异
        
        Returns:
            差异行列表
        """
        try:
            differ = difflib.SequenceMatcher(None, self.old_lines, self.new_lines)
            diff_lines = []
            
            old_line_no = 1
            new_line_no = 1
            
            for tag, i1, i2, j1, j2 in differ.get_opcodes():
                if tag == 'equal':
                    # 相同的行
                    for i in range(i1, i2):
                        diff_lines.append(DiffLine(
                            DiffLineType.CONTEXT,
                            old_line_no,
                            new_line_no,
                            self.old_lines[i]
                        ))
                        old_line_no += 1
                        new_line_no += 1
                        
                elif tag == 'delete':
                    # 删除的行
                    for i in range(i1, i2):
                        diff_lines.append(DiffLine(
                            DiffLineType.DELETE,
                            old_line_no,
                            None,
                            self.old_lines[i]
                        ))
                        old_line_no += 1
                        
                elif tag == 'insert':
                    # 插入的行
                    for j in range(j1, j2):
                        diff_lines.append(DiffLine(
                            DiffLineType.ADD,
                            None,
                            new_line_no,
                            self.new_lines[j]
                        ))
                        new_line_no += 1
                        
                elif tag == 'replace':
                    # 替换的行 - 先处理删除，再处理插入
                    old_count = i2 - i1
                    new_count = j2 - j1
                    
                    # 处理删除的行
                    for i in range(i1, i2):
                        diff_lines.append(DiffLine(
                            DiffLineType.DELETE,
                            old_line_no,
                            None,
                            self.old_lines[i]
                        ))
                        old_line_no += 1
                    
                    # 处理插入的行
                    for j in range(j1, j2):
                        diff_lines.append(DiffLine(
                            DiffLineType.ADD,
                            None,
                            new_line_no,
                            self.new_lines[j]
                        ))
                        new_line_no += 1
            
            # 更新统计信息
            self.stats.update_from_diff_lines(diff_lines)
            
            return diff_lines
            
        except Exception as e:
            print(f"计算并排格式差异失败: {e}")
            return []
    
    def get_diff_hunks(self, context_lines: int = 3) -> List[Tuple[int, int, int, int, List[DiffLine]]]:
        """获取差异块（hunks）
        
        Args:
            context_lines: 上下文行数
            
        Returns:
            差异块列表，每个元素为 (old_start, old_count, new_start, new_count, lines)
        """
        try:
            differ = difflib.SequenceMatcher(None, self.old_lines, self.new_lines)
            hunks = []
            
            for tag, i1, i2, j1, j2 in differ.get_opcodes():
                if tag != 'equal':  # 只处理有变化的块
                    # 计算上下文范围
                    old_start = max(0, i1 - context_lines)
                    old_end = min(len(self.old_lines), i2 + context_lines)
                    new_start = max(0, j1 - context_lines)
                    new_end = min(len(self.new_lines), j2 + context_lines)
                    
                    # 构建差异行
                    hunk_lines = []
                    
                    # 前置上下文
                    for i in range(old_start, i1):
                        if i < len(self.old_lines):
                            hunk_lines.append(DiffLine(
                                DiffLineType.CONTEXT,
                                i + 1,
                                new_start + (i - old_start) + 1,
                                self.old_lines[i]
                            ))
                    
                    # 变更内容
                    if tag == 'delete':
                        for i in range(i1, i2):
                            hunk_lines.append(DiffLine(
                                DiffLineType.DELETE,
                                i + 1,
                                None,
                                self.old_lines[i]
                            ))
                    elif tag == 'insert':
                        for j in range(j1, j2):
                            hunk_lines.append(DiffLine(
                                DiffLineType.ADD,
                                None,
                                j + 1,
                                self.new_lines[j]
                            ))
                    elif tag == 'replace':
                        for i in range(i1, i2):
                            hunk_lines.append(DiffLine(
                                DiffLineType.DELETE,
                                i + 1,
                                None,
                                self.old_lines[i]
                            ))
                        for j in range(j1, j2):
                            hunk_lines.append(DiffLine(
                                DiffLineType.ADD,
                                None,
                                j + 1,
                                self.new_lines[j]
                            ))
                    
                    # 后置上下文
                    for i in range(i2, old_end):
                        if i < len(self.old_lines):
                            hunk_lines.append(DiffLine(
                                DiffLineType.CONTEXT,
                                i + 1,
                                new_end - (old_end - i) + 1,
                                self.old_lines[i]
                            ))
                    
                    hunks.append((
                        old_start + 1,  # 行号从1开始
                        old_end - old_start,
                        new_start + 1,
                        new_end - new_start,
                        hunk_lines
                    ))
            
            return hunks
            
        except Exception as e:
            print(f"获取差异块失败: {e}")
            return []
    
    def has_differences(self) -> bool:
        """检查是否有差异"""
        return self.old_content != self.new_content
    
    def get_stats(self) -> DiffStats:
        """获取差异统计信息"""
        if self.stats.total_changes == 0:
            # 如果还没有计算过，先计算一次
            self.calculate_side_by_side_diff()
        return self.stats
