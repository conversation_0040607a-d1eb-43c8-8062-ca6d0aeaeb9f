"""
性能监控模块
"""
import time
import psutil
import os
import threading
import random
from collections import deque
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

class PerformanceMetrics:
    """性能指标数据类"""
    def __init__(self):
        """初始化性能指标"""
        self.cpu_usage = 0.0  # CPU使用率（百分比）
        self.memory_usage = 0.0  # 内存使用率（百分比）
        self.memory_used = 0  # 已使用内存（MB）
        self.ui_response_time = 0.0  # UI响应时间（毫秒）
        self.event_processing_time = 0.0  # 事件处理时间（毫秒）
        self.render_time = 0.0  # 渲染时间（毫秒）
        self.log_processing_rate = 0.0  # 日志处理速率（行/秒）
        self.timestamp = time.time()  # 时间戳

    def to_dict(self):
        """转换为字典"""
        return {
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'memory_used': self.memory_used,
            'ui_response_time': self.ui_response_time,
            'event_processing_time': self.event_processing_time,
            'render_time': self.render_time,
            'log_processing_rate': self.log_processing_rate,
            'timestamp': self.timestamp
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建实例"""
        metrics = cls()
        metrics.cpu_usage = data.get('cpu_usage', 0.0)
        metrics.memory_usage = data.get('memory_usage', 0.0)
        metrics.memory_used = data.get('memory_used', 0)
        metrics.ui_response_time = data.get('ui_response_time', 0.0)
        metrics.event_processing_time = data.get('event_processing_time', 0.0)
        metrics.render_time = data.get('render_time', 0.0)
        metrics.log_processing_rate = data.get('log_processing_rate', 0.0)
        metrics.timestamp = data.get('timestamp', time.time())
        return metrics

class PerformanceMonitor(QObject):
    """性能监控器，用于实时监控界面响应性能（全局单例优化版本）"""

    # 定义信号
    metrics_updated = pyqtSignal(object)  # 性能指标更新信号
    performance_alert = pyqtSignal(str, object)  # 性能警报信号（警报消息，性能指标）

    # 常量定义 - 优化后的参数
    DEFAULT_INTERVAL = 5.0  # 默认监控间隔（秒）- 从2秒增加到5秒
    HISTORY_SIZE = 30  # 历史记录大小（保存最近30个采样点）- 从60减少到30
    CPU_ALERT_THRESHOLD = 85.0  # CPU使用率警报阈值（百分比）- 提高阈值
    MEMORY_ALERT_THRESHOLD = 85.0  # 内存使用率警报阈值（百分比）- 提高阈值
    UI_RESPONSE_ALERT_THRESHOLD = 200.0  # UI响应时间警报阈值（毫秒）- 提高阈值

    # 全局单例相关
    _instance = None
    _lock = threading.Lock()

    def __init__(self):
        """初始化性能监控器"""
        super().__init__()
        self._metrics_history = deque(maxlen=self.HISTORY_SIZE)  # 性能指标历史记录
        self._thread_lock = threading.Lock()  # 线程锁
        self._running = False  # 运行标志
        self._thread = None  # 监控线程
        self._update_interval = self.DEFAULT_INTERVAL  # 更新间隔（秒）
        self._process = None  # 延迟初始化进程对象
        self._ui_event_times = {}  # UI事件时间记录
        self._render_times = {}  # 渲染时间记录
        self._log_processing_stats = {
            'last_time': time.time(),
            'last_count': 0,
            'total_count': 0
        }  # 日志处理统计

        # 警报冷却机制 - 优化参数
        self._alert_cooldowns = {
            'cpu': 0,
            'memory': 0,
            'ui': 0
        }  # 警报冷却时间（秒）
        self._alert_cooldown_period = 120  # 警报冷却周期（秒）- 从60秒增加到120秒
        self._alerts_enabled = True  # 警报启用标志

    @classmethod
    def instance(cls):
        """获取全局单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    @classmethod
    def cleanup_instance(cls):
        """清理全局单例实例"""
        if cls._instance is not None:
            with cls._lock:
                if cls._instance is not None:
                    cls._instance.stop_monitoring()
                    cls._instance = None

    def start_monitoring(self, interval=None):
        """
        开始监控性能

        Args:
            interval (float, optional): 监控间隔（秒）
        """
        if self._running:
            return

        if interval is not None:
            self._update_interval = interval

        # 延迟初始化进程对象，减少启动开销
        if self._process is None:
            try:
                self._process = psutil.Process(os.getpid())
            except Exception:
                self._process = None

        self._running = True
        self._thread = threading.Thread(target=self._monitor_performance, daemon=True)
        self._thread.start()

    def stop_monitoring(self):
        """停止监控性能"""
        self._running = False
        # 只在非当前线程时尝试join，避免"cannot join current thread"错误
        if self._thread and self._thread.is_alive() and self._thread is not threading.current_thread():
            try:
                self._thread.join(timeout=1.0)
            except RuntimeError:
                # 忽略"cannot join current thread"错误
                pass

    def get_current_metrics(self):
        """
        获取当前性能指标

        Returns:
            PerformanceMetrics: 当前性能指标
        """
        with self._lock:
            if not self._metrics_history:
                return PerformanceMetrics()
            return self._metrics_history[-1]

    def get_metrics_history(self):
        """
        获取性能指标历史记录

        Returns:
            list: 性能指标历史记录
        """
        with self._lock:
            return list(self._metrics_history)

    def clear_history(self):
        """
        清除性能指标历史记录
        """
        with self._lock:
            self._metrics_history.clear()

    def record_ui_event_start(self, event_id):
        """
        记录UI事件开始时间

        Args:
            event_id (str): 事件ID
        """
        self._ui_event_times[event_id] = time.time()

    def record_ui_event_end(self, event_id):
        """
        记录UI事件结束时间并计算响应时间

        Args:
            event_id (str): 事件ID

        Returns:
            float: UI响应时间（毫秒）
        """
        if event_id not in self._ui_event_times:
            return 0.0

        start_time = self._ui_event_times.pop(event_id)
        response_time = (time.time() - start_time) * 1000.0  # 转换为毫秒

        # 更新当前指标
        with self._lock:
            if self._metrics_history:
                self._metrics_history[-1].ui_response_time = response_time

        return response_time

    def record_render_start(self, widget_id):
        """
        记录渲染开始时间

        Args:
            widget_id (str): 控件ID
        """
        self._render_times[widget_id] = time.time()

    def record_render_end(self, widget_id):
        """
        记录渲染结束时间并计算渲染时间

        Args:
            widget_id (str): 控件ID

        Returns:
            float: 渲染时间（毫秒）
        """
        if widget_id not in self._render_times:
            return 0.0

        start_time = self._render_times.pop(widget_id)
        render_time = (time.time() - start_time) * 1000.0  # 转换为毫秒

        # 更新当前指标
        with self._lock:
            if self._metrics_history:
                self._metrics_history[-1].render_time = render_time

        return render_time

    def record_log_processing(self, line_count):
        """
        记录日志处理行数

        Args:
            line_count (int): 处理的日志行数
        """
        current_time = time.time()

        with self._lock:
            # 更新统计信息
            self._log_processing_stats['total_count'] += line_count

            # 计算处理速率
            time_diff = current_time - self._log_processing_stats['last_time']
            if time_diff >= 1.0:  # 至少1秒计算一次速率
                count_diff = self._log_processing_stats['total_count'] - self._log_processing_stats['last_count']
                rate = count_diff / time_diff

                # 更新当前指标
                if self._metrics_history:
                    self._metrics_history[-1].log_processing_rate = rate

                # 更新统计信息
                self._log_processing_stats['last_time'] = current_time
                self._log_processing_stats['last_count'] = self._log_processing_stats['total_count']

    def _monitor_performance(self):
        """监控性能的后台线程 - 优化版本"""
        last_time = time.time()
        sample_count = 0

        while self._running:
            try:
                current_time = time.time()
                sample_count += 1

                # 创建新的性能指标
                metrics = PerformanceMetrics()

                # 简化的性能指标收集
                if self._process:
                    try:
                        # 获取CPU使用率（降低精度以减少开销）
                        metrics.cpu_usage = self._process.cpu_percent()

                        # 获取内存使用情况
                        memory_info = self._process.memory_info()
                        metrics.memory_used = memory_info.rss / (1024 * 1024)  # 转换为MB
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        # 进程可能已经结束，使用默认值
                        metrics.cpu_usage = 0.0
                        metrics.memory_used = 0.0

                # 使用系统内存使用率（降低采样频率）
                if sample_count % 2 == 0:  # 每两次采样更新一次系统内存
                    try:
                        system_memory = psutil.virtual_memory()
                        metrics.memory_usage = system_memory.percent
                    except Exception:
                        metrics.memory_usage = 0.0
                else:
                    # 使用上一次的值
                    with self._thread_lock:
                        if self._metrics_history:
                            metrics.memory_usage = self._metrics_history[-1].memory_usage
                        else:
                            metrics.memory_usage = 0.0

                # 简化的UI响应时间计算
                base_response_time = 15.0  # 固定基础响应时间
                cpu_impact = (metrics.cpu_usage / 100.0) * 50.0  # 减少CPU影响
                memory_impact = (metrics.memory_usage / 100.0) * 25.0  # 减少内存影响

                # 计算最终UI响应时间（限制在10-150ms范围内）
                ui_response_raw = base_response_time + cpu_impact + memory_impact
                metrics.ui_response_time = max(10.0, min(ui_response_raw, 150.0))

                # 简化的渲染时间计算
                metrics.render_time = metrics.ui_response_time * 0.7

                # 设置时间戳
                metrics.timestamp = current_time

                # 添加到历史记录
                with self._thread_lock:
                    self._metrics_history.append(metrics)

                # 降低信号发射频率（每3次采样发射一次）
                if sample_count % 3 == 0:
                    self.metrics_updated.emit(metrics)

                # 降低警报检查频率（每5次采样检查一次）
                if sample_count % 5 == 0:
                    self._check_alerts_optimized(metrics)

            except Exception as e:
                # 减少错误输出频率
                if sample_count % 20 == 0:
                    print(f"性能监控错误: {str(e)}")

            # 等待下一次更新
            time.sleep(self._update_interval)

    def enable_alerts(self, enabled=True):
        """
        启用或禁用性能警报

        Args:
            enabled (bool): 是否启用警报
        """
        self._alerts_enabled = enabled

    def set_alert_cooldown_period(self, seconds):
        """
        设置警报冷却周期

        Args:
            seconds (int): 冷却周期（秒）
        """
        if seconds > 0:
            self._alert_cooldown_period = seconds

    def reset_alert_cooldowns(self):
        """重置所有警报冷却时间"""
        current_time = time.time()
        for key in self._alert_cooldowns:
            self._alert_cooldowns[key] = current_time - self._alert_cooldown_period

    def _check_alerts(self, metrics):
        """
        检查是否需要发出性能警报

        Args:
            metrics (PerformanceMetrics): 当前性能指标
        """
        # 如果警报被禁用，直接返回
        if not self._alerts_enabled:
            return

        current_time = time.time()

        # 检查CPU使用率
        if (metrics.cpu_usage > self.CPU_ALERT_THRESHOLD and
            current_time - self._alert_cooldowns['cpu'] > self._alert_cooldown_period):
            self.performance_alert.emit(
                f"CPU使用率过高: {metrics.cpu_usage:.1f}%",
                metrics
            )
            self._alert_cooldowns['cpu'] = current_time

        # 检查内存使用率
        if (metrics.memory_usage > self.MEMORY_ALERT_THRESHOLD and
            current_time - self._alert_cooldowns['memory'] > self._alert_cooldown_period):
            self.performance_alert.emit(
                f"内存使用率过高: {metrics.memory_usage:.1f}%",
                metrics
            )
            self._alert_cooldowns['memory'] = current_time

        # 检查UI响应时间
        if (metrics.ui_response_time > self.UI_RESPONSE_ALERT_THRESHOLD and
            current_time - self._alert_cooldowns['ui'] > self._alert_cooldown_period):
            self.performance_alert.emit(
                f"UI响应时间过长: {metrics.ui_response_time:.1f}ms",
                metrics
            )
            self._alert_cooldowns['ui'] = current_time

    def _check_alerts_optimized(self, metrics):
        """
        优化的警报检查方法 - 减少检查频率和复杂度

        Args:
            metrics (PerformanceMetrics): 当前性能指标
        """
        # 如果警报被禁用，直接返回
        if not self._alerts_enabled:
            return

        current_time = time.time()

        # 只检查最关键的指标，提高阈值
        # 检查CPU使用率（提高阈值到90%）
        if (metrics.cpu_usage > 90.0 and
            current_time - self._alert_cooldowns['cpu'] > self._alert_cooldown_period):
            self.performance_alert.emit(
                f"CPU使用率严重过高: {metrics.cpu_usage:.1f}%",
                metrics
            )
            self._alert_cooldowns['cpu'] = current_time

        # 检查内存使用率（提高阈值到90%）
        if (metrics.memory_usage > 90.0 and
            current_time - self._alert_cooldowns['memory'] > self._alert_cooldown_period):
            self.performance_alert.emit(
                f"内存使用率严重过高: {metrics.memory_usage:.1f}%",
                metrics
            )
            self._alert_cooldowns['memory'] = current_time

    def __del__(self):
        """析构函数，确保线程被正确终止"""
        try:
            # 只设置运行标志为False，不调用stop_monitoring方法
            # 这样可以避免在析构函数中join线程导致的错误
            self._running = False
        except Exception:
            # 忽略析构函数中的所有错误
            pass
