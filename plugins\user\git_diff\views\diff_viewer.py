"""
差异显示器组件

提供文件差异的可视化显示，支持：
- 并排视图和统一视图
- 语法高亮
- 差异导航和搜索
- 导出功能
"""

import os
from typing import List
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QSplitter,
    QLabel, QScrollBar, QMessageBox, QFileDialog
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QTextCharFormat, QColor, QFont, QTextDocument, QTextCursor

from ..models.git_repository import GitRepository
from ..models.file_diff import FileDiff, DiffLineType
from ..models.syntax_highlighter import SyntaxHighlighter
from .diff_navigator import DiffNavigator


class DiffTextEdit(QTextEdit):
    """差异文本编辑器"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setLineWrapMode(QTextEdit.NoWrap)

        # 设置合理的最小高度，但允许扩展
        self.setMinimumHeight(150)  # 设置最小高度
        # 移除最大高度限制，允许随窗口大小调整

        # 设置字体
        font = QFont("Consolas", 10)
        if not font.exactMatch():
            font = QFont("Courier New", 10)
        self.setFont(font)

        # 设置样式
        self.setStyleSheet("""
            QTextEdit {
                background-color: white;
                color: #333;
                border: 1px solid #d0d0d0;
                selection-background-color: #4a9eff;
                selection-color: white;
            }
        """)


class DiffViewer(QWidget):
    """差异显示器组件"""
    
    # 信号定义
    stats_updated = pyqtSignal(str)  # 统计信息更新信号
    navigation_requested = pyqtSignal(str, int)  # 导航请求信号 (action, line_number)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.git_repo = None
        self.current_file_path = ""
        self.current_diff = None
        self.view_mode = "side-by-side"  # "side-by-side" 或 "unified"

        # 导航相关
        self.navigator = None
        self.navigation_enabled = True

        # 设置大小策略，允许窗口最大化
        from PyQt5.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        # 不设置固定高度，允许窗口最大化

        self._init_ui()
        self._setup_formats()
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题标签
        self.title_label = QLabel("差异显示")
        self.title_label.setFixedHeight(30)  # 设置固定高度
        self.title_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #444;
                padding: 5px;
                background-color: #f8f8f8;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.title_label)

        # 创建差异导航器
        self.navigator = DiffNavigator()
        self.navigator.setFixedHeight(30)
        layout.addWidget(self.navigator)

        # 创建主内容区域（水平布局：差异内容 + 导航栏）
        main_content_layout = QHBoxLayout()
        main_content_widget = QWidget()
        main_content_widget.setLayout(main_content_layout)

        # 创建差异内容区域
        diff_content_layout = QVBoxLayout()
        diff_content_widget = QWidget()
        diff_content_widget.setLayout(diff_content_layout)

        # 创建并排视图容器
        self.side_by_side_widget = self._create_side_by_side_view()
        diff_content_layout.addWidget(self.side_by_side_widget)

        # 创建统一视图容器
        self.unified_widget = self._create_unified_view()
        diff_content_layout.addWidget(self.unified_widget)

        # 添加差异内容到主布局
        main_content_layout.addWidget(diff_content_widget)

        # 添加垂直导航栏到右侧，占用整个高度
        nav_bar_container = QWidget()
        nav_bar_container.setFixedWidth(35)  # 稍微增加宽度
        nav_container_layout = QVBoxLayout(nav_bar_container)
        nav_container_layout.setContentsMargins(5, 0, 0, 0)
        nav_container_layout.setSpacing(0)

        # 让导航栏占用整个容器高度
        self.navigator.navigation_bar.setMinimumHeight(100)  # 移除固定高度限制
        nav_container_layout.addWidget(self.navigator.navigation_bar, 1)  # stretch=1，占用所有可用空间

        main_content_layout.addWidget(nav_bar_container)

        layout.addWidget(main_content_widget)
        
        # 默认显示并排视图
        self.unified_widget.setVisible(False)
        
        # 提示标签
        self.hint_label = QLabel("请选择文件和版本进行比对")
        self.hint_label.setAlignment(Qt.AlignCenter)
        self.hint_label.setFixedHeight(60)  # 设置固定高度
        self.hint_label.setStyleSheet("""
            QLabel {
                color: #999;
                font-style: italic;
                font-size: 12px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.hint_label)

        # 连接导航器信号
        self._connect_navigator_signals()

    def _connect_navigator_signals(self):
        """连接导航器信号"""
        if self.navigator:
            self.navigator.jump_to_line.connect(self._jump_to_line)
            self.navigator.current_diff_changed.connect(self._on_current_diff_changed)
    
    def _create_side_by_side_view(self):
        """创建并排视图"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 标题行
        header_layout = QHBoxLayout()
        
        self.old_header = QLabel("历史版本")
        self.old_header.setAlignment(Qt.AlignCenter)
        self.old_header.setFixedHeight(30)  # 设置固定高度
        self.old_header.setStyleSheet("""
            QLabel {
                background-color: #ffe6e6;
                color: #d73a49;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #d73a49;
                border-radius: 4px;
            }
        """)
        
        self.new_header = QLabel("当前版本")
        self.new_header.setAlignment(Qt.AlignCenter)
        self.new_header.setFixedHeight(30)  # 设置固定高度
        self.new_header.setStyleSheet("""
            QLabel {
                background-color: #e6ffe6;
                color: #28a745;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #28a745;
                border-radius: 4px;
            }
        """)
        
        header_layout.addWidget(self.old_header)
        header_layout.addWidget(self.new_header)
        layout.addLayout(header_layout)
        
        # 文本编辑器分割器
        splitter = QSplitter(Qt.Horizontal)

        self.old_text_edit = DiffTextEdit()
        self.new_text_edit = DiffTextEdit()

        # 初始化语法高亮器（在设置文本之前）
        self.old_highlighter = None
        self.new_highlighter = None

        splitter.addWidget(self.old_text_edit)
        splitter.addWidget(self.new_text_edit)
        splitter.setSizes([500, 500])

        layout.addWidget(splitter)

        # 同步滚动
        self._sync_scroll_bars(self.old_text_edit, self.new_text_edit)
        
        return widget
    
    def _create_unified_view(self):
        """创建统一视图"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 标题
        self.unified_header = QLabel("统一差异视图")
        self.unified_header.setAlignment(Qt.AlignCenter)
        self.unified_header.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                color: #0366d6;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #0366d6;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.unified_header)
        
        # 统一文本编辑器
        self.unified_text_edit = DiffTextEdit()

        # 初始化统一视图的语法高亮器
        self.unified_highlighter = None

        layout.addWidget(self.unified_text_edit)
        
        return widget
    
    def _sync_scroll_bars(self, edit1, edit2):
        """同步两个文本编辑器的滚动条"""
        def sync_vertical(value):
            edit2.verticalScrollBar().setValue(value)
        
        def sync_horizontal(value):
            edit2.horizontalScrollBar().setValue(value)
        
        edit1.verticalScrollBar().valueChanged.connect(sync_vertical)
        edit1.horizontalScrollBar().valueChanged.connect(sync_horizontal)
        
        def sync_vertical_reverse(value):
            edit1.verticalScrollBar().setValue(value)
        
        def sync_horizontal_reverse(value):
            edit1.horizontalScrollBar().setValue(value)
        
        edit2.verticalScrollBar().valueChanged.connect(sync_vertical_reverse)
        edit2.horizontalScrollBar().valueChanged.connect(sync_horizontal_reverse)
    
    def _setup_formats(self):
        """设置文本格式"""
        # 删除行格式
        self.delete_format = QTextCharFormat()
        self.delete_format.setBackground(QColor(255, 235, 235))  # 浅红色背景
        self.delete_format.setForeground(QColor(215, 58, 73))    # 红色文字
        
        # 添加行格式
        self.add_format = QTextCharFormat()
        self.add_format.setBackground(QColor(235, 255, 235))     # 浅绿色背景
        self.add_format.setForeground(QColor(40, 167, 69))       # 绿色文字
        
        # 上下文行格式
        self.context_format = QTextCharFormat()
        self.context_format.setForeground(QColor(51, 51, 51))    # 深灰色文字
        
        # 行号格式
        self.line_number_format = QTextCharFormat()
        self.line_number_format.setForeground(QColor(153, 153, 153))  # 灰色
        self.line_number_format.setBackground(QColor(248, 248, 248))  # 浅灰色背景
    
    def show_diff(self, file_path: str, old_commit: str, new_commit: str):
        """显示文件差异
        
        Args:
            file_path: 文件路径
            old_commit: 旧版本提交哈希
            new_commit: 新版本提交哈希（"current"表示当前工作目录）
        """
        try:
            self.current_file_path = file_path
            
            # 初始化Git仓库
            file_dir = os.path.dirname(file_path)
            self.git_repo = GitRepository(file_dir)
            
            if not self.git_repo.is_valid_repo():
                raise ValueError("文件不在Git仓库中")
            
            # 获取文件内容（直接传递绝对路径，让Git仓库类处理路径转换）
            # 获取旧版本内容
            old_content = self.git_repo.get_file_content_at_commit(file_path, old_commit)

            # 获取新版本内容
            if new_commit == "current":
                new_content = self.git_repo.get_current_file_content(file_path)
                new_version_name = "当前工作目录"
            else:
                new_content = self.git_repo.get_file_content_at_commit(file_path, new_commit)
                new_version_name = f"提交 {new_commit[:8]}"
            
            # 创建差异对象
            old_version_name = f"提交 {old_commit[:8]}"
            self.current_diff = FileDiff(old_content, new_content, old_version_name, new_version_name)
            
            # 更新标题
            filename = os.path.basename(file_path)
            self.title_label.setText(f"文件差异: {filename}")
            
            # 隐藏提示标签
            self.hint_label.setVisible(False)
            
            # 显示差异
            if self.view_mode == "side-by-side":
                self._show_side_by_side_diff()
            else:
                self._show_unified_diff()
            
            # 更新统计信息
            stats = self.current_diff.get_stats()
            self.stats_updated.emit(str(stats))

            # 允许组件根据内容和窗口大小自适应调整

        except Exception as e:
            QMessageBox.warning(self, "错误", f"显示差异失败: {str(e)}")
    
    def _show_side_by_side_diff(self):
        """显示并排差异"""
        if not self.current_diff:
            return
        
        # 更新标题
        self.old_header.setText(self.current_diff.old_filename)
        self.new_header.setText(self.current_diff.new_filename)
        
        # 计算并排差异
        diff_lines = self.current_diff.calculate_side_by_side_diff()
        
        # 构建显示内容
        old_lines = []
        new_lines = []
        
        for line in diff_lines:
            if line.line_type == DiffLineType.CONTEXT:
                # 上下文行
                old_line = f"{line.old_line_no:4d}: {line.content}"
                new_line = f"{line.new_line_no:4d}: {line.content}"
                old_lines.append(old_line)
                new_lines.append(new_line)
                
            elif line.line_type == DiffLineType.DELETE:
                # 删除行
                old_line = f"{line.old_line_no:4d}:-{line.content}"
                new_line = "    :  "  # 空行占位
                old_lines.append(old_line)
                new_lines.append(new_line)
                
            elif line.line_type == DiffLineType.ADD:
                # 添加行
                old_line = "    :  "  # 空行占位
                new_line = f"{line.new_line_no:4d}:+{line.content}"
                old_lines.append(old_line)
                new_lines.append(new_line)
        
        # 先应用语法高亮器（在设置文本之前）
        self._apply_syntax_highlighting()

        # 设置文本内容
        self.old_text_edit.setPlainText('\n'.join(old_lines))
        self.new_text_edit.setPlainText('\n'.join(new_lines))

        # 更新导航数据
        self.update_navigation_data()
    
    def _show_unified_diff(self):
        """显示统一差异"""
        if not self.current_diff:
            return

        # 计算统一差异
        unified_diff = self.current_diff.calculate_unified_diff()

        # 先应用语法高亮器（在设置文本之前）
        self._apply_unified_syntax_highlighting()

        # 设置文本内容
        self.unified_text_edit.setPlainText('\n'.join(unified_diff))

        # 应用差异格式
        self._apply_unified_diff_formatting()

        # 更新导航数据
        self.update_navigation_data()
    
    def _apply_syntax_highlighting(self):
        """应用并排视图语法高亮"""
        if not self.current_file_path:
            return

        try:
            # 获取文件扩展名
            _, ext = os.path.splitext(self.current_file_path)

            # 清理旧的语法高亮器
            if self.old_highlighter:
                self.old_highlighter.setParent(None)
                self.old_highlighter = None
            if self.new_highlighter:
                self.new_highlighter.setParent(None)
                self.new_highlighter = None

            # 创建新的语法高亮器
            self.old_highlighter = SyntaxHighlighter(self.old_text_edit.document(), ext)
            self.new_highlighter = SyntaxHighlighter(self.new_text_edit.document(), ext)

        except Exception as e:
            print(f"应用并排视图语法高亮失败: {e}")

    def _apply_unified_syntax_highlighting(self):
        """应用统一视图语法高亮"""
        if not self.current_file_path:
            return

        try:
            # 获取文件扩展名
            _, ext = os.path.splitext(self.current_file_path)

            # 清理旧的语法高亮器
            if self.unified_highlighter:
                self.unified_highlighter.setParent(None)
                self.unified_highlighter = None

            # 创建新的语法高亮器
            self.unified_highlighter = SyntaxHighlighter(self.unified_text_edit.document(), ext)

        except Exception as e:
            print(f"应用统一视图语法高亮失败: {e}")
    
    def _apply_unified_diff_formatting(self):
        """应用统一差异格式"""
        cursor = self.unified_text_edit.textCursor()
        cursor.movePosition(QTextCursor.Start)
        
        document = self.unified_text_edit.document()
        
        for i in range(document.blockCount()):
            block = document.findBlockByNumber(i)
            text = block.text()
            
            cursor.setPosition(block.position())
            cursor.select(QTextCursor.LineUnderCursor)
            
            if text.startswith('-'):
                cursor.setCharFormat(self.delete_format)
            elif text.startswith('+'):
                cursor.setCharFormat(self.add_format)
            elif text.startswith('@@'):
                cursor.setCharFormat(self.line_number_format)
            else:
                cursor.setCharFormat(self.context_format)
    
    def set_view_mode(self, mode: str):
        """设置视图模式"""
        self.view_mode = mode
        
        if mode == "side-by-side":
            self.side_by_side_widget.setVisible(True)
            self.unified_widget.setVisible(False)
            if self.current_diff:
                self._show_side_by_side_diff()
        else:
            self.side_by_side_widget.setVisible(False)
            self.unified_widget.setVisible(True)
            if self.current_diff:
                self._show_unified_diff()
    
    def search_text(self, search_text: str):
        """搜索文本"""
        if not search_text:
            return
        
        try:
            if self.view_mode == "side-by-side":
                # 在并排视图中搜索
                found1 = self.old_text_edit.find(search_text)
                found2 = self.new_text_edit.find(search_text)
                
                if not found1 and not found2:
                    QMessageBox.information(self, "搜索", f"未找到 '{search_text}'")
            else:
                # 在统一视图中搜索
                found = self.unified_text_edit.find(search_text)
                
                if not found:
                    QMessageBox.information(self, "搜索", f"未找到 '{search_text}'")
                    
        except Exception as e:
            QMessageBox.warning(self, "错误", f"搜索失败: {str(e)}")
    
    def export_diff(self):
        """导出差异到文件"""
        if not self.current_diff:
            QMessageBox.warning(self, "警告", "没有可导出的差异内容")
            return
        
        try:
            # 选择保存文件
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出差异", 
                f"{os.path.basename(self.current_file_path)}.diff",
                "差异文件 (*.diff);;文本文件 (*.txt);;所有文件 (*.*)"
            )
            
            if not filename:
                return
            
            # 获取差异内容
            if self.view_mode == "unified":
                content = self.unified_text_edit.toPlainText()
            else:
                # 并排视图导出为统一格式
                unified_diff = self.current_diff.calculate_unified_diff()
                content = '\n'.join(unified_diff)
            
            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            QMessageBox.information(self, "成功", f"差异已导出到: {filename}")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败: {str(e)}")
    
    def clear(self):
        """清空显示"""
        self.old_text_edit.clear()
        self.new_text_edit.clear()
        self.unified_text_edit.clear()

        self.current_diff = None
        self.current_file_path = ""

        self.title_label.setText("差异显示")
        self.hint_label.setVisible(True)

        self.stats_updated.emit("无")

        # 清空后允许组件自适应调整大小
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理语法高亮器
            if hasattr(self, 'old_highlighter') and self.old_highlighter:
                self.old_highlighter.setParent(None)
                self.old_highlighter = None

            if hasattr(self, 'new_highlighter') and self.new_highlighter:
                self.new_highlighter.setParent(None)
                self.new_highlighter = None

            if hasattr(self, 'unified_highlighter') and self.unified_highlighter:
                self.unified_highlighter.setParent(None)
                self.unified_highlighter = None

        except Exception as e:
            print(f"清理差异显示器资源失败: {e}")

    # 导航相关方法
    def _jump_to_line(self, line_number: int):
        """跳转到指定行"""
        try:
            if self.view_mode == "side-by-side":
                # 并排视图：同时滚动两个文本框
                if hasattr(self, 'old_text_edit') and hasattr(self, 'new_text_edit'):
                    self._scroll_to_line(self.old_text_edit, line_number)
                    self._scroll_to_line(self.new_text_edit, line_number)
            else:
                # 统一视图：滚动统一文本框
                if hasattr(self, 'unified_text_edit'):
                    self._scroll_to_line(self.unified_text_edit, line_number)
        except Exception as e:
            print(f"跳转到行失败: {e}")

    def _scroll_to_line(self, text_edit: DiffTextEdit, line_number: int):
        """滚动文本编辑器到指定行"""
        try:
            document = text_edit.document()
            if line_number < document.blockCount():
                block = document.findBlockByLineNumber(line_number)
                cursor = QTextCursor(block)
                text_edit.setTextCursor(cursor)
                text_edit.ensureCursorVisible()

                # 高亮当前行
                self._highlight_current_line(text_edit, line_number)
        except Exception as e:
            print(f"滚动到行失败: {e}")

    def _highlight_current_line(self, text_edit: DiffTextEdit, line_number: int):
        """高亮当前行"""
        try:
            # 创建高亮格式
            highlight_format = QTextCharFormat()
            highlight_format.setBackground(QColor(74, 158, 255, 50))  # 半透明蓝色

            # 应用高亮
            cursor = text_edit.textCursor()
            cursor.movePosition(QTextCursor.Start)
            cursor.movePosition(QTextCursor.Down, QTextCursor.MoveAnchor, line_number)
            cursor.select(QTextCursor.LineUnderCursor)

            # 创建额外选择来高亮行
            extra_selections = []
            selection = QTextEdit.ExtraSelection()
            selection.format = highlight_format
            selection.cursor = cursor
            extra_selections.append(selection)

            text_edit.setExtraSelections(extra_selections)

            # 设置定时器清除高亮
            QTimer.singleShot(2000, lambda: text_edit.setExtraSelections([]))
        except Exception as e:
            print(f"高亮当前行失败: {e}")

    def _on_current_diff_changed(self, diff_index: int):
        """当前差异改变事件"""
        # 可以在这里添加额外的处理逻辑
        pass

    def set_navigation_enabled(self, enabled: bool):
        """设置导航功能启用状态"""
        self.navigation_enabled = enabled
        if self.navigator:
            self.navigator.setVisible(enabled)

    def set_navigation_bar_visible(self, visible: bool):
        """设置导航栏可见性"""
        if self.navigator:
            self.navigator.set_navigation_bar_visible(visible)

    def handle_navigation_action(self, action: str):
        """处理导航动作"""
        if not self.navigator or not self.navigation_enabled:
            return

        if action == "previous":
            self.navigator._go_to_previous_diff()
        elif action == "next":
            self.navigator._go_to_next_diff()
        elif action == "first":
            self.navigator._go_to_first_diff()
        elif action == "last":
            self.navigator._go_to_last_diff()

    def update_navigation_data(self):
        """更新导航数据"""
        if not self.navigator or not self.current_diff:
            return

        try:
            # 获取差异行数据
            if self.view_mode == "side-by-side":
                diff_lines = self.current_diff.calculate_side_by_side_diff()
            else:
                # 对于统一视图，需要转换格式
                unified_lines = self.current_diff.calculate_unified_diff()
                diff_lines = self._convert_unified_to_diff_lines(unified_lines)

            # 更新导航器数据
            self.navigator.set_diff_data(diff_lines)
        except Exception as e:
            print(f"更新导航数据失败: {e}")

    def _convert_unified_to_diff_lines(self, unified_lines: List[str]):
        """将统一差异格式转换为差异行列表"""
        from ..models.file_diff import DiffLine, DiffLineType

        diff_lines = []
        for i, line in enumerate(unified_lines):
            if line.startswith('+'):
                line_type = DiffLineType.ADD
                content = line[1:]
            elif line.startswith('-'):
                line_type = DiffLineType.DELETE
                content = line[1:]
            elif line.startswith('@@'):
                continue  # 跳过差异头
            else:
                line_type = DiffLineType.CONTEXT
                content = line[1:] if line.startswith(' ') else line

            diff_lines.append(DiffLine(line_type, None, None, content))

        return diff_lines
