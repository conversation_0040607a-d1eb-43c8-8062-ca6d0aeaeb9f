"""
文档解析器

支持多种文档格式的内容提取
"""

import os
import re
from typing import Optional, Tuple


class DocumentParser:
    """文档解析器"""
    
    # 解析器加载状态
    _parsers_loaded = False
    
    @staticmethod
    def _load_parsers():
        """预加载解析器"""
        if DocumentParser._parsers_loaded:
            return
            
        try:
            # 尝试预加载PyPDF2
            try:
                import PyPDF2
                print("[文档解析] PyPDF2 已加载")
            except ImportError:
                print("[文档解析] PyPDF2 未安装")
            
            # 尝试预加载python-docx
            try:
                from docx import Document
                print("[文档解析] python-docx 已加载")
            except ImportError:
                print("[文档解析] python-docx 未安装")
                
            # 尝试预加载docx2txt
            try:
                import docx2txt
                print("[文档解析] docx2txt 已加载")
            except ImportError:
                print("[文档解析] docx2txt 未安装")
                
            # 尝试预加载striprtf
            try:
                from striprtf.striprtf import rtf_to_text
                print("[文档解析] striprtf 已加载")
            except ImportError:
                print("[文档解析] striprtf 未安装")
                
            DocumentParser._parsers_loaded = True
        except Exception as e:
            print(f"[文档解析] 预加载解析器失败: {e}")
    
    @staticmethod
    def parse_document(file_path: str) -> Tuple[bool, str, str]:
        """解析文档
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[bool, str, str]: (成功标志, 内容, 错误信息)
        """
        try:
            if not os.path.exists(file_path):
                return False, "", "文件不存在"
            
            extension = os.path.splitext(file_path)[1].lower()
            
            if extension == '.txt':
                return DocumentParser._parse_txt(file_path)
            elif extension == '.md':
                return DocumentParser._parse_markdown(file_path)
            elif extension == '.pdf':
                return DocumentParser._parse_pdf(file_path)
            elif extension in ['.doc', '.docx']:
                return DocumentParser._parse_word(file_path)
            elif extension == '.rtf':
                return DocumentParser._parse_rtf(file_path)
            else:
                return False, "", f"不支持的文件格式: {extension}"
                
        except Exception as e:
            return False, "", f"解析文档时出错: {str(e)}"
    
    @staticmethod
    def _parse_txt(file_path: str) -> Tuple[bool, str, str]:
        """解析TXT文件"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'ascii']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    return True, content, ""
                except UnicodeDecodeError:
                    continue
            
            return False, "", "无法识别文件编码"
            
        except Exception as e:
            return False, "", f"读取TXT文件失败: {str(e)}"
    
    @staticmethod
    def _parse_markdown(file_path: str) -> Tuple[bool, str, str]:
        """解析Markdown文件"""
        try:
            # Markdown文件通常是UTF-8编码
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单清理Markdown标记（保留基本结构）
            # 移除图片链接
            content = re.sub(r'!\[.*?\]\(.*?\)', '', content)
            # 移除链接但保留文本
            content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
            # 移除代码块标记
            content = re.sub(r'```[\s\S]*?```', '[代码块]', content)
            content = re.sub(r'`([^`]+)`', r'\1', content)
            
            return True, content, ""
            
        except Exception as e:
            return False, "", f"读取Markdown文件失败: {str(e)}"
    
    @staticmethod
    def _parse_pdf(file_path: str) -> Tuple[bool, str, str]:
        """解析PDF文件"""
        try:
            # 尝试导入PyPDF2
            try:
                import PyPDF2
            except ImportError:
                return False, "", "需要安装PyPDF2库来解析PDF文件: pip install PyPDF2"
            
            content = ""
            
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    content += page_text + "\n"
            
            if not content.strip():
                return False, "", "PDF文件中没有可提取的文本内容"
            
            return True, content, ""
            
        except ImportError:
            return False, "", "需要安装PyPDF2库: pip install PyPDF2"
        except Exception as e:
            return False, "", f"解析PDF文件失败: {str(e)}"
    
    @staticmethod
    def _parse_word(file_path: str) -> Tuple[bool, str, str]:
        """解析Word文件"""
        try:
            extension = os.path.splitext(file_path)[1].lower()
            
            if extension == '.docx':
                return DocumentParser._parse_docx(file_path)
            else:
                return DocumentParser._parse_doc(file_path)
                
        except Exception as e:
            return False, "", f"解析Word文件失败: {str(e)}"
    
    @staticmethod
    def _parse_docx(file_path: str) -> Tuple[bool, str, str]:
        """解析DOCX文件"""
        try:
            # 尝试导入python-docx
            try:
                from docx import Document
            except ImportError:
                return False, "", "需要安装python-docx库来解析DOCX文件: pip install python-docx"
            
            doc = Document(file_path)
            content = ""
            
            # 提取段落文本
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            
            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    content += " | ".join(row_text) + "\n"
            
            if not content.strip():
                return False, "", "DOCX文件中没有可提取的文本内容"
            
            return True, content, ""
            
        except ImportError:
            return False, "", "需要安装python-docx库: pip install python-docx"
        except Exception as e:
            return False, "", f"解析DOCX文件失败: {str(e)}"
    
    @staticmethod
    def _parse_doc(file_path: str) -> Tuple[bool, str, str]:
        """解析DOC文件"""
        try:
            # 尝试使用python-docx2txt
            try:
                import docx2txt
                content = docx2txt.process(file_path)
                return True, content, ""
            except ImportError:
                pass
            
            # 尝试使用win32com（仅Windows）
            try:
                import win32com.client
                
                word = win32com.client.Dispatch("Word.Application")
                word.Visible = False
                
                doc = word.Documents.Open(file_path)
                content = doc.Content.Text
                doc.Close()
                word.Quit()
                
                return True, content, ""
                
            except ImportError:
                pass
            except Exception as e:
                return False, "", f"使用Word COM组件失败: {str(e)}"
            
            return False, "", "需要安装docx2txt库或在Windows上使用Word COM组件来解析DOC文件"
            
        except Exception as e:
            return False, "", f"解析DOC文件失败: {str(e)}"
    
    @staticmethod
    def _parse_rtf(file_path: str) -> Tuple[bool, str, str]:
        """解析RTF文件"""
        try:
            # 尝试使用striprtf
            try:
                from striprtf.striprtf import rtf_to_text
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    rtf_content = f.read()
                
                content = rtf_to_text(rtf_content)
                return True, content, ""
                
            except ImportError:
                return False, "", "需要安装striprtf库来解析RTF文件: pip install striprtf"
            
        except Exception as e:
            return False, "", f"解析RTF文件失败: {str(e)}"
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文、英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s.,!?;:()[\]{}"\'-]', '', text)
        
        # 移除多余的换行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()
    
    @staticmethod
    def extract_summary(text: str, max_length: int = 200) -> str:
        """提取文本摘要
        
        Args:
            text: 文本内容
            max_length: 最大长度
            
        Returns:
            str: 摘要文本
        """
        if not text:
            return ""
        
        # 清理文本
        clean_text = DocumentParser.clean_text(text)
        
        # 如果文本长度小于最大长度，直接返回
        if len(clean_text) <= max_length:
            return clean_text
        
        # 尝试按句子分割
        sentences = re.split(r'[.!?。！？]', clean_text)
        
        summary = ""
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            if len(summary) + len(sentence) + 1 <= max_length:
                summary += sentence + "。"
            else:
                break
        
        # 如果没有找到合适的句子，直接截断
        if not summary:
            summary = clean_text[:max_length] + "..."
        
        return summary
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 文件信息
        """
        try:
            if not os.path.exists(file_path):
                return {}
            
            stat = os.stat(file_path)
            
            return {
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'extension': os.path.splitext(file_path)[1].lower(),
                'modified_time': stat.st_mtime,
                'created_time': stat.st_ctime
            }
            
        except Exception as e:
            return {'error': str(e)}
