"""
增强的日志缓冲管理器 - 集成自适应批处理
在原有LogBufferManager基础上增加智能批处理功能
"""
import time
from PyQt5.QtCore import QObject, pyqtSignal
from .log_buffer_manager import LogBufferManager
from .adaptive_batch_processor import AdaptiveBatchProcessor
from .string_pool import intern_string


class EnhancedLogBufferManager(LogBufferManager):
    """
    增强的日志缓冲管理器
    集成自适应批处理，提供更智能的批处理策略
    """
    
    # 新增信号
    adaptive_batch_processed = pyqtSignal(int, float)  # 自适应批处理完成信号
    
    def __init__(self, parent=None):
        """初始化增强的日志缓冲管理器"""
        super().__init__(parent)
        
        # 自适应批处理器
        self._adaptive_processor = AdaptiveBatchProcessor(
            process_callback=self._process_adaptive_batch,
            parent=self
        )
        
        # 连接自适应批处理信号
        self._adaptive_processor.batch_processed.connect(self._on_adaptive_batch_processed)
        
        # 增强统计信息
        self._enhanced_stats = {
            'adaptive_batches_processed': 0,
            'adaptive_items_processed': 0,
            'adaptive_total_time': 0.0,
            'traditional_vs_adaptive_ratio': 0.0
        }
        
        # 配置选项
        self._use_adaptive_processing = True
        self._adaptive_threshold = 50  # 超过此数量的项目使用自适应处理
    
    def set_adaptive_processing(self, enabled: bool):
        """启用或禁用自适应处理"""
        self._use_adaptive_processing = enabled

    def set_batch_size(self, batch_size: int):
        """设置批处理大小"""
        # 设置父类的批处理大小
        if hasattr(self, '_batch_size'):
            self._batch_size = batch_size
        # 设置自适应处理器的批处理大小
        if self._adaptive_processor:
            self._adaptive_processor.set_batch_size(batch_size)
    
    def append_text(self, text):
        """
        添加文本数据（增强版本）
        根据情况选择传统批处理或自适应批处理
        """
        if not text:
            return
        
        # 使用字符串池优化内存
        text = intern_string(text)
        
        # 根据配置选择处理方式
        if self._use_adaptive_processing:
            # 使用自适应批处理
            self._adaptive_processor.add_item(text)
        else:
            # 使用传统批处理
            super().append_text(text)
    
    def append_text_batch(self, texts):
        """
        批量添加文本数据
        
        Args:
            texts: 文本列表
        """
        if not texts:
            return
        
        # 优化字符串内存使用
        optimized_texts = [intern_string(text) for text in texts if text]
        
        if not optimized_texts:
            return
        
        # 根据数量和配置选择处理方式
        if (self._use_adaptive_processing and 
            len(optimized_texts) >= self._adaptive_threshold):
            # 使用自适应批处理
            self._adaptive_processor.add_items(optimized_texts)
        else:
            # 使用传统批处理
            for text in optimized_texts:
                super().append_text(text)
    
    def _process_adaptive_batch(self, items):
        """
        处理自适应批次
        
        Args:
            items: 要处理的项目列表
        """
        if not items:
            return
        
        try:
            # 合并所有文本
            combined_text = ''.join(items)
            
            # 直接添加到主缓冲区（绕过传统批处理）
            self._main_buffer.append(combined_text)
            
            # 更新统计
            self._update_batch_stats(len(combined_text))
            
            # 立即刷新（自适应批处理已经优化了时机）
            self._flush_main_buffer()
            
        except Exception as e:
            print(f"处理自适应批次时出错: {e}")
    
    def _on_adaptive_batch_processed(self, items_count: int, process_time: float):
        """处理自适应批处理完成信号"""
        # 更新增强统计
        self._enhanced_stats['adaptive_batches_processed'] += 1
        self._enhanced_stats['adaptive_items_processed'] += items_count
        self._enhanced_stats['adaptive_total_time'] += process_time
        
        # 发出信号
        self.adaptive_batch_processed.emit(items_count, process_time)
    
    def force_flush(self):
        """强制刷新所有缓冲区（增强版本）"""
        try:
            # 强制刷新自适应批处理器
            if self._adaptive_processor:
                self._adaptive_processor.force_flush()
            
            # 调用父类的强制刷新
            super().force_flush()
            
        except Exception as e:
            print(f"增强强制刷新时出错: {e}")
    
    def get_enhanced_stats(self):
        """获取增强的统计信息"""
        base_stats = self.get_buffer_stats()
        adaptive_stats = self._adaptive_processor.get_stats() if self._adaptive_processor else {}
        
        # 计算传统vs自适应比率
        total_traditional = base_stats.get('total_flushes', 0)
        total_adaptive = self._enhanced_stats['adaptive_batches_processed']
        
        if total_traditional + total_adaptive > 0:
            adaptive_ratio = total_adaptive / (total_traditional + total_adaptive)
        else:
            adaptive_ratio = 0.0
        
        self._enhanced_stats['traditional_vs_adaptive_ratio'] = adaptive_ratio
        
        return {
            'base_stats': base_stats,
            'adaptive_stats': adaptive_stats,
            'enhanced_stats': self._enhanced_stats,
            'performance_summary': {
                'total_batches': total_traditional + total_adaptive,
                'adaptive_ratio': adaptive_ratio,
                'avg_adaptive_batch_size': (
                    self._enhanced_stats['adaptive_items_processed'] / 
                    max(1, self._enhanced_stats['adaptive_batches_processed'])
                ),
                'avg_adaptive_process_time': (
                    self._enhanced_stats['adaptive_total_time'] / 
                    max(1, self._enhanced_stats['adaptive_batches_processed'])
                )
            }
        }
    
    def get_adaptive_recommendations(self):
        """获取自适应处理建议"""
        stats = self.get_enhanced_stats()
        adaptive_stats = stats.get('adaptive_stats', {})
        
        recommendations = []
        
        # 系统负载建议
        system_load = adaptive_stats.get('system_load', 0.0)
        if system_load > 0.8:
            recommendations.append("系统负载较高，建议减少批处理大小")
        elif system_load < 0.3:
            recommendations.append("系统负载较低，可以增加批处理大小")
        
        # 批处理效率建议
        avg_process_time = stats['performance_summary']['avg_adaptive_process_time']
        if avg_process_time > 0.1:
            recommendations.append("批处理时间较长，建议优化处理逻辑")
        
        # 内存使用建议
        memory_usage = adaptive_stats.get('memory_usage', 0.0)
        if memory_usage > 80:
            recommendations.append("内存使用率较高，建议增加刷新频率")
        
        # 自适应使用率建议
        adaptive_ratio = stats['performance_summary']['adaptive_ratio']
        if adaptive_ratio < 0.5:
            recommendations.append("自适应批处理使用率较低，可以降低自适应阈值")
        
        return recommendations
    
    def optimize_settings(self):
        """根据当前性能自动优化设置"""
        try:
            stats = self.get_enhanced_stats()
            adaptive_stats = stats.get('adaptive_stats', {})
            
            # 根据系统负载调整自适应阈值
            system_load = adaptive_stats.get('system_load', 0.5)
            
            if system_load > 0.7:
                # 高负载：提高阈值，减少自适应处理
                self._adaptive_threshold = min(100, self._adaptive_threshold + 10)
            elif system_load < 0.3:
                # 低负载：降低阈值，增加自适应处理
                self._adaptive_threshold = max(20, self._adaptive_threshold - 10)
            
            # 根据处理时间调整
            avg_time = stats['performance_summary']['avg_adaptive_process_time']
            if avg_time > 0.05:  # 处理时间过长
                self._adaptive_threshold = min(100, self._adaptive_threshold + 5)
            
            return True
            
        except Exception as e:
            print(f"优化设置时出错: {e}")
            return False
    
    def cleanup(self):
        """清理资源（增强版本）"""
        try:
            # 清理自适应批处理器
            if self._adaptive_processor:
                self._adaptive_processor.cleanup()
                self._adaptive_processor = None
            
            # 调用父类清理
            super().cleanup()
            
        except Exception as e:
            print(f"清理增强日志缓冲管理器时出错: {e}")


def create_optimized_log_buffer_manager(parent=None, use_adaptive=True):
    """
    创建优化的日志缓冲管理器
    
    Args:
        parent: 父对象
        use_adaptive: 是否使用自适应处理
        
    Returns:
        EnhancedLogBufferManager实例
    """
    manager = EnhancedLogBufferManager(parent)
    manager.set_adaptive_processing(use_adaptive)
    return manager
