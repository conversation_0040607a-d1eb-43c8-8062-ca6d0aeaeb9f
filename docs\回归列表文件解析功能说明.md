# 批量执行插件 - 回归列表文件解析功能

## 功能概述

批量执行插件现已支持回归列表文件解析功能，除了现有的case config文件支持外，新增对回归列表文件（.list文件）的解析能力。

## 回归列表文件格式规范

### 文件格式
每行格式为逗号分隔的字段，**最少需要3个字段**，完整格式为11个字段：
```
on/off, block, case, [seed, iterative, tag, priority, config, CFG_DEF, env/base, plusargs]
```

**灵活性说明**：
- **必需字段**：前3个字段（on/off, block, case）
- **可选字段**：后续字段可以部分省略
- **智能解析**：第6个字段`tag`使用方括号包围，内部可以包含多个逗号分隔的标签，如`[POST_ALL, POST_FAKE]`

### 示例文件内容
```
// 回归列表文件示例
// 格式：on/off, block, case, seed, iterative, tag, priority, config, CFG_DEF, env/base, plusargs

// 启用的用例示例
ON, udtb/apcpu_sys, apcpu_sys_a2u1, rand, 1, [POST_ALL, POST_FAKE], POST_NOHPC, H, default, NOHPC0, apcpu_sys
ON, udtb/apcpu_sys, apcpu_adapt_4vts_top_adapt4vts_random_trig_for_apcpu_test, rand, 1, [POST_ALL, POST_FAKE], POST_PG, H, default, default, top
ON, udtb/usvp, apcpu_dsu_dynamic_power_trans_test, rand, 1, [POST_ALL, POST_FAKE], POST_NOHPC, H, default, default, apcpu_sys

// 禁用的用例示例（应该被跳过）
OFF, udtb/usvp, disabled_test, rand, 1, [POST_ALL, POST_FAKE], POST_NOHPC, H, default, default, apcpu_sys

// 不同block路径的用例
ON, dv/subsys1, test_case_1, rand, 1, [POST_ALL], H, default, , subsys1_base, EXEC_CPU=[0:7]
ON, dv/subsys2, test_case_2, rand, 1, [POST_ALL], H, default, CFG_DEF, subsys2_base, EXEC_CPU=[0:7]

// 简化格式示例（只有必需字段）
ON, udtb/simple, simple_test
ON, udtb/test, test_case, rand, 1, [POST_ALL], H, default, UPF_SIM
```

## 字段解析规则

### 必需字段
1. **状态字段（第1个字段）**：`ON`表示用例启用，`OFF`表示用例禁用
2. **block路径（第2个字段）**：对应runsim命令的`-block`参数值
3. **用例名（第3个字段）**：对应runsim命令的`-case`参数值

### 可选字段
9. **CFG_DEF（第9个字段）**：对应runsim命令的`-cfd_def`参数值
   - 如果为空或`default`（不区分大小写）则不添加此参数
   - 其他值会添加`-cfd_def`参数
10. **env/base值（第10个字段）**：对应runsim命令的`-base`参数值
   - 如果为空或`default`（不区分大小写）则不添加此参数
   - 其他值会添加`-base`参数

### 忽略字段
- 第4个字段：seed（随机种子）
- 第5个字段：iterative（重复次数）
- 第6个字段：tag（回归标签，方括号包围）
- 第7个字段：priority（优先级）
- 第8个字段：config（配置名）
- 第11个字段：plusargs（仿真参数）

## 特殊处理规则

1. **注释行**：以`//`开头的行视为注释行，完全忽略
2. **禁用用例**：状态为`OFF`的行视为禁用用例，跳过处理
3. **空行**：自动忽略
4. **字段清理**：字段值两端的空格自动去除，方括号`[]`会被自动移除
5. **灵活字段数量**：只要包含前3个必需字段即可，后续字段可以部分省略
6. **智能方括号处理**：正确解析tag字段中的方括号和逗号
7. **default值处理**：CFG_DEF和env/base字段值为`default`时（不区分大小写）不添加对应参数

## 命令生成规则

解析每个有效行（状态为ON且非注释行），生成对应的runsim命令参数组合：

### 基本命令格式
```bash
runsim -case <用例名> -block <block路径>
```

### 完整命令格式（包含可选参数）
```bash
runsim -case <用例名> -block <block路径> -base <base值> -cfd_def <CFD定义>
```

### 示例命令输出
根据上述示例文件，会生成以下命令：
```bash
# 完整字段的用例（展示default值处理）
runsim -case apcpu_sys_a2u1 -block udtb/apcpu_sys -base NOHPC0 -cfd_def UPF_SIM
runsim -case apcpu_adapt_4vts_top_adapt4vts_random_trig_for_apcpu_test -block udtb/apcpu_sys
runsim -case apcpu_dsu_dynamic_power_trans_test -block udtb/usvp -base top_base
runsim -case apcpu_voltage_sensor_rosc_test -block udtb/usvp -cfd_def PERF_SIM
runsim -case test_case_1 -block dv/subsys1 -base subsys1_base
runsim -case test_case_2 -block dv/subsys2 -base subsys2_base -cfd_def CFG_DEF

# 简化字段的用例
runsim -case simple_test -block udtb/simple
runsim -case test_case -block udtb/test -cfd_def UPF_SIM
```

**说明**：
- 当CFG_DEF或env/base字段为`default`时，不会添加对应的`-cfd_def`或`-base`参数
- 只有当字段有具体值时才会添加相应参数

## 使用方法

1. 在批量执行插件中点击"添加用例"按钮
2. 在文件选择对话框中选择回归列表文件（.list扩展名）
3. 插件会自动识别文件类型并使用相应的解析器
4. 解析完成后，有效用例会显示在用例表格中
5. 可以选择要执行的用例并开始批量执行

## 文件类型支持

- **.list文件**：使用回归列表文件解析器
- **.cfg/.txt文件**：使用传统的case config文件解析器
- **混合支持**：可以同时选择不同类型的文件，插件会自动使用对应的解析器

## 错误处理

- 字段数量不正确的行会被跳过并记录错误信息
- 缺少必需字段的行会被跳过并记录错误信息
- 解析失败的行会被跳过并继续处理其他行
- 所有错误信息会在控制台输出，便于调试
