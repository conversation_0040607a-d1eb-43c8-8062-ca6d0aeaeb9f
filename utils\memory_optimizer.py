#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存管理优化模块

提供智能内存管理，减少内存占用和垃圾回收开销
"""

import gc
import sys
import time
import threading
import weakref
from collections import deque
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

class MemoryOptimizer(QObject):
    """内存优化器"""
    
    # 定义信号
    memory_warning = pyqtSignal(float)  # 内存使用率警告
    cleanup_completed = pyqtSignal(int)  # 清理完成，参数为释放的对象数
    
    def __init__(self):
        super().__init__()
        
        # 内存管理配置
        self.memory_threshold = 80.0  # 内存使用率阈值（百分比）
        self.cleanup_interval = 30.0  # 清理间隔（秒）
        self.force_gc_threshold = 90.0  # 强制垃圾回收阈值
        
        # 对象池管理
        self._object_pools = {}
        self._weak_refs = set()
        self._cleanup_callbacks = []
        
        # 统计信息
        self._stats = {
            'total_cleanups': 0,
            'objects_cleaned': 0,
            'memory_saved_mb': 0.0,
            'last_cleanup_time': 0.0
        }
        
        # 清理定时器
        self._cleanup_timer = QTimer()
        self._cleanup_timer.timeout.connect(self._periodic_cleanup)
        self._cleanup_timer.start(int(self.cleanup_interval * 1000))
        
        # 线程锁
        self._lock = threading.Lock()
    
    def register_object(self, obj, cleanup_callback=None):
        """
        注册对象进行内存管理
        
        Args:
            obj: 要管理的对象
            cleanup_callback: 清理回调函数
        """
        try:
            # 创建弱引用
            if cleanup_callback:
                weak_ref = weakref.ref(obj, cleanup_callback)
            else:
                weak_ref = weakref.ref(obj)
            
            with self._lock:
                self._weak_refs.add(weak_ref)
                
        except TypeError:
            # 某些对象不支持弱引用
            pass
    
    def register_cleanup_callback(self, callback):
        """注册清理回调函数"""
        with self._lock:
            self._cleanup_callbacks.append(callback)
    
    def create_object_pool(self, pool_name, factory_func, max_size=50):
        """
        创建对象池
        
        Args:
            pool_name (str): 池名称
            factory_func: 对象工厂函数
            max_size (int): 最大池大小
        """
        with self._lock:
            self._object_pools[pool_name] = {
                'factory': factory_func,
                'pool': deque(maxlen=max_size),
                'max_size': max_size,
                'created_count': 0,
                'reused_count': 0
            }
    
    def get_pooled_object(self, pool_name, *args, **kwargs):
        """
        从对象池获取对象
        
        Args:
            pool_name (str): 池名称
            *args, **kwargs: 传递给工厂函数的参数
            
        Returns:
            object: 池化对象
        """
        with self._lock:
            if pool_name not in self._object_pools:
                return None
            
            pool_info = self._object_pools[pool_name]
            
            # 尝试从池中获取对象
            if pool_info['pool']:
                obj = pool_info['pool'].popleft()
                pool_info['reused_count'] += 1
                return obj
            
            # 池为空，创建新对象
            try:
                obj = pool_info['factory'](*args, **kwargs)
                pool_info['created_count'] += 1
                return obj
            except Exception as e:
                print(f"创建池化对象失败: {e}")
                return None
    
    def return_pooled_object(self, pool_name, obj):
        """
        将对象返回到池中
        
        Args:
            pool_name (str): 池名称
            obj: 要返回的对象
        """
        with self._lock:
            if pool_name not in self._object_pools:
                return
            
            pool_info = self._object_pools[pool_name]
            
            # 重置对象状态（如果有重置方法）
            if hasattr(obj, 'reset'):
                try:
                    obj.reset()
                except Exception:
                    return  # 重置失败，不返回池中
            
            # 如果池未满，添加到池中
            if len(pool_info['pool']) < pool_info['max_size']:
                pool_info['pool'].append(obj)
    
    def _periodic_cleanup(self):
        """定期清理"""
        try:
            # 获取当前内存使用情况
            memory_usage = self._get_memory_usage()
            
            # 如果内存使用率超过阈值，执行清理
            if memory_usage > self.memory_threshold:
                self.memory_warning.emit(memory_usage)
                cleaned_objects = self._perform_cleanup(force=memory_usage > self.force_gc_threshold)
                self.cleanup_completed.emit(cleaned_objects)
            
        except Exception as e:
            print(f"定期清理错误: {e}")
    
    def _get_memory_usage(self):
        """获取内存使用率"""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except ImportError:
            # 如果没有psutil，使用简单的估算
            return 0.0
    
    def _perform_cleanup(self, force=False):
        """执行清理操作"""
        start_time = time.time()
        cleaned_objects = 0
        
        with self._lock:
            # 清理失效的弱引用
            dead_refs = set()
            for weak_ref in self._weak_refs:
                if weak_ref() is None:
                    dead_refs.add(weak_ref)
                    cleaned_objects += 1
            
            self._weak_refs -= dead_refs
            
            # 执行注册的清理回调
            for callback in self._cleanup_callbacks[:]:
                try:
                    result = callback()
                    if isinstance(result, int):
                        cleaned_objects += result
                except Exception as e:
                    print(f"清理回调错误: {e}")
                    # 移除失效的回调
                    self._cleanup_callbacks.remove(callback)
            
            # 清理对象池中的过期对象
            for pool_name, pool_info in self._object_pools.items():
                pool = pool_info['pool']
                # 保留最近使用的一半对象
                keep_count = len(pool) // 2
                while len(pool) > keep_count:
                    pool.pop()
                    cleaned_objects += 1
        
        # 如果需要强制清理，执行垃圾回收
        if force:
            gc.collect()
            cleaned_objects += len(gc.garbage)
            gc.garbage.clear()
        
        # 更新统计信息
        cleanup_time = time.time() - start_time
        self._stats['total_cleanups'] += 1
        self._stats['objects_cleaned'] += cleaned_objects
        self._stats['last_cleanup_time'] = cleanup_time
        
        return cleaned_objects
    
    def force_cleanup(self):
        """强制执行清理"""
        return self._perform_cleanup(force=True)
    
    def get_stats(self):
        """获取统计信息"""
        with self._lock:
            pool_stats = {}
            for pool_name, pool_info in self._object_pools.items():
                pool_stats[pool_name] = {
                    'pool_size': len(pool_info['pool']),
                    'max_size': pool_info['max_size'],
                    'created_count': pool_info['created_count'],
                    'reused_count': pool_info['reused_count'],
                    'reuse_ratio': (pool_info['reused_count'] / max(1, pool_info['created_count'] + pool_info['reused_count'])) * 100
                }
            
            return {
                'memory_usage': self._get_memory_usage(),
                'weak_refs_count': len(self._weak_refs),
                'cleanup_callbacks_count': len(self._cleanup_callbacks),
                'total_cleanups': self._stats['total_cleanups'],
                'objects_cleaned': self._stats['objects_cleaned'],
                'last_cleanup_time': self._stats['last_cleanup_time'],
                'object_pools': pool_stats
            }
    
    def set_memory_threshold(self, threshold):
        """设置内存阈值"""
        self.memory_threshold = max(50.0, min(95.0, threshold))
    
    def set_cleanup_interval(self, interval):
        """设置清理间隔"""
        self.cleanup_interval = max(10.0, interval)
        self._cleanup_timer.setInterval(int(self.cleanup_interval * 1000))
    
    def cleanup(self):
        """清理优化器本身"""
        self._cleanup_timer.stop()
        self.force_cleanup()
        
        with self._lock:
            self._weak_refs.clear()
            self._cleanup_callbacks.clear()
            self._object_pools.clear()

class SmartStringPool:
    """智能字符串池，用于减少重复字符串的内存占用"""
    
    def __init__(self, max_size=1000):
        self._pool = {}
        self._access_count = {}
        self._max_size = max_size
        self._lock = threading.Lock()
    
    def intern_string(self, string):
        """
        字符串驻留
        
        Args:
            string (str): 输入字符串
            
        Returns:
            str: 驻留的字符串
        """
        if not isinstance(string, str) or len(string) > 1000:  # 不池化过长的字符串
            return string
        
        with self._lock:
            if string in self._pool:
                self._access_count[string] += 1
                return self._pool[string]
            
            # 如果池已满，移除最少使用的字符串
            if len(self._pool) >= self._max_size:
                min_access_string = min(self._access_count, key=self._access_count.get)
                del self._pool[min_access_string]
                del self._access_count[min_access_string]
            
            # 添加新字符串到池中
            self._pool[string] = string
            self._access_count[string] = 1
            return string
    
    def get_stats(self):
        """获取字符串池统计信息"""
        with self._lock:
            total_access = sum(self._access_count.values())
            return {
                'pool_size': len(self._pool),
                'max_size': self._max_size,
                'total_access': total_access,
                'avg_access': total_access / max(1, len(self._pool))
            }

# 全局实例
_global_memory_optimizer = None
_global_string_pool = None

def get_memory_optimizer():
    """获取全局内存优化器实例"""
    global _global_memory_optimizer
    if _global_memory_optimizer is None:
        _global_memory_optimizer = MemoryOptimizer()
    return _global_memory_optimizer

def get_string_pool():
    """获取全局字符串池实例"""
    global _global_string_pool
    if _global_string_pool is None:
        _global_string_pool = SmartStringPool()
    return _global_string_pool

def cleanup_memory_optimizer():
    """清理内存优化器资源"""
    global _global_memory_optimizer, _global_string_pool
    if _global_memory_optimizer:
        _global_memory_optimizer.cleanup()
        _global_memory_optimizer = None
    if _global_string_pool:
        _global_string_pool = None
