"""
历史记录模型
"""
import os
import json
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal

class HistoryModel(QObject):
    """历史记录模型，负责管理命令历史记录"""
    
    # 定义信号
    history_changed = pyqtSignal(list)
    history_cleared = pyqtSignal()
    
    def __init__(self, history_file="runsim_command_history.json", limit=50):
        """
        初始化历史记录模型
        
        Args:
            history_file (str): 历史记录文件路径
            limit (int): 历史记录数量限制
        """
        super().__init__()
        self.history_file = history_file
        self.history_limit = limit
        self.history = []
    
    def load_history(self):
        """
        从文件加载历史记录
        
        Returns:
            list: 历史记录列表
        """
        self.history = []
        
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    
                # 确保 history 是列表类型
                if isinstance(history, list):
                    # 按时间戳倒序排序
                    history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
                    
                    # 添加到历史记录
                    for record in history:
                        if isinstance(record, dict) and 'command' in record:
                            self.history.append(record)
                            
                    # 限制数量
                    self.history = self.history[:self.history_limit]
        except Exception as e:
            print(f"加载历史记录失败: {str(e)}")

        self.history_changed.emit(self.history)
        return self.history
    
    def save_history(self):
        """
        保存历史记录到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存历史记录失败: {str(e)}")
            return False
    
    def add_command(self, command):
        """
        添加命令到历史记录
        
        Args:
            command (str): 命令
            
        Returns:
            list: 更新后的历史记录列表
        """
        # 避免重复添加相同的命令
        for record in self.history:
            if record.get('command') == command:
                # 将已有记录移到最前面
                self.history.remove(record)
                self.history.insert(0, record)
                self.history_changed.emit(self.history)
                self.save_history()
                return self.history
        
        # 添加新命令，包含时间戳
        new_record = {
            "command": command,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 将新命令添加到列表开头
        self.history.insert(0, new_record)
        
        # 限制文件中的历史记录数量
        self.history = self.history[:self.history_limit]
        
        self.history_changed.emit(self.history)
        self.save_history()
        return self.history
    
    def clear_history(self):
        """
        清空历史记录
        
        Returns:
            list: 空列表
        """
        self.history = []
        
        # 删除历史记录文件
        if os.path.exists(self.history_file):
            os.remove(self.history_file)
            
        self.history_cleared.emit()
        return self.history
    
    def get_history(self):
        """
        获取历史记录
        
        Returns:
            list: 历史记录列表
        """
        return self.history
