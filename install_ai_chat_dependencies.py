"""
AI聊天插件依赖安装脚本

自动安装AI聊天插件所需的Python包
"""

import subprocess
import sys
import os


def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr


def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False


def install_package(package_name, pip_name=None):
    """安装Python包"""
    if pip_name is None:
        pip_name = package_name
    
    print(f"正在安装 {package_name}...")
    
    success, output = run_command(f"pip install {pip_name}")
    
    if success:
        print(f"✓ {package_name} 安装成功")
        return True
    else:
        print(f"✗ {package_name} 安装失败: {output}")
        return False


def main():
    """主安装函数"""
    print("AI聊天插件依赖安装程序")
    print("=" * 50)
    
    # 基础依赖包
    basic_packages = [
        ("requests", "requests"),
    ]
    
    # 文档解析依赖包（可选）
    optional_packages = [
        ("PyPDF2", "PyPDF2"),
        ("docx", "python-docx"),
        ("docx2txt", "docx2txt"),
        ("striprtf", "striprtf"),
    ]
    
    print("\n检查基础依赖...")
    basic_success = True
    
    for package_name, pip_name in basic_packages:
        if check_package(package_name):
            print(f"✓ {package_name} 已安装")
        else:
            if not install_package(package_name, pip_name):
                basic_success = False
    
    if not basic_success:
        print("\n❌ 基础依赖安装失败，请手动安装后重试")
        return False
    
    print("\n检查可选依赖（用于文档解析）...")
    
    # 询问是否安装可选依赖
    install_optional = input("\n是否安装文档解析依赖？(y/n): ").lower().strip()
    
    if install_optional in ['y', 'yes', '是']:
        optional_success = 0
        total_optional = len(optional_packages)
        
        for package_name, pip_name in optional_packages:
            if check_package(package_name):
                print(f"✓ {package_name} 已安装")
                optional_success += 1
            else:
                if install_package(package_name, pip_name):
                    optional_success += 1
        
        print(f"\n可选依赖安装完成: {optional_success}/{total_optional}")
        
        if optional_success < total_optional:
            print("\n⚠️  部分可选依赖安装失败，但不影响基本功能")
            print("您仍然可以使用聊天和翻译功能")
            print("如需文档解析功能，请手动安装失败的包")
    else:
        print("跳过可选依赖安装")
    
    print("\n" + "=" * 50)
    print("🎉 依赖安装完成！")
    print("\n使用说明:")
    print("1. 启动RunSim GUI应用程序")
    print("2. 在菜单栏选择 '工具' → 'AI聊天助手'")
    print("3. 在设置中配置您的API信息")
    print("4. 开始使用AI聊天功能")
    
    print("\n支持的文档格式:")
    supported_formats = []
    
    if check_package("PyPDF2"):
        supported_formats.append("PDF")
    
    if check_package("docx"):
        supported_formats.append("Word (.docx)")
    
    if check_package("docx2txt"):
        supported_formats.append("Word (.doc)")
    
    if check_package("striprtf"):
        supported_formats.append("RTF")
    
    # 基础格式总是支持
    supported_formats.extend(["TXT", "Markdown"])
    
    print("- " + "\n- ".join(supported_formats))
    
    print("\n配置API的步骤:")
    print("1. 获取API Key:")
    print("   - DeepSeek: https://platform.deepseek.com/")
    print("   - 通义千问: https://dashscope.console.aliyun.com/")
    print("   - OpenAI: https://platform.openai.com/")
    print("   - Open Router: https://openrouter.ai/keys")
    print("2. 在插件设置中选择模型提供商")
    print("3. 输入对应的API Key和选择模型")
    print("4. 点击'测试连接'验证配置")
    print("\n🆓 推荐使用Open Router (支持免费模型):")
    print("- 一个API Key访问30+种AI模型")
    print("- 包含16个免费模型，零成本使用")
    print("- 免费模型推荐: deepseek-r1:free, gemini-2.0-flash-exp:free")
    print("- 支持OpenAI、Anthropic、Google、Meta等")
    print("- 详细配置指南: plugins/user/ai_chat/OPEN_ROUTER_GUIDE.md")
    print("- 免费模型指南: plugins/user/ai_chat/FREE_MODELS_GUIDE.md")
    
    return True


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
    except Exception as e:
        print(f"\n安装过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
