import os
import re
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog, QCheckBox, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class FindReplaceApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件查找替换工具")
        self.setGeometry(100, 100, 800, 600)
        self.setStyleSheet("QMainWindow { background-color: #f5f5f5; }")
        
        # 存储替换历史用于撤销
        self.replace_history = []
        
        self.init_ui()
    
    def init_ui(self):
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        layout = QVBoxLayout()
        
        # 目录选择和查找替换区域分组框
        dir_group = QGroupBox("目录选择与查找替换")
        dir_layout = QVBoxLayout()
        dir_input_layout = QHBoxLayout()
        dir_input_layout.addWidget(QLabel("目录:"))
        self.dir_input = QLineEdit()
        dir_input_layout.addWidget(self.dir_input)
        self.dir_browse_btn = QPushButton("浏览...")
        self.dir_browse_btn.clicked.connect(self.browse_directory)
        dir_input_layout.addWidget(self.dir_browse_btn)
        dir_layout.addLayout(dir_input_layout)
        
        find_layout = QHBoxLayout()
        find_layout.addWidget(QLabel("查找:"))
        self.find_input = QLineEdit()
        find_layout.addWidget(self.find_input)
        dir_layout.addLayout(find_layout)
        
        replace_layout = QHBoxLayout()
        replace_layout.addWidget(QLabel("替换:"))
        self.replace_input = QLineEdit()
        replace_layout.addWidget(self.replace_input)
        dir_layout.addLayout(replace_layout)
        
        # 正则匹配选项
        self.regex_checkbox = QCheckBox("使用正则表达式")
        dir_layout.addWidget(self.regex_checkbox)
        # 添加文件后缀输入框
        ext_layout = QHBoxLayout()
        ext_layout.addWidget(QLabel("文件后缀(多个用分号分隔):"))
        self.ext_input = QLineEdit()
        ext_layout.addWidget(self.ext_input)
        dir_layout.addLayout(ext_layout)
        dir_group.setLayout(dir_layout)
        layout.addWidget(dir_group)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        self.find_btn = QPushButton("查找")
        self.find_btn.setStyleSheet("background-color: #007bff; color: white;")
        self.find_btn.clicked.connect(self.find_text)
        btn_layout.addWidget(self.find_btn)
        
        self.replace_btn = QPushButton("替换")
        self.replace_btn.setStyleSheet("background-color: #28a745; color: white;")
        self.replace_btn.clicked.connect(self.replace_text)
        btn_layout.addWidget(self.replace_btn)
        
        self.undo_btn = QPushButton("撤销")
        self.undo_btn.setStyleSheet("background-color: #ffc107; color: white;")
        self.undo_btn.clicked.connect(self.undo_replace)
        btn_layout.addWidget(self.undo_btn)
        layout.addLayout(btn_layout)
        
        # 日志显示分组框
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout()
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setStyleSheet("background-color: #333; color: white; font-family: Consolas;")
        log_layout.addWidget(self.log_output)
        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.setStyleSheet("background-color: #6c757d; color: white;")
        self.clear_btn.clicked.connect(self.clear_log)
        log_layout.addWidget(self.clear_btn)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        main_widget.setLayout(layout)
    
    def browse_directory(self):
        directory = QFileDialog.getExistingDirectory(self, "选择目录")
        if directory:
            self.dir_input.setText(directory)
    
    def find_text(self):
        directory = self.dir_input.text()
        search_text = self.find_input.text()
        file_extension = self.ext_input.text()
        
        if not directory or not search_text:
            self.log("请先选择目录并输入查找内容")
            return
        
        self.log(f"开始在目录 {directory} 中查找后缀为 {file_extension} 的文件: {search_text}")
        
        try:
            for root, _, files in os.walk(directory):
                for file in files:
                    if file_extension:
                        extensions = [ext.strip() for ext in file_extension.split(';') if ext.strip()]
                        if extensions and not any(file.endswith(ext) for ext in extensions):
                            continue
                    
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            
                            if self.regex_checkbox.isChecked():
                                for i, line in enumerate(lines, 1):
                                    if re.search(search_text, line):
                                        context_start = max(0, i-3)
                                        context_end = min(len(lines), i+3)
                                        context = lines[context_start:context_end]
                                        self.log(f"找到匹配: {file_path} 行号: {i}")
                                        self.log("上下文内容:")
                                        for j, ctx_line in enumerate(context, context_start+1):
                                            prefix = "> " if j == i else "  "
                                            self.log(f"{prefix}{j}: {ctx_line.strip()}")
                            else:
                                for i, line in enumerate(lines, 1):
                                    if search_text in line:
                                        context_start = max(0, i-3)
                                        context_end = min(len(lines), i+3)
                                        context = lines[context_start:context_end]
                                        self.log(f"找到匹配: {file_path} 行号: {i}")
                                        self.log("上下文内容:")
                                        for j, ctx_line in enumerate(context, context_start+1):
                                            prefix = "> " if j == i else "  "
                                            self.log(f"{prefix}{j}: {ctx_line.strip()}")
                    except (UnicodeDecodeError, PermissionError):
                        continue
            
            self.log("查找完成")
        except Exception as e:
            self.log(f"查找出错: {str(e)}")
    
    def replace_text(self):
        directory = self.dir_input.text()
        search_text = self.find_input.text()
        replace_text = self.replace_input.text()
        file_extension = self.ext_input.text()
        
        if not directory or not search_text:
            self.log("请先选择目录并输入查找和替换内容")
            return
        
        self.log(f"开始在目录 {directory} 中替换后缀为 {file_extension} 的文件: {search_text} -> {replace_text}")
        
        try:
            replacements = []
            
            for root, _, files in os.walk(directory):
                for file in files:
                    if file_extension:
                        extensions = [ext.strip() for ext in file_extension.split(';') if ext.strip()]
                        if extensions and not any(file.endswith(ext) for ext in extensions):
                            continue
                    
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                            if self.regex_checkbox.isChecked():
                                if re.search(search_text, content):
                                    new_content = re.sub(search_text, replace_text, content)
                                    if new_content != content:
                                        replacements.append((file_path, content, new_content))
                                        self.log(f"将在 {file_path} 中执行替换")
                            else:
                                if search_text in content:
                                    new_content = content.replace(search_text, replace_text)
                                    if new_content != content:
                                        replacements.append((file_path, content, new_content))
                                        self.log(f"将在 {file_path} 中执行替换")
                    except (UnicodeDecodeError, PermissionError):
                        continue
            
            # 执行实际替换
            for file_path, original_content, new_content in replacements:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
            if replacements:
                self.replace_history.append(replacements)
                self.log(f"成功替换了 {len(replacements)} 个文件")
            else:
                self.log("没有找到需要替换的内容")
            
        except Exception as e:
            self.log(f"替换出错: {str(e)}")
    
    def undo_replace(self):
        if not self.replace_history:
            self.log("没有可撤销的操作")
            return
        
        last_replacements = self.replace_history.pop()
        undo_count = 0
        
        try:
            for file_path, original_content, _ in last_replacements:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                    undo_count += 1
            
            self.log(f"已撤销 {undo_count} 个文件的替换操作")
        except Exception as e:
            self.log(f"撤销出错: {str(e)}")
    
    def log(self, message):
        self.log_output.append(message)
    
    def clear_log(self):
        self.log_output.clear()
        self.log("日志已清空")

if __name__ == "__main__":
    app = QApplication([])
    window = FindReplaceApp()
    window.show()
    app.exec_()