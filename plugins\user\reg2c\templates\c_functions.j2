// Register access functions
{% for reg in registers %}
{% for field in reg.fields %}
{% if field.rw == "RW" %}
static inline void {{ reg.name }}_{{ field.name }}_set(uint32_t value) {
    {{ module_name }}_t *regs = ({{ module_name }}_t *){{ module_name | upper }}_BASE_ADDR;
    regs->{{ reg.name }} = (regs->{{ reg.name }} & ~({{ reg.name|upper }}_{{ field.name|upper }}_MASK << {{ reg.name|upper }}_{{ field.name|upper }}_POS)) | ((value & {{ reg.name|upper }}_{{ field.name|upper }}_MASK) << {{ reg.name|upper }}_{{ field.name|upper }}_POS);
}
{% endif %}

static inline uint32_t {{ reg.name }}_{{ field.name }}_get() {
    {{ module_name }}_t *regs = ({{ module_name }}_t *){{ module_name | upper }}_BASE_ADDR;
    return (regs->{{ reg.name }} >> {{ reg.name|upper }}_{{ field.name|upper }}_POS) & {{ reg.name|upper }}_{{ field.name|upper }}_MASK;
}
{% endfor %}
{% endfor %}

#endif {{ module_name|upper }}_H