"""
文件处理工具类

提供文件操作相关的工具函数。
"""

import os
import mimetypes
from typing import Optional, List


class FileUtils:
    """文件处理工具类"""
    
    @staticmethod
    def detect_encoding(file_path: str) -> str:
        """检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            编码名称
        """
        try:
            import chardet
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                return result['encoding'] or 'utf-8'
        except ImportError:
            return 'utf-8'
        except Exception:
            return 'utf-8'
    
    @staticmethod
    def read_file_content(file_path: str, encoding: Optional[str] = None) -> str:
        """读取文件内容
        
        Args:
            file_path: 文件路径
            encoding: 编码，如果为None则自动检测
            
        Returns:
            文件内容
        """
        if encoding is None:
            encoding = FileUtils.detect_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                return f.read()
        except Exception as e:
            raise IOError(f"读取文件失败: {e}")
    
    @staticmethod
    def is_text_file(file_path: str) -> bool:
        """判断是否为文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为文本文件
        """
        try:
            # 通过MIME类型判断
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type and mime_type.startswith('text/'):
                return True
            
            # 通过文件扩展名判断
            text_extensions = {
                '.txt', '.py', '.pyw', '.c', '.cpp', '.cc', '.cxx', '.h', '.hpp',
                '.sv', '.v', '.vh', '.svh', '.js', '.jsx', '.ts', '.tsx',
                '.html', '.htm', '.css', '.scss', '.sass', '.less',
                '.xml', '.json', '.yaml', '.yml', '.toml', '.ini', '.cfg',
                '.md', '.rst', '.log', '.sh', '.bat', '.ps1',
                '.sql', '.r', '.R', '.m', '.pl', '.php', '.rb', '.go',
                '.java', '.kt', '.swift', '.dart', '.scala', '.clj',
                '.hs', '.elm', '.ex', '.exs', '.erl', '.fs', '.fsx',
                '.vb', '.cs', '.f', '.f90', '.f95', '.for', '.pas'
            }
            
            _, ext = os.path.splitext(file_path.lower())
            if ext in text_extensions:
                return True
            
            # 尝试读取文件开头判断
            try:
                with open(file_path, 'rb') as f:
                    chunk = f.read(1024)
                    # 检查是否包含null字节（二进制文件的特征）
                    if b'\x00' in chunk:
                        return False
                    # 尝试解码为UTF-8
                    chunk.decode('utf-8')
                    return True
            except (UnicodeDecodeError, IOError):
                return False
                
        except Exception:
            return False
    
    @staticmethod
    def get_file_extension(file_path: str) -> str:
        """获取文件扩展名
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件扩展名（包含点号）
        """
        _, ext = os.path.splitext(file_path)
        return ext.lower()
    
    @staticmethod
    def get_language_from_extension(extension: str) -> str:
        """根据文件扩展名获取编程语言
        
        Args:
            extension: 文件扩展名
            
        Returns:
            编程语言名称
        """
        language_map = {
            '.py': 'python',
            '.pyw': 'python',
            '.c': 'c',
            '.cpp': 'cpp',
            '.cc': 'cpp',
            '.cxx': 'cpp',
            '.h': 'c',
            '.hpp': 'cpp',
            '.sv': 'systemverilog',
            '.v': 'verilog',
            '.vh': 'verilog',
            '.svh': 'systemverilog',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.html': 'html',
            '.htm': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.less': 'less',
            '.xml': 'xml',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.rst': 'rst',
            '.sh': 'bash',
            '.bat': 'batch',
            '.ps1': 'powershell',
            '.sql': 'sql',
            '.r': 'r',
            '.R': 'r',
            '.m': 'matlab',
            '.pl': 'perl',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.java': 'java',
            '.kt': 'kotlin',
            '.swift': 'swift',
            '.dart': 'dart',
            '.scala': 'scala',
            '.clj': 'clojure',
            '.hs': 'haskell',
            '.elm': 'elm',
            '.ex': 'elixir',
            '.exs': 'elixir',
            '.erl': 'erlang',
            '.fs': 'fsharp',
            '.fsx': 'fsharp',
            '.vb': 'vbnet',
            '.cs': 'csharp',
            '.f': 'fortran',
            '.f90': 'fortran',
            '.f95': 'fortran',
            '.for': 'fortran',
            '.pas': 'pascal'
        }
        
        return language_map.get(extension.lower(), 'text')
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            格式化的文件大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @staticmethod
    def get_relative_path(file_path: str, base_path: str) -> str:
        """获取相对路径
        
        Args:
            file_path: 文件路径
            base_path: 基础路径
            
        Returns:
            相对路径
        """
        try:
            return os.path.relpath(file_path, base_path)
        except ValueError:
            # 在Windows上，如果路径在不同驱动器上会抛出ValueError
            return file_path
    
    @staticmethod
    def ensure_directory_exists(directory: str):
        """确保目录存在
        
        Args:
            directory: 目录路径
        """
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'size_formatted': FileUtils.format_file_size(stat.st_size),
                'modified_time': stat.st_mtime,
                'is_text': FileUtils.is_text_file(file_path),
                'extension': FileUtils.get_file_extension(file_path),
                'language': FileUtils.get_language_from_extension(
                    FileUtils.get_file_extension(file_path)
                )
            }
        except OSError:
            return {
                'size': 0,
                'size_formatted': '0 B',
                'modified_time': 0,
                'is_text': False,
                'extension': '',
                'language': 'unknown'
            }
