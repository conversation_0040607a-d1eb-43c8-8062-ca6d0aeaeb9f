# AI聊天助手插件更新日志

## v1.1.1 - 2024-01-XX

### 新增功能
- **免费模型支持**：为Open Router添加多个免费模型选项
  - 新增9个免费模型，包括DeepSeek、Gemini、Llama等
  - 默认优先选择免费模型，降低使用成本
  - 支持免费和付费模型混合使用

### 免费模型列表
- `deepseek/deepseek-r1:free` - DeepSeek最新推理模型（默认推荐）
- `google/gemini-2.0-flash-exp:free` - Google Gemini 2.0实验版
- `google/gemini-flash-1.5-8b` - Google轻量级模型
- `meta-llama/llama-3.2-3b-instruct:free` - Meta Llama 3.2
- `meta-llama/llama-3.2-1b-instruct:free` - 超轻量级模型
- `meta-llama/llama-3.1-8b-instruct:free` - 经典Llama模型
- `microsoft/phi-3-mini-128k-instruct:free` - 微软Phi-3
- `qwen/qwen-2.5-7b-instruct:free` - 阿里通义千问开源版
- `openrouter/auto` - 自动选择最佳免费模型

### 改进内容
- **智能默认选择**：Open Router现在默认选择免费模型
- **成本优化指南**：更新文档提供免费模型使用建议
- **模型分类**：在界面中区分免费和付费模型

---

## v1.1.0 - 2024-01-XX

### 新增功能
- **Open Router支持**：添加Open Router作为新的AI模型提供商
  - 支持通过一个API Key访问多种AI模型
  - 包含OpenAI、Anthropic、Google、Meta等30+个热门模型
  - 自动配置API URL为 `https://openrouter.ai/api/v1`

### 支持的Open Router模型
- **OpenAI系列**：
  - `openai/gpt-4o` - 最新的GPT-4模型
  - `openai/gpt-4o-mini` - 轻量版GPT-4
  - `openai/gpt-4-turbo` - GPT-4 Turbo
  - `openai/gpt-3.5-turbo` - GPT-3.5 Turbo

- **Anthropic系列**：
  - `anthropic/claude-3.5-sonnet` - Claude 3.5 Sonnet
  - `anthropic/claude-3-haiku` - Claude 3 Haiku

- **Google系列**：
  - `google/gemini-pro-1.5` - Gemini Pro 1.5
  - `google/gemini-flash-1.5` - Gemini Flash 1.5

- **Meta系列**：
  - `meta-llama/llama-3.1-405b-instruct` - Llama 3.1 405B
  - `meta-llama/llama-3.1-70b-instruct` - Llama 3.1 70B
  - `meta-llama/llama-3.1-8b-instruct` - Llama 3.1 8B

- **其他模型**：
  - `mistralai/mistral-large` - Mistral Large
  - `mistralai/mistral-medium` - Mistral Medium
  - `cohere/command-r-plus` - Cohere Command R+
  - `perplexity/llama-3.1-sonar-large-128k-online` - Perplexity在线搜索模型

### 改进内容
- **配置管理**：更新配置模型以支持Open Router
- **设置界面**：在设置对话框中添加Open Router选项
- **文档更新**：
  - 更新README.md添加Open Router配置说明
  - 新增OPEN_ROUTER_GUIDE.md详细配置指南
  - 更新使用示例和安装脚本

### 技术改进
- 扩展`PREDEFINED_MODELS`字典包含Open Router模型
- 更新`set_model_provider`方法支持Open Router URL自动配置
- 增强设置对话框的提供商切换功能
- 添加专门的Open Router测试脚本

### 文档更新
- **新增文件**：
  - `OPEN_ROUTER_GUIDE.md` - Open Router详细配置指南
  - `test_openrouter_config.py` - Open Router配置测试脚本
  - `CHANGELOG.md` - 更新日志文件

- **更新文件**：
  - `README.md` - 添加Open Router配置说明
  - `AI_CHAT_PLUGIN_SUMMARY.md` - 更新项目总结
  - `install_ai_chat_dependencies.py` - 添加Open Router安装说明
  - `usage_examples.py` - 更新使用示例

### 使用指南
1. **获取Open Router API Key**：
   - 访问 https://openrouter.ai
   - 注册账户并创建API Key

2. **配置插件**：
   - 在设置中选择"Open Router"作为模型提供商
   - 输入API Key
   - 选择想要使用的模型

3. **推荐模型**：
   - 日常对话：`openai/gpt-4o-mini`
   - 复杂任务：`anthropic/claude-3.5-sonnet`
   - 代码相关：`openai/gpt-4o`
   - 长文档：`google/gemini-pro-1.5`

### 优势特性
- **模型多样性**：一个API Key访问15+种不同的AI模型
- **成本优化**：可以根据任务选择最适合的模型
- **简化管理**：统一的API接口和账单管理
- **高可用性**：多个模型提供商确保服务稳定性

---

## v1.0.0 - 2024-01-XX

### 初始版本
- **AI聊天对话**：现代化聊天界面，支持流式响应
- **文档上传解析**：支持PDF、Word、TXT、Markdown等格式
- **中英文翻译**：智能语言检测和双向翻译
- **多模型支持**：DeepSeek、通义千问、OpenAI
- **配置管理**：完整的设置界面和配置验证
- **插件集成**：完美融入RunSim GUI应用程序

### 核心功能
- 多会话管理
- 消息历史持久化
- 文档智能摘要
- 实时翻译功能
- 错误处理和日志记录
- 模块化架构设计

### 技术特性
- MVC架构模式
- 异步处理支持
- 现代化UI设计
- 完善的测试覆盖
- 详细的使用文档
