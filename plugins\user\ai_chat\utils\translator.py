"""
翻译工具类

提供中英文互译功能
"""

import re
from typing import <PERSON>ple, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QThread


class TranslationWorker(QThread):
    """翻译工作线程"""
    
    # 信号
    translation_finished = pyqtSignal(bool, str, str)  # success, result, error
    
    def __init__(self, api_client, text: str, target_language: str):
        super().__init__()
        self.api_client = api_client
        self.text = text
        self.target_language = target_language
        self.is_cancelled = False
    
    def cancel(self):
        """取消翻译"""
        self.is_cancelled = True
    
    def run(self):
        """执行翻译"""
        try:
            if self.is_cancelled:
                return
            
            response = self.api_client.translate_text(self.text, self.target_language)
            
            if not self.is_cancelled:
                if response.success:
                    self.translation_finished.emit(True, response.data, "")
                else:
                    self.translation_finished.emit(False, "", response.error)
                    
        except Exception as e:
            if not self.is_cancelled:
                self.translation_finished.emit(False, "", str(e))


class Translator(QObject):
    """翻译器类"""
    
    # 信号
    translation_started = pyqtSignal(str)  # text
    translation_finished = pyqtSignal(str, str, str)  # original, translated, target_lang
    translation_error = pyqtSignal(str)  # error_message
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.current_worker = None
    
    def detect_language(self, text: str) -> str:
        """检测文本语言
        
        Args:
            text: 输入文本
            
        Returns:
            str: 语言代码 ('zh' 或 'en')
        """
        if not text:
            return 'en'
        
        # 简单的语言检测：统计中文字符比例
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        total_chars = len(re.sub(r'\s', '', text))
        
        if total_chars == 0:
            return 'en'
        
        chinese_ratio = chinese_chars / total_chars
        
        # 如果中文字符比例超过30%，认为是中文
        return 'zh' if chinese_ratio > 0.3 else 'en'
    
    def get_target_language(self, text: str) -> str:
        """根据输入文本确定目标语言
        
        Args:
            text: 输入文本
            
        Returns:
            str: 目标语言代码
        """
        source_lang = self.detect_language(text)
        return 'en' if source_lang == 'zh' else 'zh'
    
    def translate_text(self, text: str, target_language: str = None) -> bool:
        """翻译文本
        
        Args:
            text: 要翻译的文本
            target_language: 目标语言，如果为None则自动检测
            
        Returns:
            bool: 是否成功启动翻译
        """
        if not text.strip():
            self.translation_error.emit("翻译文本不能为空")
            return False
        
        # 取消当前翻译
        self.cancel_translation()
        
        # 确定目标语言
        if target_language is None:
            target_language = self.get_target_language(text)
        
        # 发送开始信号
        self.translation_started.emit(text)
        
        # 创建翻译工作线程
        self.current_worker = TranslationWorker(self.api_client, text, target_language)
        self.current_worker.translation_finished.connect(self._on_translation_finished)
        self.current_worker.start()
        
        return True
    
    def translate_to_chinese(self, text: str) -> bool:
        """翻译为中文
        
        Args:
            text: 要翻译的文本
            
        Returns:
            bool: 是否成功启动翻译
        """
        return self.translate_text(text, 'zh')
    
    def translate_to_english(self, text: str) -> bool:
        """翻译为英文
        
        Args:
            text: 要翻译的文本
            
        Returns:
            bool: 是否成功启动翻译
        """
        return self.translate_text(text, 'en')
    
    def cancel_translation(self):
        """取消当前翻译"""
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.cancel()
            self.current_worker.wait(1000)  # 等待最多1秒
            if self.current_worker.isRunning():
                self.current_worker.terminate()
    
    def _on_translation_finished(self, success: bool, result: str, error: str):
        """翻译完成处理"""
        if success:
            # 获取原始文本和目标语言
            original_text = self.current_worker.text
            target_lang = self.current_worker.target_language
            
            self.translation_finished.emit(original_text, result, target_lang)
        else:
            self.translation_error.emit(error)
        
        # 清理工作线程
        if self.current_worker:
            self.current_worker.deleteLater()
            self.current_worker = None
    
    def is_translating(self) -> bool:
        """检查是否正在翻译
        
        Returns:
            bool: 是否正在翻译
        """
        return self.current_worker is not None and self.current_worker.isRunning()
    
    @staticmethod
    def split_text_for_translation(text: str, max_length: int = 2000) -> list:
        """将长文本分割为适合翻译的片段
        
        Args:
            text: 原始文本
            max_length: 每个片段的最大长度
            
        Returns:
            list: 文本片段列表
        """
        if len(text) <= max_length:
            return [text]
        
        segments = []
        current_segment = ""
        
        # 按句子分割
        sentences = re.split(r'([.!?。！？\n])', text)
        
        for i in range(0, len(sentences), 2):
            sentence = sentences[i]
            punctuation = sentences[i + 1] if i + 1 < len(sentences) else ""
            full_sentence = sentence + punctuation
            
            if len(current_segment) + len(full_sentence) <= max_length:
                current_segment += full_sentence
            else:
                if current_segment:
                    segments.append(current_segment.strip())
                    current_segment = full_sentence
                else:
                    # 单个句子太长，强制分割
                    segments.append(full_sentence[:max_length])
                    current_segment = full_sentence[max_length:]
        
        if current_segment:
            segments.append(current_segment.strip())
        
        return [seg for seg in segments if seg.strip()]
    
    @staticmethod
    def clean_translation_result(text: str) -> str:
        """清理翻译结果
        
        Args:
            text: 翻译结果
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除可能的翻译提示语
        patterns = [
            r'^(翻译结果?[:：]?\s*)',
            r'^(Translation[:：]?\s*)',
            r'^(英文翻译[:：]?\s*)',
            r'^(中文翻译[:：]?\s*)',
            r'^(以下是翻译[:：]?\s*)',
        ]
        
        for pattern in patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    @staticmethod
    def format_translation_pair(original: str, translated: str, target_lang: str) -> str:
        """格式化翻译对
        
        Args:
            original: 原文
            translated: 译文
            target_lang: 目标语言
            
        Returns:
            str: 格式化的翻译对
        """
        if target_lang == 'zh':
            return f"原文：{original}\n译文：{translated}"
        else:
            return f"中文：{original}\nEnglish：{translated}"
    
    def cleanup(self):
        """清理资源"""
        self.cancel_translation()
        if self.current_worker:
            self.current_worker.deleteLater()
            self.current_worker = None
