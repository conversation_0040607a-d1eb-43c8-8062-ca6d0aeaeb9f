"""
任务队列配置管理
提供任务队列的配置加载、保存和管理功能
"""
import os
import json
from typing import Dict, Any, Optional
from datetime import datetime


class TaskQueueConfig:
    """任务队列配置管理器"""
    
    DEFAULT_CONFIG = {
        # 队列设置
        'max_concurrent_tasks': 5,
        'scheduling_interval': 1000,
        'auto_retry_failed': True,
        'max_retry_count': 3,
        
        # 持久化设置
        'save_queue_state': True,
        'queue_state_file': 'task_queue_state.json',
        'auto_save_interval': 30000,  # 30秒
        
        # 资源管理
        'max_memory_mb': 4096,
        'max_cpu_percent': 80,
        'resource_check_interval': 5000,
        
        # 日志设置
        'enable_task_logging': True,
        'log_level': 'INFO',
        'log_file': 'task_queue.log',
        'max_log_size_mb': 10,
        
        # 界面设置
        'auto_refresh_interval': 2000,
        'show_completed_tasks': True,
        'max_displayed_tasks': 1000,
        
        # 通知设置
        'enable_notifications': True,
        'notify_on_completion': True,
        'notify_on_failure': True,
        
        # 高级设置
        'enable_task_dependencies': True,
        'enable_priority_scheduling': True,
        'cleanup_completed_after_hours': 24
    }
    
    def __init__(self, config_file: str = 'task_queue_config.json'):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self.DEFAULT_CONFIG.copy()
        self.load_config()
    
    def load_config(self) -> bool:
        """
        从文件加载配置
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                
                # 合并配置，保留默认值
                self._merge_config(file_config)
                
                print(f"任务队列配置已从 {self.config_file} 加载")
                return True
            else:
                # 文件不存在，使用默认配置并保存
                self.save_config()
                print(f"使用默认配置并保存到 {self.config_file}")
                return True
                
        except Exception as e:
            print(f"加载任务队列配置失败: {e}")
            # 使用默认配置
            self.config = self.DEFAULT_CONFIG.copy()
            return False
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 添加保存时间戳
            config_to_save = self.config.copy()
            config_to_save['_saved_at'] = datetime.now().isoformat()
            config_to_save['_version'] = '1.0'
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            print(f"任务队列配置已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            print(f"保存任务队列配置失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            self.config[key] = value
            return True
        except Exception as e:
            print(f"设置配置失败: {e}")
            return False
    
    def update(self, config_dict: Dict[str, Any]) -> bool:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            self.config.update(config_dict)
            return True
        except Exception as e:
            print(f"更新配置失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """
        重置为默认配置
        
        Returns:
            bool: 重置是否成功
        """
        try:
            self.config = self.DEFAULT_CONFIG.copy()
            return self.save_config()
        except Exception as e:
            print(f"重置配置失败: {e}")
            return False
    
    def validate_config(self) -> Dict[str, str]:
        """
        验证配置有效性
        
        Returns:
            Dict[str, str]: 验证错误信息，空字典表示验证通过
        """
        errors = {}
        
        # 验证数值范围
        if not (1 <= self.config.get('max_concurrent_tasks', 5) <= 20):
            errors['max_concurrent_tasks'] = '最大并发任务数必须在1-20之间'
        
        if not (100 <= self.config.get('scheduling_interval', 1000) <= 60000):
            errors['scheduling_interval'] = '调度间隔必须在100-60000毫秒之间'
        
        if not (0 <= self.config.get('max_retry_count', 3) <= 10):
            errors['max_retry_count'] = '最大重试次数必须在0-10之间'
        
        if not (512 <= self.config.get('max_memory_mb', 4096) <= 32768):
            errors['max_memory_mb'] = '最大内存限制必须在512-32768MB之间'
        
        if not (10 <= self.config.get('max_cpu_percent', 80) <= 100):
            errors['max_cpu_percent'] = 'CPU使用率限制必须在10-100%之间'
        
        # 验证文件路径
        state_file = self.config.get('queue_state_file', '')
        if not state_file or not isinstance(state_file, str):
            errors['queue_state_file'] = '队列状态文件路径不能为空'
        
        log_file = self.config.get('log_file', '')
        if not log_file or not isinstance(log_file, str):
            errors['log_file'] = '日志文件路径不能为空'
        
        return errors
    
    def get_display_config(self) -> Dict[str, Any]:
        """
        获取用于显示的配置信息
        
        Returns:
            Dict[str, Any]: 格式化的配置信息
        """
        display_config = {}
        
        # 基本设置
        display_config['基本设置'] = {
            '最大并发任务数': self.config.get('max_concurrent_tasks', 5),
            '调度间隔(毫秒)': self.config.get('scheduling_interval', 1000),
            '自动重试失败任务': '是' if self.config.get('auto_retry_failed', True) else '否',
            '最大重试次数': self.config.get('max_retry_count', 3)
        }
        
        # 持久化设置
        display_config['持久化设置'] = {
            '保存队列状态': '是' if self.config.get('save_queue_state', True) else '否',
            '状态文件': self.config.get('queue_state_file', ''),
            '自动保存间隔(毫秒)': self.config.get('auto_save_interval', 30000)
        }
        
        # 资源管理
        display_config['资源管理'] = {
            '最大内存(MB)': self.config.get('max_memory_mb', 4096),
            '最大CPU使用率(%)': self.config.get('max_cpu_percent', 80),
            '资源检查间隔(毫秒)': self.config.get('resource_check_interval', 5000)
        }
        
        # 界面设置
        display_config['界面设置'] = {
            '自动刷新间隔(毫秒)': self.config.get('auto_refresh_interval', 2000),
            '显示已完成任务': '是' if self.config.get('show_completed_tasks', True) else '否',
            '最大显示任务数': self.config.get('max_displayed_tasks', 1000)
        }
        
        return display_config
    
    def _merge_config(self, file_config: Dict[str, Any]):
        """
        合并文件配置和默认配置
        
        Args:
            file_config: 从文件加载的配置
        """
        # 只更新存在于默认配置中的键
        for key, value in file_config.items():
            if key.startswith('_'):  # 跳过元数据
                continue
            if key in self.DEFAULT_CONFIG:
                self.config[key] = value
            else:
                print(f"警告: 忽略未知配置项 '{key}'")
    
    def export_config(self, export_file: str) -> bool:
        """
        导出配置到指定文件
        
        Args:
            export_file: 导出文件路径
            
        Returns:
            bool: 导出是否成功
        """
        try:
            export_data = {
                'config': self.config,
                'exported_at': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"配置已导出到 {export_file}")
            return True
            
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str) -> bool:
        """
        从指定文件导入配置
        
        Args:
            import_file: 导入文件路径
            
        Returns:
            bool: 导入是否成功
        """
        try:
            if not os.path.exists(import_file):
                print(f"导入文件不存在: {import_file}")
                return False
            
            with open(import_file, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if 'config' in import_data:
                self._merge_config(import_data['config'])
            else:
                self._merge_config(import_data)
            
            # 验证导入的配置
            errors = self.validate_config()
            if errors:
                print(f"导入的配置存在错误: {errors}")
                return False
            
            # 保存导入的配置
            success = self.save_config()
            if success:
                print(f"配置已从 {import_file} 导入")
            
            return success
            
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
