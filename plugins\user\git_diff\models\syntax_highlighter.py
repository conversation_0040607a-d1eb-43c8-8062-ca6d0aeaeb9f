"""
语法高亮模型

提供基于PyQt5的语法高亮功能，支持多种编程语言：
- Python
- C/C++
- SystemVerilog/Verilog
- JavaScript
- 通用文本格式
"""

from PyQt5.QtGui import QSyntaxHighlighter, QTextCharFormat, QColor, QFont
from PyQt5.QtCore import QRegExp, Qt
from typing import Dict, List, Tuple
import re


class HighlightingRule:
    """高亮规则类"""
    
    def __init__(self, pattern: str, format: QTextCharFormat, is_regex: bool = True):
        self.pattern = pattern
        self.format = format
        self.is_regex = is_regex
        if is_regex:
            self.regex = QRegExp(pattern)
        else:
            self.regex = None


class SyntaxHighlighter(QSyntaxHighlighter):
    """语法高亮器"""
    
    def __init__(self, parent=None, file_extension: str = ""):
        super().__init__(parent)
        self.file_extension = file_extension.lower()
        self.highlighting_rules = []
        self._setup_formats()
        self._setup_highlighting_rules()
    
    def _setup_formats(self):
        """设置文本格式 - 与RunSim GUI主题兼容"""
        # 关键字格式 - 使用RunSim GUI的蓝色主题
        self.keyword_format = QTextCharFormat()
        self.keyword_format.setForeground(QColor(74, 158, 255))  # RunSim GUI主色调
        self.keyword_format.setFontWeight(QFont.Bold)

        # 字符串格式 - 绿色
        self.string_format = QTextCharFormat()
        self.string_format.setForeground(QColor(40, 167, 69))  # 与差异显示的绿色一致

        # 注释格式 - 灰色
        self.comment_format = QTextCharFormat()
        self.comment_format.setForeground(QColor(153, 153, 153))  # 与行号颜色一致
        self.comment_format.setFontItalic(True)

        # 数字格式 - 紫色
        self.number_format = QTextCharFormat()
        self.number_format.setForeground(QColor(128, 0, 128))

        # 函数格式 - 青色
        self.function_format = QTextCharFormat()
        self.function_format.setForeground(QColor(0, 128, 128))
        self.function_format.setFontWeight(QFont.Bold)

        # 类名格式 - 深紫色
        self.class_format = QTextCharFormat()
        self.class_format.setForeground(QColor(102, 0, 153))
        self.class_format.setFontWeight(QFont.Bold)

        # 预处理器格式 - 橙色
        self.preprocessor_format = QTextCharFormat()
        self.preprocessor_format.setForeground(QColor(255, 140, 0))

        # 操作符格式 - 深橙色
        self.operator_format = QTextCharFormat()
        self.operator_format.setForeground(QColor(255, 102, 0))
    
    def _setup_highlighting_rules(self):
        """设置高亮规则"""
        self.highlighting_rules = []
        
        if self.file_extension in ['.py', '.pyw']:
            self._setup_python_rules()
        elif self.file_extension in ['.c', '.cpp', '.cc', '.cxx', '.h', '.hpp']:
            self._setup_c_cpp_rules()
        elif self.file_extension in ['.sv', '.v', '.vh', '.svh']:
            self._setup_systemverilog_rules()
        elif self.file_extension in ['.js', '.jsx', '.ts', '.tsx']:
            self._setup_javascript_rules()
        else:
            self._setup_generic_rules()
    
    def _setup_python_rules(self):
        """设置Python语法高亮规则"""
        # Python关键字
        python_keywords = [
            'and', 'as', 'assert', 'break', 'class', 'continue', 'def',
            'del', 'elif', 'else', 'except', 'exec', 'finally', 'for',
            'from', 'global', 'if', 'import', 'in', 'is', 'lambda',
            'not', 'or', 'pass', 'print', 'raise', 'return', 'try',
            'while', 'with', 'yield', 'True', 'False', 'None'
        ]
        
        for keyword in python_keywords:
            pattern = f'\\b{keyword}\\b'
            self.highlighting_rules.append(
                HighlightingRule(pattern, self.keyword_format)
            )
        
        # Python内置函数
        builtin_functions = [
            'abs', 'all', 'any', 'bin', 'bool', 'chr', 'dict', 'dir',
            'enumerate', 'eval', 'filter', 'float', 'format', 'frozenset',
            'getattr', 'globals', 'hasattr', 'hash', 'help', 'hex', 'id',
            'input', 'int', 'isinstance', 'issubclass', 'iter', 'len',
            'list', 'locals', 'map', 'max', 'min', 'next', 'object',
            'oct', 'open', 'ord', 'pow', 'print', 'range', 'repr',
            'reversed', 'round', 'set', 'setattr', 'slice', 'sorted',
            'str', 'sum', 'super', 'tuple', 'type', 'vars', 'zip'
        ]
        
        for func in builtin_functions:
            pattern = f'\\b{func}\\b'
            self.highlighting_rules.append(
                HighlightingRule(pattern, self.function_format)
            )
        
        # 字符串
        self.highlighting_rules.extend([
            HighlightingRule('"[^"\\\\]*(\\\\.[^"\\\\]*)*"', self.string_format),
            HighlightingRule("'[^'\\\\]*(\\\\.[^'\\\\]*)*'", self.string_format),
            HighlightingRule('""".*"""', self.string_format),
            HighlightingRule("'''.*'''", self.string_format),
        ])
        
        # 注释
        self.highlighting_rules.append(
            HighlightingRule('#[^\n]*', self.comment_format)
        )
        
        # 数字
        self.highlighting_rules.append(
            HighlightingRule('\\b[0-9]+\\.?[0-9]*\\b', self.number_format)
        )
        
        # 函数定义
        self.highlighting_rules.append(
            HighlightingRule('\\bdef\\s+([A-Za-z_][A-Za-z0-9_]*)', self.function_format)
        )
        
        # 类定义
        self.highlighting_rules.append(
            HighlightingRule('\\bclass\\s+([A-Za-z_][A-Za-z0-9_]*)', self.class_format)
        )
    
    def _setup_c_cpp_rules(self):
        """设置C/C++语法高亮规则"""
        # C/C++关键字
        c_keywords = [
            'auto', 'break', 'case', 'char', 'const', 'continue', 'default',
            'do', 'double', 'else', 'enum', 'extern', 'float', 'for',
            'goto', 'if', 'int', 'long', 'register', 'return', 'short',
            'signed', 'sizeof', 'static', 'struct', 'switch', 'typedef',
            'union', 'unsigned', 'void', 'volatile', 'while'
        ]
        
        # C++特有关键字
        cpp_keywords = [
            'class', 'private', 'protected', 'public', 'virtual', 'friend',
            'inline', 'template', 'typename', 'namespace', 'using', 'new',
            'delete', 'this', 'operator', 'bool', 'true', 'false', 'try',
            'catch', 'throw', 'const_cast', 'dynamic_cast', 'reinterpret_cast',
            'static_cast'
        ]
        
        all_keywords = c_keywords + cpp_keywords
        
        for keyword in all_keywords:
            pattern = f'\\b{keyword}\\b'
            self.highlighting_rules.append(
                HighlightingRule(pattern, self.keyword_format)
            )
        
        # 预处理器指令
        self.highlighting_rules.append(
            HighlightingRule('^\\s*#[^\n]*', self.preprocessor_format)
        )
        
        # 字符串
        self.highlighting_rules.extend([
            HighlightingRule('"[^"\\\\]*(\\\\.[^"\\\\]*)*"', self.string_format),
            HighlightingRule("'[^'\\\\]*(\\\\.[^'\\\\]*)*'", self.string_format),
        ])
        
        # 注释
        self.highlighting_rules.extend([
            HighlightingRule('//[^\n]*', self.comment_format),
            HighlightingRule('/\\*.*\\*/', self.comment_format),
        ])
        
        # 数字
        self.highlighting_rules.append(
            HighlightingRule('\\b[0-9]+\\.?[0-9]*[fFlL]?\\b', self.number_format)
        )
    
    def _setup_systemverilog_rules(self):
        """设置SystemVerilog语法高亮规则"""
        # SystemVerilog关键字
        sv_keywords = [
            'module', 'endmodule', 'input', 'output', 'inout', 'wire', 'reg',
            'logic', 'bit', 'byte', 'shortint', 'int', 'longint', 'time',
            'shortreal', 'real', 'realtime', 'always', 'always_ff',
            'always_comb', 'always_latch', 'initial', 'final', 'if', 'else',
            'case', 'casex', 'casez', 'endcase', 'for', 'foreach', 'while',
            'do', 'repeat', 'forever', 'begin', 'end', 'fork', 'join',
            'join_any', 'join_none', 'function', 'endfunction', 'task',
            'endtask', 'class', 'endclass', 'interface', 'endinterface',
            'package', 'endpackage', 'program', 'endprogram', 'generate',
            'endgenerate', 'genvar', 'parameter', 'localparam', 'typedef',
            'enum', 'struct', 'union', 'packed', 'unpacked', 'signed',
            'unsigned', 'const', 'static', 'automatic', 'extern', 'pure',
            'virtual', 'protected', 'local', 'rand', 'randc', 'constraint',
            'solve', 'before', 'inside', 'dist', 'covergroup', 'endgroup',
            'coverpoint', 'cross', 'bins', 'binsof', 'intersect', 'wildcard',
            'iff', 'matches', 'tagged', 'return', 'break', 'continue',
            'disable', 'wait', 'wait_order', 'expect', 'assert', 'assume',
            'cover', 'restrict', 'property', 'endproperty', 'sequence',
            'endsequence', 'clocking', 'endclocking', 'default', 'global',
            'soft', 'priority', 'unique', 'unique0'
        ]
        
        for keyword in sv_keywords:
            pattern = f'\\b{keyword}\\b'
            self.highlighting_rules.append(
                HighlightingRule(pattern, self.keyword_format)
            )
        
        # 系统任务和函数
        system_tasks = [
            '$display', '$write', '$monitor', '$strobe', '$finish', '$stop',
            '$time', '$realtime', '$random', '$urandom', '$urandom_range',
            '$readmemh', '$readmemb', '$writememh', '$writememb', '$dumpfile',
            '$dumpvars', '$dumpall', '$dumpon', '$dumpoff', '$dumplimit'
        ]
        
        for task in system_tasks:
            pattern = f'\\{task}\\b'
            self.highlighting_rules.append(
                HighlightingRule(pattern, self.function_format)
            )
        
        # 编译器指令
        self.highlighting_rules.append(
            HighlightingRule('`[A-Za-z_][A-Za-z0-9_]*', self.preprocessor_format)
        )
        
        # 字符串
        self.highlighting_rules.append(
            HighlightingRule('"[^"\\\\]*(\\\\.[^"\\\\]*)*"', self.string_format)
        )
        
        # 注释
        self.highlighting_rules.extend([
            HighlightingRule('//[^\n]*', self.comment_format),
            HighlightingRule('/\\*.*\\*/', self.comment_format),
        ])
        
        # 数字
        self.highlighting_rules.extend([
            HighlightingRule("\\b[0-9]*'[bBoOdDhH][0-9a-fA-F_xXzZ]+\\b", self.number_format),
            HighlightingRule('\\b[0-9]+\\.?[0-9]*\\b', self.number_format),
        ])
    
    def _setup_javascript_rules(self):
        """设置JavaScript语法高亮规则"""
        # JavaScript关键字
        js_keywords = [
            'break', 'case', 'catch', 'continue', 'debugger', 'default',
            'delete', 'do', 'else', 'finally', 'for', 'function', 'if',
            'in', 'instanceof', 'new', 'return', 'switch', 'this', 'throw',
            'try', 'typeof', 'var', 'void', 'while', 'with', 'class',
            'const', 'enum', 'export', 'extends', 'import', 'super',
            'implements', 'interface', 'let', 'package', 'private',
            'protected', 'public', 'static', 'yield', 'async', 'await',
            'true', 'false', 'null', 'undefined'
        ]
        
        for keyword in js_keywords:
            pattern = f'\\b{keyword}\\b'
            self.highlighting_rules.append(
                HighlightingRule(pattern, self.keyword_format)
            )
        
        # 字符串
        self.highlighting_rules.extend([
            HighlightingRule('"[^"\\\\]*(\\\\.[^"\\\\]*)*"', self.string_format),
            HighlightingRule("'[^'\\\\]*(\\\\.[^'\\\\]*)*'", self.string_format),
            HighlightingRule('`[^`\\\\]*(\\\\.[^`\\\\]*)*`', self.string_format),
        ])
        
        # 注释
        self.highlighting_rules.extend([
            HighlightingRule('//[^\n]*', self.comment_format),
            HighlightingRule('/\\*.*\\*/', self.comment_format),
        ])
        
        # 数字
        self.highlighting_rules.append(
            HighlightingRule('\\b[0-9]+\\.?[0-9]*\\b', self.number_format)
        )
    
    def _setup_generic_rules(self):
        """设置通用语法高亮规则"""
        # 字符串
        self.highlighting_rules.extend([
            HighlightingRule('"[^"\\\\]*(\\\\.[^"\\\\]*)*"', self.string_format),
            HighlightingRule("'[^'\\\\]*(\\\\.[^'\\\\]*)*'", self.string_format),
        ])
        
        # 数字
        self.highlighting_rules.append(
            HighlightingRule('\\b[0-9]+\\.?[0-9]*\\b', self.number_format)
        )
    
    def highlightBlock(self, text):
        """高亮文本块"""
        # 检查是否包含行号前缀（格式：数字: 或 数字:+ 或 数字:- 或空格:）
        line_prefix_match = re.match(r'^(\s*\d*\s*:\s*[+-]?\s*)', text)

        if line_prefix_match:
            # 如果有行号前缀，只对内容部分应用语法高亮
            prefix_length = len(line_prefix_match.group(1))
            content_text = text[prefix_length:]

            # 对内容部分应用语法高亮规则
            for rule in self.highlighting_rules:
                if rule.regex:
                    expression = rule.regex
                    index = expression.indexIn(content_text)
                    while index >= 0:
                        length = expression.matchedLength()
                        # 调整索引位置，考虑行号前缀
                        self.setFormat(index + prefix_length, length, rule.format)
                        index = expression.indexIn(content_text, index + length)
        else:
            # 没有行号前缀，直接对整行应用语法高亮
            for rule in self.highlighting_rules:
                if rule.regex:
                    expression = rule.regex
                    index = expression.indexIn(text)
                    while index >= 0:
                        length = expression.matchedLength()
                        self.setFormat(index, length, rule.format)
                        index = expression.indexIn(text, index + length)
    
    def set_file_extension(self, extension: str):
        """设置文件扩展名并重新配置高亮规则"""
        self.file_extension = extension.lower()
        self._setup_highlighting_rules()
        self.rehighlight()
