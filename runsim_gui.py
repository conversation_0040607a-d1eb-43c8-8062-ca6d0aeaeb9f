#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RunSim GUI - 仿真运行控制台
这是重构后的入口点脚本，完全替代原有的runsim_gui.py
"""
import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox

def show_exception_box(exception_type, exception_value, exception_traceback):
    """显示异常对话框"""
    traceback_str = ''.join(traceback.format_exception(
        exception_type, exception_value, exception_traceback))

    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle("应用程序错误")
    msg_box.setText("程序发生错误，请查看详细信息。")
    msg_box.setDetailedText(traceback_str)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.exec_()

def main():
    """主函数"""
    # 创建 QApplication 实例
    app = QApplication(sys.argv)

    try:
        # 导入应用程序控制器
        from controllers.app_controller import AppController

        # 创建应用程序控制器
        app_controller = AppController()

        # 显示主窗口
        app_controller.show()

        # 设置应用关闭时的清理函数
        app.aboutToQuit.connect(app_controller.cleanup)

        # 运行应用程序事件循环
        exit_code = app.exec_()

        # 确保清理完成
        app_controller.cleanup()

        # 等待所有线程完成
        from PyQt5.QtCore import QThread
        import time

        # 等待最多3秒让线程完成
        max_wait_time = 3.0
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            active_threads = [t for t in QThread.currentThread().children() if isinstance(t, QThread) and t.isRunning()]
            if not active_threads:
                break
            time.sleep(0.1)
            app.processEvents()

        # 强制终止剩余线程
        for thread in QThread.currentThread().children():
            if isinstance(thread, QThread) and thread.isRunning():
                print(f"强制终止线程: {thread}")
                thread.terminate()
                thread.wait(1000)  # 等待1秒

        sys.exit(exit_code)
    except Exception as e:
        # 显示异常对话框
        show_exception_box(type(e), e, e.__traceback__)
        sys.exit(1)

if __name__ == "__main__":
    # 设置异常钩子
    sys.excepthook = show_exception_box
    main()
