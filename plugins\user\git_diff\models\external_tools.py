"""
外部比较工具管理模块

提供外部差异比较工具的检测、配置和调用功能，支持：
- gvimdiff (Vim差异比较工具)
- Beyond Compare (bcompare)
- Meld (跨平台差异比较工具)
- WinMerge (Windows平台)
- KDiff3 (跨平台)
"""

import os
import sys
import subprocess
import tempfile
import shutil
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path


@dataclass
class ExternalTool:
    """外部工具信息"""
    name: str
    display_name: str
    executable: str
    command_template: str
    platforms: List[str]  # 支持的平台: 'windows', 'linux', 'darwin'
    is_available: bool = False
    custom_path: Optional[str] = None


class ExternalToolsManager:
    """外部比较工具管理器"""
    
    def __init__(self):
        self.tools = self._init_tools()
        self.temp_files = []  # 临时文件列表，用于清理
        self._detect_available_tools()
    
    def _init_tools(self) -> Dict[str, ExternalTool]:
        """初始化支持的工具列表"""
        tools = {
            'gvimdiff': ExternalTool(
                name='gvimdiff',
                display_name='GVim Diff',
                executable='gvimdiff',
                command_template='{executable} "{old_file}" "{new_file}"',
                platforms=['windows', 'linux', 'darwin']
            ),
            'bcompare': ExternalTool(
                name='bcompare',
                display_name='Beyond Compare',
                executable='bcompare',
                command_template='{executable} "{old_file}" "{new_file}"',
                platforms=['windows', 'linux', 'darwin']
            ),
            'meld': ExternalTool(
                name='meld',
                display_name='Meld',
                executable='meld',
                command_template='{executable} "{old_file}" "{new_file}"',
                platforms=['windows', 'linux', 'darwin']
            ),
            'winmerge': ExternalTool(
                name='winmerge',
                display_name='WinMerge',
                executable='winmergeu',
                command_template='{executable} "{old_file}" "{new_file}"',
                platforms=['windows']
            ),
            'kdiff3': ExternalTool(
                name='kdiff3',
                display_name='KDiff3',
                executable='kdiff3',
                command_template='{executable} "{old_file}" "{new_file}"',
                platforms=['windows', 'linux', 'darwin']
            )
        }
        
        # Windows平台的特殊处理
        if sys.platform == 'win32':
            # Beyond Compare在Windows上的常见安装路径
            bc_paths = [
                r"C:\Program Files\Beyond Compare 4\BCompare.exe",
                r"C:\Program Files (x86)\Beyond Compare 4\BCompare.exe",
                r"C:\Program Files\Beyond Compare 5\BCompare.exe",
                r"C:\Program Files (x86)\Beyond Compare 5\BCompare.exe"
            ]
            for path in bc_paths:
                if os.path.exists(path):
                    tools['bcompare'].custom_path = path
                    break
            
            # WinMerge的常见安装路径
            wm_paths = [
                r"C:\Program Files\WinMerge\WinMergeU.exe",
                r"C:\Program Files (x86)\WinMerge\WinMergeU.exe"
            ]
            for path in wm_paths:
                if os.path.exists(path):
                    tools['winmerge'].custom_path = path
                    break
        
        return tools
    
    def _detect_available_tools(self):
        """检测系统中可用的工具"""
        current_platform = self._get_current_platform()
        
        for tool in self.tools.values():
            if current_platform not in tool.platforms:
                continue
            
            # 检查自定义路径
            if tool.custom_path and os.path.exists(tool.custom_path):
                tool.is_available = True
                continue
            
            # 检查系统PATH中的可执行文件
            tool.is_available = self._is_executable_available(tool.executable)
    
    def _get_current_platform(self) -> str:
        """获取当前平台"""
        platform_map = {
            'win32': 'windows',
            'linux': 'linux',
            'darwin': 'darwin'
        }
        return platform_map.get(sys.platform, 'linux')
    
    def _is_executable_available(self, executable: str) -> bool:
        """检查可执行文件是否在系统PATH中可用"""
        try:
            # 使用which命令检查（跨平台）
            result = subprocess.run(
                ['where' if sys.platform == 'win32' else 'which', executable],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def get_available_tools(self) -> List[ExternalTool]:
        """获取可用的工具列表"""
        return [tool for tool in self.tools.values() if tool.is_available]
    
    def get_tool(self, name: str) -> Optional[ExternalTool]:
        """根据名称获取工具"""
        return self.tools.get(name)
    
    def set_custom_path(self, tool_name: str, path: str) -> bool:
        """设置工具的自定义路径"""
        if tool_name not in self.tools:
            return False
        
        tool = self.tools[tool_name]
        if os.path.exists(path):
            tool.custom_path = path
            tool.is_available = True
            return True
        else:
            tool.custom_path = None
            tool.is_available = self._is_executable_available(tool.executable)
            return False
    
    def create_temp_file(self, content: str, filename: str, commit_hash: str = "") -> str:
        """创建临时文件"""
        try:
            # 解析文件名和扩展名
            name, ext = os.path.splitext(filename)
            
            # 构造临时文件名
            if commit_hash:
                temp_filename = f"{name}_old_{commit_hash[:8]}{ext}"
            else:
                temp_filename = f"{name}_current{ext}"
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, temp_filename)
            
            # 写入内容
            with open(temp_path, 'w', encoding='utf-8', errors='replace') as f:
                f.write(content)
            
            # 记录临时文件用于清理
            self.temp_files.append(temp_path)
            
            return temp_path
            
        except Exception as e:
            raise IOError(f"创建临时文件失败: {str(e)}")
    
    def launch_tool(self, tool_name: str, old_file: str, new_file: str) -> bool:
        """启动外部比较工具"""
        tool = self.get_tool(tool_name)
        if not tool or not tool.is_available:
            raise ValueError(f"工具 {tool_name} 不可用")

        try:
            # 确定可执行文件路径
            executable = tool.custom_path if tool.custom_path else tool.executable

            # 如果是自定义路径，直接使用；否则可能需要在PATH中查找
            if not tool.custom_path:
                # 尝试在PATH中找到完整路径
                if sys.platform == 'win32':
                    # Windows上尝试添加.exe扩展名
                    if not executable.endswith('.exe'):
                        test_exe = executable + '.exe'
                        if shutil.which(test_exe):
                            executable = test_exe

                # 获取完整路径
                full_path = shutil.which(executable)
                if full_path:
                    executable = full_path

            # 构造命令参数列表（最安全的方式）
            cmd_args = [executable, old_file, new_file]

            # 启动工具（非阻塞）
            if sys.platform == 'win32':
                subprocess.Popen(
                    cmd_args,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                subprocess.Popen(cmd_args)

            return True

        except Exception as e:
            raise RuntimeError(f"启动工具失败: {str(e)}")
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files[:]:  # 使用切片复制避免修改迭代中的列表
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                self.temp_files.remove(temp_file)
            except Exception as e:
                print(f"清理临时文件失败 {temp_file}: {e}")
    
    def __del__(self):
        """析构函数，确保清理临时文件"""
        self.cleanup_temp_files()


# 全局实例
external_tools_manager = ExternalToolsManager()
