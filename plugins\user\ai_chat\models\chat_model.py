"""
聊天数据模型

管理聊天消息、会话历史等数据
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal


class ChatMessage:
    """聊天消息类"""
    
    def __init__(self, role: str, content: str, timestamp: Optional[datetime] = None):
        """初始化消息
        
        Args:
            role: 消息角色 ('user', 'assistant', 'system')
            content: 消息内容
            timestamp: 时间戳
        """
        self.role = role
        self.content = content
        self.timestamp = timestamp or datetime.now()
        self.id = f"{self.timestamp.timestamp()}_{role}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat(),
            'id': self.id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatMessage':
        """从字典创建消息"""
        timestamp = datetime.fromisoformat(data['timestamp'])
        message = cls(data['role'], data['content'], timestamp)
        message.id = data.get('id', message.id)
        return message


class ChatSession:
    """聊天会话类"""
    
    def __init__(self, session_id: str = None, title: str = "新对话"):
        """初始化会话
        
        Args:
            session_id: 会话ID
            title: 会话标题
        """
        self.session_id = session_id or f"session_{datetime.now().timestamp()}"
        self.title = title
        self.messages: List[ChatMessage] = []
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def add_message(self, message: ChatMessage):
        """添加消息"""
        self.messages.append(message)
        self.updated_at = datetime.now()
        
        # 如果是第一条用户消息，使用其内容作为标题
        if len(self.messages) == 1 and message.role == 'user':
            self.title = message.content[:30] + ("..." if len(message.content) > 30 else "")
    
    def get_messages_for_api(self) -> List[Dict[str, str]]:
        """获取用于API调用的消息格式"""
        return [{'role': msg.role, 'content': msg.content} for msg in self.messages]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'session_id': self.session_id,
            'title': self.title,
            'messages': [msg.to_dict() for msg in self.messages],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatSession':
        """从字典创建会话"""
        session = cls(data['session_id'], data['title'])
        session.created_at = datetime.fromisoformat(data['created_at'])
        session.updated_at = datetime.fromisoformat(data['updated_at'])
        session.messages = [ChatMessage.from_dict(msg_data) for msg_data in data['messages']]
        return session


class ChatModel(QObject):
    """聊天数据模型"""
    
    # 信号
    session_added = pyqtSignal(ChatSession)
    session_updated = pyqtSignal(ChatSession)
    session_deleted = pyqtSignal(str)  # session_id
    message_added = pyqtSignal(ChatMessage)
    
    def __init__(self):
        """初始化模型"""
        super().__init__()
        self.sessions: Dict[str, ChatSession] = {}
        self.current_session: Optional[ChatSession] = None
        self.data_file = "ai_chat_history.json"
        
        # 加载历史数据
        self.load_data()
    
    def create_new_session(self, title: str = "新对话") -> ChatSession:
        """创建新会话"""
        session = ChatSession(title=title)
        self.sessions[session.session_id] = session
        self.current_session = session
        
        self.session_added.emit(session)
        self.save_data()
        
        return session
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """获取会话"""
        return self.sessions.get(session_id)
    
    def set_current_session(self, session_id: str):
        """设置当前会话"""
        session = self.get_session(session_id)
        if session:
            self.current_session = session
    
    def add_message(self, role: str, content: str, session_id: str = None) -> ChatMessage:
        """添加消息"""
        # 使用指定会话或当前会话
        session = None
        if session_id:
            session = self.get_session(session_id)
        else:
            session = self.current_session
        
        # 如果没有会话，创建新会话
        if not session:
            session = self.create_new_session()
        
        # 创建并添加消息
        message = ChatMessage(role, content)
        session.add_message(message)
        
        self.message_added.emit(message)
        self.session_updated.emit(session)
        self.save_data()
        
        return message

    def remove_message(self, message_id: str):
        """移除消息"""
        if not self.current_session:
            return

        # 从当前会话中移除消息
        self.current_session.messages = [
            msg for msg in self.current_session.messages
            if msg.id != message_id
        ]

        self.session_updated.emit(self.current_session)
        self.save_data()

    def delete_session(self, session_id: str):
        """删除会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]

            # 如果删除的是当前会话，尝试切换到其他会话
            if self.current_session and self.current_session.session_id == session_id:
                # 尝试切换到其他会话
                remaining_sessions = list(self.sessions.values())
                if remaining_sessions:
                    # 选择最新更新的会话
                    latest_session = max(remaining_sessions, key=lambda s: s.updated_at)
                    self.current_session = latest_session
                else:
                    # 没有其他会话，清空当前会话
                    self.current_session = None

            self.session_deleted.emit(session_id)
            self.save_data()
    
    def get_all_sessions(self) -> List[ChatSession]:
        """获取所有会话"""
        return list(self.sessions.values())
    
    def save_data(self):
        """保存数据到文件"""
        try:
            data = {
                'sessions': [session.to_dict() for session in self.sessions.values()],
                'current_session_id': self.current_session.session_id if self.current_session else None
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存聊天数据失败: {str(e)}")
    
    def load_data(self):
        """从文件加载数据"""
        try:
            if not os.path.exists(self.data_file):
                return
            
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载会话
            for session_data in data.get('sessions', []):
                session = ChatSession.from_dict(session_data)
                self.sessions[session.session_id] = session
            
            # 设置当前会话
            current_session_id = data.get('current_session_id')
            if current_session_id and current_session_id in self.sessions:
                self.current_session = self.sessions[current_session_id]
                
        except Exception as e:
            print(f"加载聊天数据失败: {str(e)}")
    
    def clear_all_data(self):
        """清空所有数据"""
        self.sessions.clear()
        self.current_session = None
        
        # 删除数据文件
        try:
            if os.path.exists(self.data_file):
                os.remove(self.data_file)
        except Exception as e:
            print(f"删除数据文件失败: {str(e)}")
