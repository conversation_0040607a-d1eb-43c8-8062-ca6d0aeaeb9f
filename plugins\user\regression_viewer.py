from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QLabel, QProgressBar,
                           QLineEdit, QMessageBox, QHeaderView, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QColor
import requests
from bs4 import BeautifulSoup
import re
from plugins.base import PluginBase

class RegressionDataThread(QThread):
    """线程类用于后台获取回归数据"""
    data_ready = pyqtSignal(object)
    error_occurred = pyqtSignal(str)

    def __init__(self, url):
        super().__init__()
        self.url = url

    def run(self):
        try:
            # 获取网页内容
            response = requests.get(self.url, timeout=10)
            response.raise_for_status()

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取表格数据
            data = self.extract_regression_data(soup)
            self.data_ready.emit(data)
        except Exception as e:
            self.error_occurred.emit(str(e))

    def extract_regression_data(self, soup):
        """从HTML中提取回归数据"""
        data = {
            'cases': [],
            'summary': {},
            'links': {}
        }

        # 提取表格数据
        tables = soup.find_all('table')
        if tables:
            # 假设第一个表格是回归结果表
            rows = tables[0].find_all('tr')
            for row in rows[1:]:  # 跳过表头
                cols = row.find_all('td')
                if len(cols) >= 5:  # 确保有足够的列
                    case_data = {
                        'name': cols[0].text.strip(),
                        'status': cols[1].text.strip(),
                        'seed': cols[2].text.strip() if len(cols) > 2 else '',
                        'runtime': cols[3].text.strip() if len(cols) > 3 else '',
                        'details': cols[4].text.strip() if len(cols) > 4 else ''
                    }

                    # 提取链接
                    links = {}
                    for i, col in enumerate(cols):
                        a_tags = col.find_all('a')
                        for a in a_tags:
                            href = a.get('href')
                            if href:
                                links[a.text.strip() or f"link_{i}"] = href

                    case_data['links'] = links
                    data['cases'].append(case_data)

        # 提取摘要信息
        summary_div = soup.find('div', class_='summary')
        if summary_div:
            data['summary']['text'] = summary_div.text.strip()

        return data

class RegressionViewerPlugin(PluginBase):
    """回归结果查看器插件"""

    @property
    def name(self):
        return "回归结果查看器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "快速查看回归结果网页内容"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_viewer)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

    def show_viewer(self):
        """显示回归结果查看器"""
        dialog = RegressionViewerDialog(self.main_window)
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
        # 非模态显示对话框
        dialog.show()

class RegressionViewerDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("回归结果查看器")
        self.resize(1000, 700)
        self.data = None

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # URL输入区域
        url_layout = QHBoxLayout()
        url_label = QLabel("回归结果URL:")
        self.url_input = QLineEdit("http://shvp01:8000/nxg_sim/show")
        load_btn = QPushButton("加载")
        load_btn.clicked.connect(self.load_regression_data)

        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_input, stretch=1)
        url_layout.addWidget(load_btn)

        # 过滤区域
        filter_layout = QHBoxLayout()
        filter_label = QLabel("状态过滤:")
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "PASS", "FAIL", "ERROR", "TIMEOUT"])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)

        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入用例名称搜索...")
        self.search_input.textChanged.connect(self.apply_filter)

        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.filter_combo)
        filter_layout.addWidget(search_label)
        filter_layout.addWidget(self.search_input, stretch=1)

        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(5)
        self.result_table.setHorizontalHeaderLabels(["用例名称", "状态", "种子", "运行时间", "详情"])
        self.result_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.result_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.result_table.cellDoubleClicked.connect(self.handle_cell_click)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 状态标签
        self.status_label = QLabel("就绪")

        layout.addLayout(url_layout)
        layout.addLayout(filter_layout)
        layout.addWidget(self.result_table)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def load_regression_data(self):
        """加载回归数据"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "警告", "请输入有效的URL")
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在加载数据...")

        # 创建线程获取数据
        self.thread = RegressionDataThread(url)
        self.thread.data_ready.connect(self.update_table)
        self.thread.error_occurred.connect(self.handle_error)
        self.thread.start()

    def update_table(self, data):
        """更新表格数据"""
        self.data = data
        self.progress_bar.setValue(50)

        # 清空表格
        self.result_table.setRowCount(0)

        # 填充数据
        for case in data['cases']:
            row = self.result_table.rowCount()
            self.result_table.insertRow(row)

            # 设置单元格内容
            self.result_table.setItem(row, 0, QTableWidgetItem(case['name']))

            status_item = QTableWidgetItem(case['status'])
            if case['status'] == 'PASS':
                status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
            elif case['status'] == 'FAIL':
                status_item.setBackground(QColor(255, 200, 200))  # 浅红色
            self.result_table.setItem(row, 1, status_item)

            self.result_table.setItem(row, 2, QTableWidgetItem(case['seed']))
            self.result_table.setItem(row, 3, QTableWidgetItem(case['runtime']))
            self.result_table.setItem(row, 4, QTableWidgetItem(case['details']))

        self.progress_bar.setValue(100)
        self.status_label.setText(f"加载完成，共 {len(data['cases'])} 个用例")
        self.progress_bar.setVisible(False)

    def handle_error(self, error_msg):
        """处理错误"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"错误: {error_msg}")
        QMessageBox.critical(self, "错误", f"加载数据失败: {error_msg}")

    def apply_filter(self):
        """应用过滤条件"""
        if not self.data:
            return

        filter_status = self.filter_combo.currentText()
        search_text = self.search_input.text().lower()

        for row in range(self.result_table.rowCount()):
            case_name = self.result_table.item(row, 0).text().lower()
            status = self.result_table.item(row, 1).text()

            # 应用过滤条件
            status_match = filter_status == "全部" or status == filter_status
            name_match = not search_text or search_text in case_name

            # 显示或隐藏行
            self.result_table.setRowHidden(row, not (status_match and name_match))

    def handle_cell_click(self, row, col):
        """处理单元格点击事件"""
        if not self.data or row >= len(self.data['cases']):
            return

        case = self.data['cases'][row]

        # 如果有链接，打开相应的链接
        if case['links']:
            # 创建一个简单的对话框显示可用链接
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QPushButton

            link_dialog = QDialog(self)
            link_dialog.setWindowTitle("可用链接")
            link_layout = QVBoxLayout()

            for name, url in case['links'].items():
                link_btn = QPushButton(name)
                link_btn.clicked.connect(lambda _, u=url: self.open_url(u))
                link_layout.addWidget(link_btn)

            link_dialog.setLayout(link_layout)
            # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
            link_dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
            # 非模态显示对话框
            link_dialog.show()

    def open_url(self, url):
        """打开URL"""
        import webbrowser
        try:
            webbrowser.open(url)
        except Exception as e:
            QMessageBox.warning(self, "警告", f"无法打开链接: {str(e)}")