"""
回归列表生成工具插件

提供分析case config文件并生成回归测试列表的插件。
支持GUI界面操作，包含参数配置、命令预览、历史记录管理等功能。
"""

import os
import sys
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from plugins.base import PluginBase
from regression_list_gen.gen_regr_list_gui import GenRegrListGUI


class RegressionListGenPlugin(PluginBase):
    """回归列表生成工具插件主类"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.regr_list_window = None
        self.main_window = None
        self.menu_action = None
        
    @property
    def name(self) -> str:
        """插件名称"""
        return "回归列表生成工具"
    
    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """插件描述"""
        return "分析case config文件并生成回归测试列表"
    
    def initialize(self, main_window):
        """初始化插件
        
        Args:
            main_window: 主窗口实例
        """
        try:
            self.main_window = main_window
            
            # 创建回归列表生成窗口
            self.regr_list_window = GenRegrListGUI()
            
            # 添加菜单项
            self._add_menu_item()
            
            print(f"插件 {self.name} 初始化成功")
            
        except Exception as e:
            print(f"插件 {self.name} 初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def cleanup(self):
        """清理插件资源"""
        try:
            # 移除菜单项
            if self.menu_action and self.main_window:
                tools_menu = getattr(self.main_window, 'tools_menu', None)
                if tools_menu:
                    tools_menu.removeAction(self.menu_action)
            
            # 关闭回归列表生成窗口
            if self.regr_list_window:
                self.regr_list_window.close()
                self.regr_list_window = None
            
            self.main_window = None
            self.menu_action = None
            
            print(f"插件 {self.name} 清理完成")
            
        except Exception as e:
            print(f"插件 {self.name} 清理失败: {str(e)}")
    
    def _add_menu_item(self):
        """添加菜单项"""
        try:
            if not self.main_window:
                return
            
            # 获取工具菜单
            tools_menu = getattr(self.main_window, 'tools_menu', None)
            if not tools_menu:
                print("未找到工具菜单")
                return
            
            # 创建菜单动作
            self.menu_action = QAction("回归列表生成工具", self.main_window)
            self.menu_action.setStatusTip("打开回归列表生成工具")
            self.menu_action.triggered.connect(self._show_regr_list_window)
            
            # 添加到工具菜单
            tools_menu.addAction(self.menu_action)
            
        except Exception as e:
            print(f"添加菜单项失败: {str(e)}")
    
    def _show_regr_list_window(self):
        """显示回归列表生成窗口"""
        try:
            if self.regr_list_window:
                self.regr_list_window.show()
                self.regr_list_window.raise_()
                self.regr_list_window.activateWindow()
        except Exception as e:
            print(f"显示回归列表生成窗口失败: {str(e)}")
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开回归列表生成工具: {str(e)}"
            )


# 插件实例
plugin_instance = RegressionListGenPlugin()
