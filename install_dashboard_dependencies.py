#!/usr/bin/env python3
"""
数据看板依赖安装脚本

该脚本用于安装数据看板功能所需的Python依赖包，主要包括matplotlib用于图表绘制。
"""

import subprocess
import sys
import os


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        return False
    print(f"Python版本: {sys.version}")
    return True


def install_package(package_name, import_name=None):
    """安装Python包
    
    Args:
        package_name (str): 包名
        import_name (str): 导入名称（如果与包名不同）
    
    Returns:
        bool: 安装是否成功
    """
    if import_name is None:
        import_name = package_name
    
    try:
        # 尝试导入包
        __import__(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"× {package_name} 未安装，正在安装...")
        
        try:
            # 使用pip安装
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package_name
            ])
            print(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"× {package_name} 安装失败: {e}")
            return False


def install_dashboard_dependencies():
    """安装数据看板依赖"""
    print("=" * 50)
    print("数据看板依赖安装")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 需要安装的包列表
    packages = [
        ("matplotlib", "matplotlib"),
        ("numpy", "numpy"),  # matplotlib的依赖
        ("pandas", "pandas"),  # 数据处理
    ]
    
    success_count = 0
    total_count = len(packages)
    
    print("\n正在检查和安装依赖包...")
    print("-" * 30)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("✓ 所有依赖安装成功！数据看板功能现在可以使用了。")
        
        # 测试matplotlib
        try:
            import matplotlib.pyplot as plt
            print("✓ matplotlib测试成功")
        except Exception as e:
            print(f"× matplotlib测试失败: {e}")
            return False
        
        return True
    else:
        print("× 部分依赖安装失败，数据看板功能可能无法正常使用。")
        return False


def main():
    """主函数"""
    try:
        success = install_dashboard_dependencies()
        
        if success:
            print("\n" + "=" * 50)
            print("安装完成！")
            print("现在可以使用数据看板功能了。")
            print("请重新启动RunSim GUI以使用新功能。")
            print("=" * 50)
            sys.exit(0)
        else:
            print("\n" + "=" * 50)
            print("安装失败！")
            print("请检查网络连接和Python环境，然后重试。")
            print("=" * 50)
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户取消安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
