"""
信号安全工具 - 解决Linux环境下PyQt5信号断开连接错误
专门解决 "QObject::disconnect: No such signal" 错误
"""
import sys
from PyQt5.QtCore import QObject


class SignalSafetyManager:
    """
    信号安全管理器
    
    解决PyQt5信号断开连接时的常见错误（跨平台）：
    - QObject::disconnect: No such signal QObject::output_ready(PyQt_PyObject)
    - QObject::disconnect: No such signal QObject::process_finished(int)
    - QObject::disconnect: No such signal QObject::process_error(QString)
    - QObject::disconnect: No such signal QObject::process_started()
    - QObject::disconnect: No such signal QObject::process_stopped()
    - QObject::disconnect: No such signal QObject::timer_triggered(QString,PyQt_PyObject)
    - QObject::disconnect: No such signal QObject::state_changed(QString,PyQt_PyObject,PyQt_PyObject)

    这些错误通常发生在对象清理时尝试断开已经断开或不存在的信号连接。
    """
    
    @staticmethod
    def safe_disconnect_signal(signal_obj, signal_name="unknown"):
        """
        安全地断开单个信号连接
        
        Args:
            signal_obj: PyQt5信号对象
            signal_name (str): 信号名称（用于日志）
            
        Returns:
            bool: 断开是否成功
        """
        if signal_obj is None:
            return True
            
        try:
            # 检查信号对象是否有效
            if not hasattr(signal_obj, 'disconnect'):
                return True
                
            # 直接尝试断开信号连接，使用更好的异常处理
            signal_obj.disconnect()
            return True
            
        except (TypeError, RuntimeError, AttributeError) as e:
            # 这些异常在所有平台都很常见，特别是当对象已被销毁或信号未连接时
            # 检查是否是"No such signal"错误
            error_msg = str(e).lower()
            if ("no such signal" in error_msg or
                "wrapped c/c++ object" in error_msg or
                "has been deleted" in error_msg):
                # 这是正常的清理过程，不需要显示错误信息
                return True
            else:
                # 其他类型的错误，可能需要关注
                return True  # 仍然返回True，避免影响清理流程
        except Exception as e:
            # 处理其他未预期的异常
            error_msg = str(e).lower()
            if "no such signal" in error_msg:
                # 这也是正常的清理过程
                return True
            else:
                print(f"断开信号 {signal_name} 时出现未预期异常: {str(e)}")
                return True  # 返回True，避免影响清理流程
    
    @staticmethod
    def safe_disconnect_signals(obj, signal_names):
        """
        安全地断开多个信号连接
        
        Args:
            obj: 包含信号的对象
            signal_names (list): 信号名称列表
            
        Returns:
            dict: 每个信号的断开结果
        """
        results = {}
        
        for signal_name in signal_names:
            try:
                # 检查对象是否仍然有效
                if obj is None:
                    results[signal_name] = True
                    continue

                # 检查信号是否存在且有效
                if hasattr(obj, signal_name):
                    signal_obj = getattr(obj, signal_name)
                    results[signal_name] = SignalSafetyManager.safe_disconnect_signal(
                        signal_obj, signal_name
                    )
                else:
                    # 信号不存在，标记为成功
                    results[signal_name] = True

            except (RuntimeError, TypeError) as e:
                # 处理对象已被删除的情况 - 这是正常的清理过程
                if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                    # Qt对象已被删除，这是正常情况，不需要显示错误
                    results[signal_name] = True
                else:
                    # 其他运行时错误
                    results[signal_name] = False
            except Exception as e:
                # 处理其他异常
                results[signal_name] = False
                
        return results
    
    @staticmethod
    def safe_disconnect_all_signals(obj):
        """
        安全地断开对象的所有信号连接
        
        Args:
            obj: QObject实例
            
        Returns:
            bool: 是否成功
        """
        if not isinstance(obj, QObject):
            return True
            
        try:
            # 检查对象是否仍然有效
            if obj is None:
                return True

            # 获取对象的所有属性
            signal_names = []
            try:
                for attr_name in dir(obj):
                    try:
                        attr = getattr(obj, attr_name)
                        # 检查是否是PyQt5信号
                        if hasattr(attr, 'disconnect') and hasattr(attr, 'emit'):
                            signal_names.append(attr_name)
                    except (RuntimeError, TypeError) as e:
                        # 对象可能已被删除，跳过这个属性
                        if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                            continue
                        else:
                            continue
                    except Exception:
                        continue
            except (RuntimeError, TypeError) as e:
                # 对象已被删除，返回成功
                if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                    return True
                else:
                    return False

            # 断开所有找到的信号
            results = SignalSafetyManager.safe_disconnect_signals(obj, signal_names)

            # 检查是否所有信号都成功断开
            return all(results.values())

        except (RuntimeError, TypeError) as e:
            # 对象已被删除，返回成功
            if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                return True
            else:
                return False
        except Exception as e:
            return False


class LinuxSignalFix:
    """
    Linux信号修复装饰器和上下文管理器
    """
    
    @staticmethod
    def safe_cleanup(cleanup_func):
        """
        装饰器：为cleanup函数添加安全的信号断开
        
        Usage:
            @LinuxSignalFix.safe_cleanup
            def cleanup(self):
                # 原有的清理代码
                pass
        """
        def wrapper(self, *args, **kwargs):
            try:
                # 先安全地断开所有信号
                SignalSafetyManager.safe_disconnect_all_signals(self)
                
                # 然后执行原有的清理函数
                return cleanup_func(self, *args, **kwargs)
                
            except Exception as e:
                print(f"安全清理时出错: {str(e)}")
                
        return wrapper
    
    def __init__(self, obj, signal_names=None):
        """
        上下文管理器：在退出时安全断开信号
        
        Args:
            obj: 包含信号的对象
            signal_names (list): 要断开的信号名称列表，None表示所有信号
        """
        self.obj = obj
        self.signal_names = signal_names
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.signal_names is None:
            SignalSafetyManager.safe_disconnect_all_signals(self.obj)
        else:
            SignalSafetyManager.safe_disconnect_signals(self.obj, self.signal_names)


# 便捷函数
def safe_disconnect(signal_obj, signal_name="unknown"):
    """便捷函数：安全断开单个信号"""
    return SignalSafetyManager.safe_disconnect_signal(signal_obj, signal_name)


def safe_disconnect_multiple(obj, signal_names):
    """便捷函数：安全断开多个信号"""
    return SignalSafetyManager.safe_disconnect_signals(obj, signal_names)


def safe_disconnect_all(obj):
    """便捷函数：安全断开对象的所有信号"""
    return SignalSafetyManager.safe_disconnect_all_signals(obj)
