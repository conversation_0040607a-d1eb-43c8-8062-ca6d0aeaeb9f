"""
批量执行插件
"""
from PyQt5.QtWidgets import (
    QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
    QCheckBox, QMessageBox, QFileDialog, QComboBox, QSpinBox,
    QGroupBox, QLineEdit, QSplitter, QTabWidget, QTextEdit,
    QWidget, QApplication, QToolTip, QMenu
)
from PyQt5.QtCore import Qt, QProcess, QProcessEnvironment, pyqtSignal, QObject, QTimer, QPoint
from PyQt5.QtGui import QColor, QIcon, QFont, QCursor, QTextCursor
from plugins.base import PluginBase, NonModalDialog
import os
import re
import json
import time
import datetime
from utils.command_generator import CommandGenerator
from utils.path_resolver import PathResolver


class CommandTableWidgetItem(QTableWidgetItem):
    """自定义表格项，支持命令悬停提示"""

    def __init__(self, command_text):
        super().__init__(command_text)
        # 设置工具提示为完整的命令文本
        self.setToolTip(self._format_command_tooltip(command_text))

        # 设置文本省略模式
        self.setData(Qt.DisplayRole, command_text)

    def _format_command_tooltip(self, command_text):
        """格式化命令工具提示"""
        # 将长命令分行显示，提高可读性
        if len(command_text) > 80:
            # 在参数处分行
            parts = command_text.split(' -')
            if len(parts) > 1:
                formatted = parts[0]  # 基本命令
                for part in parts[1:]:
                    formatted += '\n  -' + part
                return formatted

        return command_text


class BatchExecutionProcess(QObject):
    """批量执行进程管理器"""

    # 定义信号
    execution_started = pyqtSignal(int, str)  # 行索引, 命令
    execution_finished = pyqtSignal(int, int)  # 行索引, 退出码
    execution_output = pyqtSignal(int, str)  # 行索引, 输出
    all_finished = pyqtSignal()  # 所有命令执行完成

    def __init__(self, parent=None):
        """初始化批量执行进程管理器"""
        super().__init__(parent)
        self.processes = {}  # 存储进程 {行索引: QProcess}
        self.commands = []  # 存储命令 [(行索引, 命令, 用例名)]
        self.current_index = 0  # 当前执行的命令索引
        self.max_parallel = 1  # 最大并行执行数
        self.active_count = 0  # 当前活动进程数
        self.stopping = False  # 停止标志，防止在停止过程中启动新进程

    def add_command(self, row_index, command, case_name):
        """添加命令到队列"""
        self.commands.append((row_index, command, case_name))

    def set_max_parallel(self, count):
        """设置最大并行执行数"""
        self.max_parallel = max(1, count)

    def is_running(self):
        """检查是否有任务正在执行"""
        return self.active_count > 0 or self.current_index < len(self.commands)

    def start_execution(self):
        """开始执行命令队列"""
        self.current_index = 0
        self.active_count = 0
        self.stopping = False  # 重置停止标志
        self._execute_next_batch()

    def _execute_next_batch(self):
        """执行下一批命令"""
        # 如果正在停止，不启动新进程
        if self.stopping:
            return

        # 计算可以启动的进程数
        available_slots = self.max_parallel - self.active_count

        if available_slots <= 0 or self.current_index >= len(self.commands):
            # 如果没有可用槽位或已经执行完所有命令，检查是否全部完成
            if self.active_count == 0:
                self.all_finished.emit()
            return

        # 启动新的进程，直到填满可用槽位或执行完所有命令
        for _ in range(available_slots):
            if self.current_index < len(self.commands):
                row_index, command, case_name = self.commands[self.current_index]
                self._start_process(row_index, command)
                self.current_index += 1
                self.active_count += 1
            else:
                break

    def _start_process(self, row_index, command):
        """启动单个进程"""
        process = QProcess(self)

        # 设置进程环境变量，确保子进程可以显示自己的通知窗口
        env = QProcessEnvironment.systemEnvironment()
        # 添加特殊环境变量，允许子进程创建自己的窗口
        env.insert("QT_PROCESS_SEPARATE_UI", "1")
        # 设置环境变量
        process.setProcessEnvironment(env)

        # 设置工作目录
        process.setWorkingDirectory(os.getcwd())

        # 连接信号
        process.readyReadStandardOutput.connect(
            lambda: self._handle_output(row_index, process)
        )
        process.readyReadStandardError.connect(
            lambda: self._handle_error(row_index, process)
        )
        process.finished.connect(
            lambda exit_code, _: self._handle_finished(row_index, exit_code)
        )

        # 存储进程
        self.processes[row_index] = process

        # 发送开始信号
        self.execution_started.emit(row_index, command)

        # 记录启动信息
        self.execution_output.emit(row_index, f"开始执行命令: {command}\n")

        # 启动进程（使用shell模式）
        if os.name == 'nt':  # Windows
            process.setProcessChannelMode(QProcess.MergedChannels)
            process.start("cmd.exe", ["/c", command])
        else:  # Linux/Mac
            process.setProcessChannelMode(QProcess.MergedChannels)
            # 使用setsid命令创建新会话，确保子进程可以显示自己的通知
            process.start("/bin/sh", ["-c", f"setsid {command}"])

    def _handle_output(self, row_index, process):
        """处理标准输出"""
        try:
            # 尝试多种编码方式解码输出
            raw_data = process.readAllStandardOutput().data()
            output = self._decode_output(raw_data)
            self.execution_output.emit(row_index, output)
        except Exception as e:
            print(f"处理标准输出时出错: {str(e)}")
            # 如果出错，使用安全的替换模式
            self.execution_output.emit(row_index, raw_data.decode('utf-8', errors='replace'))

    def _handle_error(self, row_index, process):
        """处理标准错误"""
        try:
            # 尝试多种编码方式解码错误输出
            raw_data = process.readAllStandardError().data()
            error = self._decode_output(raw_data)
            self.execution_output.emit(row_index, error)
        except Exception as e:
            print(f"处理标准错误时出错: {str(e)}")
            # 如果出错，使用安全的替换模式
            self.execution_output.emit(row_index, raw_data.decode('utf-8', errors='replace'))

    def _decode_output(self, data):
        """尝试使用多种编码解码输出数据"""
        # 常用编码列表，按优先级排序
        encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 'latin1']

        # 首先尝试检测编码
        try:
            import chardet
            detected = chardet.detect(data)
            if detected and detected['confidence'] > 0.7:
                # 如果检测到的编码可信度高，优先使用
                encodings.insert(0, detected['encoding'])
        except ImportError:
            # 如果没有安装chardet，忽略
            pass

        # 尝试不同的编码
        for encoding in encodings:
            try:
                return data.decode(encoding)
            except UnicodeDecodeError:
                continue

        # 如果所有编码都失败，使用替换模式
        return data.decode('utf-8', errors='replace')

    def _handle_finished(self, row_index, exit_code):
        """处理进程结束"""
        try:
            # 添加执行完成的日志信息
            if exit_code == 0:
                self.execution_output.emit(row_index, f"\n命令执行成功，退出码: {exit_code}\n")
            else:
                self.execution_output.emit(row_index, f"\n命令执行失败，退出码: {exit_code}\n")

            # 发送结束信号
            self.execution_finished.emit(row_index, exit_code)

            # 从进程字典中安全移除（使用pop避免KeyError）
            if row_index in self.processes:
                self.processes.pop(row_index, None)

            # 减少活动进程计数（确保不会小于0）
            if self.active_count > 0:
                self.active_count -= 1

            # 尝试执行下一批命令
            self._execute_next_batch()
        except Exception as e:
            print(f"处理进程结束时出错: {str(e)}")

    def stop_all(self):
        """停止所有进程"""
        import sys
        import subprocess
        import time

        # 设置停止标志，防止启动新进程
        self.stopping = True

        # 使用list()创建字典项的副本，避免在迭代过程中字典大小改变的错误
        for row_index, process in list(self.processes.items()):
            if process.state() != QProcess.NotRunning:
                try:
                    # 获取进程ID
                    pid = process.processId()
                    if pid > 0:
                        if sys.platform == 'win32':
                            # Windows系统使用taskkill /T命令终止进程树
                            kill_cmd = f"taskkill /F /T /PID {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[正在终止进程树...]\n")
                        else:
                            # Linux系统使用pkill -P命令终止子进程
                            # 首先终止所有子进程
                            kill_cmd = f"pkill -9 -P {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[正在终止子进程...]\n")
                            # 等待一小段时间确保命令执行
                            time.sleep(0.2)

                            # 然后尝试使用进程组ID终止整个进程组
                            try:
                                # 获取进程组ID并终止整个组
                                pgid_cmd = f"ps -o pgid= {pid} | tr -d ' '"
                                pgid = subprocess.check_output(pgid_cmd, shell=True, text=True).strip()
                                if pgid and pgid.isdigit():
                                    kill_group_cmd = f"kill -9 -{pgid}"
                                    subprocess.Popen(kill_group_cmd, shell=True)
                                    self.execution_output.emit(row_index, "\n[正在终止进程组...]\n")
                            except Exception as pgid_error:
                                print(f"获取或终止进程组时出错: {str(pgid_error)}")

                    # 最后使用QProcess的方法终止进程
                    process.terminate()
                    # 给进程一点时间来终止
                    time.sleep(0.1)
                    # 如果进程仍在运行，强制终止
                    if process.state() == QProcess.Running:
                        process.kill()

                except Exception as e:
                    self.execution_output.emit(row_index, f"\n[终止进程时出错: {str(e)}]\n")
                    print(f"终止进程时出错: {str(e)}")

        # 等待一段时间后强制结束
        QTimer.singleShot(3000, self._kill_remaining)

    def _kill_remaining(self):
        """强制结束剩余进程"""
        import sys
        import subprocess
        import time

        for row_index, process in list(self.processes.items()):
            if process.state() != QProcess.NotRunning:
                try:
                    # 获取进程ID
                    pid = process.processId()
                    if pid > 0:
                        if sys.platform == 'win32':
                            # Windows系统使用taskkill /F /T命令强制终止进程树
                            kill_cmd = f"taskkill /F /T /PID {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[强制终止进程树...]\n")
                        else:
                            # Linux系统使用pkill -9 -P命令强制终止子进程
                            kill_cmd = f"pkill -9 -P {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[强制终止子进程...]\n")
                            # 等待一小段时间确保命令执行
                            time.sleep(0.2)

                            # 然后尝试使用进程组ID终止整个进程组
                            try:
                                # 获取进程组ID并终止整个组
                                pgid_cmd = f"ps -o pgid= {pid} | tr -d ' '"
                                pgid = subprocess.check_output(pgid_cmd, shell=True, text=True).strip()
                                if pgid and pgid.isdigit():
                                    kill_group_cmd = f"kill -9 -{pgid}"
                                    subprocess.Popen(kill_group_cmd, shell=True)
                                    self.execution_output.emit(row_index, "\n[强制终止进程组...]\n")
                            except Exception as pgid_error:
                                print(f"获取或终止进程组时出错: {str(pgid_error)}")

                    # 最后使用QProcess的kill方法强制终止进程
                    process.kill()

                except Exception as e:
                    self.execution_output.emit(row_index, f"\n[强制终止进程时出错: {str(e)}]\n")
                    print(f"强制终止进程时出错: {str(e)}")

class BatchExecutionDialog(NonModalDialog):
    """批量执行对话框"""

    def __init__(self, parent=None, plugin_name=""):
        """初始化批量执行对话框"""
        try:
            super().__init__(parent, plugin_name)
            self.setWindowTitle("批量执行")
            self.resize(1200, 800)

            # 保存主窗口引用
            self.main_window = parent

            # 获取execution_controller引用
            self.execution_controller = None
            if hasattr(parent, 'app_controller') and parent.app_controller:
                if hasattr(parent.app_controller, 'execution_controller'):
                    self.execution_controller = parent.app_controller.execution_controller
                    #print(f"批量执行插件: 成功从app_controller获取execution_controller")
            elif hasattr(parent, 'execution_controller'):
                # 备用方案：直接从parent获取
                self.execution_controller = parent.execution_controller
                #print(f"批量执行插件: 从parent直接获取execution_controller")

            if not self.execution_controller:
                print(f"批量执行插件: 警告 - 无法获取execution_controller引用")

            # 创建命令生成器和路径解析器
            self.command_generator = CommandGenerator()
            self.path_resolver = PathResolver()

            # 创建进程管理器
            self.process_manager = BatchExecutionProcess(self)

            # 连接信号
            self.process_manager.execution_started.connect(self.on_execution_started)
            self.process_manager.execution_finished.connect(self.on_execution_finished)
            self.process_manager.execution_output.connect(self.on_execution_output)
            self.process_manager.all_finished.connect(self.on_all_finished)

            # 初始化UI
            self.init_ui()
        except Exception as e:
            print(f"初始化批量执行对话框时出错: {str(e)}")
            # 重新抛出异常，让上层处理
            raise

    def init_ui(self):
        """初始化UI"""
        try:
            layout = QVBoxLayout()

            # 创建分割器
            splitter = QSplitter(Qt.Vertical)

            # 创建上部分（用例表格）
            top_widget = QWidget()
            top_layout = QVBoxLayout(top_widget)

            # 创建用例表格
            self.case_table = QTableWidget()
            self.case_table.setColumnCount(6)
            self.case_table.setHorizontalHeaderLabels([
                "选择", "用例名", "状态", "命令", "开始时间", "结束时间"
            ])

            # 设置可调整列宽功能
            header = self.case_table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.Interactive)  # 允许用户拖拽调整列宽
            header.setStretchLastSection(True)  # 最后一列自动拉伸填充剩余空间

            # 设置初始列宽
            self.case_table.setColumnWidth(0, 60)   # 选择列
            self.case_table.setColumnWidth(1, 200)  # 用例名列
            self.case_table.setColumnWidth(2, 100)  # 状态列
            self.case_table.setColumnWidth(3, 300)  # 命令列
            self.case_table.setColumnWidth(4, 100)  # 开始时间列
            self.case_table.setColumnWidth(5, 100)  # 结束时间列

            # 设置右键菜单
            self.case_table.setContextMenuPolicy(Qt.CustomContextMenu)
            self.case_table.customContextMenuRequested.connect(self.show_context_menu)

            # 连接选择变化信号以更新按钮状态
            self.case_table.itemSelectionChanged.connect(self.update_button_states)

            # 添加到上部分布局
            top_layout.addWidget(self.case_table)

            # 创建下部分（日志和控制）
            bottom_widget = QWidget()
            bottom_layout = QVBoxLayout(bottom_widget)

            # 创建标签页控件
            self.tab_widget = QTabWidget()

            # 创建日志标签页
            self.log_tabs = {}

            # 添加到下部分布局
            bottom_layout.addWidget(self.tab_widget)

            # 添加控制按钮
            control_layout = QHBoxLayout()

            # 添加用例按钮
            add_button = QPushButton("添加用例")
            add_button.clicked.connect(self.add_cases)

            # 清除按钮
            clear_button = QPushButton("清除所有")
            clear_button.clicked.connect(self.clear_all)

            # 并行执行设置
            parallel_label = QLabel("并行执行数:")
            self.parallel_spin = QSpinBox()
            self.parallel_spin.setRange(1, 10)
            self.parallel_spin.setValue(1)
            self.parallel_spin.valueChanged.connect(self.set_parallel_count)

            # 执行按钮
            self.execute_button = QPushButton("开始执行")
            self.execute_button.clicked.connect(self.start_execution)

            # 停止按钮
            self.stop_button = QPushButton("停止执行")
            self.stop_button.clicked.connect(self.stop_execution)
            self.stop_button.setEnabled(False)

            # 重新仿真按钮
            self.rerun_button = QPushButton("重新仿真")
            self.rerun_button.clicked.connect(self.rerun_selected_cases)
            self.rerun_button.setToolTip("重新执行选中的已完成用例")
            self.rerun_button.setEnabled(False)  # 默认禁用

            # 日志和文件打开按钮
            self.open_compile_log_button = QPushButton("打开编译日志")
            self.open_compile_log_button.clicked.connect(self.open_compile_log)
            self.open_compile_log_button.setToolTip("打开选中案例的编译日志文件")
            self.open_compile_log_button.setEnabled(False)  # 默认禁用

            self.open_sim_log_button = QPushButton("打开仿真日志")
            self.open_sim_log_button.clicked.connect(self.open_sim_log)
            self.open_sim_log_button.setToolTip("打开选中案例的仿真日志文件")
            self.open_sim_log_button.setEnabled(False)  # 默认禁用

            self.open_disasm_button = QPushButton("打开反汇编文件")
            self.open_disasm_button.clicked.connect(self.open_disasm_file)
            self.open_disasm_button.setToolTip("打开选中案例的反汇编文件")
            self.open_disasm_button.setEnabled(False)  # 默认禁用

            # 添加到控制布局
            control_layout.addWidget(add_button)
            control_layout.addWidget(clear_button)
            control_layout.addWidget(self.rerun_button)
            control_layout.addWidget(self.open_compile_log_button)
            control_layout.addWidget(self.open_sim_log_button)
            control_layout.addWidget(self.open_disasm_button)
            control_layout.addStretch()
            control_layout.addWidget(parallel_label)
            control_layout.addWidget(self.parallel_spin)
            control_layout.addWidget(self.execute_button)
            control_layout.addWidget(self.stop_button)

            # 添加到下部分布局
            bottom_layout.addLayout(control_layout)

            # 添加到分割器
            splitter.addWidget(top_widget)
            splitter.addWidget(bottom_widget)
            splitter.setSizes([400, 400])

            # 添加到主布局
            layout.addWidget(splitter)
            self.setLayout(layout)
        except Exception as e:
            print(f"初始化批量执行对话框UI时出错: {str(e)}")
            # 重新抛出异常，让上层处理
            raise

    def add_cases(self):
        """添加用例"""
        # 获取用例列表
        case_files = self._get_case_files()
        if not case_files:
            return

        # 解析用例文件
        cases = self._parse_case_files(case_files)
        if not cases:
            QMessageBox.warning(self, "警告", "未找到有效的用例")
            return

        # 添加到表格
        self._add_cases_to_table(cases)

    def _get_case_files(self):
        """获取用例文件"""
        # 打开文件对话框
        paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择用例文件",
            "",
            "用例文件 (*.txt *.cfg *.list *.lst);;回归列表文件 (*.list *.lst);;用例配置文件 (*.txt *.cfg);;所有文件 (*.*)"
        )
        return paths

    def _parse_case_files(self, case_files):
        """解析用例文件"""
        cases = []

        for file_path in case_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 判断文件类型并解析
                if file_path.endswith('.list') or file_path.endswith('.lst'):
                    # 解析回归列表文件
                    regression_cases = self._parse_regression_list_file(content, file_path)
                    cases.extend(regression_cases)
                else:
                    # 解析传统的case config文件
                    config_cases = self._parse_case_config_file(content, file_path)
                    cases.extend(config_cases)

            except Exception as e:
                print(f"解析用例文件 {file_path} 失败: {str(e)}")

        return cases

    def _parse_case_config_file(self, content, file_path):
        """解析传统的case config文件"""
        cases = []

        # 使用正则表达式查找用例
        pattern = r'\[case\s+([\w_]+)(?:\s*:\s*([\w_]+))?.*'
        matches = re.findall(pattern, content)

        if matches:
            # 提取文件名作为前缀
            file_name = os.path.basename(file_path)

            # 解析base和block参数
            base, block = self._parse_base_block_from_path(file_path)

            # 添加用例
            for case, parent in matches:
                cases.append({
                    'name': case,
                    'file': file_name,
                    'path': file_path,
                    'base': base,
                    'block': block,
                    'cfd_def': '',  # case config文件不包含cfd_def
                })

        return cases

    def _parse_regression_list_file(self, content, file_path):
        """解析回归列表文件"""
        cases = []
        file_name = os.path.basename(file_path)

        # 按行分割内容
        lines = content.strip().split('\n')

        for line_num, line in enumerate(lines, 1):
            try:
                # 去除首尾空格
                line = line.strip()

                # 跳过空行
                if not line:
                    continue

                # 跳过注释行（以//开头）
                if line.startswith('//'):
                    continue

                # 解析CSV格式的行
                case_info = self._parse_regression_line(line, line_num)
                if case_info:
                    case_info['file'] = file_name
                    case_info['path'] = file_path
                    cases.append(case_info)

            except Exception as e:
                print(f"解析回归列表文件 {file_path} 第 {line_num} 行失败: {str(e)}")
                continue

        return cases

    def _parse_regression_line(self, line, line_num):
        """解析回归列表文件的单行"""
        try:
            # 分割CSV字段，但要考虑字段中可能包含逗号的情况
            fields = self._split_csv_line(line)

            # 检查最少字段数量（至少需要前3个必需字段）
            if len(fields) < 3:
                print(f"第 {line_num} 行字段数量不足，至少需要3个字段（on/off, block, case），实际{len(fields)}个")
                return None

            # 解析各字段 - 根据正确格式：on/off, block, case, seed, iterative, tag, priority, config, CFG_DEF, env/base, plusargs
            status = fields[0]          # 第1个字段：on/off状态
            block_path = fields[1]      # 第2个字段：block路径
            case_name = fields[2]       # 第3个字段：用例名
            # fields[3] seed - 忽略
            # fields[4] iterative - 忽略
            # fields[5] tag - 忽略（方括号包围的标签）
            # fields[6] priority - 忽略
            # fields[7] config - 忽略
            cfg_def = fields[8] if len(fields) > 8 else ''         # 第9个字段：CFG_DEF定义
            base_value = fields[9] if len(fields) > 9 else ''      # 第10个字段：env/base值
            # fields[10] plusargs - 忽略

            # 检查状态字段
            if status.upper() != 'ON':
                # 状态为OFF或其他值，跳过此用例
                return None

            # 检查必需字段
            if not block_path or not case_name:
                print(f"第 {line_num} 行缺少必需字段（block路径或用例名）")
                print(f"block: '{block_path}', case: '{case_name}'")
                return None

            # 处理可选字段（去除方括号等特殊字符）
            cfg_def = self._clean_field_value(cfg_def)
            base_value = self._clean_field_value(base_value)

            # 特殊处理：default值表示不使用该参数
            if cfg_def.lower() == 'default':
                cfg_def = ''
            if base_value.lower() == 'default':
                base_value = ''

            return {
                'name': case_name,
                'block': block_path,
                'base': base_value,
                'cfd_def': cfg_def,
            }

        except Exception as e:
            print(f"解析第 {line_num} 行时出错: {str(e)}")
            print(f"行内容: {line}")
            return None

    def _split_csv_line(self, line):
        """智能分割CSV行，处理tag字段中包含方括号和逗号的情况"""
        import re

        try:
            # 处理方括号内的逗号，避免被错误分割
            # 先找到所有方括号内的内容，临时替换逗号
            bracket_pattern = r'\[([^\]]*)\]'
            bracket_matches = re.findall(bracket_pattern, line)

            # 创建临时替换字符串
            temp_line = line
            temp_replacements = {}

            for i, match in enumerate(bracket_matches):
                placeholder = f"__BRACKET_CONTENT_{i}__"
                # 将方括号内的逗号替换为临时标记
                bracket_content = match.replace(',', '__COMMA__')
                temp_replacements[placeholder] = f"[{bracket_content}]"
                temp_line = temp_line.replace(f"[{match}]", placeholder)

            # 现在可以安全地按逗号分割
            fields = [field.strip() for field in temp_line.split(',')]

            # 恢复方括号内的内容
            for i, field in enumerate(fields):
                for placeholder, original in temp_replacements.items():
                    if placeholder in field:
                        # 恢复逗号
                        restored = original.replace('__COMMA__', ',')
                        fields[i] = field.replace(placeholder, restored)

            return fields

        except Exception as e:
            print(f"CSV解析失败，使用简单分割: {str(e)}")
            # 如果解析失败，回退到简单的逗号分割
            return [field.strip() for field in line.split(',')]

    def _clean_field_value(self, value):
        """清理字段值，去除方括号等特殊字符"""
        if not value:
            return ''

        # 去除首尾空格
        value = value.strip()

        # 去除方括号
        if value.startswith('[') and value.endswith(']'):
            value = value[1:-1].strip()

        return value

    def _parse_base_block_from_path(self, file_path):
        """从文件路径解析base和block参数"""
        # 默认值
        base = ""
        block = ""

        try:
            # 规则1: dv/{subsys}/bin/case_cfg/xxx_case.cfg
            if match := re.search(r'dv/([^/]+)/bin/case_cfg/', file_path):
                subsys = match.group(1)
                base = ""
                block = subsys
                return base, block

            # 规则2: dv/udtb/{subsys}/子环境名/bin/xxx.cfg
            if match := re.search(r'dv/udtb/([^/]+)/([^/]+)/bin/', file_path):
                subsys = match.group(1)
                subenv = match.group(2)
                base = subsys
                block = f"udtb/{subsys}/{subenv}"
                return base, block

            # 规则3: dv/udtb/usvp/bin/case_cfg/<sys>_subsys_case.cfg
            if match := re.search(r'dv/udtb/usvp/bin/case_cfg/([^_]+)_subsys_case\.cfg', file_path):
                sys_name = match.group(1)
                base = f"{sys_name}_sys"
                block = "udtb/usvp"
                return base, block

            # 规则4: dv/udtb/usvp/bin/case_cfg/<sys>_top_case.cfg 或 xxx.cfg
            if re.search(r'dv/udtb/usvp/bin/case_cfg/.*_top_case\.cfg', file_path) or \
               re.search(r'dv/udtb/usvp/bin/case_cfg/.*\.cfg', file_path):
                base = "top"
                block = "udtb/usvp"
                return base, block
        except Exception as e:
            print(f"解析base/block参数失败: {str(e)}")

        return base, block

    def add_case_to_table(self, case_name, command, description=""):
        """
        添加单个用例到表格

        Args:
            case_name (str): 用例名称
            command (str): 执行命令
            description (str): 描述信息

        Returns:
            int: 添加的行索引
        """
        # 获取当前行数
        row_count = self.case_table.rowCount()

        # 添加新行
        self.case_table.insertRow(row_count)

        # 添加复选框
        checkbox = QCheckBox()
        checkbox.setChecked(True)
        self.case_table.setCellWidget(row_count, 0, checkbox)

        # 添加用例名（如果有描述，显示为 "用例名 - 描述"）
        display_name = f"{case_name} - {description}" if description else case_name
        self.case_table.setItem(row_count, 1, QTableWidgetItem(display_name))

        # 添加状态
        status_item = QTableWidgetItem("等待执行")
        status_item.setForeground(QColor(128, 128, 128))
        self.case_table.setItem(row_count, 2, status_item)

        # 添加命令
        self.case_table.setItem(row_count, 3, CommandTableWidgetItem(command))

        # 添加开始时间和结束时间
        self.case_table.setItem(row_count, 4, QTableWidgetItem(""))
        self.case_table.setItem(row_count, 5, QTableWidgetItem(""))

        # 调整表格大小
        self.case_table.resizeColumnsToContents()

        return row_count

    def _add_cases_to_table(self, cases):
        """添加用例到表格"""
        # 获取当前行数
        row_count = self.case_table.rowCount()

        # 添加新行
        for case in cases:
            # 检查是否已存在
            exists = False
            for row in range(row_count):
                if self.case_table.item(row, 1).text() == case['name']:
                    exists = True
                    break

            if exists:
                continue

            # 添加新行
            self.case_table.insertRow(row_count)

            # 添加复选框
            checkbox = QCheckBox()
            checkbox.setChecked(True)
            self.case_table.setCellWidget(row_count, 0, checkbox)

            # 添加用例名
            self.case_table.setItem(row_count, 1, QTableWidgetItem(case['name']))

            # 添加状态
            status_item = QTableWidgetItem("等待执行")
            status_item.setForeground(QColor(128, 128, 128))
            self.case_table.setItem(row_count, 2, status_item)

            # 生成命令
            command = self._generate_command(case)
            self.case_table.setItem(row_count, 3, CommandTableWidgetItem(command))

            # 添加开始时间和结束时间
            self.case_table.setItem(row_count, 4, QTableWidgetItem(""))
            self.case_table.setItem(row_count, 5, QTableWidgetItem(""))

            # 存储用例数据
            self.case_table.item(row_count, 1).setData(Qt.UserRole, case)

            # 增加行数
            row_count += 1

    def _generate_command(self, case):
        """生成命令"""
        # 基本命令
        command = f"runsim -case {case['name']}"

        # 添加block参数（必需）
        if case['block']:
            command += f" -block {case['block']}"

        # 添加base参数（可选）
        if case.get('base'):
            command += f" -base {case['base']}"

        # 添加cfd_def参数（可选）
        if case.get('cfd_def'):
            command += f" -cfd_def {case['cfd_def']}"

        return command

    def clear_all(self):
        """清除所有用例"""
        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认清除",
            "确定要清除所有用例吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 清除表格
        self.case_table.setRowCount(0)

        # 清除日志标签页
        for i in range(self.tab_widget.count() - 1, -1, -1):
            self.tab_widget.removeTab(i)
        self.log_tabs = {}

        # 重置进程管理器状态
        self.process_manager.commands.clear()
        self.process_manager.processes.clear()
        self.process_manager.current_index = 0
        self.process_manager.active_count = 0
        self.process_manager.stopping = False

    def set_parallel_count(self, count):
        """设置并行执行数"""
        self.process_manager.set_max_parallel(count)

    def start_execution(self):
        """开始执行"""
        # 检查是否有选中的用例
        selected_rows = []
        for row in range(self.case_table.rowCount()):
            checkbox = self.case_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_rows.append(row)

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请选择要执行的用例")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认执行",
            f"确定要执行选中的 {len(selected_rows)} 个用例吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 清除进程管理器中的命令
        self.process_manager.commands = []

        # 添加命令到进程管理器
        for row in selected_rows:
            # 获取命令
            command = self.case_table.item(row, 3).text()

            # 获取用例名
            case_name = self.case_table.item(row, 1).text()

            # 添加命令
            self.process_manager.add_command(row, command, case_name)

            # 更新状态
            status_item = self.case_table.item(row, 2)
            status_item.setText("等待执行")
            status_item.setForeground(QColor(128, 128, 128))

            # 清除时间
            self.case_table.item(row, 4).setText("")
            self.case_table.item(row, 5).setText("")

        # 设置按钮状态
        self.execute_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 开始执行
        self.process_manager.start_execution()

    def stop_execution(self):
        """停止执行"""
        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认停止",
            "确定要停止所有正在执行的用例吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 停止所有进程
        self.process_manager.stop_all()

        # 设置按钮状态
        self.execute_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def on_execution_started(self, row_index, command):
        """处理执行开始事件"""
        try:
            # 检查行索引是否有效
            if row_index < 0 or row_index >= self.case_table.rowCount():
                print(f"警告: 无效的行索引 {row_index}")
                return

            # 更新状态
            status_item = self.case_table.item(row_index, 2)
            if status_item is None:
                print(f"警告: 行 {row_index} 的状态项为空")
                # 创建新的状态项
                status_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 2, status_item)

            status_item.setText("正在执行")
            status_item.setForeground(QColor(0, 0, 255))

            # 更新开始时间
            start_time_item = self.case_table.item(row_index, 4)
            if start_time_item is None:
                start_time_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 4, start_time_item)

            start_time = datetime.datetime.now().strftime("%H:%M:%S")
            start_time_item.setText(start_time)

            # 获取用例名
            case_name_item = self.case_table.item(row_index, 1)
            if case_name_item is None:
                print(f"警告: 行 {row_index} 的用例名项为空")
                case_name = f"用例_{row_index}"
            else:
                case_name = case_name_item.text()

            # 创建日志标签页
            self._create_log_tab(row_index, case_name)

        except Exception as e:
            print(f"处理执行开始事件时出错: {str(e)}")

    def on_execution_finished(self, row_index, exit_code):
        """处理执行结束事件"""
        try:
            # 检查行索引是否有效
            if row_index < 0 or row_index >= self.case_table.rowCount():
                print(f"警告: 无效的行索引 {row_index}")
                return

            # 获取状态项
            status_item = self.case_table.item(row_index, 2)
            if status_item is None:
                print(f"警告: 行 {row_index} 的状态项为空")
                # 创建新的状态项
                status_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 2, status_item)

            # 先显示"分析中..."状态
            status_item.setText("分析中...")
            status_item.setForeground(QColor(255, 165, 0))  # 橙色

            # 获取命令和用例名，用于分析日志
            command_item = self.case_table.item(row_index, 3)
            case_name_item = self.case_table.item(row_index, 1)

            command = command_item.text() if command_item else ""
            case_name = case_name_item.text() if case_name_item else f"用例_{row_index}"

            # 分析日志文件确定真实状态
            log_path = self.get_log_path_from_command(command, case_name)
            log_status = self.check_simulation_status_from_log(log_path)

            # 根据日志分析结果和进程退出码确定最终状态
            final_status, final_color, status_reason = self._determine_final_status(
                exit_code, log_status, log_path
            )

            # 更新状态显示
            status_item.setText(final_status)
            status_item.setForeground(final_color)

            # 设置工具提示，显示状态判断依据
            tooltip = f"进程退出码: {exit_code}\n日志分析结果: {log_status}\n判断依据: {status_reason}"
            status_item.setToolTip(tooltip)

            print(f"用例 {case_name} 执行完成 - 退出码: {exit_code}, 日志状态: {log_status}, 最终状态: {final_status}")

            # 更新结束时间
            end_time_item = self.case_table.item(row_index, 5)
            if end_time_item is None:
                end_time_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 5, end_time_item)

            end_time = datetime.datetime.now().strftime("%H:%M:%S")
            end_time_item.setText(end_time)

            # 更新按钮状态
            self.update_button_states()

        except Exception as e:
            print(f"处理执行结束事件时出错: {str(e)}")

    def on_execution_output(self, row_index, output):
        """处理执行输出事件"""
        try:
            # 检查行索引是否有效
            if row_index < 0:
                print(f"警告: 无效的行索引 {row_index}")
                return

            # 如果日志标签页不存在，尝试创建
            if row_index not in self.log_tabs:
                # 尝试获取用例名
                try:
                    if row_index < self.case_table.rowCount():
                        case_name_item = self.case_table.item(row_index, 1)
                        if case_name_item:
                            case_name = case_name_item.text()
                        else:
                            case_name = f"用例_{row_index}"
                    else:
                        case_name = f"用例_{row_index}"

                    # 创建日志标签页
                    self._create_log_tab(row_index, case_name)
                except Exception as tab_error:
                    print(f"创建日志标签页失败: {str(tab_error)}")
                    return

            # 添加到日志
            if row_index in self.log_tabs:
                # 确保输出不为空
                if output and output.strip():
                    # 处理可能的HTML特殊字符
                    output_html = self._prepare_output_for_html(output)

                    # 获取日志编辑器
                    log_edit = self.log_tabs[row_index]
                    if log_edit is None:
                        print(f"警告: 行 {row_index} 的日志编辑器为空")
                        return

                    # 使用HTML格式添加文本，保留格式和颜色
                    log_edit.append(output_html)

                    # 滚动到底部
                    cursor = log_edit.textCursor()
                    cursor.movePosition(QTextCursor.End)
                    log_edit.setTextCursor(cursor)

                    # 确保更新UI
                    QApplication.processEvents()
        except Exception as e:
            print(f"处理执行输出时出错: {str(e)}")

    def _prepare_output_for_html(self, text):
        """准备输出文本用于HTML显示"""
        # 转义HTML特殊字符
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')

        # 保留空格和换行
        text = text.replace(' ', '&nbsp;')
        text = text.replace('\n', '<br>')

        # 使用等宽字体确保正确显示
        return f'<span style="font-family: Courier New, monospace;">{text}</span>'

    def on_all_finished(self):
        """处理所有命令执行完成事件"""
        # 重置进程管理器的停止标志
        self.process_manager.stopping = False

        # 设置按钮状态
        self.execute_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        # 更新重新仿真按钮状态
        self.update_button_states()

        # 显示完成消息
        QMessageBox.information(self, "完成", "所有选中的用例已执行完成")

    def _create_log_tab(self, row_index, case_name):
        """创建日志标签页"""
        try:
            # 检查参数有效性
            if row_index < 0:
                print(f"警告: 无效的行索引 {row_index}")
                return

            if not case_name:
                case_name = f"用例_{row_index}"

            # 检查是否已存在
            if row_index in self.log_tabs and self.log_tabs[row_index] is not None:
                # 切换到已有标签页
                tab_index = self.tab_widget.indexOf(self.log_tabs[row_index])
                if tab_index != -1:
                    self.tab_widget.setCurrentIndex(tab_index)
                return

            # 创建文本编辑器
            log_edit = QTextEdit()
            log_edit.setReadOnly(True)
            log_edit.setLineWrapMode(QTextEdit.NoWrap)

            # 设置更好的字体
            font = QFont("Consolas", 10)  # 使用Consolas字体，更适合显示代码和日志
            if not font.exactMatch():  # 如果Consolas不可用
                font = QFont("Courier New", 10)  # 回退到Courier New
            if not font.exactMatch():  # 如果Courier New也不可用
                font = QFont("Monospace", 10)  # 回退到通用等宽字体
            log_edit.setFont(font)

            # 设置文本颜色和背景
            log_edit.setStyleSheet("""
                QTextEdit {
                    background-color: #F8F8F8;
                    color: #333333;
                    border: 1px solid #CCCCCC;
                }
            """)

            # 添加欢迎信息
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_edit.setHtml(f"""
                <div style="color: #0066CC; font-family: Arial, sans-serif; margin-bottom: 10px;">
                    <b>批量执行 - {case_name}</b><br>
                    <span style="font-size: smaller;">开始时间: {current_time}</span>
                </div>
                <div style="color: #666666; font-family: Consolas, 'Courier New', monospace; margin-bottom: 10px;">
                    准备执行命令，请稍候...
                </div>
            """)

            # 添加到标签页
            tab_index = self.tab_widget.addTab(log_edit, case_name)
            self.tab_widget.setCurrentIndex(tab_index)

            # 存储引用
            self.log_tabs[row_index] = log_edit

        except Exception as e:
            print(f"创建日志标签页时出错: {str(e)}")

    def show_context_menu(self, position):
        """显示右键菜单"""
        try:
            # 获取点击的行
            item = self.case_table.itemAt(position)
            if item is None:
                return

            row = item.row()

            # 检查该行是否有有效的状态
            status_item = self.case_table.item(row, 2)
            if status_item is None:
                return

            status = status_item.text()

            # 创建右键菜单
            menu = QMenu(self)

            # 添加重新仿真选项（仅对已完成的用例可用）
            if (status in ["执行成功", "执行失败", "状态未知"] or
                "执行失败" in status):
                rerun_action = QAction("重新仿真", self)
                rerun_action.triggered.connect(lambda: self.rerun_specific_case(row))
                menu.addAction(rerun_action)

            # 添加查看日志选项
            if row in self.log_tabs:
                view_log_action = QAction("查看日志", self)
                view_log_action.triggered.connect(lambda: self.show_log_tab(row))
                menu.addAction(view_log_action)

            # 显示菜单
            if not menu.isEmpty():
                menu.exec_(self.case_table.mapToGlobal(position))

        except Exception as e:
            print(f"显示右键菜单时出错: {str(e)}")

    def show_log_tab(self, row):
        """显示指定行的日志标签页"""
        try:
            if row in self.log_tabs:
                # 切换到对应的日志标签页
                for i in range(self.tab_widget.count()):
                    if self.tab_widget.widget(i) == self.log_tabs[row]:
                        self.tab_widget.setCurrentIndex(i)
                        break
        except Exception as e:
            print(f"显示日志标签页时出错: {str(e)}")

    def update_button_states(self):
        """更新按钮状态"""
        try:
            # 检查是否有可重新仿真的用例被选中
            has_rerunnable = False
            has_selected = False

            for row in range(self.case_table.rowCount()):
                checkbox = self.case_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    has_selected = True
                    status_item = self.case_table.item(row, 2)
                    if status_item:
                        status = status_item.text()
                        if (status in ["执行成功", "执行失败", "状态未知"] or
                            "执行失败" in status):
                            has_rerunnable = True
                            break

            # 更新重新仿真按钮状态
            self.rerun_button.setEnabled(has_rerunnable and not self.process_manager.is_running())

            # 更新日志和文件打开按钮状态（只要有选中的案例就启用）
            self.open_compile_log_button.setEnabled(has_selected)
            self.open_sim_log_button.setEnabled(has_selected)
            self.open_disasm_button.setEnabled(has_selected)

        except Exception as e:
            print(f"更新按钮状态时出错: {str(e)}")

    def get_selected_case_name(self):
        """获取当前选中的案例名称"""
        try:
            # 首先尝试获取表格当前选中的行
            current_row = self.case_table.currentRow()
            if current_row >= 0:
                case_name_item = self.case_table.item(current_row, 1)
                if case_name_item:
                    case_name = case_name_item.text()
                    # 如果案例名包含描述（格式：用例名 - 描述），提取用例名部分
                    if " - " in case_name:
                        case_name = case_name.split(" - ")[0]
                    return case_name

            # 如果没有选中行，则查找第一个勾选的案例
            for row in range(self.case_table.rowCount()):
                checkbox = self.case_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    case_name_item = self.case_table.item(row, 1)
                    if case_name_item:
                        case_name = case_name_item.text()
                        # 如果案例名包含描述（格式：用例名 - 描述），提取用例名部分
                        if " - " in case_name:
                            case_name = case_name.split(" - ")[0]
                        return case_name

            return None
        except Exception as e:
            print(f"获取选中案例名称时出错: {str(e)}")
            return None

    def rerun_selected_cases(self):
        """重新仿真选中的用例"""
        try:
            # 获取选中的可重新仿真的用例
            rerunnable_rows = []

            for row in range(self.case_table.rowCount()):
                checkbox = self.case_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    status_item = self.case_table.item(row, 2)
                    if status_item:
                        status = status_item.text()
                        if (status in ["执行成功", "执行失败", "状态未知"] or
                            "执行失败" in status):
                            rerunnable_rows.append(row)

            if not rerunnable_rows:
                QMessageBox.warning(self, "警告", "请选择已完成的用例进行重新仿真")
                return

            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认重新仿真",
                f"确定要重新仿真选中的 {len(rerunnable_rows)} 个用例吗？\n"
                "这将清除之前的执行结果和日志。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 执行重新仿真
            self._perform_rerun(rerunnable_rows)

        except Exception as e:
            print(f"重新仿真选中用例时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"重新仿真时出错: {str(e)}")

    def rerun_specific_case(self, row):
        """重新仿真指定的用例"""
        try:
            # 检查用例状态
            status_item = self.case_table.item(row, 2)
            if status_item is None:
                return

            status = status_item.text()
            if not (status in ["执行成功", "执行失败", "状态未知"] or
                    "执行失败" in status):
                QMessageBox.warning(self, "警告", "只能重新仿真已完成的用例")
                return

            # 获取用例名
            case_name_item = self.case_table.item(row, 1)
            case_name = case_name_item.text() if case_name_item else f"用例_{row}"

            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认重新仿真",
                f"确定要重新仿真用例 '{case_name}' 吗？\n"
                "这将清除之前的执行结果和日志。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 执行重新仿真
            self._perform_rerun([row])

        except Exception as e:
            print(f"重新仿真指定用例时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"重新仿真时出错: {str(e)}")

    def _perform_rerun(self, rows):
        """执行重新仿真的核心逻辑"""
        try:
            # 重置选中用例的状态
            for row in rows:
                # 重置状态
                status_item = self.case_table.item(row, 2)
                if status_item:
                    status_item.setText("等待执行")
                    status_item.setForeground(QColor(128, 128, 128))

                # 清除时间
                start_time_item = self.case_table.item(row, 4)
                if start_time_item:
                    start_time_item.setText("")

                end_time_item = self.case_table.item(row, 5)
                if end_time_item:
                    end_time_item.setText("")

                # 清除或重置日志标签页
                if row in self.log_tabs:
                    log_edit = self.log_tabs[row]
                    log_edit.clear()
                    log_edit.append(f"=== 重新仿真开始 ===\n")

            # 如果当前没有任务在执行，立即开始执行
            if not self.process_manager.is_running():
                # 清除进程管理器中的命令队列
                self.process_manager.commands = []

                # 添加要重新执行的命令到进程管理器
                for row in rows:
                    # 获取命令
                    command_item = self.case_table.item(row, 3)
                    if command_item:
                        command = command_item.text()

                        # 获取用例名
                        case_name_item = self.case_table.item(row, 1)
                        case_name = case_name_item.text() if case_name_item else f"用例_{row}"

                        # 添加命令到队列
                        self.process_manager.add_command(row, command, case_name)

                # 更新按钮状态
                self.execute_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.rerun_button.setEnabled(False)

                # 开始执行
                self.process_manager.start_execution()

                print(f"开始重新仿真 {len(rows)} 个用例")
            else:
                # 如果有任务在执行，将重新仿真的任务加入队列
                for row in rows:
                    # 获取命令
                    command_item = self.case_table.item(row, 3)
                    if command_item:
                        command = command_item.text()

                        # 获取用例名
                        case_name_item = self.case_table.item(row, 1)
                        case_name = case_name_item.text() if case_name_item else f"用例_{row}"

                        # 添加命令到队列
                        self.process_manager.add_command(row, command, case_name)

                print(f"已将 {len(rows)} 个用例加入重新仿真队列")
                QMessageBox.information(
                    self,
                    "重新仿真",
                    f"已将 {len(rows)} 个用例加入执行队列，将在当前任务完成后执行。"
                )

            # 更新按钮状态
            self.update_button_states()

        except Exception as e:
            print(f"执行重新仿真时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行重新仿真时出错: {str(e)}")

    def check_simulation_status_from_log(self, log_path: str) -> str:
        """通过分析日志文件检查仿真状态

        Args:
            log_path (str): 日志文件路径

        Returns:
            str: 仿真状态 (PASS/FAIL/On-Going/UNKNOWN)
        """
        try:
            if not os.path.exists(log_path):
                return "UNKNOWN"

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

                # 读取最后100行，增加检查范围
                lines = content.split('\n')[-100:]
                last_lines = '\n'.join(lines)

                # 检查多种PASS标识
                pass_patterns = [
                    "SPRD_PASSED",
                    "TEST PASSED",
                    "PASSED",
                    "Simulation completed successfully",
                    "SUCCESS",
                    "FINISH",
                    "simulation finished",
                    "Simulation PASSED",
                    "Test completed successfully"
                ]

                for pattern in pass_patterns:
                    if pattern in last_lines:
                        return "PASS"

                # 检查多种FAIL标识
                fail_patterns = [
                    "SPRD_FAILED",
                    "TEST FAILED",
                    "FAILED",
                    "Error", "ERROR",
                    "FATAL", "Fatal",
                    "Simulation FAILED",
                    "SIMULATION FAILED",
                    "simulation failed",
                    "Test failed",
                    "ABORT",
                    "TIMEOUT"
                ]

                for pattern in fail_patterns:
                    if pattern in last_lines:
                        return "FAIL"

                # 检查文件修改时间
                file_mtime = os.path.getmtime(log_path)
                current_time = datetime.datetime.now().timestamp()
                time_diff = current_time - file_mtime

                # 如果文件超过5分钟没有更新，可能已经完成但没有明确标识
                if time_diff > 300:  # 5分钟
                    # 检查是否有仿真结束的其他标识
                    end_patterns = [
                        "exit",
                        "quit",
                        "stop",
                        "end",
                        "finish",
                        "done",
                        "completed",
                        "terminated"
                    ]

                    for pattern in end_patterns:
                        if pattern.lower() in last_lines.lower():
                            print(f"检测到结束标识: {pattern}，但无明确PASS/FAIL，默认为PASS")
                            return "PASS"

                    print("文件长时间未更新，可能已失败")
                    return "FAIL"

                # 仿真仍在进行中
                return "On-Going"

        except Exception as e:
            print(f"检查仿真状态失败: {str(e)}")
            return "UNKNOWN"

    def get_log_path_from_command(self, command: str, case_name: str) -> str:
        """从命令中提取日志文件路径

        Args:
            command (str): 执行命令
            case_name (str): 用例名称

        Returns:
            str: 日志文件路径
        """
        try:
            # 解析命令中的-rundir参数
            rundir = None
            parts = command.split()

            for i, part in enumerate(parts):
                if part == "-rundir" and i + 1 < len(parts):
                    rundir = parts[i + 1]
                    break

            # 如果没有-rundir参数，使用用例名作为目录
            if not rundir:
                rundir = case_name

            # 构建日志文件路径
            log_path = os.path.join(rundir, "log", "irun_sim.log")

            # 如果路径不存在，尝试其他可能的路径
            if not os.path.exists(log_path):
                alternative_paths = [
                    os.path.join(rundir, "log", "vcs_sim.log"),
                    os.path.join(rundir, "log", "sim.log"),
                    os.path.join(rundir, "irun_sim.log"),
                    os.path.join(rundir, "vcs_sim.log"),
                    os.path.join(rundir, "sim.log"),
                    os.path.join(case_name, "log", "irun_sim.log"),
                    os.path.join(case_name, "log", "vcs_sim.log"),
                    os.path.join(case_name, "log", "sim.log")
                ]

                for alt_path in alternative_paths:
                    if os.path.exists(alt_path):
                        log_path = alt_path
                        break

            return log_path

        except Exception as e:
            print(f"从命令提取日志路径失败: {str(e)}")
            # 返回默认路径
            return os.path.join(case_name, "log", "irun_sim.log")

    def _determine_final_status(self, exit_code: int, log_status: str, log_path: str) -> tuple:
        """确定最终的执行状态

        Args:
            exit_code (int): 进程退出码
            log_status (str): 日志分析结果
            log_path (str): 日志文件路径

        Returns:
            tuple: (final_status, final_color, status_reason)
        """
        try:
            # 优先使用日志分析结果
            if log_status == "PASS":
                return "执行成功", QColor(0, 128, 0), "日志分析显示PASS"
            elif log_status == "FAIL":
                return "执行失败", QColor(255, 0, 0), "日志分析显示FAIL"
            elif log_status == "On-Going":
                # 进程已结束但日志显示仍在进行，可能是异常情况
                if exit_code == 0:
                    return "状态未知", QColor(255, 165, 0), "进程正常退出但日志无明确结果"
                else:
                    return "执行失败", QColor(255, 0, 0), f"进程异常退出(退出码:{exit_code})且日志无明确结果"
            elif log_status == "UNKNOWN":
                # 无法分析日志，回退到退出码判断
                if not os.path.exists(log_path):
                    reason = "日志文件不存在，基于退出码判断"
                else:
                    reason = "日志分析失败，基于退出码判断"

                if exit_code == 0:
                    return "执行成功", QColor(0, 128, 0), reason
                else:
                    return f"执行失败 ({exit_code})", QColor(255, 0, 0), reason
            else:
                # 未知的日志状态，回退到退出码判断
                if exit_code == 0:
                    return "执行成功", QColor(0, 128, 0), "未知日志状态，基于退出码判断"
                else:
                    return f"执行失败 ({exit_code})", QColor(255, 0, 0), "未知日志状态，基于退出码判断"

        except Exception as e:
            print(f"确定最终状态时出错: {str(e)}")
            # 出错时回退到退出码判断
            if exit_code == 0:
                return "执行成功", QColor(0, 128, 0), "状态判断出错，基于退出码判断"
            else:
                return f"执行失败 ({exit_code})", QColor(255, 0, 0), "状态判断出错，基于退出码判断"

    def open_compile_log(self):
        """打开编译日志"""
        try:
            case_name = self.get_selected_case_name()
            if not case_name:
                if self.main_window:
                    self.main_window.show_warning("操作失败", "请先选择一个用例")
                return

            # 调用execution_controller的方法
            if self.execution_controller:
                self.execution_controller.open_compile_log(case_name)
            else:
                print(f"批量执行插件: execution_controller为None，无法打开编译日志")
                if self.main_window:
                    self.main_window.show_warning("操作失败", "无法访问执行控制器，请检查主窗口初始化状态")
        except Exception as e:
            print(f"打开编译日志时出错: {str(e)}")
            if self.main_window:
                self.main_window.show_error("打开编译日志失败", f"无法打开编译日志: {str(e)}")

    def open_sim_log(self):
        """打开仿真日志"""
        try:
            case_name = self.get_selected_case_name()
            if not case_name:
                if self.main_window:
                    self.main_window.show_warning("操作失败", "请先选择一个用例")
                return

            # 调用execution_controller的方法
            if self.execution_controller:
                self.execution_controller.open_sim_log(case_name)
            else:
                print(f"批量执行插件: execution_controller为None，无法打开仿真日志")
                if self.main_window:
                    self.main_window.show_warning("操作失败", "无法访问执行控制器，请检查主窗口初始化状态")
        except Exception as e:
            print(f"打开仿真日志时出错: {str(e)}")
            if self.main_window:
                self.main_window.show_error("打开仿真日志失败", f"无法打开仿真日志: {str(e)}")

    def open_disasm_file(self):
        """打开反汇编文件"""
        try:
            case_name = self.get_selected_case_name()
            if not case_name:
                if self.main_window:
                    self.main_window.show_warning("操作失败", "请先选择一个用例")
                return

            # 调用execution_controller的方法（使用open_asm_file方法）
            if self.execution_controller:
                self.execution_controller.open_asm_file(case_name)
            else:
                print(f"批量执行插件: execution_controller为None，无法打开反汇编文件")
                if self.main_window:
                    self.main_window.show_warning("操作失败", "无法访问执行控制器，请检查主窗口初始化状态")
        except Exception as e:
            print(f"打开反汇编文件时出错: {str(e)}")
            if self.main_window:
                self.main_window.show_error("打开反汇编文件失败", f"无法打开反汇编文件: {str(e)}")

class BatchExecutionPlugin(PluginBase):
    """批量执行插件"""

    @property
    def name(self):
        return "批量执行工具"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "批量执行多个用例，支持并行执行和结果管理"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window
            self.dialog = None

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_dialog)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addAction(self.menu_action)

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        try:
            # 关闭对话框
            if self.dialog and self.dialog.isVisible():
                self.dialog.close()

            # 移除菜单项
            if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
                self.main_window.tools_menu.removeAction(self.menu_action)

            print(f"成功清理插件: {self.name}")

        except Exception as e:
            print(f"清理插件 {self.name} 失败: {str(e)}")

    def show_dialog(self):
        """显示批量执行对话框"""
        try:
            if not self.dialog:
                self.dialog = BatchExecutionDialog(self.main_window, self.name)

            # 显示对话框
            self.dialog.show()
            self.dialog.raise_()
            self.dialog.activateWindow()
        except KeyboardInterrupt:
            print("批量执行对话框被用户中断")
        except Exception as e:
            print(f"显示批量执行对话框时出错: {str(e)}")
            # 如果创建对话框失败，重置对话框引用
            self.dialog = None
