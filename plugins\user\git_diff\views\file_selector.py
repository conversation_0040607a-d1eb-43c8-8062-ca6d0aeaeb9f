"""
文件选择器组件

提供文件选择功能，支持：
- 单文件模式：直接选择文件
- 目录模式：浏览目录中的Git跟踪文件
- 文件过滤和搜索
"""

import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QRadioButton,
    QPushButton, QLineEdit, QTreeWidget, QTreeWidgetItem, QFileDialog,
    QMessageBox, QLabel, QButtonGroup
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

from ..models.git_repository import GitRepository


class FileSelector(QWidget):
    """文件选择器组件"""
    
    # 信号定义
    file_selected = pyqtSignal(str)  # 文件选择信号
    repo_changed = pyqtSignal(str)   # 仓库变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.git_repo = None
        self.current_file_path = ""
        self.current_repo_path = ""
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建分组框
        group_box = QGroupBox("文件选择")
        layout.addWidget(group_box)
        
        group_layout = QVBoxLayout(group_box)
        group_layout.setSpacing(10)
        
        # 模式选择
        mode_layout = QHBoxLayout()
        
        self.mode_group = QButtonGroup(self)
        self.single_file_radio = QRadioButton("单文件模式")
        self.directory_radio = QRadioButton("目录模式")
        self.single_file_radio.setChecked(True)
        
        self.mode_group.addButton(self.single_file_radio, 0)
        self.mode_group.addButton(self.directory_radio, 1)
        
        mode_layout.addWidget(self.single_file_radio)
        mode_layout.addWidget(self.directory_radio)
        mode_layout.addStretch()
        
        group_layout.addLayout(mode_layout)
        
        # 文件/目录路径输入
        path_layout = QHBoxLayout()
        
        self.path_label = QLabel("文件路径:")
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("请选择文件或输入文件路径...")
        self.browse_button = QPushButton("浏览...")
        self.browse_button.setMaximumWidth(80)
        
        path_layout.addWidget(self.path_label)
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_button)
        
        group_layout.addLayout(path_layout)
        
        # 文件树（目录模式时显示）
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabel("Git跟踪的文件")
        self.file_tree.setVisible(False)
        group_layout.addWidget(self.file_tree)
        
        # 状态信息
        self.status_label = QLabel("请选择Git仓库中的文件")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        group_layout.addWidget(self.status_label)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.mode_group.buttonClicked.connect(self._on_mode_changed)
        self.browse_button.clicked.connect(self._on_browse_clicked)
        self.path_edit.textChanged.connect(self._on_path_changed)
        self.path_edit.returnPressed.connect(self._on_path_entered)
        self.file_tree.itemClicked.connect(self._on_tree_item_clicked)
    
    def _on_mode_changed(self, button):
        """模式变更事件处理"""
        is_directory_mode = button == self.directory_radio
        
        # 更新UI显示
        if is_directory_mode:
            self.path_label.setText("目录路径:")
            self.path_edit.setPlaceholderText("请选择目录...")
            self.file_tree.setVisible(True)
        else:
            self.path_label.setText("文件路径:")
            self.path_edit.setPlaceholderText("请选择文件或输入文件路径...")
            self.file_tree.setVisible(False)
        
        # 清空当前选择
        self.path_edit.clear()
        self.file_tree.clear()
        self.current_file_path = ""
        self.status_label.setText("请选择Git仓库中的文件")
    
    def _on_browse_clicked(self):
        """浏览按钮点击事件处理"""
        try:
            if self.directory_radio.isChecked():
                # 目录模式
                directory = QFileDialog.getExistingDirectory(
                    self, "选择目录", self.current_repo_path or os.getcwd()
                )
                if directory:
                    self.path_edit.setText(directory)
                    self._load_directory_files(directory)
            else:
                # 单文件模式
                file_path, _ = QFileDialog.getOpenFileName(
                    self, "选择文件", self.current_repo_path or os.getcwd(),
                    "所有文件 (*.*)"
                )
                if file_path:
                    self.path_edit.setText(file_path)
                    self._select_file(file_path)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"浏览文件失败: {str(e)}")
    
    def _on_path_changed(self, text):
        """路径输入变更事件处理"""
        if not text.strip():
            self.status_label.setText("请选择Git仓库中的文件")
            return
        
        # 实时验证路径
        if os.path.exists(text):
            if self.directory_radio.isChecked() and os.path.isdir(text):
                self.status_label.setText("目录有效，按回车键加载文件")
            elif self.single_file_radio.isChecked() and os.path.isfile(text):
                self.status_label.setText("文件有效，按回车键选择")
            else:
                self.status_label.setText("路径类型不匹配当前模式")
        else:
            self.status_label.setText("路径不存在")
    
    def _on_path_entered(self):
        """路径输入回车事件处理"""
        path = self.path_edit.text().strip()
        if not path:
            return
        
        try:
            if self.directory_radio.isChecked():
                if os.path.isdir(path):
                    self._load_directory_files(path)
                else:
                    QMessageBox.warning(self, "错误", "请输入有效的目录路径")
            else:
                if os.path.isfile(path):
                    self._select_file(path)
                else:
                    QMessageBox.warning(self, "错误", "请输入有效的文件路径")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"处理路径失败: {str(e)}")
    
    def _on_tree_item_clicked(self, item, column):
        """文件树项目点击事件处理"""
        if item and not item.childCount():  # 只处理叶子节点（文件）
            file_path = item.data(0, Qt.UserRole)
            if file_path:
                self._select_file(file_path)
    
    def _load_directory_files(self, directory):
        """加载目录中的Git跟踪文件"""
        try:
            self.status_label.setText("正在加载文件列表...")
            
            # 初始化Git仓库
            self.git_repo = GitRepository(directory)
            
            if not self.git_repo.is_valid_repo():
                self.status_label.setText("所选目录不是Git仓库")
                QMessageBox.warning(self, "警告", "所选目录不是Git仓库或不包含Git仓库")
                return
            
            # 更新仓库路径
            repo_root = self.git_repo.get_repo_root()
            self.current_repo_path = repo_root
            self.repo_changed.emit(repo_root)
            
            # 获取Git跟踪的文件（如果选择的目录不是仓库根目录，则过滤文件）
            if directory == repo_root:
                # 如果选择的是仓库根目录，显示所有跟踪文件
                tracked_files = self.git_repo.get_tracked_files()
            else:
                # 如果选择的是子目录，只显示该目录下的跟踪文件
                tracked_files = self.git_repo.get_tracked_files(directory)

            if not tracked_files:
                self.status_label.setText("该目录中没有跟踪的文件")
                return

            # 构建文件树
            self._build_file_tree(tracked_files, repo_root, directory)
            
            self.status_label.setText(f"已加载 {len(tracked_files)} 个跟踪文件")
            
        except Exception as e:
            self.status_label.setText("加载文件列表失败")
            QMessageBox.warning(self, "错误", f"加载文件列表失败: {str(e)}")
    
    def _build_file_tree(self, file_paths, repo_root, selected_directory=None):
        """构建文件树

        Args:
            file_paths: 文件路径列表（相对于仓库根目录）
            repo_root: 仓库根目录
            selected_directory: 选择的目录（如果不是仓库根目录）
        """
        self.file_tree.clear()

        # 按目录组织文件
        dir_items = {}

        for file_path in file_paths:
            # 获取绝对路径
            abs_path = os.path.join(repo_root, file_path)

            # 如果指定了选择目录，调整显示的路径结构
            if selected_directory and selected_directory != repo_root:
                # 计算相对于选择目录的路径
                try:
                    rel_to_selected = os.path.relpath(abs_path, selected_directory)
                    if rel_to_selected.startswith('..'):
                        # 文件不在选择的目录下，跳过
                        continue
                    display_path = rel_to_selected
                except ValueError:
                    # 路径计算失败，使用原始路径
                    display_path = file_path
            else:
                display_path = file_path

            # 分割路径
            parts = display_path.split(os.sep)
            
            # 构建目录结构
            current_parent = self.file_tree.invisibleRootItem()
            current_path = ""
            
            for i, part in enumerate(parts):
                if i == len(parts) - 1:
                    # 文件节点
                    file_item = QTreeWidgetItem(current_parent)
                    file_item.setText(0, part)
                    file_item.setData(0, Qt.UserRole, abs_path)
                    file_item.setToolTip(0, abs_path)
                else:
                    # 目录节点
                    current_path = os.path.join(current_path, part) if current_path else part
                    
                    if current_path not in dir_items:
                        dir_item = QTreeWidgetItem(current_parent)
                        dir_item.setText(0, part)
                        dir_item.setData(0, Qt.UserRole, None)  # 目录节点没有文件路径
                        dir_items[current_path] = dir_item
                        current_parent = dir_item
                    else:
                        current_parent = dir_items[current_path]
        
        # 展开根目录
        self.file_tree.expandToDepth(1)
    
    def _select_file(self, file_path):
        """选择文件"""
        try:
            if not os.path.isfile(file_path):
                QMessageBox.warning(self, "错误", "所选路径不是有效文件")
                return
            
            # 检查是否在Git仓库中
            file_dir = os.path.dirname(file_path)
            self.git_repo = GitRepository(file_dir)
            
            if not self.git_repo.is_valid_repo():
                QMessageBox.warning(self, "警告", "所选文件不在Git仓库中")
                return
            
            # 更新当前状态
            self.current_file_path = file_path
            repo_root = self.git_repo.get_repo_root()
            self.current_repo_path = repo_root
            
            # 发送信号
            self.file_selected.emit(file_path)
            self.repo_changed.emit(repo_root)
            
            # 更新状态
            rel_path = os.path.relpath(file_path, repo_root)
            self.status_label.setText(f"已选择: {rel_path}")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"选择文件失败: {str(e)}")
    
    def refresh(self):
        """刷新文件列表"""
        if self.directory_radio.isChecked() and self.current_repo_path:
            self._load_directory_files(self.current_repo_path)
        elif self.single_file_radio.isChecked() and self.current_file_path:
            self._select_file(self.current_file_path)
    
    def get_current_file(self) -> str:
        """获取当前选中的文件路径"""
        return self.current_file_path
    
    def get_current_repo(self) -> str:
        """获取当前仓库路径"""
        return self.current_repo_path
