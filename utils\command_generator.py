"""
命令生成器模块
"""
import sys
import os

class CommandParser:
    """命令解析器，用于从命令字符串中提取参数"""

    @staticmethod
    def parse_rundir_from_command(command):
        """
        从命令字符串中解析rundir参数

        Args:
            command (str): 命令字符串

        Returns:
            str: rundir参数值，如果没有找到则返回None
        """
        if not command:
            return None

        try:
            # 分割命令参数
            parts = command.split()

            # 查找-rundir参数
            for i, part in enumerate(parts):
                if part == "-rundir" and i + 1 < len(parts):
                    return parts[i + 1]

            return None
        except Exception:
            return None

    @staticmethod
    def parse_case_from_command(command):
        """
        从命令字符串中解析case参数

        对于BATCH RUN模式的分号分隔命令，优先解析最后一个命令中的case参数
        （即用户实际选择的case，而不是base_case）

        Args:
            command (str): 命令字符串

        Returns:
            str: case参数值，如果没有找到则返回None
        """
        if not command:
            return None

        try:
            # 检查是否是BATCH RUN模式的分号分隔命令
            if ' ; ' in command:
                # BATCH RUN模式：分号分隔的多个命令
                # 分割命令并获取最后一个命令（这是实际用户选中的case的命令）
                commands = command.split(' ; ')
                if commands:
                    # 使用最后一个命令来解析用例名称
                    last_command = commands[-1].strip()
                    return CommandParser._parse_case_from_single_command(last_command)
            else:
                # 普通模式：单个命令
                return CommandParser._parse_case_from_single_command(command)

        except Exception:
            return None

    @staticmethod
    def _parse_case_from_single_command(command):
        """
        从单个命令字符串中解析case参数

        Args:
            command (str): 单个命令字符串

        Returns:
            str: case参数值，如果没有找到则返回None
        """
        if not command:
            return None

        try:
            # 分割命令参数
            parts = command.split()

            # 查找-case参数
            for i, part in enumerate(parts):
                if part == "-case" and i + 1 < len(parts):
                    return parts[i + 1]

            return None
        except Exception:
            return None

    @staticmethod
    def parse_tag_from_command(command):
        """
        从命令字符串中解析tag参数

        Args:
            command (str): 命令字符串

        Returns:
            str: tag参数值，如果没有找到则返回None
        """
        if not command:
            return None

        try:
            # 分割命令参数
            parts = command.split()

            # 查找-tag参数
            for i, part in enumerate(parts):
                if part == "-tag" and i + 1 < len(parts):
                    return parts[i + 1]

            return None
        except Exception:
            return None

    @staticmethod
    def parse_post_from_command(command):
        """
        从命令字符串中解析post参数

        Args:
            command (str): 命令字符串

        Returns:
            str: post参数值，如果没有找到则返回None
        """
        if not command:
            return None

        try:
            # 分割命令参数
            parts = command.split()

            # 查找-post参数
            for i, part in enumerate(parts):
                if part == "-post" and i + 1 < len(parts):
                    return parts[i + 1]

            return None
        except Exception:
            return None
            
    @staticmethod
    def parse_regr_file_from_command(command):
        """
        从命令字符串中解析regr文件参数

        Args:
            command (str): 命令字符串

        Returns:
            str: regr文件路径，如果没有找到则返回None
        """
        if not command:
            return None

        try:
            # 分割命令参数
            parts = command.split()

            # 查找-regr参数
            for i, part in enumerate(parts):
                if part == "-regr" and i + 1 < len(parts):
                    return parts[i + 1]

            return None
        except Exception:
            return None

    @staticmethod
    def modify_command_options(command, fsdb_enabled=None, r_enabled=None):
        """
        修改命令中的-fsdb和-R选项

        Args:
            command (str): 原始命令字符串
            fsdb_enabled (bool, optional): 是否启用-fsdb选项，None表示不修改
            r_enabled (bool, optional): 是否启用-R选项，None表示不修改

        Returns:
            str: 修改后的命令字符串
        """
        if not command:
            return command

        try:
            # 处理BATCH RUN模式的分号分隔命令
            if ' ; ' in command:
                commands = command.split(' ; ')
                modified_commands = []

                for cmd in commands:
                    cmd = cmd.strip()
                    if cmd:
                        modified_cmd = CommandParser._modify_single_command_options(
                            cmd, fsdb_enabled, r_enabled
                        )
                        modified_commands.append(modified_cmd)

                return ' ; '.join(modified_commands)
            else:
                # 普通模式：单个命令
                return CommandParser._modify_single_command_options(
                    command, fsdb_enabled, r_enabled
                )

        except Exception as e:
            print(f"修改命令选项时出错: {str(e)}")
            return command

    @staticmethod
    def _modify_single_command_options(command, fsdb_enabled, r_enabled):
        """
        修改单个命令中的选项

        Args:
            command (str): 单个命令字符串
            fsdb_enabled (bool, optional): 是否启用-fsdb选项
            r_enabled (bool, optional): 是否启用-R选项

        Returns:
            str: 修改后的命令字符串
        """
        if not command:
            return command

        parts = command.split()
        if not parts:
            return command

        # 处理-fsdb选项
        if fsdb_enabled is not None:
            parts = CommandParser._modify_fsdb_option(parts, fsdb_enabled)

        # 处理-R选项
        if r_enabled is not None:
            parts = CommandParser._modify_r_option(parts, r_enabled)

        return ' '.join(parts)

    @staticmethod
    def _modify_fsdb_option(parts, enabled):
        """
        修改命令中的-fsdb选项

        Args:
            parts (list): 命令参数列表
            enabled (bool): 是否启用-fsdb选项

        Returns:
            list: 修改后的命令参数列表
        """
        # 查找并移除现有的-fsdb选项
        i = 0
        while i < len(parts):
            if parts[i] == "-fsdb":
                # 移除-fsdb参数
                parts.pop(i)
                # 检查是否有跟随的TCL文件参数
                if i < len(parts) and not parts[i].startswith("-"):
                    # 移除TCL文件参数
                    parts.pop(i)
                # 不增加i，因为已经移除了元素
            else:
                i += 1

        # 如果需要启用-fsdb，添加到命令中
        if enabled:
            parts.append("-fsdb")

        return parts

    @staticmethod
    def _modify_r_option(parts, enabled):
        """
        修改命令中的-R选项

        Args:
            parts (list): 命令参数列表
            enabled (bool): 是否启用-R选项

        Returns:
            list: 修改后的命令参数列表
        """
        # 查找并移除现有的-R选项
        i = 0
        while i < len(parts):
            if parts[i] == "-R":
                # 移除-R参数
                parts.pop(i)
                # 检查是否有跟随的路径参数
                if i < len(parts) and not parts[i].startswith("-"):
                    # 移除路径参数
                    parts.pop(i)
                # 不增加i，因为已经移除了元素
            else:
                i += 1

        # 如果需要启用-R，添加到命令末尾
        if enabled:
            parts.append("-R")

        return parts

    @staticmethod
    def has_fsdb_option(command):
        """
        检查命令中是否包含-fsdb选项

        Args:
            command (str): 命令字符串

        Returns:
            bool: 是否包含-fsdb选项
        """
        if not command:
            return False

        try:
            # 处理BATCH RUN模式，检查任一命令是否包含-fsdb
            if ' ; ' in command:
                commands = command.split(' ; ')
                for cmd in commands:
                    if CommandParser._has_option_in_single_command(cmd.strip(), "-fsdb"):
                        return True
                return False
            else:
                return CommandParser._has_option_in_single_command(command, "-fsdb")
        except Exception:
            return False

    @staticmethod
    def has_r_option(command):
        """
        检查命令中是否包含-R选项

        Args:
            command (str): 命令字符串

        Returns:
            bool: 是否包含-R选项
        """
        if not command:
            return False

        try:
            # 处理BATCH RUN模式，检查任一命令是否包含-R
            if ' ; ' in command:
                commands = command.split(' ; ')
                for cmd in commands:
                    if CommandParser._has_option_in_single_command(cmd.strip(), "-R"):
                        return True
                return False
            else:
                return CommandParser._has_option_in_single_command(command, "-R")
        except Exception:
            return False

    @staticmethod
    def _has_option_in_single_command(command, option):
        """
        检查单个命令中是否包含指定选项

        Args:
            command (str): 单个命令字符串
            option (str): 要检查的选项（如"-fsdb", "-R"）

        Returns:
            bool: 是否包含指定选项
        """
        if not command:
            return False

        parts = command.split()
        return option in parts

class CommandGenerator:
    """runsim 命令生成器"""

    # 添加类变量用于防止递归
    _recursion_depth = 0
    _max_recursion_depth = 10

    @classmethod
    def generate_command(cls, config, mode="normal", case_name=None, has_terminal=False, batch_run_info=None):
        """
        生成 runsim 命令。

        Args:
            config (dict): 配置参数字典
            mode (str): 执行模式，例如 "normal", "R", "C" 等
            case_name (str, optional): 指定的用例名称，默认为 None
            has_terminal (bool): 是否有终端集成支持
            batch_run_info (dict, optional): BATCH RUN 模式信息，包含 enabled, force_update, base_case_name

        Returns:
            str or list: 生成的 runsim 命令字符串，或在 BATCH RUN 模式下返回命令列表
        """
        # 防止无限递归
        cls._recursion_depth += 1
        if cls._recursion_depth > cls._max_recursion_depth:
            cls._recursion_depth = 0
            return "runsim [递归深度超过限制]"

        try:
            # 检查是否启用 BATCH RUN 模式
            if batch_run_info and batch_run_info.get('enabled', False):
                return cls._generate_batch_run_commands(config, mode, case_name, has_terminal, batch_run_info)

            # 使用单个命令生成逻辑
            return cls._generate_single_command(config, mode, case_name, has_terminal)
        finally:
            # 减少递归深度
            cls._recursion_depth -= 1

    @classmethod
    def _generate_batch_run_commands(cls, config, mode, case_name, has_terminal, batch_run_info):
        """
        生成 BATCH RUN 模式的命令序列

        Args:
            config (dict): 配置参数字典
            mode (str): 执行模式
            case_name (str): 用例名称
            has_terminal (bool): 是否有终端集成支持
            batch_run_info (dict): BATCH RUN 模式信息

        Returns:
            list: 命令列表，每个元素是 (command, description) 元组
        """
        from utils.batch_run_utils import BatchRunUtils

        base_case_name = batch_run_info.get('base_case_name', case_name)
        force_update = batch_run_info.get('force_update', False)
        execution_count = batch_run_info.get('execution_count', 1)

        # 生成原始命令（不包含 BATCH RUN 特殊处理）
        original_command = cls._generate_single_command(config, mode, case_name, has_terminal)

        # 使用 BatchRunUtils 生成命令序列
        return BatchRunUtils.generate_batch_run_commands(
            original_command, base_case_name, force_update, execution_count, case_name
        )

    @classmethod
    def _generate_single_command(cls, config, mode, case_name, has_terminal):
        """
        生成单个命令（原有的命令生成逻辑）

        Args:
            config (dict): 配置参数字典
            mode (str): 执行模式
            case_name (str): 用例名称
            has_terminal (bool): 是否有终端集成支持

        Returns:
            str: 生成的命令字符串
        """
        cmd = ["runsim"]  # 初始化命令列表，runsim 命令主体

        if not config.get("regr_file"):  # 如果没有选择回归列表文件，则使用基本参数和用例参数
            if config.get("base"):  # 如果 BASE 参数不为空
                cmd += ["-base", config["base"]]  # 添加 -base 参数
            if config.get("block"):  # 如果 BLOCK 参数不为空
                cmd += ["-block", config["block"]]  # 添加 -block 参数
            if case_name:  # 如果指定了用例名称
                cmd += ["-case", case_name]  # 添加 -case 参数
        # 添加 rundir 参数，指定工作目录
            rundir = config.get("rundir", "").strip()
            if rundir:
                # 处理rundir中的{case_name}占位符
                if "{case_name}" in rundir and case_name:
                    rundir = rundir.replace("{case_name}", case_name)
                cmd += ["-rundir", rundir]
        else:  # 如果选择了回归列表文件
            cmd += ["-regr", config["regr_file"]]  # 添加 -regr 参数，指定回归列表文件
            # 添加-fm参数
            if config.get("fm_checked", False):
                cmd.append("-fm")
            if tag := config.get("tag", "").strip():
                cmd += ["-tag", tag]
            if nt := config.get("nt", "").strip():
                cmd += ["-nt", nt]
            if dashboard := config.get("dashboard", "").strip():
                cmd += ["-m", dashboard]

        # 添加复选框选项
        if config.get("fsdb_checked", False):
            cmd.append("-fsdb")
            # 如果选择了 TCL 文件，添加文件路径
            if tcl_file := config.get("fsdb_file", "").strip():
                cmd.append(tcl_file)

        if config.get("vwdb_checked", False):
            cmd.append("-vwdb")

        if config.get("cl_checked", False):
            cmd.append("-cl")

        if config.get("sva_checked", False):
            cmd.append("-dump_sva")

        if config.get("cov_checked", False):
            cmd.append("-cov")

        if config.get("upf_checked", False):
            cmd.append("-upf")

        # 如果 Dump Memory 参数不为空
        dump_mem = config.get("dump_mem", "")
        if dump_mem:
            # 如果dump_mem包含多个选项，需要用引号包裹
            if " " in dump_mem:
                cmd += ["-dump_mem", f'"{dump_mem}"']  # 添加 -dump_mem 参数，多个选项用引号包裹
            else:
                cmd += ["-dump_mem", dump_mem]  # 添加 -dump_mem 参数

        # 如果 波形 Dump 起始时间 参数不为空
        wdd = config.get("wdd", "").strip()
        if wdd:
            cmd += ["-wdd", wdd]  # 添加 -wdd 参数

        # 如果 种子号 参数不为空
        seed = config.get("seed", "").strip()
        if seed:
            cmd += ["-seed", seed]  # 添加 -seed 参数

        # 如果 仿真参数 不为空
        simarg = config.get("simarg", "").strip()
        if simarg:
            cmd += ["-simarg", f'"{simarg}"']  # 添加 -simarg 参数，参数值用双引号包裹

        # 如果 配置定义 不为空
        cfg_def = config.get("cfg_def", "").strip()
        if cfg_def:
            cmd += ["-cfg_def", cfg_def]  # 添加 -cfg_def 参数

        # 如果 后仿参数 不为空
        post = config.get("post", "").strip()
        if post:
            cmd += ["-post", post]  # 添加 -post 参数

        if mode != "normal":  # 如果执行模式不是 normal
            cmd.append(f"-{mode}")  # 添加模式参数，例如 -R, -C

        # 添加其他选项，直接添加到命令末尾
        other_options = config.get("other_options", "").strip()
        if other_options:  # 如果 其他选项 不为空
            cmd.append(other_options)  # 添加其他选项

        # 针对终端集成,添加必要的环境设置
        prefix = ""
        if has_terminal and sys.platform != 'win32':
            # 添加工作目录切换
            rundir = config.get("rundir", "").strip()
            if rundir:
                # 处理rundir中的{case_name}占位符
                if "{case_name}" in rundir and case_name:
                    rundir = rundir.replace("{case_name}", case_name)
                prefix = f"cd {rundir} && "

        # 构建完整命令
        final_cmd = prefix + " ".join(cmd)

        # 添加命令执行完成提示
        if has_terminal and sys.platform != 'win32':
            final_cmd += ' ; echo "\n[Command completed]"'

        return final_cmd
