"""
系统资源监控模块
"""
import psutil
import threading
import time
from PyQt5.QtCore import QObject, pyqtSignal

class ResourceMonitor(QObject):
    """系统资源监控类，使用后台线程监控系统资源"""

    # 定义信号
    resources_updated = pyqtSignal(bool, float, float)  # high_usage, cpu_percent, mem_percent

    def __init__(self, parent=None):
        """初始化资源监控器"""
        super().__init__(parent)
        self._cpu_percent = 0
        self._mem_percent = 0
        self._high_usage = False
        self._lock = threading.Lock()
        self._running = False
        self._thread = None
        self._update_interval = 2.0  # 更新间隔（秒）
        self._destroyed = False  # 标记对象是否已被销毁

    def start_monitoring(self, interval=2.0):
        """
        开始监控系统资源

        Args:
            interval (float): 监控间隔（秒）
        """
        if self._running:
            return

        self._update_interval = interval
        self._running = True
        self._thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self._thread.start()

    def stop_monitoring(self):
        """停止监控系统资源 - 优化版本，避免阻塞UI"""
        self._running = False
        self._destroyed = True  # 标记对象即将被销毁
        # 不等待线程结束，让线程自然退出，避免阻塞UI
        # 线程会在下一次循环时检查_running标志并自动退出

    def stop_monitoring_sync(self):
        """同步停止监控系统资源（仅在必要时使用）"""
        self._running = False
        # 只在非当前线程时尝试join，避免"cannot join current thread"错误
        if self._thread and self._thread.is_alive() and self._thread is not threading.current_thread():
            try:
                self._thread.join(timeout=0.1)  # 减少超时时间到100ms
            except RuntimeError:
                # 忽略"cannot join current thread"错误
                pass

    def _monitor_resources(self):
        """监控系统资源的后台线程"""
        while self._running and not self._destroyed:
            try:
                # 检查对象是否仍然有效
                if self._destroyed:
                    break

                # 非阻塞方式获取CPU使用率
                cpu_percent = psutil.cpu_percent(interval=None)
                # 如果是第一次调用，可能返回0，再次获取
                if cpu_percent == 0:
                    time.sleep(0.1)
                    cpu_percent = psutil.cpu_percent(interval=None)

                mem_percent = psutil.virtual_memory().percent
                high_usage = cpu_percent > 80 or mem_percent > 80

                with self._lock:
                    if self._destroyed:
                        break
                    self._cpu_percent = cpu_percent
                    self._mem_percent = mem_percent
                    self._high_usage = high_usage

                # 安全地发出信号通知资源更新
                try:
                    if not self._destroyed:
                        self.resources_updated.emit(high_usage, cpu_percent, mem_percent)
                except RuntimeError as e:
                    if "wrapped C/C++ object" in str(e):
                        print(f"ResourceMonitor对象已被删除，停止监控")
                        self._destroyed = True
                        break
                    else:
                        raise

            except Exception as e:
                print(f"资源监控错误: {str(e)}")
                # 如果是严重错误，停止监控
                if "wrapped C/C++ object" in str(e):
                    self._destroyed = True
                    break

            # 等待下一次更新
            time.sleep(self._update_interval)

    def check_resources(self):
        """
        检查系统资源使用情况（非阻塞）

        Returns:
            tuple: (high_usage, cpu_percent, mem_percent)
        """
        if self._destroyed:
            return False, 0.0, 0.0

        with self._lock:
            return self._high_usage, self._cpu_percent, self._mem_percent

    def __del__(self):
        """析构函数，确保线程被正确终止"""
        try:
            # 标记对象已被销毁
            self._destroyed = True
            self._running = False
        except Exception:
            # 忽略析构函数中的所有错误
            pass


class SafeResourceMonitor(QObject):
    """安全的资源监控器包装器，防止Qt对象过早删除导致的错误"""

    # 定义信号
    resources_updated = pyqtSignal(bool, float, float)  # high_usage, cpu_percent, mem_percent

    def __init__(self, parent=None):
        """初始化安全资源监控器"""
        super().__init__(parent)
        self._monitor = None
        self._is_valid = True
        self._last_cpu = 0.0
        self._last_mem = 0.0
        self._last_high_usage = False
        self._destroyed = False

        try:
            # 将当前对象作为parent传递给ResourceMonitor
            self._monitor = ResourceMonitor(parent=self)
            # 连接信号，使用安全的方式
            self._monitor.resources_updated.connect(self._on_resources_updated)
        except Exception as e:
            print(f"创建内部资源监控器失败: {str(e)}")
            self._monitor = None

    def _on_resources_updated(self, high_usage, cpu_percent, mem_percent):
        """安全的资源更新处理"""
        try:
            if self._is_valid and not self._destroyed:
                self._last_cpu = cpu_percent
                self._last_mem = mem_percent
                self._last_high_usage = high_usage
                self.resources_updated.emit(high_usage, cpu_percent, mem_percent)
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                print(f"SafeResourceMonitor对象已删除: {str(e)}")
                self._is_valid = False
                self._destroyed = True
                if self._monitor:
                    self._monitor.stop_monitoring()
                    self._monitor = None
            else:
                raise
        except Exception as e:
            print(f"处理资源更新时出错: {str(e)}")

    def start_monitoring(self, interval=2.0):
        """开始监控系统资源"""
        if self._monitor and self._is_valid:
            try:
                self._monitor.start_monitoring(interval)
            except Exception as e:
                print(f"启动资源监控失败: {str(e)}")
                self._is_valid = False

    def stop_monitoring(self):
        """停止监控系统资源"""
        self._destroyed = True
        self._is_valid = False

        if self._monitor:
            try:
                self._monitor.stop_monitoring()
            except Exception as e:
                print(f"停止资源监控失败: {str(e)}")
            finally:
                self._monitor = None

    def check_resources(self):
        """检查系统资源使用情况（非阻塞）"""
        if self._monitor and self._is_valid:
            try:
                return self._monitor.check_resources()
            except RuntimeError as e:
                if "wrapped C/C++ object" in str(e):
                    print(f"资源监控器对象已删除: {str(e)}")
                    self._is_valid = False
                    self._monitor = None
                    # 返回最后已知的值
                    return self._last_high_usage, self._last_cpu, self._last_mem
                else:
                    raise
            except Exception as e:
                print(f"检查资源时出错: {str(e)}")
                return self._last_high_usage, self._last_cpu, self._last_mem
        else:
            # 返回最后已知的值或默认值
            return self._last_high_usage, self._last_cpu, self._last_mem

    def __del__(self):
        """析构函数"""
        try:
            self._destroyed = True
            self._is_valid = False
            if self._monitor:
                self._monitor.stop_monitoring()
                self._monitor = None
        except Exception:
            # 忽略析构函数中的所有错误
            pass
