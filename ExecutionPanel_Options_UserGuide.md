# RunSim GUI 执行日志面板仿真选项控制功能使用指南

## 功能介绍

在RunSim GUI的执行日志面板中，每个用例标签页现在都包含了便捷的仿真选项控制功能。您可以直接在执行日志页面修改仿真选项并重新执行，无需切换到其他页面。

## 界面说明

### 仿真选项控制区域

在每个用例标签页的状态栏中，您会看到两个红色的复选框：

- **`-fsdb`** 复选框：控制波形输出选项
- **`-R`** 复选框：控制重新运行选项

这些复选框位于状态栏的右侧，与"实时模式"复选框并列显示。

### 复选框功能

#### -fsdb 复选框
- **用途**：启用波形输出选项，用于调试失败的仿真
- **工具提示**：启用波形输出选项，用于调试失败的仿真
- **效果**：勾选后会在命令中添加`-fsdb`参数

#### -R 复选框
- **用途**：启用重新运行选项，跳过编译直接运行仿真
- **工具提示**：启用重新运行选项，跳过编译直接运行仿真
- **效果**：勾选后会在命令中添加`-R`参数

## 使用场景

### 场景1：初次仿真失败，需要添加波形输出

1. **问题**：仿真失败，需要查看波形进行调试
2. **操作步骤**：
   - 在失败的用例标签页中，勾选`-fsdb`复选框
   - 观察命令预览区域，确认`-fsdb`选项已添加
   - 点击"重新执行"按钮
3. **结果**：仿真将重新运行并生成波形文件

### 场景2：需要跳过编译直接运行仿真

1. **问题**：编译已完成，只需重新运行仿真部分
2. **操作步骤**：
   - 勾选`-R`复选框
   - 观察命令预览，确认`-R`选项已添加
   - 点击"重新执行"按钮
3. **结果**：将跳过编译步骤，直接运行仿真

### 场景3：同时需要波形输出和跳过编译

1. **问题**：需要快速重新仿真并生成波形
2. **操作步骤**：
   - 同时勾选`-fsdb`和`-R`复选框
   - 确认命令预览中包含两个选项
   - 点击"重新执行"按钮
3. **结果**：跳过编译，直接运行仿真并生成波形

## 操作指南

### 基本操作

1. **查看当前选项状态**
   - 复选框的勾选状态反映当前命令中是否包含对应选项
   - 初始状态根据执行命令自动设置

2. **修改选项**
   - 勾选复选框：添加对应选项到命令中
   - 取消勾选：从命令中移除对应选项
   - 修改后命令预览会立即更新

3. **重新执行**
   - 修改选项后，点击"重新执行"按钮
   - 系统将使用更新后的命令重新运行仿真

### 高级功能

#### 支持BATCH RUN模式
- 对于BATCH RUN模式的命令（包含分号分隔的多个命令）
- 选项修改会应用到所有子命令中
- 复选框状态反映任一子命令中是否包含选项

#### 实时命令预览
- 命令预览区域会实时显示当前的执行命令
- 选项修改后立即更新预览
- 便于确认命令正确性

## 注意事项

### 选项冲突
- `-fsdb`和`-R`选项可以同时使用，没有冲突
- 系统会智能处理选项的添加和移除，避免重复

### 命令完整性
- 选项修改不会影响命令的其他参数
- 原有的base、block、case等参数保持不变
- 只修改指定的仿真选项

### 执行状态
- 只有在非运行状态时才能修改选项
- 正在运行的仿真需要先停止才能修改选项
- 修改后的选项在下次执行时生效

## 故障排除

### 复选框状态不正确
- **问题**：复选框状态与实际命令不符
- **解决**：重新打开标签页或重启应用程序

### 选项修改无效
- **问题**：勾选复选框后命令没有变化
- **检查**：确认当前不在执行状态
- **解决**：停止当前执行后重试

### 命令预览异常
- **问题**：命令预览显示异常
- **解决**：检查原始命令格式是否正确

## 技术说明

### 支持的命令格式
- 标准runsim命令
- 包含各种参数的复杂命令
- BATCH RUN模式的分号分隔命令
- 已包含-fsdb/-R选项的命令

### 选项处理逻辑
- 智能检测现有选项
- 避免重复添加相同选项
- 保持命令结构完整性
- 支持选项的任意组合

## 最佳实践

### 调试工作流程
1. **初次运行**：使用基本参数执行仿真
2. **失败分析**：如果失败，勾选`-fsdb`添加波形输出
3. **快速重试**：后续调试时勾选`-R`跳过编译
4. **选项管理**：根据需要灵活组合选项

### 效率提升
- 利用选项控制减少页面切换
- 使用`-R`选项节省编译时间
- 合理使用`-fsdb`选项避免不必要的波形生成

### 团队协作
- 选项状态在历史记录中保持一致
- 便于团队成员理解调试配置
- 标准化的调试流程

## 总结

执行日志面板的仿真选项控制功能显著简化了仿真调试流程，让您能够：

- ✅ 在执行页面直接修改仿真选项
- ✅ 无需切换页面即可重新配置
- ✅ 实时预览命令变化
- ✅ 提高调试效率
- ✅ 减少操作步骤

这个功能特别适合需要频繁调试的场景，能够大大提升您的工作效率。
