"""
Git文件版本比对插件主文件

提供Git仓库中文件的版本比对功能的插件入口。
"""

import os
import sys
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
git_diff_dir = os.path.join(plugin_dir, 'git_diff')
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)
if git_diff_dir not in sys.path:
    sys.path.insert(0, git_diff_dir)

from plugins.base import PluginBase


class GitDiffPlugin(PluginBase):
    """Git文件版本比对插件主类"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.main_window = None
        self.diff_window = None
        self.menu_action = None
        
    @property
    def name(self) -> str:
        """插件名称"""
        return "Git文件版本比对工具"
    
    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """插件描述"""
        return "对Git仓库中的文件进行当前版本与历史版本的比对"
    
    def initialize(self, main_window):
        """初始化插件
        
        Args:
            main_window: 主窗口实例
        """
        try:
            self.main_window = main_window
            
            # 添加菜单项
            self._add_menu_item()
            
            print(f"插件 {self.name} 初始化成功")
            
        except Exception as e:
            print(f"插件 {self.name} 初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _add_menu_item(self):
        """添加菜单项"""
        try:
            if not self.main_window:
                return
            
            # 获取工具菜单
            tools_menu = getattr(self.main_window, 'tools_menu', None)
            if not tools_menu:
                print("未找到工具菜单")
                return
            
            # 创建菜单动作
            self.menu_action = QAction(self.name, self.main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self._on_menu_triggered)
            
            # 添加到工具菜单
            tools_menu.addAction(self.menu_action)
            
        except Exception as e:
            print(f"添加菜单项失败: {str(e)}")
    
    def _on_menu_triggered(self):
        """菜单项被点击时的处理函数"""
        try:
            # 检查依赖
            if not self._check_dependencies():
                return
            
            # 延迟导入主窗口类
            from git_diff.views.main_window import GitDiffMainWindow
            
            # 如果窗口尚未创建，创建新窗口
            if self.diff_window is None:
                self.diff_window = GitDiffMainWindow(self.main_window)
                # 连接窗口关闭信号
                self.diff_window.finished.connect(self._on_window_closed)
            
            # 显示窗口
            self.diff_window.show()
            self.diff_window.raise_()
            self.diff_window.activateWindow()
            
        except Exception as e:
            print(f"打开Git文件版本比对工具失败: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开Git文件版本比对工具: {str(e)}\n\n请确保已安装必要的依赖包。"
            )
    
    def _check_dependencies(self):
        """检查依赖包"""
        try:
            import git
            return True
        except ImportError:
            QMessageBox.warning(
                self.main_window,
                "依赖缺失",
                "Git文件版本比对工具需要GitPython库支持。\n\n"
                "请运行以下命令安装依赖：\n"
                "pip install GitPython\n\n"
                "或运行插件目录下的install_git_diff_dependencies.py脚本。"
            )
            return False
    
    def _on_window_closed(self):
        """窗口关闭时的处理函数"""
        self.diff_window = None
    
    def cleanup(self):
        """清理插件资源"""
        try:
            if self.diff_window:
                self.diff_window.close()
                self.diff_window = None
            
            print(f"插件 {self.name} 清理完成")
            
        except Exception as e:
            print(f"插件 {self.name} 清理失败: {str(e)}")
