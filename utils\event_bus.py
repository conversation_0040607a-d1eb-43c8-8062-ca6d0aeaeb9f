"""
事件总线模块，用于优化模块间的通信（优化版本）
"""
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot
import weakref
import time
import threading

class EventBus(QObject):
    """
    事件总线类，用于模块间的事件传递（优化版本）

    使用单例模式确保全局只有一个事件总线实例
    增加了信号去重和频率控制机制
    """

    # 定义信号
    case_selected = pyqtSignal(str)  # 用例选择信号
    command_executed = pyqtSignal(str, str)  # 命令执行信号，参数：命令字符串，用例名称
    batch_run_executed = pyqtSignal(list, str)  # BATCH RUN 执行信号，参数：命令列表，用例名称
    config_changed = pyqtSignal(dict)  # 配置变更信号
    history_updated = pyqtSignal(list)  # 历史记录更新信号

    # 单例实例，使用弱引用避免内存泄漏
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """创建单例实例"""
        if cls._instance is None or cls._instance() is None:
            instance = super(EventBus, cls).__new__(cls)
            instance._initialized = False
            cls._instance = weakref.ref(instance)
            return instance
        return cls._instance()

    def __init__(self):
        """初始化事件总线"""
        if hasattr(self, '_initialized') and self._initialized:
            return
        super().__init__()
        self._initialized = True

        # 信号去重和频率控制
        self._last_emit_time = {}
        self._last_emit_data = {}
        self._min_emit_interval = 0.1  # 最小发射间隔（秒）
        self._emit_lock = threading.Lock()

    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None or cls._instance() is None:
            with cls._lock:
                if cls._instance is None or cls._instance() is None:
                    return cls()
        return cls._instance()

    def _should_emit(self, signal_key, data=None):
        """
        检查是否应该发射信号（去重和频率控制）

        Args:
            signal_key (str): 信号键
            data: 信号数据

        Returns:
            bool: 是否应该发射信号
        """
        current_time = time.time()

        with self._emit_lock:
            # 检查时间间隔
            last_time = self._last_emit_time.get(signal_key, 0)
            if current_time - last_time < self._min_emit_interval:
                return False

            # 检查数据是否相同（去重）
            if data is not None:
                last_data = self._last_emit_data.get(signal_key)
                if last_data == data:
                    return False
                self._last_emit_data[signal_key] = data

            # 更新发射时间
            self._last_emit_time[signal_key] = current_time
            return True

    def emit_case_selected(self, case_name):
        """
        发射用例选择信号（带去重）

        Args:
            case_name (str): 用例名称
        """
        if self._should_emit('case_selected', case_name):
            self.case_selected.emit(case_name)

    def emit_command_executed(self, command, case_name):
        """
        发射命令执行信号（不去重，因为可能需要重复执行）

        Args:
            command (str): 命令字符串
            case_name (str): 用例名称
        """
        # 命令执行信号不去重，但仍然有频率限制
        signal_key = f'command_executed_{case_name}'
        if self._should_emit(signal_key):
            self.command_executed.emit(command, case_name)

    def emit_batch_run_executed(self, command_list, case_name):
        """
        发射 BATCH RUN 执行信号

        Args:
            command_list (list): 命令列表
            case_name (str): 用例名称
        """
        self.batch_run_executed.emit(command_list, case_name)

    def emit_config_changed(self, config):
        """
        发射配置变更信号（带去重）

        Args:
            config (dict): 配置数据
        """
        # 对于包含BASE或BLOCK的配置变更，跳过去重检查，确保这些重要配置能够及时更新
        if 'base' in config or 'block' in config:
            self.config_changed.emit(config)
            return

        # 简化配置对比，只检查关键字段
        config_hash = hash(str(sorted(config.items())))
        if self._should_emit('config_changed', config_hash):
            self.config_changed.emit(config)

    def emit_history_updated(self, history):
        """
        发射历史记录更新信号（带去重）

        Args:
            history (list): 历史记录列表
        """
        # 只检查历史记录长度，避免深度比较
        history_length = len(history) if history else 0
        if self._should_emit('history_updated', history_length):
            self.history_updated.emit(history)

    def clear_emit_cache(self):
        """清空发射缓存"""
        with self._emit_lock:
            self._last_emit_time.clear()
            self._last_emit_data.clear()

    def set_min_emit_interval(self, interval):
        """
        设置最小发射间隔

        Args:
            interval (float): 最小发射间隔（秒）
        """
        if interval > 0:
            self._min_emit_interval = interval
