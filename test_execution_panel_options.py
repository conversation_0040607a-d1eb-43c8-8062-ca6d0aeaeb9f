#!/usr/bin/env python3
"""
测试ExecutionPanel中的仿真选项控制功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from views.execution_panel import ExecutionPanel

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ExecutionPanel 仿真选项控制测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 创建ExecutionPanel
        self.execution_panel = ExecutionPanel()
        layout.addWidget(self.execution_panel)
        
        # 添加测试用例
        self.add_test_cases()
    
    def add_test_cases(self):
        """添加测试用例"""
        test_cases = [
            {
                "tab_name": "test_case1",
                "command": "runsim -base test_base -block test_block -case test_case1 -rundir ./run1"
            },
            {
                "tab_name": "test_case2_with_fsdb",
                "command": "runsim -base test_base -block test_block -case test_case2 -fsdb -rundir ./run2"
            },
            {
                "tab_name": "test_case3_with_R",
                "command": "runsim -base test_base -block test_block -case test_case3 -R -rundir ./run3"
            },
            {
                "tab_name": "test_case4_both_options",
                "command": "runsim -base test_base -block test_block -case test_case4 -fsdb -R -rundir ./run4"
            },
            {
                "tab_name": "batch_run_case",
                "command": "runsim -base test_base -block test_block -case base_case -rundir ./base ; runsim -base test_base -block test_block -case target_case -R -rundir ./target"
            }
        ]
        
        for case in test_cases:
            success = self.execution_panel.add_log_tab(case["tab_name"], case["command"])
            if success:
                print(f"成功添加测试用例: {case['tab_name']}")
            else:
                print(f"添加测试用例失败: {case['tab_name']}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
