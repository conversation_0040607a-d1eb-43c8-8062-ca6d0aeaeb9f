"""
编码检测器 - 专门处理文本编码检测和转换
从LogPanel中提取的编码处理逻辑，提供高效的编码检测和转换功能
"""
import sys
import time
from collections import OrderedDict

# chardet是可选依赖
try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False


class EncodingDetector:
    """
    编码检测器，负责自动检测和转换文本编码
    
    职责：
    1. 自动检测字节数据的编码格式
    2. 高效的编码转换
    3. 编码缓存机制
    4. 平台特定的编码优化
    """
    
    def __init__(self, cache_size=1000, enable_chardet=True):
        """
        初始化编码检测器

        Args:
            cache_size (int): 编码缓存大小
            enable_chardet (bool): 是否启用chardet库进行高级检测
        """
        self.cache_size = cache_size
        self.enable_chardet = enable_chardet and CHARDET_AVAILABLE
        
        # 编码缓存（LRU）
        self._encoding_cache = OrderedDict()
        
        # 平台特定的编码优先级
        if sys.platform == 'win32':
            self._primary_encodings = ['utf-8', 'gbk', 'cp936', 'latin1']
        else:
            self._primary_encodings = ['utf-8', 'latin1', 'ascii']
        
        self._fallback_encoding = 'utf-8'
        
        # 统计信息
        self._stats = {
            'total_detections': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'chardet_calls': 0,
            'encoding_distribution': {},
            'avg_detection_time': 0.0
        }
        
        # 性能优化设置
        self._min_bytes_for_detection = 16  # 最少字节数才进行检测
        self._max_bytes_for_detection = 1024  # 最多使用的字节数进行检测
        
    def decode(self, raw_bytes, hint_encoding=None):
        """
        解码字节数据为文本
        
        Args:
            raw_bytes (bytes): 原始字节数据
            hint_encoding (str): 编码提示
            
        Returns:
            str: 解码后的文本
        """
        if not raw_bytes:
            return ""
        
        start_time = time.time()
        self._stats['total_detections'] += 1
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(raw_bytes)
            
            # 尝试使用缓存
            encoding = self._get_cached_encoding(cache_key)
            if encoding:
                try:
                    text = raw_bytes.decode(encoding, errors='strict')
                    self._update_detection_time(start_time)
                    return text
                except UnicodeDecodeError:
                    # 缓存的编码失效，移除缓存
                    self._remove_from_cache(cache_key)
            
            # 检测编码
            detected_encoding = self._detect_encoding(raw_bytes, hint_encoding)
            
            # 解码文本
            text = self._decode_with_encoding(raw_bytes, detected_encoding)
            
            # 缓存成功的编码
            if detected_encoding and text:
                self._cache_encoding(cache_key, detected_encoding)
            
            self._update_detection_time(start_time)
            return text
            
        except Exception as e:
            print(f"编码检测出错: {str(e)}")
            # 使用替换模式的UTF-8作为最后手段
            self._update_detection_time(start_time)
            return raw_bytes.decode(self._fallback_encoding, errors='replace')
    
    def _detect_encoding(self, raw_bytes, hint_encoding=None):
        """
        检测字节数据的编码
        
        Args:
            raw_bytes (bytes): 原始字节数据
            hint_encoding (str): 编码提示
            
        Returns:
            str: 检测到的编码名称
        """
        # 如果数据太少，使用默认编码
        if len(raw_bytes) < self._min_bytes_for_detection:
            return hint_encoding or self._primary_encodings[0]
        
        # 准备检测用的数据（限制大小以提高性能）
        detection_bytes = raw_bytes[:self._max_bytes_for_detection]
        
        # 构建编码尝试列表
        encodings_to_try = []
        
        # 1. 优先尝试提示编码
        if hint_encoding:
            encodings_to_try.append(hint_encoding)
        
        # 2. 尝试平台特定的主要编码
        encodings_to_try.extend(self._primary_encodings)
        
        # 3. 尝试快速检测
        quick_detected = self._quick_encoding_detection(detection_bytes)
        if quick_detected and quick_detected not in encodings_to_try:
            encodings_to_try.insert(1, quick_detected)
        
        # 4. 尝试各种编码
        for encoding in encodings_to_try:
            if self._test_encoding(detection_bytes, encoding):
                return encoding
        
        # 5. 使用chardet进行高级检测（如果启用且可用）
        if self.enable_chardet and CHARDET_AVAILABLE and len(detection_bytes) >= 32:
            chardet_result = self._chardet_detection(detection_bytes)
            if chardet_result:
                return chardet_result
        
        # 6. 返回默认编码
        return self._fallback_encoding
    
    def _quick_encoding_detection(self, data):
        """
        快速编码检测，基于字节模式
        
        Args:
            data (bytes): 字节数据
            
        Returns:
            str: 可能的编码名称
        """
        # UTF-8 BOM检测
        if data.startswith(b'\xef\xbb\xbf'):
            return 'utf-8-sig'
        
        # UTF-16 BOM检测
        if data.startswith(b'\xff\xfe') or data.startswith(b'\xfe\xff'):
            return 'utf-16'
        
        # 简单的ASCII检测
        try:
            data.decode('ascii')
            return 'ascii'
        except UnicodeDecodeError:
            pass
        
        # 中文编码特征检测（Windows平台）
        if sys.platform == 'win32':
            # 检测GBK特征字节
            gbk_indicators = [b'\xa1', b'\xa3', b'\xb0', b'\xc4', b'\xd6']
            if any(indicator in data for indicator in gbk_indicators):
                return 'gbk'
        
        return None
    
    def _test_encoding(self, data, encoding):
        """
        测试编码是否有效
        
        Args:
            data (bytes): 字节数据
            encoding (str): 编码名称
            
        Returns:
            bool: 编码是否有效
        """
        try:
            data.decode(encoding, errors='strict')
            return True
        except (UnicodeDecodeError, LookupError):
            return False
    
    def _chardet_detection(self, data):
        """
        使用chardet进行编码检测

        Args:
            data (bytes): 字节数据

        Returns:
            str: 检测到的编码名称
        """
        if not CHARDET_AVAILABLE:
            return None

        try:
            self._stats['chardet_calls'] += 1
            result = chardet.detect(data)
            
            if result and result['confidence'] > 0.7:  # 置信度阈值
                encoding = result['encoding']
                
                # 标准化编码名称
                if encoding:
                    encoding = encoding.lower()
                    if encoding in ['gb2312', 'gb18030']:
                        encoding = 'gbk'  # 统一使用GBK
                    elif encoding.startswith('utf-8'):
                        encoding = 'utf-8'
                
                return encoding
                
        except Exception as e:
            print(f"chardet检测出错: {str(e)}")
        
        return None
    
    def _decode_with_encoding(self, raw_bytes, encoding):
        """
        使用指定编码解码数据
        
        Args:
            raw_bytes (bytes): 原始字节数据
            encoding (str): 编码名称
            
        Returns:
            str: 解码后的文本
        """
        try:
            text = raw_bytes.decode(encoding, errors='strict')
            
            # 更新编码分布统计
            if encoding not in self._stats['encoding_distribution']:
                self._stats['encoding_distribution'][encoding] = 0
            self._stats['encoding_distribution'][encoding] += 1
            
            return text
            
        except UnicodeDecodeError:
            # 尝试使用替换模式
            try:
                return raw_bytes.decode(encoding, errors='replace')
            except LookupError:
                # 编码不存在，使用默认编码
                return raw_bytes.decode(self._fallback_encoding, errors='replace')
    
    def _generate_cache_key(self, raw_bytes):
        """
        生成缓存键
        
        Args:
            raw_bytes (bytes): 原始字节数据
            
        Returns:
            int: 缓存键
        """
        # 使用前64字节的哈希作为缓存键
        sample_size = min(64, len(raw_bytes))
        return hash(raw_bytes[:sample_size])
    
    def _get_cached_encoding(self, cache_key):
        """
        获取缓存的编码
        
        Args:
            cache_key: 缓存键
            
        Returns:
            str: 缓存的编码名称
        """
        if cache_key in self._encoding_cache:
            # 移动到末尾（LRU）
            encoding = self._encoding_cache[cache_key]
            del self._encoding_cache[cache_key]
            self._encoding_cache[cache_key] = encoding
            
            self._stats['cache_hits'] += 1
            return encoding
        
        self._stats['cache_misses'] += 1
        return None
    
    def _cache_encoding(self, cache_key, encoding):
        """
        缓存编码
        
        Args:
            cache_key: 缓存键
            encoding (str): 编码名称
        """
        # 如果缓存已满，移除最旧的项
        if len(self._encoding_cache) >= self.cache_size:
            self._encoding_cache.popitem(last=False)
        
        self._encoding_cache[cache_key] = encoding
    
    def _remove_from_cache(self, cache_key):
        """
        从缓存中移除项
        
        Args:
            cache_key: 缓存键
        """
        self._encoding_cache.pop(cache_key, None)
    
    def _update_detection_time(self, start_time):
        """
        更新检测时间统计
        
        Args:
            start_time (float): 开始时间
        """
        detection_time = (time.time() - start_time) * 1000  # 转换为毫秒
        
        # 计算移动平均
        if self._stats['avg_detection_time'] == 0:
            self._stats['avg_detection_time'] = detection_time
        else:
            self._stats['avg_detection_time'] = (
                self._stats['avg_detection_time'] * 0.9 + detection_time * 0.1
            )
    
    def get_stats(self):
        """
        获取统计信息
        
        Returns:
            dict: 统计信息
        """
        total_attempts = self._stats['cache_hits'] + self._stats['cache_misses']
        cache_hit_rate = (self._stats['cache_hits'] / total_attempts * 100) if total_attempts > 0 else 0
        
        return {
            'cache_size': len(self._encoding_cache),
            'cache_hit_rate': cache_hit_rate,
            'most_common_encoding': self._get_most_common_encoding(),
            **self._stats
        }
    
    def _get_most_common_encoding(self):
        """获取最常用的编码"""
        if not self._stats['encoding_distribution']:
            return None
        
        return max(self._stats['encoding_distribution'].items(), key=lambda x: x[1])[0]
    
    def clear_cache(self):
        """清空编码缓存"""
        self._encoding_cache.clear()
    
    def set_primary_encodings(self, encodings):
        """
        设置主要编码列表
        
        Args:
            encodings (list): 编码名称列表
        """
        self._primary_encodings = encodings[:]
    
    def cleanup(self):
        """清理资源"""
        self.clear_cache()
        self._stats = {
            'total_detections': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'chardet_calls': 0,
            'encoding_distribution': {},
            'avg_detection_time': 0.0
        }
