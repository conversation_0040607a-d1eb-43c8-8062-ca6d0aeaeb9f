"""
渲染缓存管理器 - 优化字体度量计算和渲染结果缓存
提供高效的渲染缓存机制，减少重复计算
"""
import time
import hashlib
import threading
from typing import Dict, Any, Optional, Tuple, List
from collections import OrderedDict
from PyQt5.QtGui import QFont, QFontMetrics, QColor
from PyQt5.QtCore import QSize
from utils.string_pool import intern_string


class FontMetricsCache:
    """
    字体度量缓存 - 缓存字体度量计算结果
    """
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self._cache: OrderedDict[str, QFontMetrics] = OrderedDict()
        self._char_width_cache: OrderedDict[str, Dict[str, int]] = OrderedDict()
        self._lock = threading.RLock()
        
        # 性能统计
        self._stats = {
            'font_cache_hits': 0,
            'font_cache_misses': 0,
            'char_width_hits': 0,
            'char_width_misses': 0,
            'total_calculations': 0
        }
    
    def get_font_metrics(self, font: QFont) -> QFontMetrics:
        """获取字体度量（带缓存）"""
        font_key = self._generate_font_key(font)
        
        with self._lock:
            if font_key in self._cache:
                # 移动到末尾（LRU）
                self._cache.move_to_end(font_key)
                self._stats['font_cache_hits'] += 1
                return self._cache[font_key]
            
            # 缓存未命中，创建新的字体度量
            self._stats['font_cache_misses'] += 1
            font_metrics = QFontMetrics(font)
            
            # 如果缓存已满，移除最旧的项
            if len(self._cache) >= self.max_size:
                self._cache.popitem(last=False)
            
            self._cache[font_key] = font_metrics
            return font_metrics
    
    def get_char_width(self, font: QFont, char: str) -> int:
        """获取字符宽度（带缓存）"""
        font_key = self._generate_font_key(font)
        
        with self._lock:
            # 检查字符宽度缓存
            if font_key in self._char_width_cache:
                char_cache = self._char_width_cache[font_key]
                if char in char_cache:
                    self._stats['char_width_hits'] += 1
                    return char_cache[char]
            
            # 缓存未命中，计算字符宽度
            self._stats['char_width_misses'] += 1
            self._stats['total_calculations'] += 1
            
            font_metrics = self.get_font_metrics(font)
            char_width = font_metrics.width(char)
            
            # 缓存字符宽度
            if font_key not in self._char_width_cache:
                self._char_width_cache[font_key] = {}
            
            self._char_width_cache[font_key][char] = char_width
            
            # 限制字符缓存大小
            if len(self._char_width_cache[font_key]) > 1000:
                # 移除一半的缓存项
                items = list(self._char_width_cache[font_key].items())
                self._char_width_cache[font_key] = dict(items[500:])
            
            return char_width
    
    def get_text_width(self, font: QFont, text: str) -> int:
        """获取文本宽度（优化版本）"""
        if not text:
            return 0
        
        # 对于短文本，直接使用字体度量
        if len(text) <= 10:
            font_metrics = self.get_font_metrics(font)
            return font_metrics.width(text)
        
        # 对于长文本，使用字符宽度累加（更快）
        total_width = 0
        for char in text:
            total_width += self.get_char_width(font, char)
        
        return total_width
    
    def _generate_font_key(self, font: QFont) -> str:
        """生成字体缓存键"""
        # 使用字符串池优化键的内存使用
        key_parts = [
            font.family(),
            str(font.pointSize()),
            str(font.weight()),
            str(font.italic()),
            str(font.bold())
        ]
        return intern_string('_'.join(key_parts))
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._char_width_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            font_total = self._stats['font_cache_hits'] + self._stats['font_cache_misses']
            char_total = self._stats['char_width_hits'] + self._stats['char_width_misses']
            
            return {
                'font_cache_size': len(self._cache),
                'char_cache_count': len(self._char_width_cache),
                'font_hit_rate': (self._stats['font_cache_hits'] / font_total * 100) if font_total > 0 else 0,
                'char_hit_rate': (self._stats['char_width_hits'] / char_total * 100) if char_total > 0 else 0,
                **self._stats
            }


class RenderResultCache:
    """
    渲染结果缓存 - 缓存完整的渲染结果
    """
    
    def __init__(self, max_size: int = 200):
        self.max_size = max_size
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._lock = threading.RLock()
        
        # 性能统计
        self._stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'cache_evictions': 0,
            'total_render_time_saved': 0.0
        }
    
    def generate_render_key(self, text: str, font_key: str, color: QColor, 
                          width: int, height: int, has_selection: bool = False) -> str:
        """生成渲染缓存键"""
        # 创建唯一的渲染键
        key_data = f"{hash(text)}_{font_key}_{color.name()}_{width}_{height}_{has_selection}"
        
        # 使用MD5哈希缩短键长度
        key_hash = hashlib.md5(key_data.encode()).hexdigest()[:16]
        return intern_string(key_hash)
    
    def get_cached_render(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的渲染结果"""
        with self._lock:
            if cache_key in self._cache:
                # 移动到末尾（LRU）
                self._cache.move_to_end(cache_key)
                self._stats['cache_hits'] += 1
                
                # 记录节省的时间（估算）
                cached_data = self._cache[cache_key]
                self._stats['total_render_time_saved'] += cached_data.get('render_time', 0.001)
                
                return cached_data
            
            self._stats['cache_misses'] += 1
            return None
    
    def cache_render_result(self, cache_key: str, render_data: Dict[str, Any], 
                          render_time: float):
        """缓存渲染结果"""
        with self._lock:
            # 如果缓存已满，移除最旧的项
            if len(self._cache) >= self.max_size:
                self._cache.popitem(last=False)
                self._stats['cache_evictions'] += 1
            
            # 添加渲染时间信息
            render_data['render_time'] = render_time
            render_data['cached_at'] = time.time()
            
            self._cache[cache_key] = render_data
    
    def invalidate_cache(self, pattern: str = None):
        """使缓存失效"""
        with self._lock:
            if pattern is None:
                # 清空所有缓存
                self._cache.clear()
            else:
                # 清空匹配模式的缓存
                keys_to_remove = [key for key in self._cache.keys() if pattern in key]
                for key in keys_to_remove:
                    del self._cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            total_requests = self._stats['cache_hits'] + self._stats['cache_misses']
            hit_rate = (self._stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'cache_size': len(self._cache),
                'hit_rate': hit_rate,
                'time_saved_ms': self._stats['total_render_time_saved'] * 1000,
                **self._stats
            }


class RenderCacheManager:
    """
    渲染缓存管理器 - 统一管理所有渲染缓存
    """
    
    def __init__(self):
        self.font_cache = FontMetricsCache(max_size=50)
        self.render_cache = RenderResultCache(max_size=300)
        
        # 全局性能统计
        self._global_stats = {
            'total_render_requests': 0,
            'total_cache_hits': 0,
            'total_time_saved': 0.0
        }
    
    def get_optimized_font_metrics(self, font: QFont) -> QFontMetrics:
        """获取优化的字体度量"""
        return self.font_cache.get_font_metrics(font)
    
    def get_optimized_char_width(self, font: QFont, char: str) -> int:
        """获取优化的字符宽度"""
        return self.font_cache.get_char_width(font, char)
    
    def get_optimized_text_width(self, font: QFont, text: str) -> int:
        """获取优化的文本宽度"""
        return self.font_cache.get_text_width(font, text)
    
    def get_cached_render_result(self, text: str, font: QFont, color: QColor,
                               width: int, height: int, has_selection: bool = False) -> Optional[Dict[str, Any]]:
        """获取缓存的渲染结果"""
        self._global_stats['total_render_requests'] += 1
        
        font_key = self.font_cache._generate_font_key(font)
        cache_key = self.render_cache.generate_render_key(
            text, font_key, color, width, height, has_selection
        )
        
        cached_result = self.render_cache.get_cached_render(cache_key)
        if cached_result:
            self._global_stats['total_cache_hits'] += 1
        
        return cached_result
    
    def cache_render_result(self, text: str, font: QFont, color: QColor,
                          width: int, height: int, render_data: Dict[str, Any],
                          render_time: float, has_selection: bool = False):
        """缓存渲染结果"""
        font_key = self.font_cache._generate_font_key(font)
        cache_key = self.render_cache.generate_render_key(
            text, font_key, color, width, height, has_selection
        )
        
        self.render_cache.cache_render_result(cache_key, render_data, render_time)
    
    def clear_all_caches(self):
        """清空所有缓存"""
        self.font_cache.clear()
        self.render_cache.invalidate_cache()
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        font_stats = self.font_cache.get_stats()
        render_stats = self.render_cache.get_stats()
        
        overall_hit_rate = (
            self._global_stats['total_cache_hits'] / 
            max(1, self._global_stats['total_render_requests']) * 100
        )
        
        return {
            'overall': {
                'hit_rate': overall_hit_rate,
                **self._global_stats
            },
            'font_cache': font_stats,
            'render_cache': render_stats
        }


# 全局渲染缓存管理器实例
_global_render_cache_manager = None
_cache_lock = threading.Lock()


def get_global_render_cache_manager() -> RenderCacheManager:
    """获取全局渲染缓存管理器实例（单例模式）"""
    global _global_render_cache_manager
    
    if _global_render_cache_manager is None:
        with _cache_lock:
            if _global_render_cache_manager is None:
                _global_render_cache_manager = RenderCacheManager()
    
    return _global_render_cache_manager


def get_render_cache_stats() -> Dict[str, Any]:
    """获取全局渲染缓存统计信息"""
    return get_global_render_cache_manager().get_comprehensive_stats()
