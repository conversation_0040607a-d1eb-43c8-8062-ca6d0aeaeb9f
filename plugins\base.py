from abc import ABC, abstractmethod
import os
import ctypes
import sys
from PyQt5.QtWidgets import QDialog
from PyQt5.QtCore import Qt

def is_admin():
    """检查当前用户是否为管理员"""
    try:
        import getpass
        # 获取当前用户名
        current_user = getpass.getuser()
        # 判断用户名是否为管理员用户名
        return current_user == "jiadong.he2"
    except Exception:
        return False

class NonModalDialog(QDialog):
    """非模态对话框基类，用于插件窗口"""

    def __init__(self, parent=None, plugin_name=""):
        super().__init__(parent)
        self.plugin_name = plugin_name
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

    def showEvent(self, event):
        """显示事件，记录窗口位置和大小"""
        super().showEvent(event)
        # 将窗口添加到主窗口的活动插件窗口列表中
        if hasattr(self.parent(), 'active_plugin_windows'):
            if self not in self.parent().active_plugin_windows:
                self.parent().active_plugin_windows.append(self)

    def closeEvent(self, event):
        """关闭事件，从主窗口的活动插件窗口列表中移除"""
        super().closeEvent(event)
        # 从主窗口的活动插件窗口列表中移除
        if hasattr(self.parent(), 'active_plugin_windows'):
            if self in self.parent().active_plugin_windows:
                self.parent().active_plugin_windows.remove(self)

class PluginBase(ABC):
    """插件基类，所有插件必须继承此类"""

    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass

    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """插件描述"""
        pass

    @abstractmethod
    def initialize(self, main_window):
        """初始化插件"""
        pass

    @abstractmethod
    def cleanup(self):
        """清理插件资源"""
        pass

    @property
    def admin_controlled(self) -> bool:
        """插件是否由管理员控制（默认为False）"""
        return getattr(self, '_admin_controlled', False)

    @admin_controlled.setter
    def admin_controlled(self, value: bool):
        self._admin_controlled = value

    @property
    def default_enabled(self) -> bool:
        """插件默认是否启用（默认为True）"""
        return getattr(self, '_default_enabled', True)

    @default_enabled.setter
    def default_enabled(self, value: bool):
        self._default_enabled = value

    @property
    def enabled(self) -> bool:
        """插件是否启用"""
        # 如果是管理员控制的插件，且不是管理员，则使用默认值
        if self.admin_controlled and not is_admin():
            return self.default_enabled
        return getattr(self, '_enabled', self.default_enabled)

    @enabled.setter
    def enabled(self, value: bool):
        # 如果是管理员控制的插件，且不是管理员，则不允许修改
        if self.admin_controlled and not is_admin():
            return
        self._enabled = value

    def create_dialog(self, parent=None, title=None):
        """创建非模态对话框

        Args:
            parent: 父窗口，默认为主窗口
            title: 对话框标题，默认为插件名称

        Returns:
            NonModalDialog: 非模态对话框实例
        """
        if parent is None and hasattr(self, 'main_window'):
            parent = self.main_window

        if title is None:
            title = self.name

        dialog = NonModalDialog(parent, self.name)
        dialog.setWindowTitle(title)

        # 确保主窗口有活动插件窗口列表
        if hasattr(parent, 'active_plugin_windows'):
            if not hasattr(parent, 'active_plugin_windows'):
                parent.active_plugin_windows = []

        return dialog