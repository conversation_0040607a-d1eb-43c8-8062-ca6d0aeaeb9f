"""
进程管理器 - 专门负责进程的启动、停止和监控
从LogPanel中提取的进程管理逻辑
"""
import sys
import os
import time
import signal
import subprocess
from PyQt5.QtCore import QObject, QProcess, QProcessEnvironment, QTimer, pyqtSignal, Qt
from .signal_safety import safe_disconnect_multiple


class ProcessManager(QObject):
    """
    进程管理器，负责进程的生命周期管理

    职责：
    1. 进程的启动和停止
    2. 进程状态监控
    3. 进程输出处理
    4. 进程环境配置
    5. 进程的暂停和继续控制
    """

    # 信号定义
    output_ready = pyqtSignal(bytes)  # 有新输出可用
    process_finished = pyqtSignal(int)  # 进程结束，参数为退出码
    process_error = pyqtSignal(str)  # 进程错误
    process_started = pyqtSignal()  # 进程启动
    process_stopped = pyqtSignal()  # 进程停止
    process_paused = pyqtSignal()  # 进程暂停
    process_resumed = pyqtSignal()  # 进程继续
    
    def __init__(self, command, working_directory=None, parent=None):
        """
        初始化进程管理器
        
        Args:
            command (str): 要执行的命令
            working_directory (str): 工作目录
            parent (QObject): 父对象
        """
        super().__init__(parent)
        
        self.command = command
        self.working_directory = working_directory or os.getcwd()
        self.process = None
        
        # 输出检查定时器
        self.output_check_timer = None
        self.backup_check_timer = None
        
        # 状态标志
        self._is_running = False
        self._is_paused = False
        self._start_time = None
        self._cleaned_up = False
        
    def start(self):
        """启动进程"""
        if self.is_running():
            return False
        
        try:
            # 创建进程对象
            self.process = QProcess(self)
            
            # 设置进程环境变量
            self._setup_process_environment()
            
            # 设置工作目录
            self.process.setWorkingDirectory(self.working_directory)
            
            # 合并标准输出和标准错误
            self.process.setProcessChannelMode(QProcess.MergedChannels)
            
            # 连接信号
            self._connect_process_signals()
            
            # 启动输出检查定时器
            self._start_output_monitoring()
            
            # 启动进程
            if sys.platform == 'win32':
                self.process.start('cmd.exe', ['/c', self.command])
            else:
                self.process.start('/bin/csh', ['-c', self.command])
            
            # 等待进程启动
            if self.process.waitForStarted(5000):
                self._is_running = True
                self._start_time = time.time()
                self.process_started.emit()
                return True
            else:
                self.process_error.emit("进程启动失败")
                return False
                
        except Exception as e:
            self.process_error.emit(f"启动进程时出错: {str(e)}")
            return False
    
    def stop(self):
        """停止进程"""
        if not self.is_running():
            return True
        
        try:
            # 停止输出监控
            self._stop_output_monitoring()
            
            # 终止进程
            if sys.platform == 'win32':
                self.process.kill()
            else:
                self.process.terminate()
                # 给进程一点时间优雅退出
                time.sleep(0.05)
                if self.process.state() == QProcess.Running:
                    self.process.kill()
            
            self._is_running = False
            self.process_stopped.emit()
            return True
            
        except Exception as e:
            self.process_error.emit(f"停止进程时出错: {str(e)}")
            return False
    
    def pause(self):
        """暂停进程执行"""
        if not self.is_running() or self._is_paused:
            return False

        try:
            # 先停止输出监控，防止暂停期间继续处理输出
            self._stop_output_monitoring()

            process_id = self.get_process_id()
            if not process_id:
                self.process_error.emit("无法获取进程ID")
                return False

            # 检测执行模式
            execution_mode = self._detect_remote_execution_mode()
            print(f"检测到执行模式: {execution_mode}")

            if execution_mode.startswith("remote"):
                # 远程执行模式：使用交互式暂停
                return self._pause_remote_simulation(process_id, execution_mode)
            elif sys.platform == 'win32':
                # Windows下暂停进程的实现
                return self._pause_windows_process(process_id)
            else:
                # Linux下本地暂停进程的实现
                return self._pause_linux_process(process_id)

        except Exception as e:
            self.process_error.emit(f"暂停进程时出错: {str(e)}")
            return False

    def resume(self):
        """继续进程执行"""
        if not self.is_running() or not self._is_paused:
            return False

        try:
            process_id = self.get_process_id()
            if not process_id:
                self.process_error.emit("无法获取进程ID")
                return False

            # 检测执行模式
            execution_mode = self._detect_remote_execution_mode()

            # 先尝试恢复进程
            success = False
            if execution_mode.startswith("remote"):
                # 远程执行模式：使用交互式继续
                success = self._resume_remote_simulation(process_id, execution_mode)
            elif sys.platform == 'win32':
                # Windows下继续进程的实现
                success = self._resume_windows_process(process_id)
            else:
                # Linux下继续进程的实现
                success = self._resume_linux_process(process_id)

            # 如果恢复成功，重新启动输出监控
            if success:
                self._start_output_monitoring()

            return success

        except Exception as e:
            self.process_error.emit(f"继续进程时出错: {str(e)}")
            return False

    def is_running(self):
        """检查进程是否在运行"""
        return (self._is_running and
                self.process is not None and
                self.process.state() == QProcess.Running)

    def is_paused(self):
        """检查进程是否已暂停"""
        return self._is_paused
    
    def get_runtime(self):
        """获取进程运行时间（秒）"""
        if self._start_time and self.is_running():
            return time.time() - self._start_time
        return 0
    
    def get_process_id(self):
        """获取进程ID"""
        if self.process and self.process.state() == QProcess.Running:
            pid = self.process.processId()
            # 验证PID的有效性
            if pid and pid > 0:
                return pid
            else:
                print(f"警告: 获取到无效的进程ID: {pid}")
                return None
        return None

    def update_command(self, new_command):
        """
        更新执行命令

        Args:
            new_command (str): 新的命令字符串

        Note:
            此方法只更新命令字符串，不会影响正在运行的进程。
            新命令将在下次调用start()时生效。
        """
        self.command = new_command

    def get_process_info(self):
        """获取详细的进程信息用于调试"""
        if not self.process:
            return "进程对象不存在"

        state_map = {
            QProcess.NotRunning: "未运行",
            QProcess.Starting: "启动中",
            QProcess.Running: "运行中"
        }

        pid = self.process.processId()
        state = self.process.state()
        state_name = state_map.get(state, f"未知状态({state})")

        info = f"进程状态: {state_name}, PID: {pid}"

        # 如果进程正在运行，尝试获取更多信息
        if state == QProcess.Running and pid and pid > 0:
            try:
                # 检查进程是否真的存在
                if sys.platform != 'win32':
                    os.kill(pid, 0)
                    info += " (进程存在)"
                else:
                    info += " (Windows系统)"

                # 尝试获取进程组ID（仅在非Windows系统）
                if sys.platform != 'win32':
                    try:
                        pgid = os.getpgid(pid)
                        info += f", 进程组ID: {pgid}"
                    except (OSError, AttributeError):
                        info += ", 无法获取进程组ID"
                else:
                    info += ", Windows不支持进程组"

            except (OSError, ProcessLookupError):
                info += " (进程不存在!)"

        return info

    def check_process_status_linux(self, process_id):
        """Linux下检查进程状态的详细方法"""
        if sys.platform == 'win32' or not process_id:
            return "不支持或无效的进程ID"

        try:
            # 使用ps命令获取进程状态
            import subprocess
            result = subprocess.run(['ps', '-o', 'pid,ppid,pgid,stat,pcpu,command', '-p', str(process_id)],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    header = lines[0]
                    process_line = lines[1]

                    # 解析进程状态
                    parts = process_line.split(None, 5)
                    if len(parts) >= 4:
                        pid, ppid, pgid, stat = parts[:4]

                        # 解释进程状态
                        status_meaning = {
                            'R': '运行中',
                            'S': '可中断睡眠',
                            'D': '不可中断睡眠',
                            'T': '已停止（暂停）',
                            'Z': '僵尸进程',
                            't': '被调试器停止',
                            'X': '已死亡'
                        }

                        main_status = stat[0] if stat else '?'
                        status_desc = status_meaning.get(main_status, f'未知状态({main_status})')

                        return f"进程状态: {stat} ({status_desc}), PID: {pid}, PPID: {ppid}, PGID: {pgid}"

            return f"无法获取进程 {process_id} 的状态信息"

        except Exception as e:
            return f"检查进程状态时出错: {str(e)}"
    
    def _setup_process_environment(self):
        """设置进程环境变量"""
        env = QProcessEnvironment.systemEnvironment()
        
        # 添加特殊环境变量，允许子进程创建自己的窗口
        env.insert("QT_PROCESS_SEPARATE_UI", "1")
        
        # 设置环境变量
        self.process.setProcessEnvironment(env)
    
    def _connect_process_signals(self):
        """连接进程信号"""
        try:
            # 使用队列连接避免信号丢失
            self.process.readyReadStandardOutput.connect(
                self._handle_output, Qt.QueuedConnection
            )
            self.process.readyReadStandardError.connect(
                self._handle_output, Qt.QueuedConnection
            )
        except TypeError:
            # 如果队列连接失败，使用直接连接
            self.process.readyReadStandardOutput.connect(self._handle_output)
            self.process.readyReadStandardError.connect(self._handle_output)
        
        # finished信号使用直接连接
        self.process.finished.connect(self._handle_finished)
        self.process.errorOccurred.connect(self._handle_error)
    
    def _handle_output(self):
        """处理进程输出"""
        if not self.process:
            return
        
        try:
            # 读取所有可用输出
            raw_output = self.process.readAllStandardOutput()
            if not raw_output:
                raw_output = self.process.readAllStandardError()
            
            if raw_output:
                # 发送原始字节数据，让上层处理编码
                self.output_ready.emit(bytes(raw_output))
                
        except Exception as e:
            self.process_error.emit(f"处理输出时出错: {str(e)}")
    
    def _handle_finished(self):
        """处理进程结束"""
        if not self.process:
            return
        
        try:
            # 停止输出监控
            self._stop_output_monitoring()
            
            # 读取最后的输出
            self._handle_output()
            
            # 获取退出码
            exit_code = self.process.exitCode()
            
            # 更新状态
            self._is_running = False
            
            # 发送完成信号
            self.process_finished.emit(exit_code)
            
        except Exception as e:
            self.process_error.emit(f"处理进程结束时出错: {str(e)}")
    
    def _handle_error(self, error):
        """处理进程错误"""
        error_messages = {
            QProcess.FailedToStart: "进程启动失败",
            QProcess.Crashed: "进程崩溃",
            QProcess.Timedout: "进程超时",
            QProcess.WriteError: "写入错误",
            QProcess.ReadError: "读取错误",
            QProcess.UnknownError: "未知错误"
        }
        
        error_msg = error_messages.get(error, f"进程错误: {error}")
        self.process_error.emit(error_msg)
    
    def _start_output_monitoring(self):
        """启动输出监控定时器"""
        # 主要检查定时器
        self.output_check_timer = QTimer()
        self.output_check_timer.timeout.connect(self._check_pending_output)
        self.output_check_timer.start(100)  # 每100ms检查一次
        
        # 备用检查定时器
        self.backup_check_timer = QTimer()
        self.backup_check_timer.timeout.connect(self._backup_output_check)
        self.backup_check_timer.start(500)  # 每500ms强制检查
    
    def _stop_output_monitoring(self):
        """停止输出监控定时器"""
        try:
            if self.output_check_timer:
                if self.output_check_timer.isActive():
                    self.output_check_timer.stop()
                self.output_check_timer.deleteLater()
                self.output_check_timer = None
        except Exception as e:
            print(f"停止输出检查定时器时出错: {str(e)}")

        try:
            if self.backup_check_timer:
                if self.backup_check_timer.isActive():
                    self.backup_check_timer.stop()
                self.backup_check_timer.deleteLater()
                self.backup_check_timer = None
        except Exception as e:
            print(f"停止备用检查定时器时出错: {str(e)}")
    
    def _check_pending_output(self):
        """检查是否有待处理的输出"""
        if not self.is_running():
            self._stop_output_monitoring()
            return
        
        try:
            bytes_available = self.process.bytesAvailable()
            if bytes_available > 0:
                self._handle_output()
        except Exception:
            pass
    
    def _backup_output_check(self):
        """备用输出检查"""
        if not self.is_running():
            if self.backup_check_timer:
                self.backup_check_timer.stop()
            return
        
        try:
            # 强制读取所有可用输出
            bytes_available = self.process.bytesAvailable()
            if bytes_available > 0:
                self._handle_output()
        except Exception:
            pass
    
    def cleanup(self):
        """清理资源"""
        # 防止重复清理
        if hasattr(self, '_cleaned_up') and self._cleaned_up:
            return

        try:
            # 标记为已清理，防止重复调用
            self._cleaned_up = True

            # 停止进程
            if self.is_running():
                self.stop()

            # 停止定时器
            self._stop_output_monitoring()

            # 断开信号连接并清理进程对象
            if self.process:
                try:
                    # 安全地断开所有信号连接
                    if hasattr(self.process, 'readyReadStandardOutput'):
                        try:
                            self.process.readyReadStandardOutput.disconnect()
                        except (TypeError, RuntimeError):
                            pass
                    if hasattr(self.process, 'readyReadStandardError'):
                        try:
                            self.process.readyReadStandardError.disconnect()
                        except (TypeError, RuntimeError):
                            pass
                    if hasattr(self.process, 'finished'):
                        try:
                            self.process.finished.disconnect()
                        except (TypeError, RuntimeError):
                            pass
                    if hasattr(self.process, 'errorOccurred'):
                        try:
                            self.process.errorOccurred.disconnect()
                        except (TypeError, RuntimeError):
                            pass
                except Exception:
                    pass  # 忽略断开连接时的错误

                # 确保进程已停止
                if self.process.state() != QProcess.NotRunning:
                    self.process.kill()
                    self.process.waitForFinished(1000)  # 等待1秒

                # 清理进程对象
                self.process = None

        except Exception as e:
            print(f"清理进程管理器时出错: {str(e)}")
        finally:
            # 确保清理标志被设置
            self._cleaned_up = True

    def _pause_windows_process(self, process_id):
        """Windows下暂停进程的实现"""
        try:
            # 对于仿真器，最好的方法是发送交互式命令
            # xrun和vcs都支持在运行时接收命令

            if self.process and self.process.state() == QProcess.Running:
                # 检测是否是仿真器进程
                if self._is_simulator_process():
                    # 向仿真器发送暂停命令
                    # 对于xrun: 发送Ctrl+C进入交互模式，然后可以使用stop命令
                    # 对于vcs: 类似的交互机制
                    self.process.write(b'\x03')  # Ctrl+C进入交互模式
                    time.sleep(0.1)  # 等待进入交互模式
                    # 可以选择发送stop命令
                    # self.process.write(b'stop\n')
                else:
                    # 对于普通进程，使用系统信号
                    # Windows下可以尝试发送Ctrl+C
                    self.process.write(b'\x03')

                self._is_paused = True
                self.process_paused.emit()
                return True

            return False

        except Exception as e:
            print(f"Windows暂停进程失败: {str(e)}")
            return False

    def _resume_windows_process(self, process_id):
        """Windows下继续进程的实现"""
        try:
            if self.process and self.process.state() == QProcess.Running:
                if self._is_simulator_process():
                    # 向仿真器发送继续命令
                    # 对于xrun: 发送"run"命令继续执行
                    # 对于vcs: 发送"run"命令继续执行
                    self.process.write(b'run\n')
                    # 也可以尝试发送"continue"命令
                    # self.process.write(b'continue\n')
                else:
                    # 对于普通进程，如果之前被暂停，尝试恢复
                    # Windows下没有直接的SIGCONT，这里主要依赖应用程序的响应
                    self.process.write(b'\n')  # 发送回车，可能唤醒等待输入的进程

                self._is_paused = False
                self.process_resumed.emit()
                return True

            return False

        except Exception as e:
            print(f"Windows继续进程失败: {str(e)}")
            return False

    def _pause_linux_process(self, process_id):
        """Linux下暂停进程的实现"""
        try:
            if self.process and self.process.state() == QProcess.Running:
                success = False

                if self._is_xrun_process():
                    # 对于xrun仿真器，使用专用处理方法
                    success = self._pause_xrun_simulator(process_id)
                elif self._is_simulator_process():
                    # 对于其他仿真器，使用通用方法
                    self.process.write(b'\x03')  # Ctrl+C进入交互模式
                    time.sleep(0.2)
                    self.process.write(b'stop\n')  # 发送stop命令
                    success = True
                else:
                    # 对于普通进程，使用SIGTSTP信号暂停
                    try:
                        os.kill(process_id, signal.SIGTSTP)
                        success = True
                    except (OSError, ProcessLookupError):
                        # 如果信号发送失败，尝试发送Ctrl+C
                        self.process.write(b'\x03')
                        success = True

                if success:
                    self._is_paused = True
                    self.process_paused.emit()
                    return True

            return False

        except Exception as e:
            print(f"Linux暂停进程失败: {str(e)}")
            return False

    def _resume_linux_process(self, process_id):
        """Linux下继续进程的实现"""
        try:
            if self.process and self.process.state() == QProcess.Running:
                success = False

                if self._is_xrun_process():
                    # 对于xrun仿真器，使用专用处理方法
                    success = self._resume_xrun_simulator(process_id)
                elif self._is_simulator_process():
                    # 对于其他仿真器，使用通用方法
                    self.process.write(b'run\n')
                    success = True
                else:
                    # 对于普通进程，发送SIGCONT信号
                    try:
                        os.kill(process_id, signal.SIGCONT)
                        success = True
                    except (OSError, ProcessLookupError):
                        # 如果信号发送失败，尝试发送回车
                        self.process.write(b'\n')
                        success = True

                if success:
                    self._is_paused = False
                    self.process_resumed.emit()
                    return True

            return False

        except Exception as e:
            print(f"Linux继续进程失败: {str(e)}")
            return False

    def _is_simulator_process(self):
        """检测是否是仿真器进程"""
        if not self.command:
            return False

        # 检查命令中是否包含仿真器关键字
        simulator_keywords = ['runsim', 'xrun', 'irun', 'vcs', 'simv', 'ncsim']
        command_lower = self.command.lower()

        return any(keyword in command_lower for keyword in simulator_keywords)

    def _is_remote_runsim(self):
        """检测是否是远程调度的runsim"""
        if not self.command:
            return False

        # 检查是否是runsim命令（可能被调度到远程服务器）
        return 'runsim' in self.command.lower()

    def _detect_remote_execution_mode(self):
        """检测远程执行模式 - 专门针对LSF调度"""
        if not self._is_remote_runsim():
            return "local"

        # 简化：只支持LSF调度，直接返回remote_lsf
        # runsim命令默认使用LSF调度到远程服务器
        return "remote_lsf"

    def _pause_remote_simulation(self, process_id, execution_mode):
        """处理LSF远程调度仿真的暂停"""
        try:
            print("处理LSF远程仿真暂停...")

            # 向runsim进程发送Ctrl+C，让它通过LSF传递给远程xrun
            if self.process and self.process.state() == QProcess.Running:
                print("向runsim发送Ctrl+C信号，传递给LSF远程xrun...")
                self.process.write(b'\x03')  # Ctrl+C
                time.sleep(0.5)  # 等待信号通过LSF传递到远程服务器

                print("✓ Ctrl+C已发送，远程xrun应进入交互模式")

                self._is_paused = True
                self.process_paused.emit()
                return True

            return False

        except Exception as e:
            print(f"LSF远程暂停失败: {str(e)}")
            return False

    def _resume_remote_simulation(self, process_id, execution_mode):
        """处理LSF远程调度仿真的继续"""
        try:
            print("处理LSF远程仿真继续...")

            if self.process and self.process.state() == QProcess.Running:
                print("向远程xrun发送继续命令...")

                # 发送xrun标准继续命令
                self.process.write(b'run\n')
                time.sleep(0.2)  # 等待命令传递

                # 备用命令（如果run命令不响应）
                self.process.write(b'continue\n')
                time.sleep(0.1)

                print("✓ 继续命令已发送给LSF远程xrun")

                self._is_paused = False
                self.process_resumed.emit()
                return True

            return False

        except Exception as e:
            print(f"LSF远程继续失败: {str(e)}")
            return False

    def _is_xrun_process(self):
        """检测是否是xrun/irun仿真器进程"""
        if not self.command:
            return False

        # 检查命令中是否包含xrun/irun关键字
        xrun_keywords = ['runsim', 'xrun', 'irun']
        command_lower = self.command.lower()

        return any(keyword in command_lower for keyword in xrun_keywords)

    def _pause_xrun_simulator(self, process_id):
        """专门处理xrun仿真器的暂停"""
        try:
            # 验证进程ID的有效性
            if not process_id or process_id <= 0:
                print(f"无效的进程ID: {process_id}")
                return self._fallback_pause_method()

            # 检查进程是否存在（仅在非Windows系统）
            if sys.platform != 'win32':
                try:
                    os.kill(process_id, 0)  # 发送信号0检查进程是否存在
                except (OSError, ProcessLookupError):
                    print(f"进程 {process_id} 不存在")
                    return self._fallback_pause_method()

            # 仅在Linux系统使用信号机制
            if sys.platform != 'win32':
                # 方法1：使用SIGSTOP强制暂停进程组（最可靠）
                # SIGSTOP无法被进程捕获或忽略，确保暂停成功
                try:
                    pgid = os.getpgid(process_id)
                    print(f"尝试暂停进程组 {pgid} (包含进程 {process_id})")
                    os.killpg(pgid, signal.SIGSTOP)
                    print(f"✓ 已向进程组 {pgid} 发送SIGSTOP信号（强制暂停）")
                    return True
                except (OSError, ProcessLookupError, AttributeError) as e:
                    print(f"进程组SIGSTOP失败: {e}")

                # 方法2：使用SIGSTOP暂停主进程
                try:
                    os.kill(process_id, signal.SIGSTOP)
                    print(f"✓ 已向进程 {process_id} 发送SIGSTOP信号（强制暂停）")
                    return True
                except (OSError, ProcessLookupError) as e:
                    print(f"主进程SIGSTOP失败: {e}")

                # 方法3：使用SIGTSTP作为备选（温和暂停）
                try:
                    pgid = os.getpgid(process_id)
                    os.killpg(pgid, signal.SIGTSTP)
                    print(f"✓ 已向进程组 {pgid} 发送SIGTSTP信号（温和暂停）")
                    return True
                except (OSError, ProcessLookupError, AttributeError) as e:
                    print(f"进程组SIGTSTP失败: {e}")

                # 方法4：对主进程使用SIGTSTP
                try:
                    os.kill(process_id, signal.SIGTSTP)
                    print(f"✓ 已向进程 {process_id} 发送SIGTSTP信号（温和暂停）")
                    return True
                except (OSError, ProcessLookupError) as e:
                    print(f"主进程SIGTSTP失败: {e}")
            else:
                print("Windows系统，跳过信号方法")

            # 方法3：交互式命令作为最后手段
            return self._fallback_pause_method()

        except Exception as e:
            print(f"xrun暂停处理失败: {str(e)}")
            return self._fallback_pause_method()

    def _fallback_pause_method(self):
        """备用暂停方法：使用交互式命令"""
        try:
            if self.process and self.process.state() == QProcess.Running:
                self.process.write(b'\x03')  # Ctrl+C
                time.sleep(0.2)
                self.process.write(b'stop\n')  # 发送stop命令
                print("已发送交互式暂停命令（备用方法）")
                return True
            return False
        except Exception as e:
            print(f"备用暂停方法失败: {str(e)}")
            return False

    def _resume_xrun_simulator(self, process_id):
        """专门处理xrun仿真器的继续"""
        try:
            # 验证进程ID的有效性
            if not process_id or process_id <= 0:
                print(f"无效的进程ID: {process_id}")
                return self._fallback_resume_method()

            # 检查进程是否存在（仅在非Windows系统）
            if sys.platform != 'win32':
                try:
                    os.kill(process_id, 0)  # 发送信号0检查进程是否存在
                except (OSError, ProcessLookupError):
                    print(f"进程 {process_id} 不存在")
                    return self._fallback_resume_method()

            # 仅在Linux系统使用信号机制
            if sys.platform != 'win32':
                # 方法1：使用SIGCONT恢复进程组（推荐方法）
                # 对应之前的SIGSTOP/SIGTSTP，使用SIGCONT恢复
                try:
                    pgid = os.getpgid(process_id)
                    print(f"尝试恢复进程组 {pgid} (包含进程 {process_id})")
                    os.killpg(pgid, signal.SIGCONT)
                    print(f"✓ 已向进程组 {pgid} 发送SIGCONT信号（恢复执行）")
                    time.sleep(0.1)  # 等待进程恢复
                    return True
                except (OSError, ProcessLookupError, AttributeError) as e:
                    print(f"进程组SIGCONT失败: {e}")

                # 方法2：使用SIGCONT恢复主进程
                try:
                    os.kill(process_id, signal.SIGCONT)
                    print(f"✓ 已向进程 {process_id} 发送SIGCONT信号（恢复执行）")
                    time.sleep(0.1)  # 等待进程恢复
                    return True
                except (OSError, ProcessLookupError) as e:
                    print(f"主进程SIGCONT失败: {e}")
            else:
                print("Windows系统，跳过信号方法")

            # 方法3：发送交互式继续命令
            return self._fallback_resume_method()

        except Exception as e:
            print(f"xrun继续处理失败: {str(e)}")
            return self._fallback_resume_method()

    def _fallback_resume_method(self):
        """备用继续方法：使用交互式命令"""
        try:
            if self.process and self.process.state() == QProcess.Running:
                self.process.write(b'run\n')  # 发送run命令
                print("已发送交互式继续命令（备用方法）")
                return True
            return False
        except Exception as e:
            print(f"备用继续方法失败: {str(e)}")
            return False

    def _safe_disconnect_signals(self):
        """
        安全地断开信号连接 - Linux兼容版本
        使用统一的信号安全工具
        """
        # 定义需要断开的信号列表
        signals_to_disconnect = [
            'output_ready',
            'process_finished',
            'process_error',
            'process_started',
            'process_stopped'
        ]

        # 使用安全的信号断开工具
        safe_disconnect_multiple(self, signals_to_disconnect)
    
    def __del__(self):
        """析构函数"""
        try:
            # 只在未清理时才调用cleanup
            if not hasattr(self, '_cleaned_up') or not self._cleaned_up:
                self.cleanup()
        except Exception:
            # 忽略析构函数中的错误
            pass
