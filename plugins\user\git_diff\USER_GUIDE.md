# Git文件版本比对插件使用指南

## 快速开始

### 1. 安装依赖

在使用插件之前，请确保安装了必要的依赖包：

```bash
# 进入插件目录
cd plugins/user/git_diff

# 运行依赖安装脚本
python install_git_diff_dependencies.py

# 或手动安装
pip install GitPython chardet Pygments
```

### 2. 启动插件

有两种方式启动插件：

#### 方式一：通过RunSim GUI
1. 启动RunSim GUI主程序：`python runsim_gui.py`
2. 在菜单栏选择 **"工具"** → **"Git文件版本比对工具"**

## 详细使用说明

### 文件选择

#### 单文件模式
1. 选择 **"单文件模式"** 单选按钮
2. 点击 **"浏览..."** 按钮选择文件
3. 或直接在路径输入框中输入文件路径
4. 按回车键确认选择

**注意**：选择的文件必须在Git仓库中且已被Git跟踪。

#### 目录模式
1. 选择 **"目录模式"** 单选按钮
2. 点击 **"浏览..."** 按钮选择Git仓库目录
3. 在文件树中浏览并点击选择要比对的文件

**提示**：目录模式会显示所有被Git跟踪的文件，方便浏览选择。

### 版本选择

1. 在文件选择完成后，版本选择区会自动加载该文件的提交历史
2. 提交历史按时间倒序排列（最新的在上面）
3. 每个提交显示：
   - 提交哈希（短格式）
   - 提交消息
   - 作者和提交时间
4. 点击选择要比对的历史版本
5. 点击 **"与当前版本比对"** 按钮开始比对

**快捷操作**：双击提交项可直接开始比对。

### 差异查看

#### 并排视图（默认）
- 左侧显示历史版本内容
- 右侧显示当前版本内容
- 红色背景：删除的行
- 绿色背景：新增的行
- 白色背景：未修改的行
- 支持同步滚动

#### 统一视图
- 传统的unified diff格式
- 以 `-` 开头的行表示删除
- 以 `+` 开头的行表示新增
- 以 `@@` 开头的行表示位置信息

#### 切换视图
使用工具栏的 **"视图"** 下拉菜单切换显示模式。

### 工具栏功能

#### 刷新
- 刷新文件列表和提交历史
- 快捷键：Ctrl+R

#### 导出
- 将差异结果导出为文件
- 支持 .diff 和 .txt 格式
- 快捷键：Ctrl+E

#### 搜索
- 在差异内容中搜索特定文本
- 支持在并排视图和统一视图中搜索
- 输入搜索内容后点击 **"搜索"** 按钮或按回车键

#### 视图切换
- 在 **"并排视图"** 和 **"统一视图"** 之间切换
- 实时切换，无需重新计算差异

### 状态栏信息

状态栏显示以下信息：
- **左侧**：当前操作状态
- **中间**：当前选择的文件名
- **右侧**：差异统计（+新增行数 -删除行数）

## 支持的文件类型

### 完全支持（语法高亮）
- Python (.py, .pyw)
- C/C++ (.c, .cpp, .cc, .cxx, .h, .hpp)
- SystemVerilog/Verilog (.sv, .v, .vh, .svh)
- JavaScript/TypeScript (.js, .jsx, .ts, .tsx)

### 基本支持（无语法高亮）
- 所有文本文件
- 配置文件 (.json, .yaml, .xml, .ini等)
- 脚本文件 (.sh, .bat, .ps1等)
- 文档文件 (.md, .txt, .log等)

## 常见问题解决

### Q: 提示"GitPython未安装"
**A**: 运行 `pip install GitPython` 安装依赖包。

### Q: 提示"文件不在Git仓库中"
**A**: 确保：
1. 文件所在目录是Git仓库（包含.git目录）
2. 文件已被Git跟踪（使用 `git add` 添加）

### Q: 提示"该文件没有提交历史"
**A**: 文件可能是新创建的，需要先提交：
```bash
git add filename
git commit -m "Add new file"
```

### Q: 中文字符显示乱码
**A**: 插件会自动检测文件编码，如果仍有问题：
1. 确保文件使用UTF-8编码保存
2. 检查Git配置：`git config --global core.quotepath false`

### Q: 语法高亮不工作
**A**: 安装Pygments库：`pip install Pygments`

### Q: 大文件比对很慢
**A**: 这是正常现象，建议：
1. 限制提交历史数量
2. 避免比对超大文件（>1MB）
3. 使用统一视图而非并排视图

## 性能优化建议

### 对于大型仓库
- 使用单文件模式而非目录模式
- 限制提交历史查看数量
- 避免频繁切换文件

### 对于大文件
- 优先使用统一视图
- 避免在差异很大的文件间频繁切换
- 考虑分段查看文件

### 内存优化
- 及时关闭不需要的比对窗口
- 避免同时打开多个插件实例
- 定期刷新以释放缓存

## 高级技巧

### 快捷键
- **Ctrl+R**：刷新
- **Ctrl+E**：导出
- **Enter**：确认路径输入
- **双击提交**：快速比对

### 搜索技巧
- 搜索支持部分匹配
- 区分大小写
- 可搜索行号、函数名、变量名等

### 导出技巧
- 导出的diff文件可用其他工具查看
- 支持版本控制系统的标准diff格式
- 可用于代码审查和文档记录

## 故障排除

如果遇到问题，请按以下步骤排查：

1. **检查依赖**：确保GitPython已安装
2. **检查Git仓库**：确保在有效的Git仓库中操作
3. **检查文件状态**：确保文件已被Git跟踪
4. **查看错误信息**：注意状态栏和弹出的错误提示
5. **重启插件**：关闭窗口后重新打开
6. **运行测试**：使用 `python test_git_diff_plugin.py` 进行诊断

如果问题仍然存在，请联系开发团队并提供：
- 错误信息截图
- 操作步骤描述
- Git仓库和文件信息
- 系统环境信息

## 更新日志

### v1.0.0 (当前版本)
- 初始版本发布
- 支持基本的Git文件版本比对
- 实现并排和统一两种显示模式
- 支持多种编程语言语法高亮
- 提供搜索和导出功能
- 完整的用户界面和交互体验
