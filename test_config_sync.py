#!/usr/bin/env python3
"""
测试配置面板和执行日志面板的选项同步功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.command_generator import CommandGenerator

def test_command_generation_with_checkboxes():
    """测试命令生成中复选框选项的处理"""
    print("=== 测试命令生成中复选框选项的处理 ===")
    
    # 测试配置1：启用fsdb_checked
    config1 = {
        "base": "test_base",
        "block": "test_block",
        "fsdb_checked": True,
        "vwdb_checked": False,
        "cl_checked": False,
        "sva_checked": False,
        "cov_checked": False,
        "upf_checked": False,
    }
    
    command1 = CommandGenerator.generate_command(config1, mode="normal", case_name="test_case1")
    print(f"配置1 (fsdb_checked=True): {command1}")
    print(f"包含-fsdb: {'-fsdb' in command1}")
    
    # 测试配置2：启用vwdb_checked
    config2 = {
        "base": "test_base",
        "block": "test_block",
        "fsdb_checked": False,
        "vwdb_checked": True,
        "cl_checked": False,
        "sva_checked": False,
        "cov_checked": False,
        "upf_checked": False,
    }
    
    command2 = CommandGenerator.generate_command(config2, mode="normal", case_name="test_case2")
    print(f"配置2 (vwdb_checked=True): {command2}")
    print(f"包含-vwdb: {'-vwdb' in command2}")
    
    # 测试配置3：同时启用多个选项
    config3 = {
        "base": "test_base",
        "block": "test_block",
        "fsdb_checked": True,
        "vwdb_checked": True,
        "cl_checked": True,
        "sva_checked": True,
        "cov_checked": True,
        "upf_checked": True,
    }
    
    command3 = CommandGenerator.generate_command(config3, mode="normal", case_name="test_case3")
    print(f"配置3 (所有选项启用): {command3}")
    print(f"包含-fsdb: {'-fsdb' in command3}")
    print(f"包含-vwdb: {'-vwdb' in command3}")
    print(f"包含-cl: {'-cl' in command3}")
    print(f"包含-dump_sva: {'-dump_sva' in command3}")
    print(f"包含-cov: {'-cov' in command3}")
    print(f"包含-upf: {'-upf' in command3}")

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    # 测试旧的字段名
    config_old = {
        "base": "test_base",
        "block": "test_block",
        "fsdb": True,
        "vwdb": True,
        "cl": True,
        "dump_sva": True,
        "cov": True,
        "upf": True,
    }
    
    command_old = CommandGenerator.generate_command(config_old, mode="normal", case_name="test_case_old")
    print(f"旧字段名配置: {command_old}")
    print(f"包含-fsdb: {'-fsdb' in command_old}")
    print(f"包含-vwdb: {'-vwdb' in command_old}")
    
    # 测试新的字段名
    config_new = {
        "base": "test_base",
        "block": "test_block",
        "fsdb_checked": True,
        "vwdb_checked": True,
        "cl_checked": True,
        "sva_checked": True,
        "cov_checked": True,
        "upf_checked": True,
    }
    
    command_new = CommandGenerator.generate_command(config_new, mode="normal", case_name="test_case_new")
    print(f"新字段名配置: {command_new}")
    print(f"包含-fsdb: {'-fsdb' in command_new}")
    print(f"包含-vwdb: {'-vwdb' in command_new}")
    
    # 比较结果
    print(f"命令是否相同: {command_old == command_new}")

def test_mixed_field_names():
    """测试混合字段名的情况"""
    print("\n=== 测试混合字段名的情况 ===")
    
    # 同时包含新旧字段名（新字段名应该优先）
    config_mixed = {
        "base": "test_base",
        "block": "test_block",
        "fsdb": False,  # 旧字段名，设为False
        "fsdb_checked": True,  # 新字段名，设为True
        "vwdb": True,  # 旧字段名，设为True
        "vwdb_checked": False,  # 新字段名，设为False
    }
    
    command_mixed = CommandGenerator.generate_command(config_mixed, mode="normal", case_name="test_case_mixed")
    print(f"混合字段名配置: {command_mixed}")
    print(f"包含-fsdb: {'-fsdb' in command_mixed} (应该为True，因为fsdb_checked=True)")
    print(f"包含-vwdb: {'-vwdb' in command_mixed} (应该为False，因为vwdb_checked=False)")

def test_r_option_integration():
    """测试-R选项的集成"""
    print("\n=== 测试-R选项的集成 ===")
    
    # 测试sim_only模式
    config_r = {
        "base": "test_base",
        "block": "test_block",
        "fsdb_checked": True,
        "sim_only": True,  # 仅仿真模式
    }
    
    command_r = CommandGenerator.generate_command(config_r, mode="R", case_name="test_case_r")
    print(f"仅仿真模式配置: {command_r}")
    print(f"包含-fsdb: {'-fsdb' in command_r}")
    print(f"包含-R: {'-R' in command_r}")

def test_fsdb_file_option():
    """测试fsdb文件选项"""
    print("\n=== 测试fsdb文件选项 ===")
    
    # 测试带TCL文件的fsdb选项
    config_fsdb_file = {
        "base": "test_base",
        "block": "test_block",
        "fsdb_checked": True,
        "fsdb_file": "/path/to/test.tcl",
    }
    
    command_fsdb_file = CommandGenerator.generate_command(config_fsdb_file, mode="normal", case_name="test_case_fsdb_file")
    print(f"带TCL文件的fsdb配置: {command_fsdb_file}")
    print(f"包含-fsdb: {'-fsdb' in command_fsdb_file}")
    print(f"包含TCL文件路径: {'/path/to/test.tcl' in command_fsdb_file}")

if __name__ == "__main__":
    test_command_generation_with_checkboxes()
    test_backward_compatibility()
    test_mixed_field_names()
    test_r_option_integration()
    test_fsdb_file_option()
    
    print("\n=== 测试完成 ===")
    print("\n修复总结：")
    print("✓ 修复了配置面板中复选框字段名不一致的问题")
    print("✓ 统一使用 *_checked 字段名进行命令生成")
    print("✓ 保持向后兼容性，同时支持新旧字段名")
    print("✓ 确保命令预览能正确显示选项")
    print("✓ 实现了配置面板和执行日志面板的选项同步")
