"""
结果比较插件
"""
from PyQt5.QtWidgets import (
    QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
    QFileDialog, QMessageBox, QSplitter, QTabWidget, QTextEdit,
    QComboBox, QGroupBox, QRadioButton, QButtonGroup, QLineEdit,
    QWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QObject
from PyQt5.QtGui import QColor, QFont
from plugins.base import PluginBase, NonModalDialog
import os
import re
import difflib
import json
import time
import datetime
from utils.path_resolver import PathResolver

class ResultComparisonDialog(NonModalDialog):
    """结果比较对话框"""

    def __init__(self, parent=None, plugin_name=""):
        """初始化结果比较对话框"""
        try:
            super().__init__(parent, plugin_name)
            self.setWindowTitle("结果比较")
            self.resize(1200, 800)

            # 创建路径解析器
            self.path_resolver = PathResolver()

            # 存储日志文件
            self.log_files = {
                "left": None,
                "right": None
            }

            # 初始化UI
            self.init_ui()
        except Exception as e:
            print(f"初始化结果比较对话框时出错: {str(e)}")
            # 重新抛出异常，让上层处理
            raise

    def init_ui(self):
        """初始化UI"""
        try:
            layout = QVBoxLayout()

            # 创建文件选择区域
            file_group = QGroupBox("文件选择")
            file_layout = QHBoxLayout()

            # 左侧文件选择
            left_layout = QVBoxLayout()
            left_label = QLabel("左侧文件:")
            self.left_file_label = QLabel("未选择")
            self.left_file_label.setStyleSheet("font-weight: bold;")
            left_btn = QPushButton("选择文件")
            left_btn.clicked.connect(lambda: self.select_file("left"))
            left_layout.addWidget(left_label)
            left_layout.addWidget(self.left_file_label)
            left_layout.addWidget(left_btn)

            # 右侧文件选择
            right_layout = QVBoxLayout()
            right_label = QLabel("右侧文件:")
            self.right_file_label = QLabel("未选择")
            self.right_file_label.setStyleSheet("font-weight: bold;")
            right_btn = QPushButton("选择文件")
            right_btn.clicked.connect(lambda: self.select_file("right"))
            right_layout.addWidget(right_label)
            right_layout.addWidget(self.right_file_label)
            right_layout.addWidget(right_btn)

            # 添加到文件选择布局
            file_layout.addLayout(left_layout)
            file_layout.addLayout(right_layout)
            file_group.setLayout(file_layout)

            # 创建比较选项区域
            options_group = QGroupBox("比较选项")
            options_layout = QHBoxLayout()

            # 比较模式
            mode_layout = QVBoxLayout()
            mode_label = QLabel("比较模式:")
            self.mode_combo = QComboBox()
            self.mode_combo.addItems(["整个文件", "仅差异", "按行号范围"])
            self.mode_combo.currentIndexChanged.connect(self.on_mode_changed)
            mode_layout.addWidget(mode_label)
            mode_layout.addWidget(self.mode_combo)

            # 行号范围
            range_layout = QVBoxLayout()
            range_label = QLabel("行号范围:")
            range_input_layout = QHBoxLayout()
            self.start_line_edit = QLineEdit()
            self.start_line_edit.setPlaceholderText("起始行")
            self.start_line_edit.setEnabled(False)
            self.end_line_edit = QLineEdit()
            self.end_line_edit.setPlaceholderText("结束行")
            self.end_line_edit.setEnabled(False)
            range_input_layout.addWidget(self.start_line_edit)
            range_input_layout.addWidget(QLabel("-"))
            range_input_layout.addWidget(self.end_line_edit)
            range_layout.addWidget(range_label)
            range_layout.addLayout(range_input_layout)

            # 过滤选项
            filter_layout = QVBoxLayout()
            filter_label = QLabel("过滤选项:")
            self.ignore_case_check = QRadioButton("忽略大小写")
            self.ignore_whitespace_check = QRadioButton("忽略空白")
            self.ignore_none_check = QRadioButton("不忽略")
            self.ignore_none_check.setChecked(True)
            filter_layout.addWidget(filter_label)
            filter_layout.addWidget(self.ignore_none_check)
            filter_layout.addWidget(self.ignore_case_check)
            filter_layout.addWidget(self.ignore_whitespace_check)

            # 添加到比较选项布局
            options_layout.addLayout(mode_layout)
            options_layout.addLayout(range_layout)
            options_layout.addLayout(filter_layout)
            options_group.setLayout(options_layout)

            # 创建比较按钮
            compare_btn = QPushButton("比较文件")
            compare_btn.clicked.connect(self.compare_files)

            # 创建分割器
            splitter = QSplitter(Qt.Vertical)

            # 创建比较结果区域
            self.result_tabs = QTabWidget()

            # 创建差异视图
            self.diff_edit = QTextEdit()
            self.diff_edit.setReadOnly(True)
            self.diff_edit.setLineWrapMode(QTextEdit.NoWrap)
            self.diff_edit.setFont(QFont("Courier New", 9))
            self.result_tabs.addTab(self.diff_edit, "差异视图")

            # 创建并排视图
            side_by_side_widget = QWidget()
            side_by_side_layout = QHBoxLayout(side_by_side_widget)

            self.left_edit = QTextEdit()
            self.left_edit.setReadOnly(True)
            self.left_edit.setLineWrapMode(QTextEdit.NoWrap)
            self.left_edit.setFont(QFont("Courier New", 9))

            self.right_edit = QTextEdit()
            self.right_edit.setReadOnly(True)
            self.right_edit.setLineWrapMode(QTextEdit.NoWrap)
            self.right_edit.setFont(QFont("Courier New", 9))

            side_by_side_layout.addWidget(self.left_edit)
            side_by_side_layout.addWidget(self.right_edit)

            self.result_tabs.addTab(side_by_side_widget, "并排视图")

            # 添加到分割器
            splitter.addWidget(self.result_tabs)

            # 添加到主布局
            layout.addWidget(file_group)
            layout.addWidget(options_group)
            layout.addWidget(compare_btn)
            layout.addWidget(splitter)
            self.setLayout(layout)
        except Exception as e:
            print(f"初始化结果比较对话框UI时出错: {str(e)}")
            # 重新抛出异常，让上层处理
            raise

    def on_mode_changed(self, index):
        """处理比较模式变化"""
        # 启用或禁用行号范围输入
        enable_range = (index == 2)  # "按行号范围"
        self.start_line_edit.setEnabled(enable_range)
        self.end_line_edit.setEnabled(enable_range)

    def select_file(self, side):
        """选择文件"""
        # 打开文件对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            f"选择{side}侧文件",
            "",
            "日志文件 (*.log);;文本文件 (*.txt);;所有文件 (*.*)"
        )

        if not file_path:
            return

        # 存储文件路径
        self.log_files[side] = file_path

        # 更新标签
        if side == "left":
            self.left_file_label.setText(os.path.basename(file_path))
        else:
            self.right_file_label.setText(os.path.basename(file_path))

    def compare_files(self):
        """比较文件"""
        # 检查是否选择了文件
        if not self.log_files["left"] or not self.log_files["right"]:
            QMessageBox.warning(self, "警告", "请先选择两个文件进行比较")
            return

        try:
            # 读取文件内容
            with open(self.log_files["left"], 'r', encoding='utf-8', errors='replace') as f:
                left_content = f.readlines()

            with open(self.log_files["right"], 'r', encoding='utf-8', errors='replace') as f:
                right_content = f.readlines()

            # 获取比较模式
            mode = self.mode_combo.currentIndex()

            # 如果是按行号范围，获取范围
            if mode == 2:
                try:
                    start_line = int(self.start_line_edit.text() or "1") - 1
                    end_line = int(self.end_line_edit.text() or str(len(left_content)))

                    # 确保范围有效
                    start_line = max(0, start_line)
                    end_line = min(len(left_content), end_line)

                    # 截取内容
                    left_content = left_content[start_line:end_line]
                    right_content = right_content[start_line:min(end_line, len(right_content))]
                except ValueError:
                    QMessageBox.warning(self, "警告", "行号必须是有效的整数")
                    return

            # 获取过滤选项
            ignore_case = self.ignore_case_check.isChecked()
            ignore_whitespace = self.ignore_whitespace_check.isChecked()

            # 应用过滤
            if ignore_case:
                left_content = [line.lower() for line in left_content]
                right_content = [line.lower() for line in right_content]

            if ignore_whitespace:
                left_content = [' '.join(line.split()) for line in left_content]
                right_content = [' '.join(line.split()) for line in right_content]

            # 生成差异
            diff = list(difflib.unified_diff(
                left_content,
                right_content,
                fromfile=os.path.basename(self.log_files["left"]),
                tofile=os.path.basename(self.log_files["right"]),
                lineterm=''
            ))

            # 如果是仅差异模式，过滤掉相同的行
            if mode == 1:
                filtered_diff = []
                for line in diff:
                    if line.startswith('+') or line.startswith('-') or line.startswith('@@'):
                        filtered_diff.append(line)
                diff = filtered_diff

            # 更新差异视图
            self._update_diff_view(diff)

            # 更新并排视图
            self._update_side_by_side_view(left_content, right_content)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"比较文件时出错: {str(e)}")

    def _update_diff_view(self, diff):
        """更新差异视图"""
        # 清空文本
        self.diff_edit.clear()

        # 添加差异
        html = []
        for line in diff:
            if line.startswith('+'):
                html.append(f'<span style="color: green;">{line}</span>')
            elif line.startswith('-'):
                html.append(f'<span style="color: red;">{line}</span>')
            elif line.startswith('@@'):
                html.append(f'<span style="color: blue;">{line}</span>')
            else:
                html.append(line)

        # 设置HTML
        self.diff_edit.setHtml('<pre>' + '<br>'.join(html) + '</pre>')

    def _update_side_by_side_view(self, left_content, right_content):
        """更新并排视图"""
        # 清空文本
        self.left_edit.clear()
        self.right_edit.clear()

        # 添加内容
        self.left_edit.setPlainText(''.join(left_content))
        self.right_edit.setPlainText(''.join(right_content))

class ResultComparisonPlugin(PluginBase):
    """结果比较插件"""

    @property
    def name(self):
        return "结果比较工具"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "比较两个日志文件的差异，支持多种比较模式和过滤选项"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window
            self.dialog = None

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_dialog)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addAction(self.menu_action)

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        try:
            # 关闭对话框
            if self.dialog and self.dialog.isVisible():
                self.dialog.close()

            # 移除菜单项
            if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
                self.main_window.tools_menu.removeAction(self.menu_action)

            print(f"成功清理插件: {self.name}")

        except Exception as e:
            print(f"清理插件 {self.name} 失败: {str(e)}")

    def show_dialog(self):
        """显示结果比较对话框"""
        try:
            if not self.dialog:
                self.dialog = ResultComparisonDialog(self.main_window, self.name)

            # 显示对话框
            self.dialog.show()
            self.dialog.raise_()
            self.dialog.activateWindow()
        except KeyboardInterrupt:
            print("结果比较对话框被用户中断")
        except Exception as e:
            print(f"显示结果比较对话框时出错: {str(e)}")
            # 如果创建对话框失败，重置对话框引用
            self.dialog = None
