#!/usr/bin/env python3
"""
简单的GUI测试，只测试复选框和命令修改功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
    QWidget, QLabel, QCheckBox, QTextEdit, QPushButton
)
from PyQt5.QtCore import Qt
from utils.command_generator import CommandParser

class SimpleTestWindow(QMainWindow):
    """简单的测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("仿真选项控制功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始命令
        self.command = "runsim -base test_base -block test_block -case test_case1 -rundir ./run1"
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 标题
        title_label = QLabel("仿真选项控制功能测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 复选框区域
        checkbox_layout = QHBoxLayout()
        
        # -fsdb复选框
        self.fsdb_checkbox = QCheckBox("-fsdb")
        self.fsdb_checkbox.setToolTip("启用波形输出选项")
        self.fsdb_checkbox.stateChanged.connect(self.on_fsdb_changed)
        checkbox_layout.addWidget(self.fsdb_checkbox)
        
        # -R复选框
        self.r_checkbox = QCheckBox("-R")
        self.r_checkbox.setToolTip("启用重新运行选项")
        self.r_checkbox.stateChanged.connect(self.on_r_changed)
        checkbox_layout.addWidget(self.r_checkbox)
        
        # 重置按钮
        reset_btn = QPushButton("重置命令")
        reset_btn.clicked.connect(self.reset_command)
        checkbox_layout.addWidget(reset_btn)
        
        checkbox_layout.addStretch()
        layout.addLayout(checkbox_layout)
        
        # 命令显示区域
        cmd_label = QLabel("当前命令:")
        cmd_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(cmd_label)
        
        self.cmd_display = QTextEdit()
        self.cmd_display.setReadOnly(True)
        self.cmd_display.setFixedHeight(100)
        self.cmd_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
                background-color: #f5f5f5;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 5px;
            }
        """)
        layout.addWidget(self.cmd_display)
        
        # 日志显示区域
        log_label = QLabel("操作日志:")
        log_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(log_label)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 9pt;
                background-color: #ffffff;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 5px;
            }
        """)
        layout.addWidget(self.log_display)
        
        # 初始化界面
        self.update_display()
        self.init_checkboxes()
        
        self.log("应用程序启动")
        self.log(f"初始命令: {self.command}")
    
    def init_checkboxes(self):
        """初始化复选框状态"""
        # 检查当前命令中的选项
        has_fsdb = CommandParser.has_fsdb_option(self.command)
        has_r = CommandParser.has_r_option(self.command)
        
        # 设置复选框状态（阻止信号触发）
        self.fsdb_checkbox.blockSignals(True)
        self.r_checkbox.blockSignals(True)
        
        self.fsdb_checkbox.setChecked(has_fsdb)
        self.r_checkbox.setChecked(has_r)
        
        # 恢复信号
        self.fsdb_checkbox.blockSignals(False)
        self.r_checkbox.blockSignals(False)
        
        self.log(f"初始化复选框状态: -fsdb={has_fsdb}, -R={has_r}")
    
    def on_fsdb_changed(self, state):
        """处理-fsdb选项变更"""
        enabled = state == Qt.Checked
        self.log(f"-fsdb选项变更: {'启用' if enabled else '禁用'}")
        
        # 修改命令
        self.command = CommandParser.modify_command_options(
            self.command, fsdb_enabled=enabled
        )
        
        self.update_display()
        self.log(f"命令已更新: {self.command}")
    
    def on_r_changed(self, state):
        """处理-R选项变更"""
        enabled = state == Qt.Checked
        self.log(f"-R选项变更: {'启用' if enabled else '禁用'}")
        
        # 修改命令
        self.command = CommandParser.modify_command_options(
            self.command, r_enabled=enabled
        )
        
        self.update_display()
        self.log(f"命令已更新: {self.command}")
    
    def reset_command(self):
        """重置命令"""
        self.command = "runsim -base test_base -block test_block -case test_case1 -rundir ./run1"
        self.update_display()
        self.init_checkboxes()
        self.log("命令已重置")
    
    def update_display(self):
        """更新命令显示"""
        self.cmd_display.setText(self.command)
    
    def log(self, message):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_display.append(log_entry)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = SimpleTestWindow()
    window.show()
    
    print("简单GUI测试启动成功！")
    print("请测试-fsdb和-R复选框的功能")
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
