@echo off
setlocal enabledelayedexpansion

REM Set UTF-8 code page for proper character encoding
chcp 65001 >nul 2>&1

REM RunSim Simulation Script - Windows Batch File
REM Call Python-based runsim simulator

REM Get script directory
set "SCRIPT_DIR=%~dp0"
set "PYTHON_SCRIPT=%SCRIPT_DIR%runsim.py"

REM Debug information (can be disabled by setting DEBUG=0)
set DEBUG=1
if "%DEBUG%"=="1" (
    echo [DEBUG] Script directory: %SCRIPT_DIR%
    echo [DEBUG] Python script: %PYTHON_SCRIPT%
    echo [DEBUG] Arguments: %*
)

REM Check if runsim.py exists
if not exist "%PYTHON_SCRIPT%" (
    echo Error: runsim.py not found in %SCRIPT_DIR%
    exit /b 1
)

REM Try different Python commands in order of preference
set "PYTHON_FOUND=0"

REM Method 1: Try 'python' command
where python >nul 2>&1
if !errorlevel! equ 0 (
    if "%DEBUG%"=="1" echo [DEBUG] Using 'python' command
    python "%PYTHON_SCRIPT%" %*
    set "PYTHON_FOUND=1"
    goto :end
)

REM Method 2: Try 'python3' command
where python3 >nul 2>&1
if !errorlevel! equ 0 (
    if "%DEBUG%"=="1" echo [DEBUG] Using 'python3' command
    python3 "%PYTHON_SCRIPT%" %*
    set "PYTHON_FOUND=1"
    goto :end
)

REM Method 3: Try 'py' launcher
where py >nul 2>&1
if !errorlevel! equ 0 (
    if "%DEBUG%"=="1" echo [DEBUG] Using 'py' launcher
    py "%PYTHON_SCRIPT%" %*
    set "PYTHON_FOUND=1"
    goto :end
)

REM Method 4: Try common Python installation paths
set "PYTHON_PATHS=C:\Program Files\Python312\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python311\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python310\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python39\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python38\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python312\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python311\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python310\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python39\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python38\python.exe"

for %%P in (!PYTHON_PATHS!) do (
    if exist "%%P" (
        if "%DEBUG%"=="1" echo [DEBUG] Using Python at: %%P
        "%%P" "%PYTHON_SCRIPT%" %*
        set "PYTHON_FOUND=1"
        goto :end
    )
)

REM If no Python found, show detailed error message
echo.
echo ========================================
echo ERROR: Python interpreter not found!
echo ========================================
echo.
echo This script requires Python to be installed and accessible.
echo.
echo Troubleshooting steps:
echo 1. Install Python from: https://www.python.org/downloads/
echo 2. During installation, check "Add Python to PATH"
echo 3. Or manually add Python to your system PATH
echo 4. Restart your command prompt after installation
echo.
echo Current PATH:
echo %PATH%
echo.
exit /b 1

:end
if "%DEBUG%"=="1" (
    if "!PYTHON_FOUND!"=="1" (
        echo [DEBUG] Python execution completed
    ) else (
        echo [DEBUG] Python execution failed
    )
)
endlocal
