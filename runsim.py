#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RunSim 修复版本 - 解决编码和日志生成问题
"""
import sys
import os
import time
import argparse
import random
from datetime import datetime

# 设置Windows控制台编码
if sys.platform == 'win32':
    try:
        import locale
        # 设置控制台代码页为UTF-8
        os.system('chcp 65001 >nul 2>&1')
        # 设置Python的默认编码
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except:
        pass

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='RunSim 仿真脚本模拟器')
    parser.add_argument('-case', type=str, help='测试案例名称', default='default_case')
    parser.add_argument('-base', type=str, help='基础配置')
    parser.add_argument('-block', type=str, help='模块配置')
    parser.add_argument('-C', action='store_true', help='仅编译')
    parser.add_argument('-R', action='store_true', help='仅运行仿真')
    parser.add_argument('-cov', action='store_true', help='覆盖率收集')
    parser.add_argument('-fsdb', action='store_true', help='生成FSDB波形文件')
    parser.add_argument('-vwdb', action='store_true', help='生成VWDB波形文件')
    parser.add_argument('-cl', action='store_true', help='启用CL选项')
    parser.add_argument('-dump_sva', action='store_true', help='转储SVA断言')
    parser.add_argument('-upf', action='store_true', help='启用UPF仿真')
    parser.add_argument('-seed', type=str, help='种子号')
    parser.add_argument('-rundir', type=str, help='工作目录')
    return parser.parse_args()

def create_log_directory(case_name):
    """创建日志目录"""
    log_dir = os.path.join(case_name, "log")
    try:
        os.makedirs(log_dir, exist_ok=True)
        print(f"创建日志目录: {log_dir}")
        return log_dir
    except Exception as e:
        print(f"创建日志目录失败: {str(e)}")
        return None

def simulate_compilation(case_name, args, log_dir):
    """模拟编译过程"""
    compile_log_path = os.path.join(log_dir, "irun_compile.log")
    
    print(f"[COMPILE] 开始编译 {case_name}")
    start_time = time.time()
    
    try:
        with open(compile_log_path, 'w', encoding='utf-8') as f:
            f.write(f"[{datetime.now().strftime('%H:%M:%S')}] 开始编译 {case_name}\n")
            f.write(f"编译器版本: VCS-2023.03 (模拟)\n")
            f.write(f"工作目录: {os.getcwd()}\n")
            f.write(f"编译目标: {case_name}\n")
            f.write("=" * 50 + "\n")
            
            if args.base:
                f.write(f"基础配置: {args.base}\n")
            if args.block:
                f.write(f"模块配置: {args.block}\n")
            
            f.write("\n开始详细编译过程:\n")
            f.write("-" * 40 + "\n")
            
            # 模拟编译步骤
            compile_steps = [
                "解析设计文件...",
                "分析SystemVerilog代码...",
                "处理接口和模块...",
                "生成仿真可执行文件...",
                "链接库文件...",
                "优化编译结果..."
            ]
            
            for i, step in enumerate(compile_steps, 1):
                f.write(f"[编译步骤 {i}/{len(compile_steps)}] {step}\n")
                print(f"[编译步骤 {i}/{len(compile_steps)}] {step}")
                
                if i == 1:
                    files = ["top.sv", "cpu_core.sv", "memory_ctrl.sv", "bus_fabric.sv"]
                    for j, file in enumerate(files, 1):
                        f.write(f"  解析文件 {j}/{len(files)}: {file}\n")
                        time.sleep(0.1)
                
                elif i == 2:
                    f.write("  分析模块依赖关系...\n")
                    f.write("  发现 15 个模块\n")
                    f.write("  检查语法正确性... OK\n")
                
                elif i == 4:
                    f.write("  生成中间代码...\n")
                    f.write("  生成可执行文件: simv\n")
                    f.write("  文件大小: 15.2 MB\n")
                
                time.sleep(0.2)
            
            # 计算编译时间
            end_time = time.time()
            compile_time_seconds = end_time - start_time
            compile_time_minutes = compile_time_seconds / 60
            
            f.write("\n" + "=" * 50 + "\n")
            f.write(f"[{datetime.now().strftime('%H:%M:%S')}] 编译完成\n")
            f.write("编译统计信息:\n")
            f.write(f"  编译时间: {compile_time_seconds:.1f} 秒\n")
            f.write(f"  内存使用: {random.randint(800, 1500)} MB\n")
            
            # 添加符合解析正则表达式的时间信息
            f.write(f"Total time: {compile_time_minutes:.2f} minutes\n")
            f.write(f"Compile time: {compile_time_minutes:.2f} min\n")
            f.write("Compilation completed successfully\n")
            f.write("=" * 50 + "\n")
        
        print(f"[SUCCESS] 编译完成，日志保存到: {compile_log_path}")
        return True
        
    except Exception as e:
        print(f"[ERROR] 编译失败: {str(e)}")
        return False

def simulate_simulation(case_name, args, log_dir):
    """模拟仿真过程"""
    sim_log_path = os.path.join(log_dir, "irun_sim.log")
    
    print(f"[SIMULATE] 开始仿真 {case_name}")
    start_time = time.time()
    
    try:
        with open(sim_log_path, 'w', encoding='utf-8') as f:
            f.write(f"[{datetime.now().strftime('%H:%M:%S')}] 开始仿真 {case_name}\n")
            f.write("=" * 50 + "\n")
            
            # 仿真环境初始化
            f.write("初始化仿真环境...\n")
            init_steps = [
                "加载仿真可执行文件...",
                "初始化仿真内核...",
                "设置仿真参数...",
                "配置信号监控..."
            ]
            
            for step in init_steps:
                f.write(f"  {step}\n")
                print(f"  {step}")
                time.sleep(0.1)
            
            # 设置随机种子
            seed = str(random.randint(1, 999999))
            f.write(f"\n随机种子: {seed}\n")
            f.write(f"仿真模式: 单用例测试\n")
            
            # 模拟仿真运行
            simulation_duration = 5  # 5秒的仿真
            f.write(f"\n预计仿真时间: {simulation_duration} 秒\n")
            f.write("开始仿真执行...\n")
            f.write("-" * 40 + "\n")
            
            # 简化的仿真过程
            for i in range(simulation_duration):
                current_time = i * 1000  # 模拟时间单位ns
                
                f.write(f"@{current_time}ns: 时钟周期 {i}\n")
                f.write(f"@{current_time}ns: cpu_core - 状态更新\n")
                
                if i == 2:
                    f.write(f"@{current_time}ns: TEST: 执行数据路径测试\n")
                elif i == 3:
                    f.write(f"@{current_time}ns: TEST: 执行中断响应测试\n")
                
                print(f"仿真进度: {i+1}/{simulation_duration}")
                time.sleep(0.5)
            
            # 仿真结束处理
            final_time = simulation_duration * 1000
            f.write(f"\n@{final_time}ns: 仿真执行结束\n")
            f.write("=" * 50 + "\n")
            
            # 详细的结果统计
            success = random.choice([True, True, True, False])  # 75%成功率
            
            f.write("仿真结果统计:\n")
            f.write("-" * 30 + "\n")
            
            if success:
                f.write("仿真状态: PASS\n")
                f.write("错误数量: 0\n")
                f.write("警告数量: 2\n")
            else:
                f.write("仿真状态: FAIL\n")
                f.write("错误数量: 1\n")
                f.write("失败原因: 断言失败 @ 15000ns\n")
            
            # 计算仿真时间
            end_time = time.time()
            sim_time_seconds = end_time - start_time
            sim_time_minutes = sim_time_seconds / 60
            
            # 性能统计
            f.write(f"\n性能统计:\n")
            f.write(f"  仿真时间: {sim_time_seconds:.1f} 秒\n")
            f.write(f"  仿真周期: {simulation_duration}\n")
            f.write(f"  平均频率: {random.randint(200, 800)} MHz\n")
            
            # 添加符合解析正则表达式的时间信息
            f.write(f"Simulation time: {sim_time_minutes:.2f} minutes\n")
            f.write(f"Total simulation time: {sim_time_minutes:.2f} min\n")
            f.write(f"CPU time: {sim_time_seconds:.2f} seconds\n")
            
            # 添加最终状态标识
            if success:
                f.write("SPRD_PASSED\n")
                f.write("TEST PASSED\n")
                f.write("Simulation completed successfully\n")
            else:
                f.write("SPRD_FAILED\n")
                f.write("TEST FAILED\n")
                f.write("Simulation failed\n")
            
            f.write("=" * 50 + "\n")
            f.write(f"[{datetime.now().strftime('%H:%M:%S')}] 仿真完成\n")
        
        print(f"[SUCCESS] 仿真完成，日志保存到: {sim_log_path}")
        return success
        
    except Exception as e:
        print(f"[ERROR] 仿真失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("RunSim 仿真脚本模拟器 v3.1 (修复版)")
    print("解决编码问题和日志生成问题")
    print("=" * 60)
    
    args = parse_arguments()
    case_name = args.case

    if args.rundir:
        case_name = args.rundir
    
    print(f"案例名称: {case_name}")
    if args.base:
        print(f"基础配置: {args.base}")
    if args.block:
        print(f"模块配置: {args.block}")
    
    # 创建日志目录
    log_dir = create_log_directory(case_name)
    if not log_dir:
        return 1
    
    start_time = time.time()
    
    try:
        # 编译阶段
        if not args.R:  # 如果不是仅运行模式
            if not simulate_compilation(case_name, args, log_dir):
                print("[ERROR] 编译失败，退出")
                return 1
        
        # 仿真阶段
        if not args.C:  # 如果不是仅编译模式
            if not simulate_simulation(case_name, args, log_dir):
                print("[WARNING] 仿真失败，但继续执行")
                # 不要因为仿真失败就退出，这样可以测试数据看板的FAIL状态处理
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print("\n" + "=" * 60)
        print("[SUCCESS] RunSim 执行完成")
        print(f"总耗时: {total_time:.1f} 秒")
        print(f"日志目录: {log_dir}")
        
        # 显示生成的文件
        compile_log = os.path.join(log_dir, "irun_compile.log")
        sim_log = os.path.join(log_dir, "irun_sim.log")
        
        if os.path.exists(compile_log):
            print(f"编译日志: {compile_log}")
        if os.path.exists(sim_log):
            print(f"仿真日志: {sim_log}")
        
        print("=" * 60)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n[WARNING] 用户中断执行 (Ctrl+C)")
        return 130
    except Exception as e:
        print(f"\n[ERROR] 执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
