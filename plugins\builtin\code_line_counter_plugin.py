from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QFileDialog, QMessageBox,
                             QLabel, QCheckBox, QGroupBox, QProgressBar, QTextEdit,
                             QSplitter, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from plugins.base import PluginBase
import os
import time
from datetime import datetime
import csv


class CodeLineCounterThread(QThread):
    """代码行数统计线程"""
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    file_processed = pyqtSignal(str, int, str)  # 文件路径, 行数, 文件类型
    finished_signal = pyqtSignal(dict)  # 统计结果
    error_occurred = pyqtSignal(str)  # 错误信息

    def __init__(self, paths, file_extensions, include_empty_lines=True, include_comments=True):
        super().__init__()
        self.paths = paths
        self.file_extensions = file_extensions
        self.include_empty_lines = include_empty_lines
        self.include_comments = include_comments
        self.is_cancelled = False

    def cancel(self):
        """取消统计"""
        self.is_cancelled = True

    def run(self):
        """执行统计"""
        try:
            all_files = []

            # 收集所有需要统计的文件
            for path in self.paths:
                if os.path.isfile(path):
                    if self.should_include_file(path):
                        all_files.append(path)
                elif os.path.isdir(path):
                    for root, dirs, files in os.walk(path):
                        if self.is_cancelled:
                            return
                        for file in files:
                            file_path = os.path.join(root, file)
                            if self.should_include_file(file_path):
                                all_files.append(file_path)

            total_files = len(all_files)
            if total_files == 0:
                self.error_occurred.emit("未找到符合条件的文件")
                return

            results = {
                'files': [],
                'summary': {
                    'total_files': 0,
                    'total_lines': 0,
                    'by_extension': {},
                    'start_time': datetime.now(),
                    'end_time': None
                }
            }

            # 统计每个文件
            for i, file_path in enumerate(all_files):
                if self.is_cancelled:
                    return

                try:
                    line_count = self.count_lines(file_path)
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if not file_ext:
                        file_ext = '无扩展名'

                    file_info = {
                        'path': file_path,
                        'lines': line_count,
                        'extension': file_ext,
                        'size': os.path.getsize(file_path)
                    }

                    results['files'].append(file_info)
                    results['summary']['total_files'] += 1
                    results['summary']['total_lines'] += line_count

                    # 按扩展名统计
                    if file_ext not in results['summary']['by_extension']:
                        results['summary']['by_extension'][file_ext] = {'files': 0, 'lines': 0}
                    results['summary']['by_extension'][file_ext]['files'] += 1
                    results['summary']['by_extension'][file_ext]['lines'] += line_count

                    self.file_processed.emit(file_path, line_count, file_ext)
                    self.progress_updated.emit(i + 1, total_files)

                except Exception as e:
                    print(f"处理文件 {file_path} 时出错: {str(e)}")
                    continue

            results['summary']['end_time'] = datetime.now()
            self.finished_signal.emit(results)

        except Exception as e:
            self.error_occurred.emit(f"统计过程出错: {str(e)}")

    def should_include_file(self, file_path):
        """判断是否应该包含此文件"""
        if not os.path.isfile(file_path):
            return False

        # 检查文件扩展名
        file_ext = os.path.splitext(file_path)[1].lower()
        if self.file_extensions and file_ext not in self.file_extensions:
            return False

        # 排除一些常见的非代码文件
        exclude_patterns = ['.git', '__pycache__', '.svn', '.hg', 'node_modules', '.vscode']
        for pattern in exclude_patterns:
            if pattern in file_path:
                return False

        return True

    def count_lines(self, file_path):
        """统计文件行数"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            if self.include_empty_lines and self.include_comments:
                return len(lines)

            count = 0
            for line in lines:
                stripped_line = line.strip()

                # 如果不包含空行，跳过空行
                if not self.include_empty_lines and not stripped_line:
                    continue

                # 如果不包含注释，跳过注释行（简单判断）
                if not self.include_comments:
                    if (stripped_line.startswith('//') or
                        stripped_line.startswith('#') or
                        stripped_line.startswith('/*') or
                        stripped_line.startswith('*') or
                        stripped_line.startswith('<!--')):
                        continue

                count += 1

            return count

        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {str(e)}")
            return 0


class CodeLineCounterPlugin(PluginBase):
    """代码行数统计插件"""

    @property
    def name(self):
        return "Verilog代码行数统计工具"

    @property
    def version(self):
        return "1.0.1"

    @property
    def description(self):
        return "统计选定Verilog代码文件或目录的行数，生成详细统计报告"

    def __init__(self):
        super().__init__()
        self.dialog = None

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_dialog)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addAction(self.menu_action)

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

        # 关闭对话框
        if self.dialog:
            try:
                self.dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

    def show_dialog(self):
        """显示代码行数统计对话框"""
        if self.dialog is None:
            self.dialog = CodeLineCounterDialog(self.main_window)

        # 设置为非模态对话框
        self.dialog.setModal(False)
        self.dialog.show()
        self.dialog.raise_()
        self.dialog.activateWindow()


class CodeLineCounterDialog(QDialog):
    """代码行数统计对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Verilog代码行数统计工具")
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
        self.resize(1000, 700)  # 减小窗口尺寸，因为选项更少了

        self.counter_thread = None
        self.results = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout()

        # 文件选择区域
        file_group = QGroupBox("文件/目录选择")
        file_layout = QVBoxLayout()

        # 选择按钮
        button_layout = QHBoxLayout()
        self.select_files_btn = QPushButton("选择文件/目录")
        self.clear_selection_btn = QPushButton("清空选择")

        self.select_files_btn.clicked.connect(self.select_files_or_directories)
        self.clear_selection_btn.clicked.connect(self.clear_selection)

        button_layout.addWidget(self.select_files_btn)
        button_layout.addWidget(self.clear_selection_btn)
        button_layout.addStretch()

        # 选择的路径显示
        self.selected_paths = QTextEdit()
        self.selected_paths.setMaximumHeight(100)
        self.selected_paths.setPlaceholderText("请选择要统计的文件或目录...")

        file_layout.addLayout(button_layout)
        file_layout.addWidget(QLabel("已选择的路径:"))
        file_layout.addWidget(self.selected_paths)
        file_group.setLayout(file_layout)

        # 统计选项区域
        options_group = QGroupBox("统计选项")
        options_layout = QVBoxLayout()

        # 文件类型过滤
        ext_layout = QVBoxLayout()
        ext_layout.addWidget(QLabel("支持的Verilog文件类型:"))

        # Verilog相关文件扩展名
        self.extension_checkboxes = {}
        verilog_extensions = [
            ('.v', 'Verilog'),
            ('.sv', 'SystemVerilog'),
            ('.svh', 'SystemVerilog Header'),
            ('.svi', 'SystemVerilog Include')
        ]

        # 创建水平布局来放置复选框
        checkbox_layout = QHBoxLayout()
        for ext, name in verilog_extensions:
            checkbox = QCheckBox(f"{name} ({ext})")
            checkbox.setChecked(True)
            self.extension_checkboxes[ext] = checkbox
            checkbox_layout.addWidget(checkbox)

        ext_layout.addLayout(checkbox_layout)

        # 其他选项
        other_layout = QHBoxLayout()
        self.include_empty_lines = QCheckBox("包含空行")
        self.include_comments = QCheckBox("包含注释行")
        self.include_empty_lines.setChecked(True)
        self.include_comments.setChecked(True)

        other_layout.addWidget(self.include_empty_lines)
        other_layout.addWidget(self.include_comments)
        other_layout.addStretch()

        options_layout.addLayout(ext_layout)
        options_layout.addLayout(other_layout)
        options_group.setLayout(options_layout)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始统计")
        self.stop_btn = QPushButton("停止统计")
        self.export_btn = QPushButton("导出结果")

        self.start_btn.clicked.connect(self.start_counting)
        self.stop_btn.clicked.connect(self.stop_counting)
        self.export_btn.clicked.connect(self.export_results)

        self.stop_btn.setEnabled(False)
        self.export_btn.setEnabled(False)

        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.export_btn)
        control_layout.addStretch()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 状态标签
        self.status_label = QLabel("就绪")

        # 结果显示区域
        results_splitter = QSplitter(Qt.Horizontal)

        # 文件列表表格
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(4)
        self.file_table.setHorizontalHeaderLabels(["文件路径", "行数", "文件类型", "文件大小"])
        self.file_table.horizontalHeader().setStretchLastSection(True)
        self.file_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.file_table.setSortingEnabled(True)

        # 汇总信息
        self.summary_text = QTextEdit()
        self.summary_text.setMaximumWidth(300)
        self.summary_text.setReadOnly(True)

        results_splitter.addWidget(self.file_table)
        results_splitter.addWidget(self.summary_text)
        results_splitter.setSizes([800, 300])

        # 添加所有组件到主布局
        layout.addWidget(file_group)
        layout.addWidget(options_group)
        layout.addLayout(control_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        layout.addWidget(results_splitter)

        self.setLayout(layout)

        # 存储选择的路径
        self.selected_file_paths = []

    def select_files_or_directories(self):
        """选择文件或目录"""
        from PyQt5.QtWidgets import QMessageBox, QPushButton

        # 创建选择对话框
        msg = QMessageBox(self)
        msg.setWindowTitle("选择类型")
        msg.setText("请选择要添加的类型:")
        msg.setIcon(QMessageBox.Question)

        # 添加自定义按钮
        files_btn = msg.addButton("选择文件", QMessageBox.ActionRole)
        dirs_btn = msg.addButton("选择目录", QMessageBox.ActionRole)
        cancel_btn = msg.addButton("取消", QMessageBox.RejectRole)

        msg.exec_()

        if msg.clickedButton() == files_btn:
            self.select_files()
        elif msg.clickedButton() == dirs_btn:
            self.select_directories()

    def select_files(self):
        """选择文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择Verilog代码文件",
            "",
            "Verilog文件 (*.v *.sv *.svh *.svi);;所有文件 (*.*)"
        )

        if files:
            self.selected_file_paths.extend(files)
            self.update_selected_paths_display()

    def select_directories(self):
        """选择目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "选择包含Verilog代码的目录"
        )

        if directory:
            self.selected_file_paths.append(directory)
            self.update_selected_paths_display()

    def clear_selection(self):
        """清空选择"""
        self.selected_file_paths.clear()
        self.update_selected_paths_display()

    def update_selected_paths_display(self):
        """更新选择的路径显示"""
        self.selected_paths.clear()
        for path in self.selected_file_paths:
            self.selected_paths.append(path)

    def get_selected_extensions(self):
        """获取选择的文件扩展名"""
        selected_extensions = []
        for ext, checkbox in self.extension_checkboxes.items():
            if checkbox.isChecked():
                selected_extensions.append(ext)
        return selected_extensions

    def start_counting(self):
        """开始统计"""
        if not self.selected_file_paths:
            QMessageBox.warning(self, "警告", "请先选择要统计的文件或目录")
            return

        # 获取统计选项
        selected_extensions = self.get_selected_extensions()
        include_empty_lines = self.include_empty_lines.isChecked()
        include_comments = self.include_comments.isChecked()

        # 清空之前的结果
        self.file_table.setRowCount(0)
        self.summary_text.clear()

        # 创建并启动统计线程
        self.counter_thread = CodeLineCounterThread(
            self.selected_file_paths,
            selected_extensions,
            include_empty_lines,
            include_comments
        )

        # 连接信号
        self.counter_thread.progress_updated.connect(self.update_progress)
        self.counter_thread.file_processed.connect(self.add_file_to_table)
        self.counter_thread.finished_signal.connect(self.counting_finished)
        self.counter_thread.error_occurred.connect(self.counting_error)

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.export_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在统计...")

        # 启动线程
        self.counter_thread.start()

    def stop_counting(self):
        """停止统计"""
        if self.counter_thread and self.counter_thread.isRunning():
            self.counter_thread.cancel()
            self.counter_thread.wait()

        self.reset_ui_state()
        self.status_label.setText("统计已停止")

    def update_progress(self, current, total):
        """更新进度"""
        if total > 0:
            progress = int((current / total) * 100)
            self.progress_bar.setValue(progress)
            self.status_label.setText(f"正在统计... ({current}/{total})")

    def add_file_to_table(self, file_path, line_count, file_type):
        """添加文件到表格"""
        row = self.file_table.rowCount()
        self.file_table.insertRow(row)

        # 文件路径
        self.file_table.setItem(row, 0, QTableWidgetItem(file_path))

        # 行数
        line_item = QTableWidgetItem(str(line_count))
        line_item.setData(Qt.UserRole, line_count)  # 存储数值用于排序
        self.file_table.setItem(row, 1, line_item)

        # 文件类型
        self.file_table.setItem(row, 2, QTableWidgetItem(file_type))

        # 文件大小
        try:
            size = os.path.getsize(file_path)
            size_str = self.format_file_size(size)
            size_item = QTableWidgetItem(size_str)
            size_item.setData(Qt.UserRole, size)  # 存储数值用于排序
            self.file_table.setItem(row, 3, size_item)
        except:
            self.file_table.setItem(row, 3, QTableWidgetItem("未知"))

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def counting_finished(self, results):
        """统计完成"""
        self.results = results
        self.update_summary(results)
        self.reset_ui_state()
        self.export_btn.setEnabled(True)
        self.status_label.setText(f"统计完成 - 共 {results['summary']['total_files']} 个文件，{results['summary']['total_lines']} 行代码")

    def counting_error(self, error_message):
        """统计出错"""
        QMessageBox.critical(self, "错误", error_message)
        self.reset_ui_state()
        self.status_label.setText("统计出错")

    def reset_ui_state(self):
        """重置UI状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)

    def update_summary(self, results):
        """更新汇总信息"""
        summary = results['summary']
        duration = summary['end_time'] - summary['start_time']

        summary_text = f"""统计汇总报告

统计时间: {summary['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
完成时间: {summary['end_time'].strftime('%Y-%m-%d %H:%M:%S')}
耗时: {duration.total_seconds():.2f} 秒

总文件数: {summary['total_files']}
总行数: {summary['total_lines']:,}

按文件类型统计:
"""

        # 按行数排序显示文件类型统计
        sorted_extensions = sorted(
            summary['by_extension'].items(),
            key=lambda x: x[1]['lines'],
            reverse=True
        )

        for ext, stats in sorted_extensions:
            summary_text += f"\n{ext}: {stats['files']} 个文件, {stats['lines']:,} 行"

        self.summary_text.setText(summary_text)

    def export_results(self):
        """导出结果"""
        if not self.results:
            QMessageBox.warning(self, "警告", "没有可导出的结果")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出统计结果",
            f"code_line_count_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV文件 (*.csv);;所有文件 (*.*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入表头
                    writer.writerow(['文件路径', '行数', '文件类型', '文件大小(字节)'])

                    # 写入文件数据
                    for file_info in self.results['files']:
                        writer.writerow([
                            file_info['path'],
                            file_info['lines'],
                            file_info['extension'],
                            file_info['size']
                        ])

                    # 写入汇总信息
                    writer.writerow([])
                    writer.writerow(['汇总信息'])
                    writer.writerow(['总文件数', self.results['summary']['total_files']])
                    writer.writerow(['总行数', self.results['summary']['total_lines']])

                    # 按文件类型汇总
                    writer.writerow([])
                    writer.writerow(['文件类型', '文件数', '行数'])
                    for ext, stats in self.results['summary']['by_extension'].items():
                        writer.writerow([ext, stats['files'], stats['lines']])

                QMessageBox.information(self, "成功", f"结果已导出到: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        if self.counter_thread and self.counter_thread.isRunning():
            self.counter_thread.cancel()
            self.counter_thread.wait()
        event.accept()
