from concurrent.futures import Thread<PERSON>oolExecutor
from PyQt5.QtCore import QObject, pyqtSignal
import traceback

class AsyncTaskManager(QObject):
    task_completed = pyqtSignal(object)
    task_error = pyqtSignal(str)
    
    def __init__(self, max_workers=4):
        super().__init__()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
    def run_async(self, func, *args, **kwargs):
        """异步执行任务"""
        future = self.executor.submit(func, *args, **kwargs)
        future.add_done_callback(self.handle_result)
        
    def handle_result(self, future):
        """处理异步任务结果"""
        try:
            result = future.result()
            self.task_completed.emit(result)
        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}\n{traceback.format_exc()}"
            self.task_error.emit(error_msg)
            
    def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=False)
