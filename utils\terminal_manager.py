"""
终端管理模块
用于向指定的终端窗口发送命令
"""
import subprocess
import time
import os
from typing import Optional, List
from .terminal_detector import TerminalInfo, TerminalDetector


class TerminalManager:
    """终端管理器，负责向终端发送命令"""
    
    @classmethod
    def send_command_to_terminal(cls, terminal_info: TerminalInfo, command: str) -> bool:
        """
        向指定终端发送命令
        
        Args:
            terminal_info: 终端信息
            command: 要发送的命令
            
        Returns:
            bool: 是否成功发送
        """
        try:
            # 首先激活目标窗口
            if not cls._activate_window(terminal_info.window_id):
                print(f"无法激活窗口 {terminal_info.window_id}")
                return False
            
            # 等待窗口激活
            time.sleep(0.2)
            
            # 发送命令
            success = cls._send_text_to_window(terminal_info.window_id, command)
            
            if success:
                print(f"命令已发送到终端: {terminal_info.title}")
                return True
            else:
                print(f"发送命令失败: {terminal_info.title}")
                return False
                
        except Exception as e:
            print(f"发送命令时出错: {e}")
            return False
    
    @classmethod
    def _activate_window(cls, window_id: str) -> bool:
        """激活指定窗口"""
        try:
            # 使用wmctrl激活窗口
            subprocess.run(['wmctrl', '-i', '-a', window_id], check=True)
            return True
        except subprocess.CalledProcessError:
            try:
                # 备用方法：使用xdotool
                subprocess.run(['xdotool', 'windowactivate', window_id], check=True)
                return True
            except subprocess.CalledProcessError:
                return False
    
    @classmethod
    def _send_text_to_window(cls, window_id: str, text: str) -> bool:
        """向指定窗口发送文本"""
        try:
            # 方法1: 使用xdotool直接发送文本
            # 首先清空当前行（如果有内容）
            subprocess.run(['xdotool', 'windowfocus', window_id], check=True)
            time.sleep(0.1)
            
            # 发送Ctrl+C以中断当前命令（如果有）
            subprocess.run(['xdotool', 'key', '--window', window_id, 'ctrl+c'], check=True)
            time.sleep(0.1)
            
            # 发送命令文本
            subprocess.run(['xdotool', 'type', '--window', window_id, text], check=True)
            time.sleep(0.1)
            
            # 发送回车键执行命令
            subprocess.run(['xdotool', 'key', '--window', window_id, 'Return'], check=True)
            
            return True
            
        except subprocess.CalledProcessError:
            # 备用方法：使用剪贴板
            return cls._send_via_clipboard(window_id, text)
    
    @classmethod
    def _send_via_clipboard(cls, window_id: str, text: str) -> bool:
        """通过剪贴板发送文本"""
        try:
            # 将文本复制到剪贴板
            process = subprocess.Popen(['xclip', '-selection', 'clipboard'], 
                                     stdin=subprocess.PIPE, text=True)
            process.communicate(input=text)
            
            if process.returncode != 0:
                # 尝试使用xsel
                process = subprocess.Popen(['xsel', '--clipboard', '--input'], 
                                         stdin=subprocess.PIPE, text=True)
                process.communicate(input=text)
                
                if process.returncode != 0:
                    print("无法访问剪贴板")
                    return False
            
            # 激活窗口并粘贴
            subprocess.run(['xdotool', 'windowfocus', window_id], check=True)
            time.sleep(0.1)
            
            # 发送Ctrl+C以中断当前命令
            subprocess.run(['xdotool', 'key', '--window', window_id, 'ctrl+c'], check=True)
            time.sleep(0.1)
            
            # 粘贴并执行
            subprocess.run(['xdotool', 'key', '--window', window_id, 'ctrl+shift+v'], check=True)
            time.sleep(0.1)
            subprocess.run(['xdotool', 'key', '--window', window_id, 'Return'], check=True)
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"剪贴板方法失败: {e}")
            return False
    
    @classmethod
    def open_new_terminal(cls, command: Optional[str] = None, 
                          work_dir: Optional[str] = None,
                          execute_fee: bool = False,
                          execute_runsim: bool = False,
                          execute_sshnew7: bool = True) -> bool:
        """
        打开新的终端标签页
        
        Args:
            command: 可选的初始命令或runsim命令
            work_dir: 工作目录路径，如果不指定则使用当前目录
            execute_fee: 是否执行fee命令
            execute_runsim: 是否执行runsim命令
            execute_sshnew7: 是否执行sshnew7命令（默认为True）
            
        Returns:
            bool: 是否成功打开
        """
        try:
            # 获取工作目录
            if not work_dir:
                work_dir = os.getcwd()
                
            # 创建一个临时脚本文件，包含所有需要执行的命令
            script_content = []
            
            # 添加可能需要的环境设置（确保PATH等环境变量都正确设置）
            script_content.append("#!/bin/sh")
            script_content.append("# 生成的RunSim终端初始化脚本")
            
            # 执行Ctrl+C
            script_content.append("echo '^C'")
            
            # 执行sshnew7
            if execute_sshnew7:
                script_content.append("if command -v sshnew7 > /dev/null 2>&1; then")
                script_content.append("    printf '\\n' | sshnew7")  # 发送换行来选择默认服务器
                script_content.append("else")
                script_content.append("    echo 'sshnew7命令不可用'")
                script_content.append("fi")
            
            # 切换到工作目录
            script_content.append(f"cd {work_dir}")
            
            # 执行fee
            if execute_fee:
                script_content.append("if command -v fee > /dev/null 2>&1; then")
                script_content.append("    fee")
                script_content.append("else")
                script_content.append("    echo 'fee命令不可用'")
                script_content.append("fi")
            
            # 执行runsim和command命令
            if execute_runsim:
                script_content.append("if command -v runsim > /dev/null 2>&1; then")
                script_content.append("    " + (command if command else "runsim"))
                script_content.append("else")
                script_content.append("    echo 'runsim命令不可用'")
                script_content.append("fi")
            
            # 添加一个永久循环以防止终端关闭
            script_content.append("exec $SHELL")
            
            # 创建临时脚本文件
            script_path = os.path.join(work_dir, ".runsim_terminal_init.sh")
            with open(script_path, "w") as f:
                f.write("\n".join(script_content))
            
            # 使脚本可执行
            os.chmod(script_path, 0o755)
            
            # 直接尝试各种终端的标签页打开方式
            # 按成功可能性排序的终端命令列表
            terminal_commands = [
                {
                    'name': 'gnome-terminal',  # GNOME Terminal
                    'cmd': ['gnome-terminal', '--tab', '--working-directory', work_dir, '--', script_path],
                },
                {
                    'name': 'konsole',  # KDE Konsole
                    'cmd': ['konsole', '--new-tab', '--workdir', work_dir, '-e', script_path],
                },
                {
                    'name': 'terminator',  # Terminator
                    'cmd': ['terminator', '--new-tab', '-e', script_path],
                },
                {
                    'name': 'tilix',  # Tilix
                    'cmd': ['tilix', '-a', 'app-new-session', '-e', script_path, '--working-directory', work_dir],
                },
                {
                    'name': 'xfce4-terminal',  # XFCE Terminal
                    'cmd': ['xfce4-terminal', '--tab', '-e', script_path, '--working-directory', work_dir],
                },
                {
                    'name': 'xterm',  # XTerm (无标签功能，但作为备选)
                    'cmd': ['xterm', '-e', script_path],
                },
                {
                    'name': 'kitty',  # Kitty
                    'cmd': ['kitty', '--title', 'RunSim Terminal', script_path],
                },
                {
                    'name': 'alacritty',  # Alacritty
                    'cmd': ['alacritty', '-e', script_path],
                },
            ]
            
            # 尝试在同一个窗口打开新标签页
            for term in terminal_commands:
                try:
                    print(f"尝试使用 {term['name']} 打开新标签页...")
                    # 使用subprocess.run而不是Popen，这样可以捕获错误
                    subprocess.run(term['cmd'], check=True)
                    print(f"✓ 已使用 {term['name']} 打开新标签页")
                    return True
                except FileNotFoundError:
                    continue
                except subprocess.CalledProcessError as e:
                    print(f"× 使用 {term['name']} 打开标签页失败: {e}")
                    continue
            
            print("未找到可用的终端程序，或无法打开新标签页")
            return False
            
        except Exception as e:
            print(f"打开新终端时出错: {e}")
            return False
    
    @classmethod
    def get_available_terminals(cls) -> List[TerminalInfo]:
        """获取所有可用的终端"""
        return TerminalDetector.detect_terminals()
    
    @classmethod
    def is_available(cls) -> bool:
        """检查终端管理功能是否可用"""
        return TerminalDetector.is_terminal_available()


if __name__ == "__main__":
    # 测试代码
    print("检查终端管理功能...")
    if not TerminalManager.is_available():
        print("终端管理功能不可用，请安装 wmctrl 和 xdotool")
        exit(1)
    
    print("获取可用终端...")
    terminals = TerminalManager.get_available_terminals()
    
    if not terminals:
        print("未找到打开的终端，尝试打开新终端...")
        if TerminalManager.open_new_terminal("echo 'Hello from RunSim!'"):
            print("新终端已打开")
        else:
            print("无法打开新终端")
    else:
        print(f"找到 {len(terminals)} 个终端:")
        for i, terminal in enumerate(terminals, 1):
            print(f"  {i}. {terminal}")
        
        # 测试发送命令到第一个终端
        if len(terminals) > 0:
            test_command = "echo 'Test command from RunSim'"
            print(f"\n测试发送命令到第一个终端: {test_command}")
            success = TerminalManager.send_command_to_terminal(terminals[0], test_command)
            print(f"发送结果: {'成功' if success else '失败'}")
