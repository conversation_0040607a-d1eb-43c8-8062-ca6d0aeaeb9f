"""
性能监控插件
"""
from PyQt5.QtWidgets import (
    QAction, QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QWidget, QLabel, QProgressBar, QTableWidget, QTableWidgetItem,
    QHeaderView, QPushButton, QComboBox, QCheckBox, QGroupBox,
    QSplitter, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, QSize, QRectF, pyqtSlot
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush, QFont

# 尝试导入QtChart模块，如果不可用则设置标志
QTCHART_AVAILABLE = False
try:
    from PyQt5.QtChart import QChart, QChartView, QLineSeries, QValueAxis, QDateTimeAxis
    QTCHART_AVAILABLE = True
except ImportError:
    print("警告: PyQt5.QtChart 模块不可用，图表功能将被禁用")
    print("提示: 可以通过运行 'pip install PyQtChart' 或 'pip install PyQt5-Charts' 安装此模块")

import time
import datetime
from plugins.base import PluginBase
from utils.performance_monitor import PerformanceMonitor, PerformanceMetrics

# 定义图表类
if QTCHART_AVAILABLE:
    # 如果QtChart可用，使用QChartView实现
    class PerformanceChart(QChartView):
        """性能图表控件"""

        def __init__(self, title, parent=None):
            """初始化性能图表"""
            super().__init__(parent)

            # 创建图表
            self.chart = QChart()
            self.chart.setTitle(title)
            self.chart.setAnimationOptions(QChart.SeriesAnimations)
            self.chart.legend().setVisible(True)
            self.chart.legend().setAlignment(Qt.AlignBottom)

            # 设置图表视图
            self.setChart(self.chart)
            self.setRenderHint(QPainter.Antialiasing)

            # 创建X轴（时间轴）
            self.axis_x = QDateTimeAxis()
            self.axis_x.setFormat("hh:mm:ss")
            self.axis_x.setTitleText("时间")
            self.chart.addAxis(self.axis_x, Qt.AlignBottom)

            # 创建Y轴
            self.axis_y = QValueAxis()
            self.axis_y.setTitleText("值")
            self.chart.addAxis(self.axis_y, Qt.AlignLeft)

            # 创建数据系列
            self.series = {}

        def add_series(self, name, color):
            """
            添加数据系列

            Args:
                name (str): 系列名称
                color (QColor): 系列颜色
            """
            series = QLineSeries()
            series.setName(name)
            pen = QPen(color)
            pen.setWidth(2)
            series.setPen(pen)

            self.chart.addSeries(series)
            series.attachAxis(self.axis_x)
            series.attachAxis(self.axis_y)

            self.series[name] = series

        def update_data(self, metrics_history):
            """
            更新图表数据

            Args:
                metrics_history (list): 性能指标历史记录
            """
            if not metrics_history:
                return

            # 清空所有系列数据
            for series in self.series.values():
                series.clear()

            # 添加数据点
            for metrics in metrics_history:
                timestamp = metrics.timestamp
                msecs = int(timestamp * 1000)  # 转换为毫秒

                # 更新CPU使用率系列
                if 'CPU使用率' in self.series:
                    self.series['CPU使用率'].append(msecs, metrics.cpu_usage)

                # 更新内存使用率系列
                if '内存使用率' in self.series:
                    self.series['内存使用率'].append(msecs, metrics.memory_usage)

                # 更新UI响应时间系列
                if 'UI响应时间' in self.series:
                    self.series['UI响应时间'].append(msecs, metrics.ui_response_time)

                # 更新渲染时间系列
                if '渲染时间' in self.series:
                    self.series['渲染时间'].append(msecs, metrics.render_time)

                # 更新日志处理速率系列
                if '日志处理速率' in self.series:
                    self.series['日志处理速率'].append(msecs, metrics.log_processing_rate)

            # 更新X轴范围
            if metrics_history:
                start_time = metrics_history[0].timestamp
                end_time = metrics_history[-1].timestamp
                self.axis_x.setRange(
                    datetime.datetime.fromtimestamp(start_time),
                    datetime.datetime.fromtimestamp(end_time)
                )

            # 更新Y轴范围
            # 找出所有系列的最大值
            max_value = 100  # 默认最大值为100
            for series_name, series in self.series.items():
                if series.count() > 0:
                    if series_name in ["UI响应时间", "渲染时间"]:
                        # 对于时间系列，我们需要特殊处理
                        series_max = 0
                        for i in range(series.count()):
                            point = series.at(i)
                            series_max = max(series_max, point.y())
                        # 如果最大值超过100，更新Y轴范围
                        if series_max > max_value:
                            max_value = series_max

            # 设置Y轴范围，确保所有数据都可见
            self.axis_y.setRange(0, max_value * 1.1)  # 增加10%的空间
else:
    # 如果QtChart不可用，创建一个简单的替代控件
    class PerformanceChart(QWidget):
        """性能图表控件的简单替代"""

        def __init__(self, title, parent=None):
            """初始化性能图表替代控件"""
            super().__init__(parent)
            self.title = title
            self.series = {}

            # 创建一个简单的布局
            layout = QVBoxLayout(self)

            # 添加一个标签，显示图表不可用的消息
            message = QLabel(
                "图表功能不可用，请安装 PyQt5.QtChart 模块。\n"
                "可以使用以下命令安装：\n"
                "pip install PyQtChart\n"
                "或者：\n"
                "pip install PyQt5-Charts"
            )
            message.setAlignment(Qt.AlignCenter)
            message.setStyleSheet("color: #666; font-style: italic;")
            layout.addWidget(message)

        def add_series(self, name, color):
            """
            添加数据系列（空实现）

            Args:
                name (str): 系列名称
                color (QColor): 系列颜色
            """
            self.series[name] = None

        def update_data(self, metrics_history):
            """
            更新图表数据（空实现）

            Args:
                metrics_history (list): 性能指标历史记录
            """
            pass

class PerformanceMonitorDialog(QDialog):
    """性能监控对话框"""

    def __init__(self, parent=None):
        """初始化性能监控对话框"""
        super().__init__(parent)
        self.setWindowTitle("性能监控")
        self.resize(1000, 600)

        # 创建性能监控器
        self.performance_monitor = PerformanceMonitor()
        self.performance_monitor.metrics_updated.connect(self.update_metrics)
        self.performance_monitor.performance_alert.connect(self.show_alert)

        # 设置默认警报冷却周期（1分钟）
        if hasattr(self.performance_monitor, 'set_alert_cooldown_period'):
            self.performance_monitor.set_alert_cooldown_period(60)

        # 创建布局
        self.init_ui()

        # 启动性能监控
        self.performance_monitor.start_monitoring(interval=1.0)

        # 创建更新定时器
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(1000)  # 每秒更新一次UI

        # 不再显示警告，因为已经在show_monitor方法中显示了提示消息

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()

        # 创建标签页控件
        tab_widget = QTabWidget()

        # 创建概览标签页
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)

        # 创建当前指标组
        metrics_group = QGroupBox("当前性能指标")
        metrics_layout = QHBoxLayout()

        # CPU使用率
        cpu_layout = QVBoxLayout()
        cpu_label = QLabel("CPU使用率")
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setRange(0, 100)
        self.cpu_bar.setAlignment(Qt.AlignCenter)
        self.cpu_bar.setTextVisible(True)
        self.cpu_bar.setFormat("%v%")
        cpu_layout.addWidget(cpu_label)
        cpu_layout.addWidget(self.cpu_bar)

        # 内存使用率
        memory_layout = QVBoxLayout()
        memory_label = QLabel("内存使用率")
        self.memory_bar = QProgressBar()
        self.memory_bar.setRange(0, 100)
        self.memory_bar.setAlignment(Qt.AlignCenter)
        self.memory_bar.setTextVisible(True)
        self.memory_bar.setFormat("%v%")
        memory_layout.addWidget(memory_label)
        memory_layout.addWidget(self.memory_bar)

        # UI响应时间
        ui_layout = QVBoxLayout()
        ui_label = QLabel("UI响应时间")
        self.ui_bar = QProgressBar()
        self.ui_bar.setRange(0, 100)  # 使用百分比表示
        self.ui_bar.setAlignment(Qt.AlignCenter)
        self.ui_bar.setTextVisible(True)
        self.ui_bar.setFormat("0.0 ms")
        ui_layout.addWidget(ui_label)
        ui_layout.addWidget(self.ui_bar)

        # 添加到指标布局
        metrics_layout.addLayout(cpu_layout)
        metrics_layout.addLayout(memory_layout)
        metrics_layout.addLayout(ui_layout)
        metrics_group.setLayout(metrics_layout)

        # 创建图表
        self.chart = PerformanceChart("性能趋势")

        # 如果QtChart可用，添加数据系列
        if QTCHART_AVAILABLE:
            self.chart.add_series("CPU使用率", QColor(255, 0, 0))
            self.chart.add_series("内存使用率", QColor(0, 0, 255))
            self.chart.add_series("UI响应时间", QColor(0, 255, 0))
            self.chart.add_series("渲染时间", QColor(255, 165, 0))  # 橙色

        # 添加到概览布局
        overview_layout.addWidget(metrics_group)
        overview_layout.addWidget(self.chart)

        # 创建详细信息标签页
        details_tab = QWidget()
        details_layout = QVBoxLayout(details_tab)

        # 创建详细指标表格
        self.metrics_table = QTableWidget()
        self.metrics_table.setColumnCount(7)
        self.metrics_table.setHorizontalHeaderLabels([
            "时间", "CPU使用率", "内存使用率", "内存使用量",
            "UI响应时间", "渲染时间", "日志处理速率"
        ])
        self.metrics_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # 添加到详细信息布局
        details_layout.addWidget(self.metrics_table)

        # 添加标签页
        tab_widget.addTab(overview_tab, "概览")
        tab_widget.addTab(details_tab, "详细信息")

        # 创建控制按钮
        control_layout = QHBoxLayout()

        # 更新间隔选择
        interval_label = QLabel("更新间隔:")
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["1秒", "2秒", "5秒", "10秒"])
        self.interval_combo.setCurrentIndex(0)
        self.interval_combo.currentIndexChanged.connect(self.change_interval)

        # 自动滚动选择
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)

        # 启用警报选择
        self.enable_alerts_check = QCheckBox("启用警报")
        self.enable_alerts_check.setChecked(True)
        self.enable_alerts_check.stateChanged.connect(self.toggle_alerts)

        # 警报冷却周期选择
        cooldown_label = QLabel("警报冷却周期:")
        self.cooldown_combo = QComboBox()
        self.cooldown_combo.addItems(["30秒", "1分钟", "5分钟", "10分钟", "30分钟"])
        self.cooldown_combo.setCurrentIndex(1)  # 默认1分钟
        self.cooldown_combo.currentIndexChanged.connect(self.change_cooldown)

        # 清除按钮
        clear_button = QPushButton("清除历史")
        clear_button.clicked.connect(self.clear_history)

        # 添加到控制布局
        control_layout.addWidget(interval_label)
        control_layout.addWidget(self.interval_combo)
        control_layout.addWidget(self.auto_scroll_check)
        control_layout.addWidget(self.enable_alerts_check)
        control_layout.addWidget(cooldown_label)
        control_layout.addWidget(self.cooldown_combo)
        control_layout.addStretch()
        control_layout.addWidget(clear_button)

        # 添加到主布局
        layout.addWidget(tab_widget)
        layout.addLayout(control_layout)

        self.setLayout(layout)

    def update_metrics(self, metrics):
        """
        更新性能指标

        Args:
            metrics (PerformanceMetrics): 性能指标
        """
        # 更新进度条
        self.cpu_bar.setValue(int(metrics.cpu_usage))
        self.memory_bar.setValue(int(metrics.memory_usage))

        # 将UI响应时间转换为百分比值（0-100）
        # UI响应时间范围是0-200ms，转换为0-100%
        ui_response_percent = min(int(metrics.ui_response_time / 2), 100)
        self.ui_bar.setValue(ui_response_percent)

        # 更新进度条提示文本
        self.cpu_bar.setFormat(f"{metrics.cpu_usage:.1f}%")
        self.memory_bar.setFormat(f"{metrics.memory_usage:.1f}%")
        self.ui_bar.setFormat(f"{metrics.ui_response_time:.1f} ms")

        # 设置进度条颜色
        self._set_progress_bar_color(self.cpu_bar, metrics.cpu_usage)
        self._set_progress_bar_color(self.memory_bar, metrics.memory_usage)
        self._set_progress_bar_color(self.ui_bar, ui_response_percent)

    def update_ui(self):
        """更新UI"""
        # 获取性能指标历史记录
        metrics_history = self.performance_monitor.get_metrics_history()

        # 更新图表（如果QtChart可用）
        if QTCHART_AVAILABLE or hasattr(self.chart, 'update_data'):
            self.chart.update_data(metrics_history)

        # 更新表格
        self._update_table(metrics_history)

    def _update_table(self, metrics_history):
        """
        更新指标表格

        Args:
            metrics_history (list): 性能指标历史记录
        """
        # 设置行数
        self.metrics_table.setRowCount(len(metrics_history))

        # 添加数据
        for i, metrics in enumerate(metrics_history):
            # 时间
            time_str = datetime.datetime.fromtimestamp(metrics.timestamp).strftime("%H:%M:%S")
            self.metrics_table.setItem(i, 0, QTableWidgetItem(time_str))

            # CPU使用率
            self.metrics_table.setItem(i, 1, QTableWidgetItem(f"{metrics.cpu_usage:.1f}%"))

            # 内存使用率
            self.metrics_table.setItem(i, 2, QTableWidgetItem(f"{metrics.memory_usage:.1f}%"))

            # 内存使用量
            self.metrics_table.setItem(i, 3, QTableWidgetItem(f"{metrics.memory_used:.1f} MB"))

            # UI响应时间
            self.metrics_table.setItem(i, 4, QTableWidgetItem(f"{metrics.ui_response_time:.1f} ms"))

            # 渲染时间
            self.metrics_table.setItem(i, 5, QTableWidgetItem(f"{metrics.render_time:.1f} ms"))

            # 日志处理速率
            self.metrics_table.setItem(i, 6, QTableWidgetItem(f"{metrics.log_processing_rate:.1f} 行/秒"))

        # 如果启用了自动滚动，滚动到底部
        if self.auto_scroll_check.isChecked():
            self.metrics_table.scrollToBottom()

    def _set_progress_bar_color(self, progress_bar, value, max_value=100):
        """
        设置进度条颜色

        Args:
            progress_bar (QProgressBar): 进度条
            value (float): 当前值
            max_value (float): 最大值
        """
        ratio = value / max_value

        if ratio < 0.6:
            # 绿色
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #4CAF50;
                }
            """)
        elif ratio < 0.8:
            # 黄色
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #FFC107;
                }
            """)
        else:
            # 红色
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #F44336;
                }
            """)

    def change_interval(self, index):
        """
        更改更新间隔

        Args:
            index (int): 下拉框索引
        """
        intervals = [1.0, 2.0, 5.0, 10.0]
        interval = intervals[index]

        # 更新性能监控器的更新间隔
        self.performance_monitor.stop_monitoring()
        self.performance_monitor.start_monitoring(interval=interval)

        # 更新UI更新定时器的间隔
        self.update_timer.setInterval(int(interval * 1000))

    def toggle_alerts(self, state):
        """
        启用或禁用性能警报

        Args:
            state (int): 复选框状态
        """
        enabled = (state == Qt.Checked)
        if hasattr(self.performance_monitor, 'enable_alerts'):
            self.performance_monitor.enable_alerts(enabled)

        # 更新UI
        self.cooldown_combo.setEnabled(enabled)

    def change_cooldown(self, index):
        """
        更改警报冷却周期

        Args:
            index (int): 下拉框索引
        """
        # 冷却周期（秒）
        cooldowns = [30, 60, 300, 600, 1800]  # 30秒, 1分钟, 5分钟, 10分钟, 30分钟
        cooldown = cooldowns[index]

        if hasattr(self.performance_monitor, 'set_alert_cooldown_period'):
            self.performance_monitor.set_alert_cooldown_period(cooldown)

        # 重置冷却时间，以便新的设置立即生效
        if hasattr(self.performance_monitor, 'reset_alert_cooldowns'):
            self.performance_monitor.reset_alert_cooldowns()

    def clear_history(self):
        """清除历史记录"""
        # 清空表格
        self.metrics_table.setRowCount(0)

        # 清空图表（如果QtChart可用）
        if QTCHART_AVAILABLE and hasattr(self.chart, 'series'):
            for series in self.chart.series.values():
                if series is not None:
                    series.clear()

        # 清空性能监控器的历史记录
        if hasattr(self.performance_monitor, 'clear_history'):
            self.performance_monitor.clear_history()

    @pyqtSlot(str, object)
    def show_alert(self, message, metrics):
        """
        显示性能警报

        Args:
            message (str): 警报消息
            metrics (PerformanceMetrics): 性能指标
        """
        # 创建自定义警报对话框
        alert_dialog = QMessageBox(self)
        alert_dialog.setWindowTitle("性能警报")
        alert_dialog.setText(message)
        alert_dialog.setIcon(QMessageBox.Warning)

        # 添加按钮
        ok_button = alert_dialog.addButton("确定", QMessageBox.AcceptRole)
        disable_button = alert_dialog.addButton("禁用警报", QMessageBox.RejectRole)

        # 设置窗口标志，确保不会始终保持在最上层
        alert_dialog.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)

        # 非模态显示对话框
        alert_dialog.setModal(False)
        alert_dialog.show()

        # 连接按钮点击事件
        alert_dialog.buttonClicked.connect(
            lambda button: self.handle_alert_response(button, alert_dialog, disable_button)
        )

    def handle_alert_response(self, button, dialog, disable_button):
        """
        处理警报对话框响应

        Args:
            button (QPushButton): 点击的按钮
            dialog (QMessageBox): 对话框
            disable_button (QPushButton): 禁用警报按钮
        """
        if button == disable_button:
            # 禁用警报
            self.enable_alerts_check.setChecked(False)
            # 关闭对话框
            dialog.close()

    def closeEvent(self, event):
        """
        关闭事件处理

        Args:
            event (QCloseEvent): 关闭事件
        """
        # 停止性能监控
        self.performance_monitor.stop_monitoring()

        # 停止更新定时器
        self.update_timer.stop()

        # 发出finished信号，通知插件对话框已关闭
        self.finished.emit(0)

        event.accept()

class PerformanceMonitorPlugin(PluginBase):
    """性能监控插件，实时监控界面响应性能"""

    @property
    def name(self):
        return "性能监控器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        if not QTCHART_AVAILABLE:
            return "实时监控界面响应性能 (需要安装 PyQt5.QtChart)"
        return "实时监控界面响应性能"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)

            # 如果QtChart不可用，更新提示但仍然启用菜单项
            if not QTCHART_AVAILABLE:
                self.menu_action.setToolTip("图表功能受限，但仍可使用基本监控功能")
                # 仍然连接到show_monitor，它会处理没有QtChart的情况
                self.menu_action.triggered.connect(self.show_monitor)
            else:
                self.menu_action.triggered.connect(self.show_monitor)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addSeparator()
                main_window.tools_menu.addAction(self.menu_action)

            # 初始化对话框引用
            self.monitor_dialog = None

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

        except Exception as e:
            print(f"初始化性能监控插件失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            from PyQt5.QtWidgets import QLabel, QToolButton

            # 创建状态指示器标签
            self.status_label = QLabel("性能监控器运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.monitor_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.monitor_dialog.isVisible():
                self.monitor_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.monitor_dialog.isMinimized():
                self.monitor_dialog.showNormal()
            # 将对话框置于前台
            self.monitor_dialog.raise_()
            self.monitor_dialog.activateWindow()

    def show_install_message(self):
        """显示安装提示消息"""
        QMessageBox.information(
            self.main_window,
            "需要安装依赖",
            "性能监控器需要 PyQt5.QtChart 模块。\n\n"
            "请使用以下命令安装：\n"
            "pip install PyQtChart\n\n"
            "或者：\n"
            "pip install PyQt5-Charts"
        )

    def show_monitor(self):
        """显示性能监控对话框"""
        if not QTCHART_AVAILABLE:
            # 显示提示消息，但仍然继续创建对话框
            QMessageBox.information(
                self.main_window,
                "功能受限",
                "性能监控器的图表功能受限，因为 PyQt5.QtChart 模块不可用。\n\n"
                "将显示基本的性能监控功能。\n\n"
                "如需完整功能，请使用以下命令安装：\n"
                "pip install PyQtChart\n\n"
                "或者：\n"
                "pip install PyQt5-Charts"
            )
            # 继续创建对话框，不返回

        if not self.monitor_dialog:
            # 创建对话框
            self.monitor_dialog = PerformanceMonitorDialog(self.main_window)

            # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
            self.monitor_dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

            # 连接关闭事件
            self.monitor_dialog.finished.connect(self.on_dialog_closed)

            # 添加最小化和后台运行按钮
            self.add_window_controls(self.monitor_dialog)

            # 显示状态指示器
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

        # 非模态显示对话框
        self.monitor_dialog.show()
        self.monitor_dialog.raise_()
        self.monitor_dialog.activateWindow()

    def add_window_controls(self, dialog):
        """添加窗口控制按钮"""
        try:
            # 查找底部按钮布局
            for child in dialog.children():
                if isinstance(child, QVBoxLayout):
                    main_layout = child
                    break
            else:
                return  # 未找到主布局

            # 创建按钮布局
            button_layout = QHBoxLayout()

            # 添加最小化按钮
            minimize_button = QPushButton("最小化窗口")
            minimize_button.clicked.connect(dialog.showMinimized)
            button_layout.addWidget(minimize_button)

            # 添加后台运行按钮
            background_button = QPushButton("后台运行")
            background_button.clicked.connect(lambda: self.run_in_background(dialog))
            button_layout.addWidget(background_button)

            # 添加到主布局
            main_layout.addLayout(button_layout)

        except Exception as e:
            print(f"添加窗口控制按钮失败: {str(e)}")

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("性能监控器正在后台运行", 5000)

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 隐藏状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(False)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(False)

        # 清理对话框引用
        self.monitor_dialog = None

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理性能监控插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        if hasattr(self, 'monitor_dialog') and self.monitor_dialog:
            try:
                self.monitor_dialog.close()
            except Exception as e:
                print(f"关闭性能监控对话框失败: {str(e)}")
