"""
差异导航组件

提供差异快速定位和导航功能，包括：
- 差异导航栏（缩略图概览）
- 差异跳转按钮
- 差异统计和定位
- 视觉增强功能
"""

import math
from typing import List, Optional, Tuple
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QScrollArea, QFrame, QSpinBox, QToolButton, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QRect, QTimer
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush, QFont, QFontMetrics

from ..models.file_diff import DiffLine, DiffLineType


class DiffNavigationBar(QWidget):
    """差异导航栏 - 显示文件缩略图和差异位置"""
    
    # 信号定义
    jump_to_line = pyqtSignal(int)  # 跳转到指定行
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedWidth(30)  # 固定宽度
        self.setMinimumHeight(100)  # 设置最小高度
        # 设置大小策略，允许垂直拉伸
        from PyQt5.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)
        
        # 数据
        self.diff_lines: List[DiffLine] = []
        self.total_lines = 0
        self.current_line = 0
        
        # 样式配置
        self.colors = {
            DiffLineType.ADD: QColor(40, 167, 69),      # 绿色 - 新增
            DiffLineType.DELETE: QColor(220, 53, 69),   # 红色 - 删除
            DiffLineType.MODIFY: QColor(255, 193, 7),   # 黄色 - 修改
            DiffLineType.CONTEXT: QColor(248, 249, 250) # 浅灰 - 上下文
        }
        self.current_line_color = QColor(74, 158, 255)  # 蓝色 - 当前行
        
        # 鼠标交互
        self.setMouseTracking(True)
        self.setCursor(Qt.PointingHandCursor)
    
    def set_diff_data(self, diff_lines: List[DiffLine]):
        """设置差异数据"""
        self.diff_lines = diff_lines
        self.total_lines = len(diff_lines)
        self.update()
    
    def set_current_line(self, line_number: int):
        """设置当前行"""
        self.current_line = line_number
        self.update()
    
    def paintEvent(self, event):
        """绘制导航栏"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        if not self.diff_lines or self.total_lines == 0:
            # 没有数据时显示占位符
            painter.fillRect(rect, QColor(248, 249, 250))

            # 绘制边框
            painter.setPen(QPen(QColor(200, 200, 200), 1))
            painter.drawRect(rect.adjusted(0, 0, -1, -1))

            # 绘制提示文字（垂直居中）
            painter.setPen(QColor(150, 150, 150))
            font = QFont("Arial", 7)
            painter.setFont(font)

            # 计算文字位置，使其在导航栏中央
            text_rect = QRect(rect.x(), rect.y() + rect.height() // 2 - 20, rect.width(), 40)
            painter.drawText(text_rect, Qt.AlignCenter | Qt.TextWordWrap, "差异\n导航")
            return
        
        # 计算每行的高度
        line_height = max(1, rect.height() / self.total_lines)
        
        # 绘制差异行
        for i, diff_line in enumerate(self.diff_lines):
            y = int(i * line_height)
            line_rect = QRect(0, y, rect.width(), max(1, int(line_height)))
            
            # 选择颜色
            color = self.colors.get(diff_line.line_type, self.colors[DiffLineType.CONTEXT])
            painter.fillRect(line_rect, color)
        
        # 绘制当前行指示器
        if 0 <= self.current_line < self.total_lines:
            y = int(self.current_line * line_height)
            current_rect = QRect(0, y, rect.width(), max(2, int(line_height)))
            painter.fillRect(current_rect, self.current_line_color)

        # 绘制边框
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.drawRect(rect.adjusted(0, 0, -1, -1))
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton and self.total_lines > 0:
            # 计算点击的行号
            click_y = event.y()
            line_number = int(click_y * self.total_lines / self.height())
            line_number = max(0, min(line_number, self.total_lines - 1))
            
            self.jump_to_line.emit(line_number)


class DiffNavigationControls(QWidget):
    """差异导航控制组件"""
    
    # 信号定义
    previous_diff = pyqtSignal()
    next_diff = pyqtSignal()
    first_diff = pyqtSignal()
    last_diff = pyqtSignal()
    jump_to_line = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.diff_positions: List[int] = []  # 差异行位置列表
        self.current_diff_index = -1
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 差异导航按钮
        self.first_btn = QToolButton()
        self.first_btn.setText("⏮")
        self.first_btn.setToolTip("第一个差异 (Ctrl+Home)")
        
        self.prev_btn = QToolButton()
        self.prev_btn.setText("⏪")
        self.prev_btn.setToolTip("上一个差异 (F7)")
        
        self.next_btn = QToolButton()
        self.next_btn.setText("⏩")
        self.next_btn.setToolTip("下一个差异 (F8)")
        
        self.last_btn = QToolButton()
        self.last_btn.setText("⏭")
        self.last_btn.setToolTip("最后一个差异 (Ctrl+End)")
        
        # 差异统计标签
        self.stats_label = QLabel("差异: 0/0")
        self.stats_label.setMinimumWidth(80)
        
        # 行号跳转
        jump_label = QLabel("跳转到行:")
        self.line_spinbox = QSpinBox()
        self.line_spinbox.setMinimum(1)
        self.line_spinbox.setMaximum(1)
        self.line_spinbox.setMinimumWidth(60)
        
        self.jump_btn = QPushButton("跳转")
        self.jump_btn.setMaximumWidth(50)
        
        # 布局
        layout.addWidget(self.first_btn)
        layout.addWidget(self.prev_btn)
        layout.addWidget(self.next_btn)
        layout.addWidget(self.last_btn)
        layout.addWidget(QFrame())  # 分隔符
        layout.addWidget(self.stats_label)
        layout.addWidget(QFrame())  # 分隔符
        layout.addWidget(jump_label)
        layout.addWidget(self.line_spinbox)
        layout.addWidget(self.jump_btn)
        layout.addStretch()
        
        # 初始状态
        self._update_button_states()
    
    def _connect_signals(self):
        """连接信号"""
        self.first_btn.clicked.connect(self.first_diff.emit)
        self.prev_btn.clicked.connect(self.previous_diff.emit)
        self.next_btn.clicked.connect(self.next_diff.emit)
        self.last_btn.clicked.connect(self.last_diff.emit)
        
        self.jump_btn.clicked.connect(self._on_jump_clicked)
        # QSpinBox没有returnPressed信号，使用editingFinished信号代替
        self.line_spinbox.editingFinished.connect(self._on_jump_clicked)
    
    def _on_jump_clicked(self):
        """跳转按钮点击事件"""
        line_number = self.line_spinbox.value() - 1  # 转换为0基索引
        self.jump_to_line.emit(line_number)
    
    def set_diff_data(self, diff_lines: List[DiffLine]):
        """设置差异数据"""
        # 找出所有差异行的位置
        self.diff_positions = []
        for i, diff_line in enumerate(diff_lines):
            if diff_line.line_type != DiffLineType.CONTEXT:
                self.diff_positions.append(i)
        
        # 更新行号范围
        total_lines = len(diff_lines)
        self.line_spinbox.setMaximum(max(1, total_lines))
        
        # 更新统计信息
        self._update_stats()
        self._update_button_states()
    
    def set_current_diff_index(self, index: int):
        """设置当前差异索引"""
        self.current_diff_index = index
        self._update_stats()
        self._update_button_states()
    
    def _update_stats(self):
        """更新统计信息"""
        total_diffs = len(self.diff_positions)
        current_display = self.current_diff_index + 1 if self.current_diff_index >= 0 else 0
        self.stats_label.setText(f"差异: {current_display}/{total_diffs}")
    
    def _update_button_states(self):
        """更新按钮状态"""
        has_diffs = len(self.diff_positions) > 0
        has_current = self.current_diff_index >= 0
        
        self.first_btn.setEnabled(has_diffs and self.current_diff_index > 0)
        self.prev_btn.setEnabled(has_diffs and self.current_diff_index > 0)
        self.next_btn.setEnabled(has_diffs and self.current_diff_index < len(self.diff_positions) - 1)
        self.last_btn.setEnabled(has_diffs and self.current_diff_index < len(self.diff_positions) - 1)
    
    def get_diff_positions(self) -> List[int]:
        """获取差异位置列表"""
        return self.diff_positions.copy()


class DiffNavigator(QWidget):
    """差异导航器 - 整合导航栏和控制组件"""
    
    # 信号定义
    jump_to_line = pyqtSignal(int)
    current_diff_changed = pyqtSignal(int)  # 当前差异改变
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.diff_lines: List[DiffLine] = []
        self.current_diff_index = -1
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 导航控制组件
        self.controls = DiffNavigationControls()
        layout.addWidget(self.controls)
        
        # 导航栏（可选显示）
        self.navigation_bar = DiffNavigationBar()
        layout.addWidget(self.navigation_bar, 1)  # stretch=1，让导航栏占用大部分空间

        # 默认显示导航栏
        self.navigation_bar.setVisible(True)
    
    def _connect_signals(self):
        """连接信号"""
        # 控制组件信号
        self.controls.previous_diff.connect(self._go_to_previous_diff)
        self.controls.next_diff.connect(self._go_to_next_diff)
        self.controls.first_diff.connect(self._go_to_first_diff)
        self.controls.last_diff.connect(self._go_to_last_diff)
        self.controls.jump_to_line.connect(self.jump_to_line.emit)
        
        # 导航栏信号
        self.navigation_bar.jump_to_line.connect(self.jump_to_line.emit)
    
    def set_diff_data(self, diff_lines: List[DiffLine]):
        """设置差异数据"""
        self.diff_lines = diff_lines
        self.current_diff_index = -1
        
        # 更新子组件
        self.controls.set_diff_data(diff_lines)
        self.navigation_bar.set_diff_data(diff_lines)
    
    def set_navigation_bar_visible(self, visible: bool):
        """设置导航栏可见性"""
        self.navigation_bar.setVisible(visible)
    
    def _go_to_previous_diff(self):
        """跳转到上一个差异"""
        diff_positions = self.controls.get_diff_positions()
        if diff_positions and self.current_diff_index > 0:
            self.current_diff_index -= 1
            self._update_current_diff()
    
    def _go_to_next_diff(self):
        """跳转到下一个差异"""
        diff_positions = self.controls.get_diff_positions()
        if diff_positions and self.current_diff_index < len(diff_positions) - 1:
            self.current_diff_index += 1
            self._update_current_diff()
    
    def _go_to_first_diff(self):
        """跳转到第一个差异"""
        diff_positions = self.controls.get_diff_positions()
        if diff_positions:
            self.current_diff_index = 0
            self._update_current_diff()
    
    def _go_to_last_diff(self):
        """跳转到最后一个差异"""
        diff_positions = self.controls.get_diff_positions()
        if diff_positions:
            self.current_diff_index = len(diff_positions) - 1
            self._update_current_diff()
    
    def _update_current_diff(self):
        """更新当前差异"""
        diff_positions = self.controls.get_diff_positions()
        if 0 <= self.current_diff_index < len(diff_positions):
            line_number = diff_positions[self.current_diff_index]
            
            # 更新子组件
            self.controls.set_current_diff_index(self.current_diff_index)
            self.navigation_bar.set_current_line(line_number)
            
            # 发射信号
            self.jump_to_line.emit(line_number)
            self.current_diff_changed.emit(self.current_diff_index)
    
    def update_current_line(self, line_number: int):
        """更新当前行（从外部调用）"""
        self.navigation_bar.set_current_line(line_number)
        
        # 更新当前差异索引
        diff_positions = self.controls.get_diff_positions()
        for i, pos in enumerate(diff_positions):
            if pos == line_number:
                self.current_diff_index = i
                self.controls.set_current_diff_index(i)
                break
