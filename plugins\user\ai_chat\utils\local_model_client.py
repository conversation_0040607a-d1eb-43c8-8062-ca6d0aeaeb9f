"""
本地AI模型客户端

支持本地部署的AI模型，包括GGUF、ONNX等格式的模型。
提供与OpenAI API兼容的接口。
"""

import os
import json
import requests
import subprocess
import threading
import time
from typing import Dict, List, Any, Optional, Generator
from PyQt5.QtCore import QObject, pyqtSignal


class LocalModelInfo:
    """本地模型信息"""
    
    def __init__(self, name: str, path: str, model_type: str, config: Dict[str, Any] = None):
        self.name = name
        self.path = path
        # 支持的模型格式：'gguf', 'onnx', 'pytorch', 'ollama', 'safetensors', 'transformers', 'llamacpp', 'gptq', 'awq'
        self.model_type = model_type
        self.config = config or {}
        self.is_loaded = False
        self.server_port = None
        self.process = None
    
    def to_dict(self):
        """转换为字典"""
        return {
            'name': self.name,
            'path': self.path,
            'model_type': self.model_type,
            'config': self.config,
            'is_loaded': self.is_loaded,
            'server_port': self.server_port
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建"""
        model = cls(
            name=data['name'],
            path=data['path'],
            model_type=data['model_type'],
            config=data.get('config', {})
        )
        model.is_loaded = data.get('is_loaded', False)
        model.server_port = data.get('server_port')
        return model


class LocalModelManager(QObject):
    """本地模型管理器"""
    
    # 信号
    model_loaded = pyqtSignal(str)  # model_name
    model_unloaded = pyqtSignal(str)  # model_name
    loading_progress = pyqtSignal(str, int)  # model_name, progress
    error_occurred = pyqtSignal(str, str)  # model_name, error
    
    def __init__(self):
        super().__init__()
        self.models: Dict[str, LocalModelInfo] = {}
        self.config_file = "local_models.json"
        self.base_port = 11434  # 起始端口
        self.load_models_config()
    
    def load_models_config(self):
        """加载模型配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for model_data in data.get('models', []):
                        model = LocalModelInfo.from_dict(model_data)
                        self.models[model.name] = model
        except Exception as e:
            print(f"加载模型配置失败: {e}")
    
    def save_models_config(self):
        """保存模型配置"""
        try:
            data = {
                'models': [model.to_dict() for model in self.models.values()]
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存模型配置失败: {e}")
    
    def add_model(self, name: str, path: str, model_type: str, config: Dict[str, Any] = None) -> bool:
        """添加模型"""
        try:
            # 检查文件是否存在
            if not os.path.exists(path):
                self.error_occurred.emit(name, f"模型文件不存在: {path}")
                return False
            
            # 检查模型是否已存在
            if name in self.models:
                self.error_occurred.emit(name, "模型名称已存在")
                return False
            
            # 创建模型信息
            model = LocalModelInfo(name, path, model_type, config)
            self.models[name] = model
            
            # 保存配置
            self.save_models_config()
            
            return True
            
        except Exception as e:
            self.error_occurred.emit(name, f"添加模型失败: {str(e)}")
            return False
    
    def remove_model(self, name: str) -> bool:
        """移除模型"""
        try:
            if name not in self.models:
                return False
            
            # 如果模型正在运行，先停止
            if self.models[name].is_loaded:
                self.unload_model(name)
            
            # 删除模型
            del self.models[name]
            
            # 保存配置
            self.save_models_config()
            
            return True
            
        except Exception as e:
            self.error_occurred.emit(name, f"移除模型失败: {str(e)}")
            return False
    
    def get_available_port(self) -> int:
        """获取可用端口"""
        import socket
        
        for port in range(self.base_port, self.base_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        
        raise Exception("无法找到可用端口")
    
    def load_model(self, name: str) -> bool:
        """加载模型"""
        if name not in self.models:
            self.error_occurred.emit(name, "模型不存在")
            return False
        
        model = self.models[name]
        if model.is_loaded:
            return True
        
        try:
            # 根据模型类型启动相应的服务
            if model.model_type == 'ollama':
                return self._load_ollama_model(model)
            elif model.model_type in ['gguf', 'llamacpp']:
                return self._load_gguf_model(model)
            elif model.model_type in ['transformers', 'pytorch', 'safetensors']:
                return self._load_transformers_model(model)
            elif model.model_type in ['gptq', 'awq']:
                return self._load_quantized_model(model)
            elif model.model_type == 'onnx':
                return self._load_onnx_model(model)
            else:
                self.error_occurred.emit(name, f"不支持的模型类型: {model.model_type}")
                return False
                
        except Exception as e:
            self.error_occurred.emit(name, f"加载模型失败: {str(e)}")
            return False
    
    def _load_ollama_model(self, model: LocalModelInfo) -> bool:
        """加载Ollama模型"""
        try:
            # 检查Ollama是否安装
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode != 0:
                self.error_occurred.emit(model.name, "Ollama未安装或未启动")
                return False
            
            # 检查模型是否已在Ollama中
            if model.name not in result.stdout:
                # 尝试从文件加载模型
                if os.path.exists(model.path):
                    load_result = subprocess.run(
                        ['ollama', 'create', model.name, '-f', model.path],
                        capture_output=True, text=True
                    )
                    if load_result.returncode != 0:
                        self.error_occurred.emit(model.name, f"Ollama加载失败: {load_result.stderr}")
                        return False
            
            # 启动模型
            run_result = subprocess.run(
                ['ollama', 'run', model.name],
                capture_output=True, text=True
            )
            
            if run_result.returncode == 0:
                model.is_loaded = True
                model.server_port = 11434  # Ollama默认端口
                self.model_loaded.emit(model.name)
                self.save_models_config()
                return True
            else:
                self.error_occurred.emit(model.name, f"启动模型失败: {run_result.stderr}")
                return False
                
        except Exception as e:
            self.error_occurred.emit(model.name, f"Ollama模型加载失败: {str(e)}")
            return False
    
    def _load_gguf_model(self, model: LocalModelInfo) -> bool:
        """加载GGUF模型（使用llama.cpp）"""
        try:
            # 检查llama.cpp是否可用
            # 这里需要用户预先安装llama.cpp或提供可执行文件路径
            llama_cpp_path = model.config.get('llama_cpp_path', 'llama-server')
            
            # 获取可用端口
            port = self.get_available_port()
            
            # 启动llama.cpp服务器
            cmd = [
                llama_cpp_path,
                '-m', model.path,
                '--port', str(port),
                '--host', '127.0.0.1',
                '--ctx-size', str(model.config.get('context_size', 2048)),
                '--threads', str(model.config.get('threads', 4))
            ]
            
            # 在后台启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务器启动
            time.sleep(3)
            
            # 检查服务器是否启动成功
            try:
                response = requests.get(f'http://127.0.0.1:{port}/health', timeout=5)
                if response.status_code == 200:
                    model.is_loaded = True
                    model.server_port = port
                    model.process = process
                    self.model_loaded.emit(model.name)
                    self.save_models_config()
                    return True
                else:
                    process.terminate()
                    self.error_occurred.emit(model.name, "服务器启动失败")
                    return False
            except requests.RequestException:
                process.terminate()
                self.error_occurred.emit(model.name, "无法连接到模型服务器")
                return False
                
        except Exception as e:
            self.error_occurred.emit(model.name, f"GGUF模型加载失败: {str(e)}")
            return False

    def _load_transformers_model(self, model: LocalModelInfo) -> bool:
        """加载Transformers模型（使用text-generation-webui或vLLM）"""
        try:
            # 检查是否有可用的推理引擎
            inference_engine = model.config.get('inference_engine', 'text-generation-webui')

            if inference_engine == 'vllm':
                return self._load_vllm_model(model)
            else:
                return self._load_text_generation_webui_model(model)

        except Exception as e:
            self.error_occurred.emit(model.name, f"Transformers模型加载失败: {str(e)}")
            return False

    def _load_vllm_model(self, model: LocalModelInfo) -> bool:
        """使用vLLM加载模型"""
        try:
            # 获取可用端口
            port = self.get_available_port()

            # 构建vLLM启动命令
            cmd = [
                'python', '-m', 'vllm.entrypoints.openai.api_server',
                '--model', model.path,
                '--host', '127.0.0.1',
                '--port', str(port),
                '--max-model-len', str(model.config.get('max_model_len', 2048)),
                '--gpu-memory-utilization', str(model.config.get('gpu_memory_utilization', 0.9))
            ]

            # 添加量化参数
            if model.config.get('quantization'):
                cmd.extend(['--quantization', model.config['quantization']])

            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 等待服务器启动
            max_wait = 60  # 最多等待60秒
            for _ in range(max_wait):
                time.sleep(1)
                try:
                    response = requests.get(f'http://127.0.0.1:{port}/health', timeout=2)
                    if response.status_code == 200:
                        model.is_loaded = True
                        model.server_port = port
                        model.process = process
                        self.model_loaded.emit(model.name)
                        self.save_models_config()
                        return True
                except requests.RequestException:
                    continue

            # 启动失败
            process.terminate()
            self.error_occurred.emit(model.name, "vLLM服务器启动超时")
            return False

        except Exception as e:
            self.error_occurred.emit(model.name, f"vLLM模型加载失败: {str(e)}")
            return False

    def _load_text_generation_webui_model(self, model: LocalModelInfo) -> bool:
        """使用text-generation-webui加载模型"""
        try:
            # 获取可用端口
            port = self.get_available_port()

            # 构建启动命令
            webui_path = model.config.get('webui_path', 'text-generation-webui')
            cmd = [
                'python', os.path.join(webui_path, 'server.py'),
                '--model', model.path,
                '--listen-host', '127.0.0.1',
                '--listen-port', str(port),
                '--api'
            ]

            # 添加GPU参数
            if model.config.get('use_gpu', True):
                cmd.append('--gpu-memory')
                cmd.append(str(model.config.get('gpu_memory', 0.8)))

            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 等待服务器启动
            max_wait = 60
            for _ in range(max_wait):
                time.sleep(1)
                try:
                    response = requests.get(f'http://127.0.0.1:{port}/api/v1/models', timeout=2)
                    if response.status_code == 200:
                        model.is_loaded = True
                        model.server_port = port
                        model.process = process
                        self.model_loaded.emit(model.name)
                        self.save_models_config()
                        return True
                except requests.RequestException:
                    continue

            # 启动失败
            process.terminate()
            self.error_occurred.emit(model.name, "text-generation-webui启动超时")
            return False

        except Exception as e:
            self.error_occurred.emit(model.name, f"text-generation-webui模型加载失败: {str(e)}")
            return False

    def _load_quantized_model(self, model: LocalModelInfo) -> bool:
        """加载量化模型（GPTQ/AWQ）"""
        try:
            # 量化模型通常需要特殊的推理引擎
            quantization_type = model.model_type.upper()

            # 获取可用端口
            port = self.get_available_port()

            # 根据量化类型选择推理引擎
            if quantization_type == 'GPTQ':
                # 使用AutoGPTQ或ExLlama
                inference_engine = model.config.get('inference_engine', 'autogptq')
                if inference_engine == 'exllama':
                    return self._load_exllama_model(model, port)
                else:
                    return self._load_autogptq_model(model, port)
            elif quantization_type == 'AWQ':
                # 使用AutoAWQ
                return self._load_autoawq_model(model, port)
            else:
                self.error_occurred.emit(model.name, f"不支持的量化类型: {quantization_type}")
                return False

        except Exception as e:
            self.error_occurred.emit(model.name, f"量化模型加载失败: {str(e)}")
            return False

    def _load_autogptq_model(self, model: LocalModelInfo, port: int) -> bool:
        """使用AutoGPTQ加载GPTQ模型"""
        try:
            # 创建简单的API服务器脚本
            server_script = f"""
import torch
from transformers import AutoTokenizer
from auto_gptq import AutoGPTQForCausalLM
from flask import Flask, request, jsonify
import json

app = Flask(__name__)

# 加载模型和分词器
model_path = "{model.path}"
tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoGPTQForCausalLM.from_quantized(
    model_path,
    device="cuda:0" if torch.cuda.is_available() else "cpu",
    use_triton=False
)

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    data = request.json
    messages = data.get('messages', [])
    max_tokens = data.get('max_tokens', 512)
    temperature = data.get('temperature', 0.7)

    # 构建提示
    prompt = ""
    for msg in messages:
        role = msg.get('role', '')
        content = msg.get('content', '')
        if role == 'user':
            prompt += f"User: {{content}}\\n"
        elif role == 'assistant':
            prompt += f"Assistant: {{content}}\\n"
    prompt += "Assistant: "

    # 生成回复
    inputs = tokenizer(prompt, return_tensors="pt")
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_new_tokens=max_tokens,
            temperature=temperature,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )

    response_text = tokenizer.decode(outputs[0][inputs.input_ids.shape[1]:], skip_special_tokens=True)

    return jsonify({{
        "choices": [{{
            "message": {{
                "role": "assistant",
                "content": response_text
            }}
        }}]
    }})

@app.route('/health', methods=['GET'])
def health():
    return jsonify({{"status": "ok"}})

if __name__ == '__main__':
    app.run(host='127.0.0.1', port={port})
"""

            # 保存服务器脚本
            script_path = f"gptq_server_{model.name}_{port}.py"
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(server_script)

            # 启动服务器
            process = subprocess.Popen(
                ['python', script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 等待服务器启动
            max_wait = 60
            for _ in range(max_wait):
                time.sleep(1)
                try:
                    response = requests.get(f'http://127.0.0.1:{port}/health', timeout=2)
                    if response.status_code == 200:
                        model.is_loaded = True
                        model.server_port = port
                        model.process = process
                        model.config['server_script'] = script_path
                        self.model_loaded.emit(model.name)
                        self.save_models_config()
                        return True
                except requests.RequestException:
                    continue

            # 启动失败
            process.terminate()
            os.remove(script_path)
            self.error_occurred.emit(model.name, "AutoGPTQ服务器启动超时")
            return False

        except Exception as e:
            self.error_occurred.emit(model.name, f"AutoGPTQ模型加载失败: {str(e)}")
            return False

    def _load_autoawq_model(self, model: LocalModelInfo, port: int) -> bool:
        """使用AutoAWQ加载AWQ模型"""
        # 类似AutoGPTQ的实现，但使用AutoAWQ库
        try:
            # 这里可以实现AWQ模型的加载逻辑
            # 由于篇幅限制，暂时返回False并提示用户
            self.error_occurred.emit(model.name, "AWQ模型支持正在开发中，请使用其他格式")
            return False
        except Exception as e:
            self.error_occurred.emit(model.name, f"AutoAWQ模型加载失败: {str(e)}")
            return False

    def _load_exllama_model(self, model: LocalModelInfo, port: int) -> bool:
        """使用ExLlama加载GPTQ模型"""
        # ExLlama的实现
        try:
            self.error_occurred.emit(model.name, "ExLlama模型支持正在开发中，请使用AutoGPTQ")
            return False
        except Exception as e:
            self.error_occurred.emit(model.name, f"ExLlama模型加载失败: {str(e)}")
            return False

    def _load_onnx_model(self, model: LocalModelInfo) -> bool:
        """加载ONNX模型"""
        try:
            # ONNX模型需要使用onnxruntime
            port = self.get_available_port()

            # 创建ONNX推理服务器
            server_script = f"""
import onnxruntime as ort
from transformers import AutoTokenizer
from flask import Flask, request, jsonify
import numpy as np

app = Flask(__name__)

# 加载ONNX模型和分词器
model_path = "{model.path}"
tokenizer_path = model.config.get('tokenizer_path', model_path)

session = ort.InferenceSession(model_path)
tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    data = request.json
    messages = data.get('messages', [])
    max_tokens = data.get('max_tokens', 512)

    # 构建提示
    prompt = ""
    for msg in messages:
        role = msg.get('role', '')
        content = msg.get('content', '')
        if role == 'user':
            prompt += f"User: {{content}}\\n"
        elif role == 'assistant':
            prompt += f"Assistant: {{content}}\\n"
    prompt += "Assistant: "

    # 分词
    inputs = tokenizer(prompt, return_tensors="np", padding=True, truncation=True)

    # ONNX推理
    outputs = session.run(None, dict(inputs))

    # 解码输出（这里需要根据具体模型调整）
    # 简化实现，实际需要根据模型输出格式调整
    response_text = "ONNX模型推理结果（需要根据具体模型调整解码逻辑）"

    return jsonify({{
        "choices": [{{
            "message": {{
                "role": "assistant",
                "content": response_text
            }}
        }}]
    }})

@app.route('/health', methods=['GET'])
def health():
    return jsonify({{"status": "ok"}})

if __name__ == '__main__':
    app.run(host='127.0.0.1', port={port})
"""

            # 保存并启动服务器
            script_path = f"onnx_server_{model.name}_{port}.py"
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(server_script)

            process = subprocess.Popen(
                ['python', script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 等待启动
            time.sleep(5)
            try:
                response = requests.get(f'http://127.0.0.1:{port}/health', timeout=5)
                if response.status_code == 200:
                    model.is_loaded = True
                    model.server_port = port
                    model.process = process
                    model.config['server_script'] = script_path
                    self.model_loaded.emit(model.name)
                    self.save_models_config()
                    return True
            except requests.RequestException:
                pass

            process.terminate()
            os.remove(script_path)
            self.error_occurred.emit(model.name, "ONNX服务器启动失败")
            return False

        except Exception as e:
            self.error_occurred.emit(model.name, f"ONNX模型加载失败: {str(e)}")
            return False
    
    def unload_model(self, name: str) -> bool:
        """卸载模型"""
        if name not in self.models:
            return False
        
        model = self.models[name]
        if not model.is_loaded:
            return True
        
        try:
            # 停止进程
            if model.process:
                model.process.terminate()
                model.process.wait(timeout=10)
                model.process = None
            
            model.is_loaded = False
            model.server_port = None
            
            self.model_unloaded.emit(name)
            self.save_models_config()
            
            return True
            
        except Exception as e:
            self.error_occurred.emit(name, f"卸载模型失败: {str(e)}")
            return False
    
    def get_model_list(self) -> List[LocalModelInfo]:
        """获取模型列表"""
        return list(self.models.values())
    
    def get_loaded_models(self) -> List[LocalModelInfo]:
        """获取已加载的模型列表"""
        return [model for model in self.models.values() if model.is_loaded]
    
    def is_model_loaded(self, name: str) -> bool:
        """检查模型是否已加载"""
        return name in self.models and self.models[name].is_loaded
    
    def get_model_endpoint(self, name: str) -> Optional[str]:
        """获取模型的API端点"""
        if name in self.models and self.models[name].is_loaded:
            port = self.models[name].server_port
            return f"http://127.0.0.1:{port}"
        return None


class LocalModelClient(QObject):
    """本地模型API客户端"""
    
    def __init__(self, model_manager: LocalModelManager):
        super().__init__()
        self.model_manager = model_manager
        self.current_model = None
    
    def set_model(self, model_name: str) -> bool:
        """设置当前使用的模型"""
        if self.model_manager.is_model_loaded(model_name):
            self.current_model = model_name
            return True
        return False
    
    def chat_completion(self, messages: List[Dict], **kwargs) -> Dict[str, Any]:
        """聊天完成API调用（兼容OpenAI格式）"""
        if not self.current_model:
            return {"error": "未设置模型"}
        
        endpoint = self.model_manager.get_model_endpoint(self.current_model)
        if not endpoint:
            return {"error": "模型未加载"}
        
        try:
            # 构建请求数据
            data = {
                "model": self.current_model,
                "messages": messages,
                "max_tokens": kwargs.get('max_tokens', 2048),
                "temperature": kwargs.get('temperature', 0.7),
                "stream": kwargs.get('stream', False)
            }
            
            # 发送请求
            response = requests.post(
                f"{endpoint}/v1/chat/completions",
                json=data,
                timeout=kwargs.get('timeout', 30)
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API请求失败: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def stream_chat_completion(self, messages: List[Dict], **kwargs) -> Generator[Dict[str, Any], None, None]:
        """流式聊天完成"""
        if not self.current_model:
            yield {"error": "未设置模型"}
            return
        
        endpoint = self.model_manager.get_model_endpoint(self.current_model)
        if not endpoint:
            yield {"error": "模型未加载"}
            return
        
        try:
            # 构建请求数据
            data = {
                "model": self.current_model,
                "messages": messages,
                "max_tokens": kwargs.get('max_tokens', 2048),
                "temperature": kwargs.get('temperature', 0.7),
                "stream": True
            }
            
            # 发送流式请求
            response = requests.post(
                f"{endpoint}/v1/chat/completions",
                json=data,
                stream=True,
                timeout=kwargs.get('timeout', 30)
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data_str = line[6:]
                            if data_str.strip() == '[DONE]':
                                break
                            try:
                                data = json.loads(data_str)
                                yield data
                            except json.JSONDecodeError:
                                continue
            else:
                yield {"error": f"API请求失败: {response.status_code}"}
                
        except Exception as e:
            yield {"error": f"请求失败: {str(e)}"}
