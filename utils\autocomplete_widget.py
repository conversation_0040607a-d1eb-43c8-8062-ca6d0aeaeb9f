"""
智能自动补全UI组件
提供下拉建议列表、键盘导航和鼠标选择功能
"""
from PyQt5.QtWidgets import (
    QLineEdit, QListWidget, QListWidgetItem, QVBoxLayout,
    QWidget, QFrame, QApplication, QAbstractItemView, QDesktopWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QEvent, QRect, QPoint
from PyQt5.QtGui import QKeyEvent, QFocusEvent, QPalette, QFont, QResizeEvent, QMoveEvent

from models.autocomplete_model import AutoCompleteModel
from utils.autocomplete_engine import AutoCompleteEngine


class AutoCompleteListWidget(QListWidget):
    """自动补全建议列表组件"""
    
    # 信号定义
    item_selected = pyqtSignal(str)  # 项目被选择
    list_hidden = pyqtSignal()  # 列表被隐藏
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置样式和行为 - 使用ToolTip窗口标志避免焦点问题
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
        self.setFocusPolicy(Qt.NoFocus)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 设置最大显示项目数
        self.setMaximumHeight(200)
        self.setMinimumWidth(200)

        # 设置鼠标跟踪，改善hover效果
        self.setMouseTracking(True)

        # 连接信号
        self.itemClicked.connect(self._on_item_clicked)
        self.itemEntered.connect(self._on_item_entered)

        # 应用样式
        self._apply_styles()
    
    def _apply_styles(self):
        """应用样式表"""
        self.setStyleSheet("""
            QListWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
                selection-background-color: #e3f2fd;
                outline: none;
            }
            QListWidget::item {
                padding: 4px 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
    
    def _on_item_clicked(self, item):
        """处理项目点击事件"""
        if item:
            self.item_selected.emit(item.text())
            self.hide()

    def _on_item_entered(self, item):
        """处理鼠标进入项目事件"""
        if item:
            # 当鼠标悬停在项目上时，自动选中该项目
            self.setCurrentItem(item)

    def enterEvent(self, event):
        """鼠标进入建议列表事件"""
        # 取消隐藏定时器
        if hasattr(self.parent(), '_hide_timer') and self.parent()._hide_timer:
            self.parent()._hide_timer.stop()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开建议列表事件"""
        # 延迟隐藏
        if hasattr(self.parent(), '_delayed_hide'):
            QTimer.singleShot(100, self.parent()._delayed_hide)
        super().leaveEvent(event)
    
    def keyPressEvent(self, event: QKeyEvent):
        """处理键盘事件"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # 回车键选择当前项
            current_item = self.currentItem()
            if current_item:
                self.item_selected.emit(current_item.text())
                self.hide()
                return
        elif event.key() == Qt.Key_Escape:
            # ESC键隐藏列表
            self.hide()
            return
        elif event.key() == Qt.Key_Tab:
            # Tab键：向下选择下一个建议项
            current_row = self.currentRow()
            if current_row < self.count() - 1:
                self.setCurrentRow(current_row + 1)
            else:
                self.setCurrentRow(0)  # 循环到第一项
            return
        elif event.key() == Qt.Key_Backtab:  # Shift+Tab
            # Shift+Tab键：向上选择上一个建议项
            current_row = self.currentRow()
            if current_row > 0:
                self.setCurrentRow(current_row - 1)
            else:
                self.setCurrentRow(self.count() - 1)  # 循环到最后一项
            return
        elif event.key() == Qt.Key_Up:
            # 上箭头键：向上导航
            current_row = self.currentRow()
            if current_row > 0:
                self.setCurrentRow(current_row - 1)
            return
        elif event.key() == Qt.Key_Down:
            # 下箭头键：向下导航
            current_row = self.currentRow()
            if current_row < self.count() - 1:
                self.setCurrentRow(current_row + 1)
            return

        # 其他键传递给父组件
        super().keyPressEvent(event)
    
    def show_suggestions(self, suggestions, highlight_query=""):
        """
        显示建议列表

        Args:
            suggestions (List[str]): 建议列表
            highlight_query (str): 需要高亮的查询文本
        """
        self.clear()

        if not suggestions:
            self.hide()
            return

        # 添加建议项
        for suggestion in suggestions:
            item = QListWidgetItem(suggestion)

            # 设置工具提示
            item.setToolTip(suggestion)

            self.addItem(item)

        # 选择第一项
        if self.count() > 0:
            self.setCurrentRow(0)

        # 调整大小
        self._adjust_size()

        # 注意：不在这里调用show()，由父组件控制显示时机
    
    def _adjust_size(self):
        """调整列表大小"""
        if self.count() == 0:
            return
        
        # 计算所需高度
        item_height = self.sizeHintForRow(0)
        total_height = min(item_height * self.count() + 4, 200)  # 最大200px
        
        self.setFixedHeight(total_height)
    
    def hide(self):
        """隐藏列表"""
        super().hide()
        self.list_hidden.emit()


class AutoCompleteLineEdit(QLineEdit):
    """智能自动补全输入框"""
    
    # 信号定义
    value_selected = pyqtSignal(str)  # 值被选择
    
    def __init__(self, field_type: str, model: AutoCompleteModel, 
                 placeholder: str = "", parent=None):
        """
        初始化自动补全输入框
        
        Args:
            field_type (str): 字段类型
            model (AutoCompleteModel): 数据模型
            placeholder (str): 占位符文本
            parent: 父组件
        """
        super().__init__(parent)
        
        self.field_type = field_type
        self.model = model
        self.engine = AutoCompleteEngine(self)
        
        # 设置占位符
        if placeholder:
            self.setPlaceholderText(placeholder)
        
        # 创建建议列表（不设置父窗口，稍后动态设置）
        self.suggestion_list = AutoCompleteListWidget()

        # 连接信号
        self.textChanged.connect(self._on_text_changed)
        self.suggestion_list.item_selected.connect(self._on_suggestion_selected)
        self.suggestion_list.list_hidden.connect(self._on_list_hidden)

        # 安装应用级事件过滤器来拦截Tab键
        QApplication.instance().installEventFilter(self)

        # 查询定时器（防抖动）
        self.query_timer = QTimer()
        self.query_timer.setSingleShot(True)
        self.query_timer.timeout.connect(self._perform_query)

        # 位置更新定时器
        self.position_timer = QTimer()
        self.position_timer.setSingleShot(True)
        self.position_timer.timeout.connect(self._update_suggestion_position)

        # 状态标志
        self._ignore_text_change = False
        self._list_visible = False
        self._hide_timer = None  # 隐藏定时器
        self._programmatic_change = False  # 标记是否为程序化设置文本

        # 最小查询长度 - 改为1，避免空输入时弹出建议
        self.min_query_length = 1

        # 安装事件过滤器以监听窗口移动和调整大小
        self.installEventFilter(self)

        # 设置Tab键处理策略
        self.setFocusPolicy(Qt.StrongFocus)

    def eventFilter(self, obj, event):
        """事件过滤器 - 拦截Tab键事件"""
        # 如果是键盘事件且建议列表可见
        if (event.type() == 6 and  # QEvent.KeyPress = 6
            self._list_visible and
            obj == self):

            if event.key() == Qt.Key_Tab:
                # 拦截Tab键，在建议列表中导航
                self._handle_tab_navigation(event)
                return True  # 阻止事件传播
            elif event.key() == Qt.Key_Backtab:
                # 拦截Shift+Tab键
                self._handle_tab_navigation(event)
                return True  # 阻止事件传播

        return super().eventFilter(obj, event)
    
    def _on_text_changed(self, text):
        """处理文本变化事件"""
        if self._ignore_text_change or self._programmatic_change:
            return

        # 重启查询定时器
        self.query_timer.stop()

        if len(text) >= self.min_query_length:
            self.query_timer.start(150)  # 150ms延迟
        else:
            self._hide_suggestions()
    
    def _perform_query(self):
        """执行查询"""
        query = self.text().strip()
        
        if len(query) < self.min_query_length:
            self._hide_suggestions()
            return
        
        # 获取建议
        suggestions = self.model.get_suggestions(self.field_type, query, limit=10)
        
        if suggestions:
            self._show_suggestions(suggestions, query)
        else:
            self._hide_suggestions()
    
    def _show_suggestions(self, suggestions, query=""):
        """显示建议列表"""
        if not suggestions:
            self._hide_suggestions()
            return

        # 取消任何待处理的隐藏定时器
        if self._hide_timer:
            self._hide_timer.stop()
            self._hide_timer = None

        # 准备建议列表内容
        self.suggestion_list.show_suggestions(suggestions, query)

        # 立即更新位置并显示
        self._update_suggestion_position()
        self._list_visible = True
    
    def _hide_suggestions(self):
        """隐藏建议列表"""
        # 取消任何待处理的隐藏定时器
        if self._hide_timer:
            self._hide_timer.stop()
            self._hide_timer = None

        self.suggestion_list.hide()
        self._list_visible = False

    def _update_suggestion_position(self):
        """更新建议列表位置"""
        if not self.suggestion_list:
            return

        # 计算输入框在全局坐标系中的位置
        global_pos = self.mapToGlobal(QPoint(0, 0))
        input_rect = QRect(global_pos, self.size())

        # 计算建议列表的理想位置（输入框正下方）
        list_pos = QPoint(input_rect.left(), input_rect.bottom())

        # 获取屏幕几何信息
        desktop = QApplication.desktop()
        screen_rect = desktop.availableGeometry(self)

        # 计算建议列表的大小
        list_width = max(self.width(), 200)  # 至少200px宽

        # 确保建议列表有内容后再获取高度
        if self.suggestion_list.count() > 0:
            item_height = self.suggestion_list.sizeHintForRow(0)
            list_height = min(item_height * self.suggestion_list.count() + 4, 200)
        else:
            list_height = 100  # 默认高度

        # 检查是否需要调整位置以避免超出屏幕
        if list_pos.x() + list_width > screen_rect.right():
            # 右边超出，向左调整
            list_pos.setX(screen_rect.right() - list_width)

        if list_pos.y() + list_height > screen_rect.bottom():
            # 下边超出，显示在输入框上方
            list_pos.setY(input_rect.top() - list_height)

        # 确保不会超出屏幕左边和上边
        if list_pos.x() < screen_rect.left():
            list_pos.setX(screen_rect.left())

        if list_pos.y() < screen_rect.top():
            list_pos.setY(screen_rect.top())

        # 设置建议列表的位置和大小
        self.suggestion_list.setGeometry(list_pos.x(), list_pos.y(), list_width, list_height)

        # 确保建议列表在最前面
        self.suggestion_list.raise_()
        self.suggestion_list.show()
    
    def _on_suggestion_selected(self, text):
        """处理建议选择事件"""
        # 设置文本（忽略文本变化事件）
        self._ignore_text_change = True
        self._programmatic_change = True
        self.setText(text)
        self._ignore_text_change = False
        self._programmatic_change = False

        # 记录使用
        self.model.add_value(self.field_type, text)

        # 发送信号
        self.value_selected.emit(text)

        # 隐藏建议列表
        self._hide_suggestions()
    
    def _on_list_hidden(self):
        """处理列表隐藏事件"""
        self._list_visible = False
    
    def event(self, event):
        """重写事件处理方法以完全控制Tab键行为"""
        # 拦截所有Tab键事件，当建议列表可见时
        if (event.type() == 6 and self._list_visible):  # QEvent.KeyPress = 6
            key_event = event
            if key_event.key() == Qt.Key_Tab:
                self._handle_tab_navigation(key_event)
                return True  # 完全消费这个事件，阻止传播
            elif key_event.key() == Qt.Key_Backtab:
                self._handle_tab_navigation(key_event)
                return True  # 完全消费这个事件，阻止传播

        return super().event(event)

    def _handle_tab_navigation(self, event):
        """专门处理Tab键导航"""
        if event.key() == Qt.Key_Tab:
            # Tab键：向下选择下一个建议项
            current_row = self.suggestion_list.currentRow()
            if current_row < self.suggestion_list.count() - 1:
                self.suggestion_list.setCurrentRow(current_row + 1)
            else:
                self.suggestion_list.setCurrentRow(0)  # 循环到第一项
        elif event.key() == Qt.Key_Backtab:  # Shift+Tab
            # Shift+Tab键：向上选择上一个建议项
            current_row = self.suggestion_list.currentRow()
            if current_row > 0:
                self.suggestion_list.setCurrentRow(current_row - 1)
            else:
                self.suggestion_list.setCurrentRow(self.suggestion_list.count() - 1)  # 循环到最后一项

    def keyPressEvent(self, event: QKeyEvent):
        """处理键盘事件"""
        # 首先检查是否需要拦截Tab键
        if self._list_visible and event.key() in (Qt.Key_Tab, Qt.Key_Backtab):
            self._handle_tab_navigation(event)
            event.ignore()  # 忽略事件，阻止进一步处理
            return

        if self._list_visible:
            # 处理建议列表相关的键盘事件
            if event.key() == Qt.Key_Up:
                # 上箭头键：在建议列表中向上导航
                self.suggestion_list.keyPressEvent(event)
                event.accept()  # 明确接受事件
                return
            elif event.key() == Qt.Key_Down:
                # 下箭头键：在建议列表中向下导航
                self.suggestion_list.keyPressEvent(event)
                event.accept()  # 明确接受事件
                return
            elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                # 回车键：确认选择当前高亮的建议项
                current_item = self.suggestion_list.currentItem()
                if current_item:
                    self._on_suggestion_selected(current_item.text())
                event.accept()  # 明确接受事件
                return
            elif event.key() == Qt.Key_Escape:
                # ESC键：关闭建议列表
                self._hide_suggestions()
                event.accept()  # 明确接受事件
                return
        else:
            # 当建议列表不可见时，某些快捷键可以强制显示建议
            if event.key() == Qt.Key_Down and event.modifiers() == Qt.ControlModifier:
                # Ctrl+Down：强制显示建议
                self.force_suggestions()
                event.accept()
                return

        # 其他键正常处理
        super().keyPressEvent(event)

    def focusInEvent(self, event: QFocusEvent):
        """处理获得焦点事件"""
        # 取消任何待处理的隐藏定时器
        if self._hide_timer:
            self._hide_timer.stop()
            self._hide_timer = None
        super().focusInEvent(event)
    
    def focusOutEvent(self, event: QFocusEvent):
        """处理失去焦点事件"""
        # 取消任何现有的隐藏定时器
        if self._hide_timer:
            self._hide_timer.stop()

        # 创建新的延迟隐藏定时器
        self._hide_timer = QTimer()
        self._hide_timer.setSingleShot(True)
        self._hide_timer.timeout.connect(self._delayed_hide)
        self._hide_timer.start(200)  # 增加延迟时间到200ms

        super().focusOutEvent(event)

    def _delayed_hide(self):
        """延迟隐藏建议列表"""
        # 检查鼠标是否在建议列表上
        if (self.suggestion_list.isVisible() and
            self.suggestion_list.underMouse()):
            # 如果鼠标在建议列表上，再次延迟隐藏
            if self._hide_timer:
                self._hide_timer.start(200)
            return

        self._hide_suggestions()

    def resizeEvent(self, event: QResizeEvent):
        """处理调整大小事件"""
        super().resizeEvent(event)
        # 延迟更新建议列表位置
        if self._list_visible:
            self.position_timer.stop()
            self.position_timer.start(50)  # 50ms延迟
    
    def set_min_query_length(self, length: int):
        """
        设置最小查询长度
        
        Args:
            length (int): 最小查询长度
        """
        self.min_query_length = max(0, length)
    
    def force_suggestions(self):
        """强制显示建议（即使文本为空）"""
        current_text = self.text().strip()
        suggestions = self.model.get_suggestions(self.field_type, current_text, limit=10)
        if suggestions:
            self._show_suggestions(suggestions, current_text)
        elif current_text == "":
            # 如果没有匹配的建议且输入为空，显示所有历史记录
            all_suggestions = self.model.get_suggestions(self.field_type, "", limit=10)
            if all_suggestions:
                self._show_suggestions(all_suggestions, "")
    
    def add_current_value(self):
        """将当前值添加到历史记录"""
        text = self.text().strip()
        if text:
            self.model.add_value(self.field_type, text)

    def setText(self, text):
        """重写setText方法，标记为程序化设置"""
        self._programmatic_change = True
        super().setText(text)
        self._programmatic_change = False

    def set_text_programmatically(self, text):
        """程序化设置文本，不触发自动补全"""
        self._programmatic_change = True
        self._ignore_text_change = True
        super().setText(text)
        self._ignore_text_change = False
        self._programmatic_change = False
