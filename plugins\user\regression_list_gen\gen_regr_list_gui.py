#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
回归列表生成工具GUI
用于解析case配置文件生成回归列表
"""

import os
import sys
import subprocess
import json
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QPushButton, QLabel, QLineEdit, QFileDialog, QMessageBox, QFormLayout,
    QTextEdit, QComboBox
)
from PyQt5.QtCore import Qt, QProcess
from PyQt5.QtGui import QFont, QIcon

class GenRegrListGUI(QMainWindow):
    """回归列表生成工具GUI主窗口"""
    
    CONFIG_FILE = "gen_regr_list_config.json"  # 配置文件名
    
    def __init__(self):
        super().__init__()
        self.process = None
        self.history = []
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('回归列表生成工具')
        self.setGeometry(300, 300, 800, 500)  # 减小窗口高度
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建参数配置面板
        config_panel = self.create_config_panel()
        main_layout.addWidget(config_panel)
        
        # 创建命令预览区域
        preview_group = QGroupBox("命令预览")
        preview_layout = QVBoxLayout()
        
        self.command_preview = QTextEdit()
        self.command_preview.setReadOnly(True)
        self.command_preview.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #f8f8f2;
                font-family: Consolas, 'Courier New', monospace;
                border: none;
                border-radius: 3px;
                padding: 5px;
            }
        """)
        
        # 设置预览文本使用较小的字体
        font = QFont("Consolas", 9)
        self.command_preview.setFont(font)
        
        preview_layout.addWidget(self.command_preview)
        preview_group.setLayout(preview_layout)
        main_layout.addWidget(preview_group)
        
        
        # 创建按钮区域
        button_layout = QHBoxLayout()
        
        # 生成命令按钮
        self.generate_cmd_btn = QPushButton("生成命令")
        self.generate_cmd_btn.clicked.connect(self.generate_command)
        self.generate_cmd_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        
        # 执行命令按钮
        self.execute_btn = QPushButton("执行命令")
        self.execute_btn.clicked.connect(self.execute_command)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #219a52;
            }
        """)
        
        
        # 保存配置按钮
        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.clicked.connect(self.save_config)
        
        button_layout.addWidget(self.generate_cmd_btn)
        button_layout.addWidget(self.execute_btn)
        button_layout.addWidget(self.save_config_btn)
        
        main_layout.addLayout(button_layout)
        
        # 状态栏
        self.statusBar().showMessage('就绪')
        
        # 应用整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 12px;
                padding-top: 5px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 3px;
                color: #333333;
            }
            QLineEdit {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border: 1px solid #66afe9;
            }
            QComboBox {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 5px;
                background-color: #ffffff;
            }
            QLabel {
                color: #333333;
            }
        """)
        
    def create_config_panel(self):
        """创建参数配置面板"""
        config_group = QGroupBox("参数配置")
        config_layout = QFormLayout()
        config_layout.setSpacing(10)
        config_layout.setContentsMargins(10, 15, 10, 10)
        
        # 历史配置下拉框
        history_layout = QHBoxLayout()
        
        self.history_combo = QComboBox()
        self.history_combo.setEditable(False)
        self.history_combo.currentIndexChanged.connect(self.apply_history)
        
        history_layout.addWidget(self.history_combo)
        
        # Block 参数
        self.block_input = QLineEdit()
        self.block_input.setPlaceholderText("请输入环境block名")
        
        # Base 参数
        self.base_input = QLineEdit()
        self.base_input.setPlaceholderText("请输入base环境名")
        
        # Tag 参数
        self.tag_input = QLineEdit()
        self.tag_input.setPlaceholderText("请输入TAG名")
        
        # 配置文件路径
        cfg_layout = QHBoxLayout()
        self.cfg_input = QLineEdit()
        self.cfg_input.setPlaceholderText("请选择case配置文件路径")
        self.cfg_btn = QPushButton("浏览...")
        self.cfg_btn.clicked.connect(self.browse_cfg_file)
        cfg_layout.addWidget(self.cfg_input)
        cfg_layout.addWidget(self.cfg_btn)
        
        # 输出文件路径
        output_layout = QHBoxLayout()
        self.output_input = QLineEdit()
        self.output_input.setPlaceholderText("请选择回归列表生成路径")
        self.output_btn = QPushButton("浏览...")
        self.output_btn.clicked.connect(self.browse_output_file)
        output_layout.addWidget(self.output_input)
        output_layout.addWidget(self.output_btn)
        
        # 添加表单项
        config_layout.addRow("历史配置:", history_layout)
        config_layout.addRow("环境Block (-block):", self.block_input)
        config_layout.addRow("Base环境 (-base):", self.base_input)
        config_layout.addRow("TAG名 (-tag):", self.tag_input)
        config_layout.addRow("配置文件 (-cfg):", cfg_layout)
        config_layout.addRow("输出路径 (-o):", output_layout)
        
        # 添加其他选项输入框
        self.other_options = QLineEdit()
        self.other_options.setPlaceholderText("其他命令行选项")
        config_layout.addRow("其他选项:", self.other_options)
        
        config_group.setLayout(config_layout)
        return config_group
        
    def browse_cfg_file(self):
        """浏览选择配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择配置文件", "", "配置文件 (*.cfg);;所有文件 (*.*)"
        )
        if file_path:
            self.cfg_input.setText(file_path)
            self.generate_command()  # 自动更新命令预览
            
    def browse_output_file(self):
        """浏览选择输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出文件", "", "列表文件 (*.list);;所有文件 (*.*)"
        )
        if file_path:
            self.output_input.setText(file_path)
            self.generate_command()  # 自动更新命令预览
            
    def generate_command(self):
        """生成命令并更新预览"""
        cmd = []
        
        # 假设脚本名称为gen_regr_list.py
        cmd.append("python gen_regr_list.py")
        
        # 添加参数
        if self.block_input.text():
            cmd.append(f"-block {self.block_input.text()}")
            
        if self.base_input.text():
            cmd.append(f"-base {self.base_input.text()}")
            
        if self.tag_input.text():
            cmd.append(f"-tag {self.tag_input.text()}")
            
        if self.cfg_input.text():
            cmd.append(f"-cfg \"{self.cfg_input.text()}\"")
            
        if self.output_input.text():
            cmd.append(f"-o \"{self.output_input.text()}\"")
            
        # 添加其他选项
        if self.other_options.text():
            cmd.append(self.other_options.text())
            
        # 更新命令预览
        command = " ".join(cmd)
        self.command_preview.setText(command)
        
        # 更新状态栏
        self.statusBar().showMessage("命令已生成")
        
        return command
        
    def execute_command(self):
        """执行生成的命令"""
        # 首先生成命令
        command = self.generate_command()
        
        # 检查必填参数
        if not self.cfg_input.text():
            QMessageBox.warning(self, "参数缺失", "请选择配置文件路径")
            return
            
        if not self.output_input.text():
            QMessageBox.warning(self, "参数缺失", "请选择输出文件路径")
            return
            
        # 更新状态栏显示执行信息
        self.statusBar().showMessage(f"正在执行: {command}")
        
        # 创建进程
        self.process = QProcess()
        self.process.setProcessChannelMode(QProcess.MergedChannels)
        self.process.finished.connect(self.handle_finished)
        
        # 更新按钮状态
        self.execute_btn.setEnabled(False)
        
        # 更新状态栏
        self.statusBar().showMessage("正在执行命令...")
        
        # 执行命令
        if sys.platform == 'win32':
            self.process.start('cmd.exe', ['/c', command])
        else:
            self.process.start('/bin/sh', ['-c', command])
            
    def handle_output(self):
        """处理进程输出"""
        # 由于不需要日志，只需简单处理进程输出
        pass
            
    def handle_finished(self):
        """处理进程结束事件"""
        if self.process is None:
            return
            
        try:
            exit_code = self.process.exitCode()
            if exit_code == 0:
                # 显示成功消息
                self.statusBar().showMessage("执行完成，回归列表生成成功")
                QMessageBox.information(self, "执行成功", "回归列表已成功生成！")
                
                # 保存当前配置到历史记录
                self.save_to_history()
            else:
                # 显示失败消息
                error_msg = f"执行失败，退出码: {exit_code}"
                self.statusBar().showMessage(error_msg)
                QMessageBox.warning(self, "执行失败", error_msg)
        except Exception as e:
            error_msg = f"执行异常: {str(e)}"
            self.statusBar().showMessage(error_msg)
            QMessageBox.critical(self, "执行异常", error_msg)
        finally:
            # 更新按钮状态
            self.execute_btn.setEnabled(True)
            self.process = None
            
    def save_to_history(self):
        """保存当前配置到历史记录"""
        config = {
            'block': self.block_input.text(),
            'base': self.base_input.text(),
            'tag': self.tag_input.text(),
            'cfg': self.cfg_input.text(),
            'output': self.output_input.text(),
            'other_options': self.other_options.text(),
            'timestamp': self.get_timestamp()
        }
        
        # 检查是否已存在相同配置
        for i, item in enumerate(self.history):
            if (item['block'] == config['block'] and
                item['base'] == config['base'] and
                item['tag'] == config['tag'] and
                item['cfg'] == config['cfg'] and
                item['output'] == config['output']):
                # 更新时间戳并移到最前
                self.history.pop(i)
                break
                
        # 添加到历史记录
        self.history.insert(0, config)
        
        # 限制历史记录数量
        if len(self.history) > 10:
            self.history = self.history[:10]
            
        # 更新下拉框
        self.update_history_combo()
        
        # 保存到配置文件
        self.save_config()
        
    def update_history_combo(self):
        """更新历史记录下拉框"""
        self.history_combo.clear()
        
        if not self.history:
            self.history_combo.addItem("无历史记录")
            return
            
        for i, config in enumerate(self.history):
            display_text = f"{i+1}. {os.path.basename(config['cfg'])} -> {os.path.basename(config['output'])}"
            self.history_combo.addItem(display_text)
            
    def apply_history(self, index):
        """应用选中的历史配置"""
        if not self.history or index < 0 or index >= len(self.history):
            return
            
        config = self.history[index]
        
        self.block_input.setText(config.get('block', ''))
        self.base_input.setText(config.get('base', ''))
        self.tag_input.setText(config.get('tag', ''))
        self.cfg_input.setText(config.get('cfg', ''))
        self.output_input.setText(config.get('output', ''))
        self.other_options.setText(config.get('other_options', ''))
        
        # 更新命令预览
        self.generate_command()
        
    def get_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    def save_config(self):
        """保存配置到文件"""
        config = {
            'history': self.history,
            'current': {
                'block': self.block_input.text(),
                'base': self.base_input.text(),
                'tag': self.tag_input.text(),
                'cfg': self.cfg_input.text(),
                'output': self.output_input.text(),
                'other_options': self.other_options.text()
            }
        }
        
        try:
            with open(self.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
                
            self.statusBar().showMessage("配置已保存")
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存配置失败: {str(e)}")
            
    def load_config(self):
        """从文件加载配置"""
        try:
            if not os.path.exists(self.CONFIG_FILE):
                return
                
            with open(self.CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 加载历史记录
            self.history = config.get('history', [])
            self.update_history_combo()
            
            # 加载当前配置
            current = config.get('current', {})
            self.block_input.setText(current.get('block', ''))
            self.base_input.setText(current.get('base', ''))
            self.tag_input.setText(current.get('tag', ''))
            self.cfg_input.setText(current.get('cfg', ''))
            self.output_input.setText(current.get('output', ''))
            self.other_options.setText(current.get('other_options', ''))
            
            # 更新命令预览
            self.generate_command()
            
            self.statusBar().showMessage("配置已加载")
        except Exception as e:
            QMessageBox.warning(self, "加载失败", f"加载配置失败: {str(e)}")
            
# 工具元数据，用于工具箱集成
tool_meta = {
    'name': '回归列表生成器',
    'category': 'regression',
    'entry_function': lambda: main()
}

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = GenRegrListGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()