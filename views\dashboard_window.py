"""
数据看板窗口视图
提供仿真记录管理和数据可视化功能
"""
import os
import sys
from datetime import datetime, date
from typing import List, Dict, Optional, Set

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QPushButton,
    QLabel, QComboBox, QLineEdit, QMessageBox, QFileDialog,
    QStatusBar, QSplitter, QGroupBox, QGridLayout, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QIcon

# 导入matplotlib相关模块
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    from matplotlib import rcParams
    import warnings

    # 导入字体管理器
    try:
        from utils.font_manager import font_manager
        # 配置matplotlib字体（抑制警告）
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            font_manager.configure_matplotlib_fonts()
    except ImportError:
        # 如果字体管理器不可用，使用简单的字体配置
        import platform
        system = platform.system()

        if system == "Windows":
            font_list = ['Microsoft YaHei', 'SimHei', 'SimSun']
        elif system == "Darwin":  # macOS
            font_list = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
        else:  # Linux
            font_list = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                        'Source Han Sans SC', 'DejaVu Sans', 'Liberation Sans']

        font_list.extend(['sans-serif'])

        # 抑制字体警告
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            rcParams['font.sans-serif'] = font_list
            rcParams['axes.unicode_minus'] = False

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，图表功能将不可用")

from models.dashboard_model import DashboardModel

# 导入智能刷新管理器
from utils.smart_refresh_manager import SmartRefreshManager, IncrementalTableUpdater

# 导入分页控件
from utils.pagination_widget import PaginationWidget, FilterWidget


class DirectoryScanWorker(QThread):
    """目录扫描工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(int, int, str)  # current, total, message
    scan_completed = pyqtSignal(list)  # updated_cases
    scan_failed = pyqtSignal(str)  # error_message

    def __init__(self, current_dir, dashboard_model):
        super().__init__()
        self.current_dir = current_dir
        self.dashboard_model = dashboard_model
        self.is_cancelled = False

    def cancel(self):
        """取消扫描"""
        self.is_cancelled = True

    def run(self):
        """执行目录扫描"""
        try:
            self.progress_updated.emit(0, 0, "正在扫描目录结构...")

            # 查找所有可能的日志文件
            log_files = []

            # 扫描当前目录
            items = os.listdir(self.current_dir)
            total_items = len(items)

            for i, item in enumerate(items):
                if self.is_cancelled:
                    return

                self.progress_updated.emit(i + 1, total_items, f"扫描目录: {item}")

                case_path = os.path.join(self.current_dir, item)
                if os.path.isdir(case_path):
                    log_path = os.path.join(case_path, 'log/irun_sim.log')
                    if os.path.exists(log_path):
                        log_files.append(log_path)

            if self.is_cancelled:
                return

            self.progress_updated.emit(0, len(log_files), "开始分析日志文件...")

            # 分析每个日志文件
            updated_cases = []
            for i, log_path in enumerate(log_files):
                if self.is_cancelled:
                    return

                try:
                    # 尝试从路径中提取用例名称
                    case_name = self.extract_case_name_from_path(log_path)
                    if not case_name:
                        continue

                    self.progress_updated.emit(i + 1, len(log_files), f"分析用例: {case_name}")

                    # 检查状态
                    status = self.check_simulation_status_local(log_path)

                    if status in ['PASS', 'FAIL']:
                        # 检查数据库中是否有这个用例
                        records = self.dashboard_model.get_all_simulation_records()
                        existing_record = None
                        for record in records:
                            if record.get('case_name') == case_name:
                                existing_record = record
                                break

                        if existing_record:
                            # 更新现有记录
                            success = self.dashboard_model.update_simulation_status(
                                case_name=case_name,
                                status=status,
                                log_path=log_path
                            )
                            if success:
                                updated_cases.append(case_name)
                        else:
                            # 创建新记录
                            success = self.dashboard_model.add_simulation_record(
                                case_name=case_name,
                                command_line=f"扫描发现: {case_name}",
                                simulation_stage="DVR1"
                            )
                            if success:
                                # 立即更新状态
                                self.dashboard_model.update_simulation_status(
                                    case_name=case_name,
                                    status=status,
                                    log_path=log_path
                                )
                                updated_cases.append(case_name)

                except Exception as e:
                    print(f"处理日志文件失败 {log_path}: {str(e)}")

            if not self.is_cancelled:
                self.scan_completed.emit(updated_cases)

        except Exception as e:
            if not self.is_cancelled:
                self.scan_failed.emit(str(e))

    def extract_case_name_from_path(self, log_path: str) -> Optional[str]:
        """从日志文件路径中提取用例名称"""
        try:
            # 获取相对于当前目录的路径
            rel_path = os.path.relpath(log_path, self.current_dir)

            # 分割路径
            path_parts = rel_path.split(os.sep)

            # 如果路径包含用例目录结构，如 case_name/log/irun_sim.log
            if len(path_parts) >= 3 and path_parts[1] == 'log':
                return path_parts[0]

            # 如果路径包含用例目录结构，如 case_name/irun_sim.log
            elif len(path_parts) >= 2:
                return path_parts[0]

            # 尝试从文件名中提取
            filename = os.path.basename(log_path)
            if '_' in filename:
                # 如果文件名包含下划线，可能是 case_name_sim.log 格式
                parts = filename.split('_')
                if len(parts) >= 2:
                    return '_'.join(parts[:-1])  # 去掉最后的 sim.log 部分

            return None

        except Exception as e:
            print(f"从路径提取用例名称失败: {str(e)}")
            return None

    def check_simulation_status_local(self, log_path: str) -> str:
        """本地检查仿真状态"""
        try:
            if not os.path.exists(log_path):
                return "On-Going"

            # 读取文件最后几行
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                if not lines:
                    return "On-Going"

                # 检查最后几行
                last_lines = ''.join(lines[-10:]).lower()

                # 检查PASS/FAIL标识
                if 'pass' in last_lines or 'passed' in last_lines:
                    return "PASS"
                elif 'fail' in last_lines or 'failed' in last_lines or 'error' in last_lines:
                    return "FAIL"

                # 检查文件修改时间
                file_mtime = os.path.getmtime(log_path)
                current_time = datetime.now().timestamp()
                time_diff = current_time - file_mtime

                # 如果文件超过5分钟没有更新，可能已经完成
                if time_diff > 300:  # 5分钟
                    end_patterns = ["exit", "quit", "stop", "end", "finish", "done", "completed"]
                    for pattern in end_patterns:
                        if pattern.lower() in last_lines.lower():
                            return "PASS"

                return "On-Going"

        except Exception as e:
            print(f"检查仿真状态失败: {str(e)}")
            return "On-Going"


class DashboardTableUpdater(IncrementalTableUpdater):
    """Dashboard专用的表格更新器"""

    def __init__(self, table_widget, parent_window):
        super().__init__(table_widget)
        self.parent_window = parent_window

    def _update_row(self, row: int, record: Dict):
        """更新单行数据"""
        # 序号 - 计算全局序号
        global_index = (self.parent_window.current_page - 1) * self.parent_window.page_size + row + 1
        self.table.setItem(row, 0, QTableWidgetItem(str(global_index)))

        # 用例名称
        self.table.setItem(row, 1, QTableWidgetItem(record.get('case_name', '')))

        # 用例状态
        status_item = QTableWidgetItem(record.get('status', 'Pending'))
        status = record.get('status', 'Pending')
        if status == 'PASS':
            status_item.setBackground(QColor('#d4edda'))
            status_item.setForeground(QColor('#155724'))
        elif status == 'FAIL':
            status_item.setBackground(QColor('#f8d7da'))
            status_item.setForeground(QColor('#721c24'))
        elif status == 'On-Going':
            status_item.setBackground(QColor('#fff3cd'))
            status_item.setForeground(QColor('#856404'))
        self.table.setItem(row, 2, status_item)

        # 开始时间
        start_time = record.get('start_time', '')
        if start_time:
            try:
                dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                start_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
        self.table.setItem(row, 3, QTableWidgetItem(start_time))

        # 结束时间
        end_time = record.get('end_time', '')
        if end_time:
            try:
                dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                end_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
        self.table.setItem(row, 4, QTableWidgetItem(end_time))

        # 编译耗时
        compile_time = record.get('compile_time', 0) or 0
        self.table.setItem(row, 5, QTableWidgetItem(f"{compile_time:.2f}"))

        # 仿真耗时
        sim_time = record.get('simulation_time', 0) or 0
        self.table.setItem(row, 6, QTableWidgetItem(f"{sim_time:.2f}"))

        # 仿真阶段 - 使用下拉列表
        stage_combo = QComboBox()
        stage_combo.addItems(["DVR1", "DVR2", "DVR3", "DVS1", "DVS2"])
        current_stage = record.get('simulation_stage', 'DVR1')
        stage_combo.setCurrentText(current_stage)

        # 连接下拉列表变化事件
        stage_combo.currentTextChanged.connect(
            lambda stage, case_name=record.get('case_name', ''):
            self.parent_window.on_stage_changed(case_name, stage)
        )

        self.table.setCellWidget(row, 7, stage_combo)


class DashboardWindow(QMainWindow):
    """数据看板主窗口"""
    
    # 信号定义
    window_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化数据看板窗口
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 初始化数据模型
        self.dashboard_model = DashboardModel()

        # 初始化智能刷新管理器
        self.refresh_manager = SmartRefreshManager(self)
        self.refresh_manager.refresh_requested.connect(self._perform_refresh)

        # 分页参数
        self.current_page = 1
        self.page_size = 50
        self.total_records = 0
        self.current_status_filter = ""  # 当前状态过滤值
        self.current_search_term = ""    # 当前搜索词

        # 设置窗口属性
        self.setWindowTitle('RunSim 数据看板')
        self.setMinimumSize(800, 600)
        self.resize(1200, 800)

        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon('icons/dashboard.png'))

        # 创建UI
        self.init_ui()

        # 初始化表格更新器
        self.table_updater = DashboardTableUpdater(self.records_table, self)

        # 连接信号
        self.connect_signals()

        # 初始化数据
        self.load_initial_data()

        # 设置定时器用于定期刷新数据（降低频率）
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(lambda: self.refresh_manager.request_refresh())
        self.refresh_timer.start(60000)  # 60秒刷新一次，降低频率

        # 初始化扫描工作线程
        self.scan_worker = None
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建工具栏
        toolbar_widget = self.create_toolbar()
        main_layout.addWidget(toolbar_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建仿真记录标签页
        self.records_tab = self.create_records_tab()
        self.tab_widget.addTab(self.records_tab, "仿真记录")
        
        # 创建图表标签页（如果matplotlib可用）
        if MATPLOTLIB_AVAILABLE:
            self.charts_tab = self.create_charts_tab()
            self.tab_widget.addTab(self.charts_tab, "数据图表")
        
        # 创建状态栏
        self.create_status_bar()
        
        # 应用样式
        self.apply_styles()
    
    def create_toolbar(self) -> QWidget:
        """创建工具栏
        
        Returns:
            QWidget: 工具栏部件
        """
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新数据")
        self.refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(self.refresh_btn)
        
        # 导出按钮
        self.export_btn = QPushButton("导出Excel")
        self.export_btn.clicked.connect(self.export_to_excel)
        layout.addWidget(self.export_btn)
        
        # 导入TestPlan按钮
        self.import_btn = QPushButton("导入TestPlan")
        self.import_btn.clicked.connect(self.import_testplan)
        layout.addWidget(self.import_btn)

        # 手动检查按钮
        self.manual_check_btn = QPushButton("检查完成状态")
        self.manual_check_btn.clicked.connect(self.manual_check_completion)
        self.manual_check_btn.setToolTip("手动检查所有On-Going状态的用例是否已完成")
        layout.addWidget(self.manual_check_btn)

        # 扫描当前目录按钮
        self.scan_current_btn = QPushButton("扫描当前目录")
        self.scan_current_btn.clicked.connect(self.scan_current_directory)
        self.scan_current_btn.setToolTip("扫描当前目录下的所有日志文件，检查仿真状态")
        layout.addWidget(self.scan_current_btn)

        # 清除历史数据按钮
        self.clear_data_btn = QPushButton("清除历史数据")
        self.clear_data_btn.clicked.connect(self.clear_historical_data)
        self.clear_data_btn.setToolTip("清除所有仿真历史记录")
        self.clear_data_btn.setStyleSheet("QPushButton { color: red; }")
        layout.addWidget(self.clear_data_btn)

        # 分隔符
        layout.addStretch()
        
        # 注释：移除旧版过滤控件，统一使用FilterWidget
        # 过滤控件已在create_records_tab中通过FilterWidget实现
        
        return toolbar
    
    def create_records_tab(self) -> QWidget:
        """创建仿真记录标签页

        Returns:
            QWidget: 记录标签页部件
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(5)

        # 创建过滤控件
        self.filter_widget = FilterWidget()
        self.filter_widget.filter_changed.connect(self.on_filter_changed)
        layout.addWidget(self.filter_widget)

        # 创建表格
        self.records_table = QTableWidget()
        self.setup_records_table()
        layout.addWidget(self.records_table)

        # 创建分页控件
        self.pagination_widget = PaginationWidget()
        self.pagination_widget.page_changed.connect(self.on_page_changed)
        self.pagination_widget.page_size_changed.connect(self.on_page_size_changed)
        layout.addWidget(self.pagination_widget)

        return tab
    
    def setup_records_table(self):
        """设置仿真记录表格"""
        # 设置列数和表头（删除创建时间列）
        headers = [
            "序号", "用例名称", "用例状态", "开始时间", "结束时间",
            "编译耗时(分钟)", "仿真耗时(分钟)", "仿真阶段"
        ]

        self.records_table.setColumnCount(len(headers))
        self.records_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.records_table.setAlternatingRowColors(True)
        self.records_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.records_table.setSortingEnabled(True)

        # 启用列宽拖拽调整功能
        header = self.records_table.horizontalHeader()

        # 设置所有列为可交互调整模式，允许用户拖拽调整列宽
        header.setSectionResizeMode(QHeaderView.Interactive)

        # 启用拖拽调整功能
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        header.setCascadingSectionResizes(False)  # 禁用级联调整
        header.setDefaultSectionSize(100)  # 设置默认列宽

        # 设置初始列宽（用户可以通过拖拽调整）
        self.records_table.setColumnWidth(0, 60)   # 序号
        self.records_table.setColumnWidth(1, 200)  # 用例名称
        self.records_table.setColumnWidth(2, 80)   # 状态
        self.records_table.setColumnWidth(3, 150)  # 开始时间
        self.records_table.setColumnWidth(4, 150)  # 结束时间
        self.records_table.setColumnWidth(5, 120)  # 编译耗时
        self.records_table.setColumnWidth(6, 120)  # 仿真耗时
        self.records_table.setColumnWidth(7, 100)  # 仿真阶段

        # 设置最小列宽，防止列被拖拽得太小
        header.setMinimumSectionSize(50)

        # 连接列宽变化信号，用于保存用户的列宽设置
        header.sectionResized.connect(self.on_column_resized)
    
    def create_charts_tab(self) -> QWidget:
        """创建图表标签页
        
        Returns:
            QWidget: 图表标签页部件
        """
        if not MATPLOTLIB_AVAILABLE:
            # 如果matplotlib不可用，返回提示信息
            tab = QWidget()
            layout = QVBoxLayout(tab)
            label = QLabel("图表功能需要安装matplotlib库")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            return tab
        
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建图表控制面板
        control_panel = self.create_chart_controls()
        layout.addWidget(control_panel)
        
        # 创建图表区域
        self.chart_widget = self.create_chart_widget()
        layout.addWidget(self.chart_widget)
        
        return tab
    
    def create_chart_controls(self) -> QWidget:
        """创建图表控制面板（优化版：消除冗余选项）

        Returns:
            QWidget: 控制面板部件
        """
        panel = QGroupBox("图表设置")
        layout = QGridLayout(panel)

        # 图表类型选择（简化版：移除时间维度信息，通过图表内容区分）
        layout.addWidget(QLabel("图表类型:"), 0, 0)
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "用例通过数柱状图",
            "通过用例累计趋势图",
            "用例状态分布饼图"
        ])
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)
        layout.addWidget(self.chart_type_combo, 0, 1)

        # 时间维度选择（保留，用于控制数据聚合粒度）
        layout.addWidget(QLabel("时间维度:"), 0, 2)
        self.time_dimension_combo = QComboBox()
        self.time_dimension_combo.addItems(["每日", "每周"])
        self.time_dimension_combo.currentTextChanged.connect(self.on_time_dimension_changed)
        layout.addWidget(self.time_dimension_combo, 0, 3)

        # 时间范围选择
        layout.addWidget(QLabel("时间范围:"), 0, 4)
        self.time_range_combo = QComboBox()
        self.update_time_range_options()  # 根据时间维度更新选项
        self.time_range_combo.currentTextChanged.connect(self.update_chart)
        layout.addWidget(self.time_range_combo, 0, 5)

        # 更新图表按钮
        self.update_chart_btn = QPushButton("更新图表")
        self.update_chart_btn.clicked.connect(self.update_chart)
        layout.addWidget(self.update_chart_btn, 0, 6)

        return panel

    def on_chart_type_changed(self):
        """图表类型变化时的处理（优化版：简化逻辑）"""
        # 图表类型变化时直接更新图表，不再自动调整时间维度
        # 让用户通过时间维度选择器来控制数据聚合粒度
        self.update_chart()

    def on_time_dimension_changed(self):
        """时间维度变化时的处理（优化版：更新时间范围选项和图表）"""
        self.update_time_range_options()
        self.update_chart()

    def update_time_range_options(self):
        """根据时间维度更新时间范围选项"""
        current_dimension = self.time_dimension_combo.currentText()

        # 清空当前选项
        self.time_range_combo.clear()

        if current_dimension == "每日":
            self.time_range_combo.addItems(["最近7天", "最近30天", "最近90天"])
        else:  # 每周
            self.time_range_combo.addItems(["最近4周", "最近12周", "最近24周"])

    def create_chart_widget(self) -> QWidget:
        """创建图表部件
        
        Returns:
            QWidget: 图表部件
        """
        if not MATPLOTLIB_AVAILABLE:
            widget = QWidget()
            layout = QVBoxLayout(widget)
            label = QLabel("matplotlib不可用")
            layout.addWidget(label)
            return widget
        
        # 创建matplotlib图表
        self.figure = Figure(figsize=(12, 6))
        self.canvas = FigureCanvas(self.figure)
        
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(self.canvas)
        
        return widget
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 统计信息标签
        self.stats_label = QLabel("总用例数: 0 | 通过: 0 | 失败: 0")
        self.status_bar.addPermanentWidget(self.stats_label)
        
        # 最后更新时间标签
        self.update_time_label = QLabel(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
        self.status_bar.addPermanentWidget(self.update_time_label)
    
    def apply_styles(self):
        """应用样式表"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                background-color: white;
            }
            
            QTabWidget::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background-color: #e6e6e6;
                border: 1px solid #d0d0d0;
                border-bottom: none;
                padding: 8px 16px;
                margin-right: 2px;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            
            QTableWidget::item {
                padding: 5px;
                border: none;
            }
            
            QTableWidget::item:selected {
                background-color: #e6f3ff;
                color: black;
            }
            
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #3a8eef;
            }
            
            QPushButton:pressed {
                background-color: #2a7edf;
            }
            
            QComboBox, QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: white;
            }
            
            QComboBox:focus, QLineEdit:focus {
                border: 2px solid #4a9eff;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
                background-color: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }
        """)
    
    def connect_signals(self):
        """连接信号和槽"""
        # 连接数据模型信号
        self.dashboard_model.record_added.connect(self.on_record_added)
        self.dashboard_model.record_updated.connect(self.on_record_updated)
        self.dashboard_model.statistics_updated.connect(self.on_statistics_updated)

        # 连接异步工作线程信号
        if hasattr(self.dashboard_model, 'async_worker'):
            self.dashboard_model.async_worker.record_added.connect(self.on_record_added)
            self.dashboard_model.async_worker.record_updated.connect(self.on_record_updated)
    
    def load_initial_data(self):
        """加载初始数据"""
        self.refresh_data()
    
    @pyqtSlot()
    def refresh_data(self):
        """刷新数据（兼容性方法，实际使用智能刷新）"""
        self.refresh_manager.request_refresh(force=True)

    def _perform_refresh(self):
        """执行实际的刷新操作"""
        try:
            self.status_label.setText("正在刷新数据...")

            # 尝试增量更新
            last_update_time = self.refresh_manager.get_last_update_time()
            if last_update_time:
                # 获取增量记录
                incremental_records = self.dashboard_model.get_simulation_records_incremental(last_update_time)
                if incremental_records:
                    # 有增量更新，使用增量刷新
                    self._update_incremental(incremental_records)
                    self.status_label.setText("增量刷新完成")
                else:
                    # 没有增量更新，检查是否需要刷新表格显示
                    # 对于分页和过滤操作，即使没有新数据也需要刷新显示
                    self._update_full()
                    self.status_label.setText("刷新完成")
            else:
                # 首次加载或强制刷新，全量刷新
                self._update_full()
                self.status_label.setText("全量刷新完成")

            # 更新最后刷新时间
            self.refresh_manager.set_last_update_time(datetime.now().isoformat())
            self.update_time_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            self.status_label.setText(f"刷新失败: {str(e)}")
            print(f"刷新数据失败: {str(e)}")

    def _update_full(self):
        """全量更新"""

        # 使用分页查询获取记录
        records, total_count = self.dashboard_model.get_simulation_records_paginated(
            page=self.current_page,
            page_size=self.page_size,
            status_filter=self.current_status_filter if self.current_status_filter else None,
            search_term=self.current_search_term if self.current_search_term else None
        )

        # 更新总记录数
        self.total_records = total_count

        # 更新分页控件
        if hasattr(self, 'pagination_widget'):
            self.pagination_widget.set_pagination_info(self.current_page, total_count, self.page_size)

        # 使用表格更新器进行全量更新
        self.table_updater.update_records_incremental(records, None)

        # 更新图表（如果可用）
        if MATPLOTLIB_AVAILABLE:
            self.update_chart()

        # 更新状态栏（使用分页数据）
        self.update_status_bar_paginated(records, total_count)

        # 清除脏记录标记
        self.refresh_manager.clear_dirty_records()

    def _update_incremental(self, incremental_records: List[Dict]):
        """增量更新"""
        if not incremental_records:
            return

        # 对于分页模式，增量更新比较复杂，直接使用全量更新
        # 在大数据量情况下，分页本身已经限制了数据量，全量更新开销可控
        self._update_full()

    def on_filter_changed(self, status_filter: str, search_term: str):
        """过滤条件变化处理"""

        # 检查过滤条件是否真的发生了变化
        if (self.current_status_filter == status_filter and
            self.current_search_term == search_term):
            print("过滤条件未变化，跳过刷新")
            return

        self.current_status_filter = status_filter
        self.current_search_term = search_term
        self.current_page = 1  # 重置到第一页

        # 清除最后更新时间，确保使用新的过滤条件进行查询
        self.refresh_manager.clear_last_update_time()
        self.refresh_manager.request_refresh(force=True)

    def on_page_changed(self, page: int):
        """页码变化处理"""

        if self.current_page == page:
            return  # 页码没有变化，无需刷新

        self.current_page = page
        # 分页操作不需要清除最后更新时间，保持过滤条件的同时进行分页
        # 使用immediate=True确保分页操作立即响应，提升用户体验
        self.refresh_manager.request_refresh(immediate=True)

    def on_page_size_changed(self, page_size: int):
        """每页大小变化处理"""
        if self.page_size == page_size:
            return  # 页面大小没有变化，无需刷新

        self.page_size = page_size
        self.current_page = 1  # 重置到第一页
        # 页面大小变化需要重新计算分页，清除最后更新时间
        self.refresh_manager.clear_last_update_time()
        self.refresh_manager.request_refresh(force=True)

    def on_column_resized(self, logical_index: int, old_size: int, new_size: int):
        """处理列宽调整事件

        Args:
            logical_index (int): 列的逻辑索引
            old_size (int): 调整前的列宽
            new_size (int): 调整后的列宽
        """
        # 这里可以保存用户的列宽设置到配置文件或内存中
        # 目前只在控制台输出调试信息
        headers = [
            "序号", "用例名称", "用例状态", "开始时间", "结束时间",
            "编译耗时(分钟)", "仿真耗时(分钟)", "仿真阶段"
        ]

        if 0 <= logical_index < len(headers):
            column_name = headers[logical_index]

    def on_stage_changed(self, case_name: str, new_stage: str):
        """处理仿真阶段变化

        Args:
            case_name (str): 用例名称
            new_stage (str): 新的仿真阶段
        """
        try:
            # 异步更新数据库中的仿真阶段
            self.dashboard_model.update_simulation_status(
                case_name=case_name,
                status=None,  # 不更新状态
                simulation_stage=new_stage
            )
        except Exception as e:
            print(f"更新仿真阶段失败: {str(e)}")

    @pyqtSlot()
    def filter_records(self):
        """过滤记录（已废弃，保留用于兼容性）"""
        # 此方法已被FilterWidget的on_filter_changed替代
        # 保留空实现以避免信号连接错误
        pass
    
    def update_status_bar(self, records: List[Dict]):
        """更新状态栏统计信息

        Args:
            records (List[Dict]): 记录列表
        """
        total = len(records)
        pass_count = sum(1 for r in records if r.get('status') == 'PASS')
        fail_count = sum(1 for r in records if r.get('status') == 'FAIL')
        ongoing_count = sum(1 for r in records if r.get('status') == 'On-Going')

        self.stats_label.setText(
            f"总用例数: {total} | 通过: {pass_count} | 失败: {fail_count} | 进行中: {ongoing_count}"
        )

    def update_status_bar_paginated(self, current_page_records: List[Dict], total_count: int):
        """更新分页模式下的状态栏统计信息

        Args:
            current_page_records (List[Dict]): 当前页记录列表
            total_count (int): 总记录数
        """
        # 当前页统计
        current_page_total = len(current_page_records)
        current_page_pass = sum(1 for r in current_page_records if r.get('status') == 'PASS')
        current_page_fail = sum(1 for r in current_page_records if r.get('status') == 'FAIL')
        current_page_ongoing = sum(1 for r in current_page_records if r.get('status') == 'On-Going')

        # 显示当前页和总数统计
        self.stats_label.setText(
            f"总记录: {total_count} | 当前页: {current_page_total} | "
            f"通过: {current_page_pass} | 失败: {current_page_fail} | 进行中: {current_page_ongoing}"
        )
    
    @pyqtSlot(dict)
    def on_record_added(self, record: Dict):
        """处理记录添加信号

        Args:
            record (Dict): 新增记录
        """
        # 标记记录为脏记录并请求刷新
        record_id = str(record.get('id', ''))
        self.refresh_manager.update_cached_record(record_id, record)
        self.refresh_manager.request_refresh()

    @pyqtSlot(dict)
    def on_record_updated(self, record: Dict):
        """处理记录更新信号

        Args:
            record (Dict): 更新的记录
        """
        # 检查是否是清除所有数据的特殊信号
        if record.get('action') == 'clear_all':
            # 清除所有数据时，强制全量刷新
            self.refresh_manager.clear_last_update_time()
            self.refresh_manager.clear_dirty_records()
            self.refresh_manager.request_refresh(force=True)
        else:
            # 普通记录更新
            record_id = str(record.get('id', ''))
            if record_id:  # 只有当记录有ID时才处理
                self.refresh_manager.update_cached_record(record_id, record)
                self.refresh_manager.request_refresh()
            else:
                print(f"收到无效的记录更新信号: {record}")
    
    @pyqtSlot(dict)
    def on_statistics_updated(self, stats: Dict):
        """处理统计数据更新信号
        
        Args:
            stats (Dict): 统计数据
        """
        # 可以在这里更新特定的统计显示
        pass
    
    @pyqtSlot()
    def export_to_excel(self):
        """导出到Excel"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel文件",
                f"仿真记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel文件 (*.xlsx);;所有文件 (*.*)"
            )

            if file_path:
                self.status_label.setText("正在导出Excel...")

                # 导入openpyxl
                try:
                    import openpyxl
                    from openpyxl.styles import Font, Alignment, PatternFill
                except ImportError:
                    QMessageBox.warning(self, "错误", "需要安装openpyxl库才能导出Excel文件")
                    self.status_label.setText("就绪")
                    return

                # 获取数据
                records = self.dashboard_model.get_all_simulation_records()
                daily_stats = self.dashboard_model.get_daily_statistics(30)

                # 创建工作簿
                wb = openpyxl.Workbook()

                # 创建仿真记录工作表
                ws_records = wb.active
                ws_records.title = "仿真记录"

                # 设置表头（删除创建时间列）
                headers = [
                    "序号", "用例名称", "用例状态", "开始时间", "结束时间",
                    "编译耗时(分钟)", "仿真耗时(分钟)", "仿真阶段"
                ]

                for col, header in enumerate(headers, 1):
                    cell = ws_records.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # 填充数据
                for row, record in enumerate(records, 2):
                    ws_records.cell(row=row, column=1, value=row-1)  # 序号
                    ws_records.cell(row=row, column=2, value=record.get('case_name', ''))
                    ws_records.cell(row=row, column=3, value=record.get('status', 'Pending'))

                    # 格式化时间
                    start_time = record.get('start_time', '')
                    if start_time:
                        try:
                            dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                            start_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            pass
                    ws_records.cell(row=row, column=4, value=start_time)

                    end_time = record.get('end_time', '')
                    if end_time:
                        try:
                            dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                            end_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            pass
                    ws_records.cell(row=row, column=5, value=end_time)

                    ws_records.cell(row=row, column=6, value=record.get('compile_time', 0) or 0)
                    ws_records.cell(row=row, column=7, value=record.get('simulation_time', 0) or 0)
                    ws_records.cell(row=row, column=8, value=record.get('simulation_stage', 'DVR1'))

                # 自动调整列宽
                for column in ws_records.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws_records.column_dimensions[column_letter].width = adjusted_width

                # 创建统计数据工作表
                if daily_stats:
                    ws_stats = wb.create_sheet("每日统计")

                    # 设置统计表头
                    stats_headers = [
                        "日期", "总用例数", "通过数", "失败数", "进行中",
                        "总编译时间", "总仿真时间", "平均编译时间", "平均仿真时间"
                    ]

                    for col, header in enumerate(stats_headers, 1):
                        cell = ws_stats.cell(row=1, column=col, value=header)
                        cell.font = Font(bold=True)
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                        cell.alignment = Alignment(horizontal="center")

                    # 填充统计数据
                    for row, stat in enumerate(daily_stats, 2):
                        ws_stats.cell(row=row, column=1, value=str(stat.get('date', '')))
                        ws_stats.cell(row=row, column=2, value=stat.get('total_cases', 0))
                        ws_stats.cell(row=row, column=3, value=stat.get('pass_cases', 0))
                        ws_stats.cell(row=row, column=4, value=stat.get('fail_cases', 0))
                        ws_stats.cell(row=row, column=5, value=stat.get('ongoing_cases', 0))
                        ws_stats.cell(row=row, column=6, value=stat.get('total_compile_time', 0))
                        ws_stats.cell(row=row, column=7, value=stat.get('total_simulation_time', 0))
                        ws_stats.cell(row=row, column=8, value=stat.get('avg_compile_time', 0))
                        ws_stats.cell(row=row, column=9, value=stat.get('avg_simulation_time', 0))

                    # 自动调整列宽
                    for column in ws_stats.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 30)
                        ws_stats.column_dimensions[column_letter].width = adjusted_width

                # 保存文件
                wb.save(file_path)

                QMessageBox.information(self, "成功", f"Excel文件已导出到:\n{file_path}")
                self.status_label.setText("导出完成")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败: {str(e)}")
            self.status_label.setText("就绪")
    
    @pyqtSlot()
    def import_testplan(self):
        """导入TestPlan（异步优化版本）"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择TestPlan文件", "",
                "Excel文件 (*.xlsx *.xls);;所有文件 (*.*)"
            )

            if file_path:
                # 检查openpyxl依赖
                try:
                    import openpyxl
                except ImportError:
                    QMessageBox.warning(self, "错误", "需要安装openpyxl库才能导入Excel文件")
                    return

                # 创建并启动异步导入工作线程
                self.import_worker = TestPlanImportWorker(file_path, self.dashboard_model)

                # 连接信号
                self.import_worker.progress_updated.connect(self.on_import_progress)
                self.import_worker.import_completed.connect(self.on_import_completed)
                self.import_worker.import_failed.connect(self.on_import_failed)

                # 禁用导入按钮，防止重复操作
                self.import_btn.setEnabled(False)
                self.status_label.setText("正在导入TestPlan...")

                # 启动工作线程
                self.import_worker.start()

        except Exception as e:
            QMessageBox.warning(self, "错误", f"导入失败: {str(e)}")
            self.status_label.setText("就绪")

    def parse_testplan_sheet(self, sheet) -> List[Dict]:
        """解析TestPlan工作表

        Args:
            sheet: openpyxl工作表对象

        Returns:
            List[Dict]: 解析出的用例信息列表
        """
        cases = []

        try:
            # 查找表头行（通常在第3或第4行）
            header_row = None
            case_name_col = None

            for row_num in range(1, 6):  # 检查前5行
                row = list(sheet.iter_rows(min_row=row_num, max_row=row_num, values_only=True))[0]

                for col_num, cell_value in enumerate(row):
                    if cell_value and isinstance(cell_value, str):
                        if 'TestCase Name' in cell_value or '用例名' in cell_value or 'Case' in cell_value:
                            header_row = row_num
                            case_name_col = col_num
                            break

                if header_row:
                    break

            if not header_row or case_name_col is None:
                print("未找到用例名称列")
                return cases

            # 从表头行的下一行开始读取数据
            for row_num in range(header_row + 1, sheet.max_row + 1):
                row = list(sheet.iter_rows(min_row=row_num, max_row=row_num, values_only=True))[0]

                # 获取用例名称
                if len(row) > case_name_col and row[case_name_col]:
                    case_name = str(row[case_name_col]).strip()

                    if case_name and not case_name.startswith('#'):  # 跳过注释行
                        case_info = {
                            'case_name': case_name,
                            'stage': 'DVR1'  # 默认阶段
                        }

                        # 尝试解析其他信息（如果有的话）
                        # 这里可以根据实际的TestPlan格式进行扩展

                        cases.append(case_info)

            print(f"解析到 {len(cases)} 个用例")

        except Exception as e:
            print(f"解析TestPlan工作表失败: {str(e)}")

        return cases

    def on_import_progress(self, current, total, message):
        """处理导入进度更新"""
        if total > 0:
            progress = int((current / total) * 100)
            self.status_label.setText(f"{message} ({current}/{total}) - {progress}%")
        else:
            self.status_label.setText(message)

    def on_import_completed(self, success_count, total_count, stage_stats):
        """处理导入完成"""
        self.import_btn.setEnabled(True)
        self.status_label.setText("导入完成")

        # 重置分页到第一页，因为新导入的数据通常在第一页
        self.current_page = 1

        # 清除最后更新时间，强制全量刷新
        self.refresh_manager.clear_last_update_time()

        # 异步刷新数据显示
        QTimer.singleShot(500, self.refresh_data)

        # 构建包含仿真阶段解析统计的详细信息
        stage_info = self.format_stage_statistics(stage_stats)

        QMessageBox.information(
            self, "导入完成",
            f"成功导入 {success_count}/{total_count} 个用例\n\n"
            f"仿真阶段解析统计:\n{stage_info}"
        )

    def format_stage_statistics(self, stage_stats):
        """格式化仿真阶段解析统计信息

        Args:
            stage_stats (dict): 统计信息字典

        Returns:
            str: 格式化的统计信息字符串
        """
        try:
            lines = []

            # 解析来源统计
            lines.append(f"• 从M列解析: {stage_stats.get('from_m_column', 0)} 个")
            lines.append(f"• 从O列解析: {stage_stats.get('from_o_column', 0)} 个")
            lines.append(f"• 使用默认值: {stage_stats.get('default_used', 0)} 个")

            # 阶段分布统计
            stage_distribution = stage_stats.get('stage_distribution', {})
            if stage_distribution:
                lines.append("\n各阶段分布:")
                for stage, count in sorted(stage_distribution.items()):
                    lines.append(f"  - {stage}: {count} 个")

            return '\n'.join(lines)

        except Exception as e:
            return f"统计信息格式化失败: {str(e)}"

    def on_import_failed(self, error_message):
        """处理导入失败"""
        self.import_btn.setEnabled(True)
        self.status_label.setText("导入失败")
        QMessageBox.warning(self, "错误", f"导入失败: {error_message}")

    @pyqtSlot()
    def update_chart(self):
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        try:
            self.status_label.setText("正在更新图表...")
            
            # 清除之前的图表
            self.figure.clear()
            
            # 根据选择的图表类型和时间维度绘制图表（优化版：简化逻辑）
            chart_type = self.chart_type_combo.currentText()
            time_dimension = self.time_dimension_combo.currentText()

            # 根据图表类型和时间维度决定绘制方法
            if chart_type == "用例通过数柱状图":
                if time_dimension == "每周":
                    self.draw_weekly_pass_bar_chart()
                else:
                    self.draw_daily_pass_bar_chart()
            elif chart_type == "通过用例累计趋势图":
                if time_dimension == "每周":
                    self.draw_weekly_pass_line_chart()
                else:
                    self.draw_daily_pass_line_chart()
            elif chart_type == "用例状态分布饼图":
                self.draw_status_pie_chart()
            
            # 刷新画布
            self.canvas.draw()
            self.status_label.setText("图表更新完成")
            
        except Exception as e:
            self.status_label.setText(f"图表更新失败: {str(e)}")
            print(f"图表更新错误: {str(e)}")
    
    def draw_daily_pass_bar_chart(self):
        """绘制每日用例通过数柱状图"""
        try:
            # 获取时间范围
            days = self.get_time_range_days()

            # 获取每日统计数据
            daily_stats = self.dashboard_model.get_daily_statistics(days)

            # 创建完整的日期序列和对应的数据
            dates, pass_counts = self._prepare_complete_daily_data(daily_stats, days)

            if not dates:
                # 如果没有日期数据，显示示例图表
                ax = self.figure.add_subplot(111)
                ax.bar(['示例1', '示例2', '示例3'], [10, 15, 8])
                ax.set_title('每日用例通过数（示例数据）')
                ax.set_ylabel('通过数量')
                return

            # 绘制柱状图
            ax = self.figure.add_subplot(111)
            bars = ax.bar(dates, pass_counts, color='#4CAF50', alpha=0.7)

            # 设置标题和标签 - 根据时间维度动态调整
            time_dimension = getattr(self, 'time_dimension_combo', None)
            if time_dimension and time_dimension.currentText() == "每周":
                title = '每周用例通过数'
                xlabel = '周'
            else:
                title = '每日用例通过数'
                xlabel = '日期'

            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_ylabel('通过数量', fontsize=12)
            ax.set_xlabel(xlabel, fontsize=12)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            # 在柱子上显示数值
            for bar, count in zip(bars, pass_counts):
                # 确保count是数字类型，处理None值
                count = count if count is not None else 0
                if count > 0:
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                           str(count), ha='center', va='bottom')

            # 调整布局
            self.figure.tight_layout()

        except Exception as e:
            print(f"绘制每日通过数柱状图失败: {str(e)}")
            # 显示错误信息
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'图表加载失败\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)

    def draw_weekly_pass_bar_chart(self):
        """绘制每周用例通过数柱状图"""
        try:
            # 获取时间范围
            weeks = self.get_time_range_weeks()

            # 获取每周统计数据
            weekly_stats = self.dashboard_model.get_weekly_statistics(weeks)

            if not weekly_stats:
                # 如果没有数据，显示示例图表
                ax = self.figure.add_subplot(111)
                ax.bar(['W1', 'W2', 'W3'], [10, 15, 8])
                ax.set_title('每周用例通过数（示例数据）')
                ax.set_ylabel('通过数量')
                ax.set_xlabel('周')
                return

            # 创建完整的周序列和对应的数据
            week_labels, pass_counts = self._prepare_complete_weekly_data(weekly_stats, weeks)

            # 绘制柱状图
            ax = self.figure.add_subplot(111)
            bars = ax.bar(week_labels, pass_counts, color='#4CAF50', alpha=0.7)

            # 设置标题和标签
            ax.set_title('每周用例通过数', fontsize=14, fontweight='bold')
            ax.set_xlabel('周', fontsize=12)
            ax.set_ylabel('通过数量', fontsize=12)

            # 在柱子上显示数值
            for bar, count in zip(bars, pass_counts):
                # 确保count是数字类型，处理None值
                count = count if count is not None else 0
                if count > 0:
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                           str(count), ha='center', va='bottom', fontsize=10)

            # 设置网格
            ax.grid(True, alpha=0.3, axis='y')
            ax.set_axisbelow(True)

            # 旋转x轴标签以避免重叠
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            # 调整布局
            self.figure.tight_layout()

        except Exception as e:
            print(f"绘制每周通过数柱状图失败: {str(e)}")
            # 显示错误信息
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'图表加载失败\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)

    def draw_daily_pass_line_chart(self):
        """绘制每日通过用例总数折线图"""
        try:
            # 获取时间范围
            days = self.get_time_range_days()

            # 获取每日统计数据
            daily_stats = self.dashboard_model.get_daily_statistics(days)

            # 创建完整的日期序列和对应的数据
            dates, pass_counts = self._prepare_complete_daily_data(daily_stats, days)

            if not dates:
                # 如果没有日期数据，显示示例图表
                ax = self.figure.add_subplot(111)
                ax.plot(['Day1', 'Day2', 'Day3'], [10, 25, 33])
                ax.set_title('每日通过用例总数趋势（示例数据）')
                ax.set_ylabel('累计通过数量')
                return

            # 计算累计通过数量
            cumulative_pass = []
            total = 0
            for count in pass_counts:
                # 确保count是数字类型，处理None值
                count = count if count is not None else 0
                total += count
                cumulative_pass.append(total)

            # 绘制折线图
            ax = self.figure.add_subplot(111)
            line = ax.plot(dates, cumulative_pass, marker='o', linewidth=2,
                          markersize=6, color='#2196F3')

            # 设置标题和标签 - 根据时间维度动态调整
            time_dimension = getattr(self, 'time_dimension_combo', None)
            if time_dimension and time_dimension.currentText() == "每周":
                title = '每周通过用例累计总数趋势'
                xlabel = '周'
            else:
                title = '每日通过用例总数趋势'
                xlabel = '日期'

            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_ylabel('累计通过数量', fontsize=12)
            ax.set_xlabel(xlabel, fontsize=12)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            # 添加网格
            ax.grid(True, alpha=0.3)

            # 在点上显示数值
            for i, (date, count) in enumerate(zip(dates, cumulative_pass)):
                if i % 2 == 0:  # 每隔一个点显示数值，避免拥挤
                    ax.annotate(str(count), (date, count),
                               textcoords="offset points", xytext=(0,10), ha='center')

            # 调整布局
            self.figure.tight_layout()

        except Exception as e:
            print(f"绘制累计通过数折线图失败: {str(e)}")
            # 显示错误信息
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'图表加载失败\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)

    def draw_weekly_pass_line_chart(self):
        """绘制每周通过用例累计总数折线图"""
        try:
            # 获取时间范围
            weeks = self.get_time_range_weeks()

            # 获取每周统计数据
            weekly_stats = self.dashboard_model.get_weekly_statistics(weeks)

            if not weekly_stats:
                # 如果没有数据，显示示例图表
                ax = self.figure.add_subplot(111)
                ax.plot(['W1', 'W2', 'W3'], [10, 25, 33])
                ax.set_title('每周通过用例累计总数趋势（示例数据）')
                ax.set_ylabel('累计通过数量')
                ax.set_xlabel('周')
                return

            # 创建完整的周序列和对应的数据
            week_labels, pass_counts = self._prepare_complete_weekly_data(weekly_stats, weeks)

            # 计算累计通过数
            cumulative_pass = []
            total = 0
            for count in pass_counts:
                # 确保count是数字类型，处理None值
                count = count if count is not None else 0
                total += count
                cumulative_pass.append(total)

            # 绘制折线图
            ax = self.figure.add_subplot(111)
            line = ax.plot(week_labels, cumulative_pass, marker='o', linewidth=2,
                          markersize=6, color='#2196F3')

            # 设置标题和标签
            ax.set_title('每周通过用例累计总数趋势', fontsize=14, fontweight='bold')
            ax.set_xlabel('周', fontsize=12)
            ax.set_ylabel('累计通过数量', fontsize=12)

            # 在数据点上显示数值
            for i, (label, count) in enumerate(zip(week_labels, cumulative_pass)):
                if count > 0:
                    ax.annotate(str(count), (i, count), textcoords="offset points",
                               xytext=(0,10), ha='center', fontsize=9)

            # 设置网格
            ax.grid(True, alpha=0.3)
            ax.set_axisbelow(True)

            # 旋转x轴标签以避免重叠
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            # 调整布局
            self.figure.tight_layout()

        except Exception as e:
            print(f"绘制每周累计通过数折线图失败: {str(e)}")
            # 显示错误信息
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'图表加载失败\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)

    def draw_status_pie_chart(self):
        """绘制用例状态分布饼图"""
        try:
            # 获取所有仿真记录
            records = self.dashboard_model.get_all_simulation_records()

            if not records:
                # 如果没有数据，显示示例图表
                ax = self.figure.add_subplot(111)
                ax.pie([60, 30, 10], labels=['PASS', 'FAIL', 'Pending'], autopct='%1.1f%%')
                ax.set_title('用例状态分布（示例数据）')
                return

            # 统计各状态数量
            status_counts = {}
            for record in records:
                status = record.get('status', 'Pending')
                status_counts[status] = status_counts.get(status, 0) + 1

            if not status_counts:
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
                return

            # 准备数据
            labels = list(status_counts.keys())
            sizes = list(status_counts.values())

            # 设置颜色
            colors = {
                'PASS': '#4CAF50',
                'FAIL': '#F44336',
                'On-Going': '#FF9800',
                'Pending': '#9E9E9E'
            }
            chart_colors = [colors.get(label, '#9E9E9E') for label in labels]

            # 绘制饼图
            ax = self.figure.add_subplot(111)
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=chart_colors, startangle=90)

            # 设置标题
            ax.set_title('用例状态分布', fontsize=14, fontweight='bold')

            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

            # 确保饼图是圆形
            ax.axis('equal')

        except Exception as e:
            print(f"绘制状态分布饼图失败: {str(e)}")
            # 显示错误信息
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'图表加载失败\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)



    def get_time_range_days(self) -> int:
        """获取时间范围天数

        Returns:
            int: 天数
        """
        time_range = self.time_range_combo.currentText()
        if time_range == "最近7天":
            return 7
        elif time_range == "最近30天":
            return 30
        elif time_range == "最近90天":
            return 90
        else:
            return 30  # 默认30天

    def get_time_range_weeks(self) -> int:
        """获取时间范围周数

        Returns:
            int: 周数
        """
        time_range = self.time_range_combo.currentText()
        if time_range == "最近4周":
            return 4
        elif time_range == "最近12周":
            return 12
        elif time_range == "最近24周":
            return 24
        else:
            return 12  # 默认12周

    def _prepare_complete_daily_data(self, daily_stats, days):
        """准备完整的日期序列数据，为缺失日期补充零值

        Args:
            daily_stats: 数据库查询的日统计数据
            days: 时间范围天数

        Returns:
            tuple: (dates, pass_counts) 完整的日期序列和对应的通过数
        """
        from datetime import datetime, timedelta

        # 创建完整的日期序列（从今天往前推days天）
        today = datetime.now().date()
        complete_dates = []
        for i in range(days):
            date = today - timedelta(days=i)
            complete_dates.append(date)

        # 反转列表，使其按时间顺序排列（最早的在前）
        complete_dates.reverse()

        # 将数据库数据转换为字典，便于查找
        stats_dict = {}
        for stat in daily_stats:
            # 处理不同的日期格式
            date_str = stat.get('date', '')
            if isinstance(date_str, str):
                try:
                    # 尝试解析日期字符串
                    if 'T' in date_str:
                        date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00')).date()
                    else:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                    # 确保pass_cases是数字类型，处理None值
                    pass_cases = stat.get('pass_cases', 0)
                    stats_dict[date_obj] = pass_cases if pass_cases is not None else 0
                except:
                    continue
            elif hasattr(date_str, 'date'):
                # 如果是datetime对象
                # 确保pass_cases是数字类型，处理None值
                pass_cases = stat.get('pass_cases', 0)
                stats_dict[date_str.date()] = pass_cases if pass_cases is not None else 0

        # 为每个日期准备数据，缺失的日期使用0
        pass_counts = []
        formatted_dates = []

        for date in complete_dates:
            pass_count = stats_dict.get(date, 0)
            # 确保pass_count是数字类型，处理None值
            pass_count = pass_count if pass_count is not None else 0
            pass_counts.append(pass_count)
            # 格式化日期显示
            formatted_dates.append(date.strftime('%m-%d'))

        return formatted_dates, pass_counts

    def _prepare_complete_weekly_data(self, weekly_stats, weeks):
        """准备完整的周序列数据，为缺失周补充零值

        Args:
            weekly_stats: 数据库查询的周统计数据
            weeks: 时间范围周数

        Returns:
            tuple: (week_labels, pass_counts) 完整的周序列和对应的通过数
        """
        from datetime import datetime, timedelta
        import calendar

        # 创建完整的周序列
        today = datetime.now().date()
        complete_weeks = []

        for i in range(weeks):
            # 计算每周的开始日期（周一）
            days_back = i * 7
            target_date = today - timedelta(days=days_back)
            # 找到该周的周一
            monday = target_date - timedelta(days=target_date.weekday())

            # 计算年份和周数
            year, week_num, _ = monday.isocalendar()
            week_label = f"W{week_num}"

            complete_weeks.append({
                'week_label': week_label,
                'year': year,
                'week_num': week_num,
                'monday': monday
            })

        # 反转列表，使其按时间顺序排列
        complete_weeks.reverse()

        # 将数据库数据转换为字典
        stats_dict = {}
        for stat in weekly_stats:
            key = (stat.get('year'), stat.get('week_num'))
            # 确保pass_cases是数字类型，处理None值
            pass_cases = stat.get('pass_cases', 0)
            stats_dict[key] = pass_cases if pass_cases is not None else 0

        # 为每个周准备数据
        week_labels = []
        pass_counts = []

        for week_info in complete_weeks:
            key = (week_info['year'], week_info['week_num'])
            pass_count = stats_dict.get(key, 0)
            # 确保pass_count是数字类型，处理None值
            pass_count = pass_count if pass_count is not None else 0
            pass_counts.append(pass_count)
            week_labels.append(week_info['week_label'])

        return week_labels, pass_counts

    @pyqtSlot()
    def manual_check_completion(self):
        """手动检查完成状态"""
        try:
            self.status_label.setText("正在检查完成状态...")

            # 直接检查数据库中的On-Going记录
            self.check_ongoing_records_directly()

            # 延迟刷新数据
            QTimer.singleShot(2000, self.refresh_data)
            QTimer.singleShot(2000, lambda: self.status_label.setText("检查完成"))

        except Exception as e:
            self.status_label.setText(f"检查失败: {str(e)}")
            print(f"手动检查完成状态失败: {str(e)}")

    def check_ongoing_records_directly(self):
        """直接检查On-Going记录"""
        try:
            # 获取所有On-Going状态的记录
            records = self.dashboard_model.get_all_simulation_records()
            ongoing_cases = [r for r in records if r.get('status') == 'On-Going']

            for record in ongoing_cases:
                case_name = record.get('case_name')
                if not case_name:
                    continue

                # 构建日志路径
                import os
                current_dir = os.getcwd()
                sim_log_path = os.path.join(current_dir, case_name, "log", "irun_sim.log")
                compile_log_path = os.path.join(current_dir, case_name, "log", "irun_compile.log")

                # 检查仿真状态
                status = self.check_simulation_status_local(sim_log_path)

                if status in ['PASS', 'FAIL']:
                    # 解析时间信息
                    compile_time = self.parse_time_from_log(compile_log_path, False)
                    sim_time = self.parse_time_from_log(sim_log_path, True)

                    # 更新数据库记录
                    from datetime import datetime
                    # 注意：不传入end_time，让数据库逻辑自动处理（只在首次PASS时设置）
                    success = self.dashboard_model.update_simulation_status(
                        case_name=case_name,
                        status=status,
                        compile_time=compile_time,
                        simulation_time=sim_time,
                        log_path=sim_log_path
                    )

                    if not success:
                        print(f"更新失败: {case_name}")

        except Exception as e:
            print(f"直接检查On-Going记录失败: {str(e)}")



    def parse_time_from_log(self, log_path: str, is_simulation: bool) -> Optional[float]:
        """从日志文件解析时间"""
        try:
            if not os.path.exists(log_path):
                return None

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            import re
            if is_simulation:
                # 仿真时间模式
                patterns = [
                    r'Simulation time:\s*(\d+(?:\.\d+)?)\s*minutes',
                    r'Total simulation time:\s*(\d+(?:\.\d+)?)\s*min',
                    r'CPU time:\s*(\d+(?:\.\d+)?)\s*seconds'
                ]
            else:
                # 编译时间模式
                patterns = [
                    r'Total time:\s*(\d+(?:\.\d+)?)\s*minutes',
                    r'Compile time:\s*(\d+(?:\.\d+)?)\s*min',
                    r'Elapsed time:\s*(\d+):(\d+):(\d+)'
                ]

            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    if isinstance(matches[0], tuple) and len(matches[0]) == 3:  # HH:MM:SS格式
                        hours, minutes, seconds = map(int, matches[0])
                        return hours * 60 + minutes + seconds / 60
                    else:
                        time_value = float(matches[0])
                        # 如果是秒，转换为分钟
                        if 'seconds' in pattern:
                            time_value = time_value / 60
                        return time_value

            return None

        except Exception as e:
            print(f"解析时间失败: {str(e)}")
            return None



    @pyqtSlot()
    def scan_current_directory(self):
        """扫描当前目录下的日志文件（异步版本）"""
        try:
            # 检查是否已有扫描在进行
            if self.scan_worker and self.scan_worker.isRunning():
                QMessageBox.information(self, "提示", "扫描正在进行中，请稍候...")
                return

            # 禁用扫描按钮，防止重复操作
            self.scan_current_btn.setEnabled(False)
            self.scan_current_btn.setText("扫描中...")

            # 创建并启动扫描工作线程
            current_dir = os.getcwd()
            self.scan_worker = DirectoryScanWorker(current_dir, self.dashboard_model)

            # 连接信号
            self.scan_worker.progress_updated.connect(self.on_scan_progress)
            self.scan_worker.scan_completed.connect(self.on_scan_completed)
            self.scan_worker.scan_failed.connect(self.on_scan_failed)
            self.scan_worker.finished.connect(self.on_scan_finished)

            # 启动工作线程
            self.scan_worker.start()

            self.status_label.setText("正在启动目录扫描...")

        except Exception as e:
            self.status_label.setText(f"启动扫描失败: {str(e)}")
            self.scan_current_btn.setEnabled(True)
            self.scan_current_btn.setText("扫描当前目录")
            print(f"启动目录扫描失败: {str(e)}")

    def on_scan_progress(self, current, total, message):
        """处理扫描进度更新"""
        if total > 0:
            progress = int((current / total) * 100)
            self.status_label.setText(f"{message} ({current}/{total}) - {progress}%")
        else:
            self.status_label.setText(message)

    def on_scan_completed(self, updated_cases):
        """处理扫描完成"""
        # 刷新数据显示
        QTimer.singleShot(1000, self.refresh_data)

        if updated_cases:
            self.status_label.setText(f"扫描完成，更新了 {len(updated_cases)} 个用例")
            QMessageBox.information(
                self, "扫描完成",
                f"扫描完成！\n更新了以下用例的状态：\n" + "\n".join(updated_cases)
            )
        else:
            self.status_label.setText("扫描完成，未发现新的完成状态")
            QMessageBox.information(self, "扫描完成", "扫描完成，未发现新的完成状态")

    def on_scan_failed(self, error_message):
        """处理扫描失败"""
        self.status_label.setText(f"扫描失败: {error_message}")
        QMessageBox.warning(self, "扫描失败", f"扫描失败: {error_message}")

    def on_scan_finished(self):
        """处理扫描线程结束"""
        # 恢复扫描按钮状态
        self.scan_current_btn.setEnabled(True)
        self.scan_current_btn.setText("扫描当前目录")

        # 清理工作线程
        if self.scan_worker:
            self.scan_worker.deleteLater()
            self.scan_worker = None



    @pyqtSlot()
    def clear_historical_data(self):
        """清除历史数据"""
        try:
            # 获取当前记录数量
            total_records = self.dashboard_model.get_total_records_count()

            if total_records == 0:
                QMessageBox.information(
                    self,
                    "提示",
                    "当前没有历史记录需要清除"
                )
                return

            reply = QMessageBox.question(
                self,
                "确认清除",
                f"确定要清除所有仿真历史记录吗？\n\n"
                f"当前共有 {total_records} 条记录\n"
                f"此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 显示进度对话框
                from PyQt5.QtWidgets import QProgressDialog
                from PyQt5.QtCore import Qt

                progress = QProgressDialog("正在清除历史数据...", "取消", 0, 100, self)
                progress.setWindowModality(Qt.WindowModal)
                progress.setAutoClose(True)
                progress.setAutoReset(True)
                progress.show()

                try:
                    # 执行清除操作
                    progress.setValue(20)
                    try:
                        from PyQt5.QtWidgets import QApplication
                        QApplication.processEvents()
                    except:
                        pass  # 忽略processEvents错误

                    success = self.dashboard_model.clear_all_historical_data()

                    progress.setValue(80)
                    try:
                        from PyQt5.QtWidgets import QApplication
                        QApplication.processEvents()
                    except:
                        pass  # 忽略processEvents错误

                    if success:
                        # 注意：界面刷新由信号处理器自动完成，无需手动刷新

                        progress.setValue(100)
                        try:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                        except:
                            pass  # 忽略processEvents错误

                        QMessageBox.information(
                            self,
                            "清除完成",
                            f"成功清除了 {total_records} 条历史记录"
                        )

                        # 更新状态栏
                        self.status_label.setText("历史数据已清除")
                    else:
                        QMessageBox.warning(
                            self,
                            "清除失败",
                            "清除历史数据时发生错误，请检查数据库连接"
                        )

                except Exception as e:
                    QMessageBox.warning(
                        self,
                        "清除失败",
                        f"清除历史数据时发生错误: {str(e)}"
                    )
                finally:
                    progress.close()

        except Exception as e:
            QMessageBox.warning(
                self,
                "错误",
                f"清除历史数据失败: {str(e)}"
            )


class TestPlanImportWorker(QThread):
    """TestPlan导入异步工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(int, int, str)  # current, total, message
    import_completed = pyqtSignal(int, int, dict)  # success_count, total_count, stage_stats
    import_failed = pyqtSignal(str)  # error_message

    def __init__(self, file_path, dashboard_model):
        super().__init__()
        self.file_path = file_path
        self.dashboard_model = dashboard_model
        self.should_stop = False
        # 仿真阶段解析统计
        self.stage_stats = {
            'from_m_column': 0,  # 从M列解析的数量
            'from_o_column': 0,  # 从O列解析的数量
            'default_used': 0,   # 使用默认值的数量
            'stage_distribution': {}  # 各阶段的分布统计
        }

    def stop(self):
        """停止导入操作"""
        self.should_stop = True

    def run(self):
        """执行异步导入"""
        try:
            self.progress_updated.emit(0, 0, "正在读取Excel文件...")

            # 导入openpyxl
            import openpyxl

            # 读取Excel文件
            wb = openpyxl.load_workbook(self.file_path)

            # 查找TP工作表
            tp_sheet = None
            for sheet_name in wb.sheetnames:
                if 'TP' in sheet_name.upper() or 'TESTPLAN' in sheet_name.upper():
                    tp_sheet = wb[sheet_name]
                    break

            if tp_sheet is None:
                tp_sheet = wb.active

            self.progress_updated.emit(0, 0, "正在解析TestPlan数据...")

            # 解析TestPlan数据
            imported_cases = self.parse_testplan_sheet(tp_sheet)

            if not imported_cases:
                self.import_failed.emit("未找到有效的用例数据")
                return

            total_count = len(imported_cases)
            self.progress_updated.emit(0, total_count, "正在导入用例到数据库...")

            # 批量导入用例
            success_count = self.batch_import_cases(imported_cases)

            # 发送完成信号，包含仿真阶段解析统计信息
            self.import_completed.emit(success_count, total_count, self.stage_stats)

        except Exception as e:
            self.import_failed.emit(str(e))

    def parse_testplan_sheet(self, sheet):
        """解析TestPlan工作表（优化版本）"""
        cases = []

        try:
            # 查找表头行
            header_row = None
            case_name_col = None

            # 只检查前5行，提高效率
            for row_num in range(1, 6):
                if self.should_stop:
                    return cases

                row = list(sheet.iter_rows(min_row=row_num, max_row=row_num, values_only=True))[0]

                for col_num, cell_value in enumerate(row):
                    if cell_value and isinstance(cell_value, str):
                        if any(keyword in cell_value for keyword in ['TestCase Name', '用例名', 'Case']):
                            header_row = row_num
                            case_name_col = col_num
                            break

                if header_row:
                    break

            if not header_row or case_name_col is None:
                return cases

            # 批量读取数据行，提高效率
            data_rows = list(sheet.iter_rows(
                min_row=header_row + 1,
                max_row=sheet.max_row,
                values_only=True
            ))

            for row_num, row in enumerate(data_rows):
                if self.should_stop:
                    break

                # 每处理10行更新一次进度
                if row_num % 10 == 0:
                    self.progress_updated.emit(
                        row_num, len(data_rows),
                        f"正在解析用例数据和仿真阶段... ({row_num}/{len(data_rows)})"
                    )

                # 获取用例名称
                if len(row) > case_name_col and row[case_name_col]:
                    case_name = str(row[case_name_col]).strip()

                    if case_name and not case_name.startswith('#'):
                        # 智能解析仿真阶段
                        simulation_stage = self.parse_simulation_stage(row)

                        cases.append({
                            'case_name': case_name,
                            'stage': simulation_stage
                        })

        except Exception as e:
            print(f"解析TestPlan工作表失败: {str(e)}")

        return cases

    def parse_simulation_stage(self, row):
        """智能解析仿真阶段字段

        Args:
            row: Excel行数据

        Returns:
            str: 解析出的仿真阶段值
        """
        # 定义有效的仿真阶段值
        valid_stages = {'DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2'}

        try:
            # 1. 优先解析M列（索引12，Subsys Phase）
            if len(row) > 12 and row[12]:
                m_value = str(row[12]).strip().upper()
                if m_value in valid_stages:
                    # 更新统计信息
                    self.stage_stats['from_m_column'] += 1
                    self.stage_stats['stage_distribution'][m_value] = \
                        self.stage_stats['stage_distribution'].get(m_value, 0) + 1
                    return m_value
                elif m_value != 'N/A' and m_value != '':
                    # M列有值但不是有效阶段，记录警告但继续检查O列
                    print(f"M列值 '{m_value}' 不是有效的仿真阶段，检查O列")

            # 2. 备用解析O列（索引14，TOP Phase）
            if len(row) > 14 and row[14]:
                o_value = str(row[14]).strip().upper()
                if o_value in valid_stages:
                    # 更新统计信息
                    self.stage_stats['from_o_column'] += 1
                    self.stage_stats['stage_distribution'][o_value] = \
                        self.stage_stats['stage_distribution'].get(o_value, 0) + 1
                    return o_value
                elif o_value != 'N/A' and o_value != '':
                    print(f"O列值 '{o_value}' 不是有效的仿真阶段")

            # 3. 如果M列和O列都无有效值，使用默认值
            print("M列和O列都无有效仿真阶段值，使用默认值 DVR1")
            # 更新统计信息
            self.stage_stats['default_used'] += 1
            self.stage_stats['stage_distribution']['DVR1'] = \
                self.stage_stats['stage_distribution'].get('DVR1', 0) + 1
            return 'DVR1'

        except Exception as e:
            print(f"解析仿真阶段失败: {str(e)}，使用默认值 DVR1")
            # 更新统计信息
            self.stage_stats['default_used'] += 1
            self.stage_stats['stage_distribution']['DVR1'] = \
                self.stage_stats['stage_distribution'].get('DVR1', 0) + 1
            return 'DVR1'

    def batch_import_cases(self, cases):
        """批量导入用例（优化版本）"""
        success_count = 0
        batch_size = 100  # 批量处理大小

        try:
            # 分批处理用例，使用数据库事务优化
            for i in range(0, len(cases), batch_size):
                if self.should_stop:
                    break

                batch = cases[i:i + batch_size]

                # 更新进度
                self.progress_updated.emit(
                    i, len(cases),
                    f"正在导入用例... ({i}/{len(cases)})"
                )

                # 准备批量数据
                batch_data = []
                for case_info in batch:
                    batch_data.append({
                        'case_name': case_info['case_name'],
                        'simulation_stage': case_info.get('stage', 'DVR1'),
                        'command_line': ''
                    })

                # 批量插入
                batch_success = self.dashboard_model.batch_add_simulation_records(batch_data)
                success_count += batch_success

                # 批次间短暂休眠，保持界面响应性
                self.msleep(50)

        except Exception as e:
            print(f"批量导入用例失败: {str(e)}")

        return success_count

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止定时器
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()

        # 停止扫描工作线程
        if self.scan_worker and self.scan_worker.isRunning():
            self.scan_worker.cancel()
            self.scan_worker.quit()
            self.scan_worker.wait(3000)  # 等待最多3秒
            if self.scan_worker.isRunning():
                self.scan_worker.terminate()
                self.scan_worker.wait(1000)  # 再等待1秒

        # 清理资源
        if hasattr(self, 'refresh_manager'):
            self.refresh_manager.set_visibility(False)

        # 注意：不在这里清理dashboard_model，因为它由DashboardController管理
        # 避免重复清理导致的信号断开连接错误

        self.window_closed.emit()
        event.accept()

    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        if hasattr(self, 'refresh_manager'):
            self.refresh_manager.set_visibility(True)
            # 窗口显示时请求一次刷新
            self.refresh_manager.request_refresh()

    def hideEvent(self, event):
        """窗口隐藏事件"""
        super().hideEvent(event)
        if hasattr(self, 'refresh_manager'):
            self.refresh_manager.set_visibility(False)
