"""
集成内存监控系统 - 实时内存使用监控和预警
整合到主系统中，提供全面的内存管理功能
"""
import time
import threading
import psutil
from typing import Dict, List, Callable, Optional, Any
from collections import deque
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from utils.string_pool import intern_string, get_string_pool_stats
from utils.render_cache import get_render_cache_stats

# 导入sip用于检查Qt对象状态
try:
    import sip
except ImportError:
    # 如果没有sip模块，创建一个简单的替代
    class MockSip:
        @staticmethod
        def isdeleted(obj):
            return False
    sip = MockSip()


class MemoryAlert:
    """内存警报类"""
    
    def __init__(self, level: str, message: str, threshold: float, current_value: float):
        self.level = level  # 'warning', 'critical', 'info'
        self.message = intern_string(message)
        self.threshold = threshold
        self.current_value = current_value
        self.timestamp = time.time()
    
    def __str__(self):
        return f"[{self.level.upper()}] {self.message} (当前: {self.current_value:.1f}%, 阈值: {self.threshold:.1f}%)"


class MemoryUsageTracker:
    """内存使用跟踪器"""
    
    def __init__(self, max_history: int = 100):
        self.max_history = max_history
        self._history = deque(maxlen=max_history)
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'peak_usage': 0.0,
            'average_usage': 0.0,
            'min_usage': 100.0,
            'samples_count': 0,
            'last_update': 0.0
        }
    
    def add_sample(self, memory_percent: float, memory_mb: float):
        """添加内存使用样本"""
        with self._lock:
            timestamp = time.time()
            sample = {
                'timestamp': timestamp,
                'memory_percent': memory_percent,
                'memory_mb': memory_mb
            }
            
            self._history.append(sample)
            
            # 更新统计信息
            self._stats['samples_count'] += 1
            self._stats['last_update'] = timestamp
            
            if memory_percent > self._stats['peak_usage']:
                self._stats['peak_usage'] = memory_percent
            
            if memory_percent < self._stats['min_usage']:
                self._stats['min_usage'] = memory_percent
            
            # 计算平均值
            if len(self._history) > 0:
                total_usage = sum(s['memory_percent'] for s in self._history)
                self._stats['average_usage'] = total_usage / len(self._history)
    
    def get_recent_trend(self, minutes: int = 5) -> str:
        """获取最近的内存使用趋势"""
        with self._lock:
            if len(self._history) < 2:
                return "insufficient_data"
            
            cutoff_time = time.time() - (minutes * 60)
            recent_samples = [s for s in self._history if s['timestamp'] > cutoff_time]
            
            if len(recent_samples) < 2:
                return "insufficient_recent_data"
            
            # 计算趋势
            first_usage = recent_samples[0]['memory_percent']
            last_usage = recent_samples[-1]['memory_percent']
            
            change = last_usage - first_usage
            
            if change > 5:
                return "increasing"
            elif change < -5:
                return "decreasing"
            else:
                return "stable"
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return self._stats.copy()
    
    def get_history(self, minutes: int = 10) -> List[Dict[str, Any]]:
        """获取历史数据"""
        with self._lock:
            cutoff_time = time.time() - (minutes * 60)
            return [s for s in self._history if s['timestamp'] > cutoff_time]


class IntegratedMemoryMonitor(QObject):
    """
    集成内存监控器 - 主要的内存监控组件
    """
    
    # 信号定义
    memory_alert = pyqtSignal(object)  # 内存警报信号
    memory_stats_updated = pyqtSignal(dict)  # 内存统计更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 配置参数
        self.update_interval = 2.0  # 更新间隔（秒）
        self.warning_threshold = 75.0  # 警告阈值（%）
        self.critical_threshold = 90.0  # 严重阈值（%）
        
        # 内存跟踪器
        self.tracker = MemoryUsageTracker(max_history=200)
        
        # 进程监控（延迟初始化）
        self.process = None
        
        # 警报管理
        self._alerts = deque(maxlen=50)
        self._last_alert_time = {}
        self._alert_cooldown = 30.0  # 警报冷却时间（秒）
        
        # 定时器
        self._monitor_timer = QTimer(self)  # 设置parent
        self._monitor_timer.timeout.connect(self._update_memory_stats)

        # 销毁标志
        self._destroyed = False
        
        # 组件监控
        self._component_monitors = {}
        
        # 性能统计
        self._performance_stats = {
            'monitoring_overhead': 0.0,
            'total_updates': 0,
            'alerts_generated': 0
        }
        
        # 不在构造函数中自动启动监控
        # 需要手动调用 start_monitoring() 来启动
    
    def start_monitoring(self):
        """启动内存监控"""
        if not self._monitor_timer.isActive():
            self._monitor_timer.start(int(self.update_interval * 1000))
    
    def stop_monitoring(self):
        """停止内存监控"""
        self._destroyed = True
        if hasattr(self, '_monitor_timer') and self._monitor_timer is not None:
            try:
                # 检查Qt对象是否仍然有效
                if not sip.isdeleted(self._monitor_timer) and self._monitor_timer.isActive():
                    self._monitor_timer.stop()
            except (RuntimeError, TypeError, AttributeError):
                # Qt对象可能已被删除，忽略错误
                pass
    
    def _update_memory_stats(self):
        """更新内存统计信息"""
        if self._destroyed:
            return

        start_time = time.perf_counter()

        try:
            # 延迟初始化进程对象
            if self.process is None:
                try:
                    self.process = psutil.Process()
                except Exception as e:
                    print(f"初始化进程监控失败: {e}")
                    self._destroyed = True
                    return

            # 获取系统内存信息
            system_memory = psutil.virtual_memory()

            # 获取进程内存信息
            process_memory = self.process.memory_info()
            process_percent = self.process.memory_percent()
            
            # 添加到跟踪器
            self.tracker.add_sample(
                process_percent, 
                process_memory.rss / 1024 / 1024
            )
            
            # 检查警报条件
            self._check_memory_alerts(process_percent, system_memory.percent)
            
            # 收集组件内存信息
            component_stats = self._collect_component_stats()
            
            # 构建综合统计信息
            comprehensive_stats = {
                'system': {
                    'total_mb': system_memory.total / 1024 / 1024,
                    'available_mb': system_memory.available / 1024 / 1024,
                    'used_percent': system_memory.percent,
                    'free_mb': system_memory.free / 1024 / 1024
                },
                'process': {
                    'rss_mb': process_memory.rss / 1024 / 1024,
                    'vms_mb': process_memory.vms / 1024 / 1024,
                    'percent': process_percent,
                    'peak_mb': self.tracker.get_stats()['peak_usage']
                },
                'components': component_stats,
                'tracker': self.tracker.get_stats(),
                'trend': self.tracker.get_recent_trend(),
                'timestamp': time.time()
            }
            
            # 发出统计更新信号（安全方式）
            try:
                if not self._destroyed:
                    self.memory_stats_updated.emit(comprehensive_stats)
            except RuntimeError as e:
                if "wrapped C/C++ object" in str(e):
                    print(f"内存监控器对象已删除: {str(e)}")
                    self._destroyed = True
                    self.stop_monitoring()
                else:
                    raise
            
            # 更新性能统计
            self._performance_stats['total_updates'] += 1
            
        except Exception as e:
            print(f"更新内存统计时出错: {e}")
        
        finally:
            # 计算监控开销
            monitoring_time = time.perf_counter() - start_time
            self._performance_stats['monitoring_overhead'] = (
                (self._performance_stats['monitoring_overhead'] * 
                 (self._performance_stats['total_updates'] - 1) + monitoring_time) /
                self._performance_stats['total_updates']
            )
    
    def _check_memory_alerts(self, process_percent: float, system_percent: float):
        """检查内存警报条件"""
        current_time = time.time()
        
        # 检查进程内存使用
        if process_percent > self.critical_threshold:
            self._generate_alert(
                'critical',
                f'进程内存使用率过高: {process_percent:.1f}%',
                self.critical_threshold,
                process_percent,
                'process_critical'
            )
        elif process_percent > self.warning_threshold:
            self._generate_alert(
                'warning',
                f'进程内存使用率较高: {process_percent:.1f}%',
                self.warning_threshold,
                process_percent,
                'process_warning'
            )
        
        # 检查系统内存使用
        if system_percent > 85:
            self._generate_alert(
                'warning',
                f'系统内存使用率较高: {system_percent:.1f}%',
                85.0,
                system_percent,
                'system_warning'
            )
        
        # 检查内存趋势
        trend = self.tracker.get_recent_trend()
        if trend == "increasing":
            self._generate_alert(
                'info',
                '内存使用呈上升趋势，建议检查内存泄漏',
                0.0,
                process_percent,
                'trend_increasing'
            )
    
    def _generate_alert(self, level: str, message: str, threshold: float, 
                       current_value: float, alert_type: str):
        """生成内存警报"""
        current_time = time.time()
        
        # 检查冷却时间
        if alert_type in self._last_alert_time:
            if current_time - self._last_alert_time[alert_type] < self._alert_cooldown:
                return
        
        # 创建警报
        alert = MemoryAlert(level, message, threshold, current_value)
        self._alerts.append(alert)
        self._last_alert_time[alert_type] = current_time
        
        # 发出警报信号（安全方式）
        try:
            if not self._destroyed:
                self.memory_alert.emit(alert)
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                print(f"内存监控器对象已删除: {str(e)}")
                self._destroyed = True
                self.stop_monitoring()
            else:
                raise
        
        # 更新统计
        self._performance_stats['alerts_generated'] += 1
    
    def _collect_component_stats(self) -> Dict[str, Any]:
        """收集组件内存统计"""
        component_stats = {}
        
        try:
            # 字符串池统计
            string_pool_stats = get_string_pool_stats()
            if string_pool_stats:
                component_stats['string_pool'] = {
                    'unique_strings': string_pool_stats.get('unique_strings', 0),
                    'hit_ratio': string_pool_stats.get('hit_ratio', 0),
                    'total_requests': string_pool_stats.get('total_requests', 0)
                }
            
            # 渲染缓存统计
            render_cache_stats = get_render_cache_stats()
            if render_cache_stats:
                component_stats['render_cache'] = {
                    'overall_hit_rate': render_cache_stats.get('overall', {}).get('hit_rate', 0),
                    'font_cache_size': render_cache_stats.get('font_cache', {}).get('font_cache_size', 0),
                    'render_cache_size': render_cache_stats.get('render_cache', {}).get('cache_size', 0)
                }
            
        except Exception as e:
            print(f"收集组件统计时出错: {e}")
        
        return component_stats
    
    def register_component_monitor(self, name: str, monitor_func: Callable[[], Dict[str, Any]]):
        """注册组件监控函数"""
        self._component_monitors[name] = monitor_func
    
    def get_recent_alerts(self, minutes: int = 10) -> List[MemoryAlert]:
        """获取最近的警报"""
        cutoff_time = time.time() - (minutes * 60)
        return [alert for alert in self._alerts if alert.timestamp > cutoff_time]
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """获取内存使用摘要"""
        try:
            system_memory = psutil.virtual_memory()

            # 确保进程对象已初始化
            if self.process is None:
                try:
                    self.process = psutil.Process()
                except Exception as e:
                    print(f"初始化进程对象失败: {e}")
                    return {
                        'system_usage_percent': system_memory.percent,
                        'process_usage_mb': 0.0,
                        'process_usage_percent': 0.0,
                        'trend': 'unknown',
                        'peak_usage': 0.0,
                        'recent_alerts_count': 0,
                        'monitoring_overhead_ms': 0.0,
                        'error': 'process_init_failed'
                    }

            process_memory = self.process.memory_info()
            process_percent = self.process.memory_percent()

            return {
                'system_usage_percent': system_memory.percent,
                'process_usage_mb': process_memory.rss / 1024 / 1024,
                'process_usage_percent': process_percent,
                'trend': self.tracker.get_recent_trend(),
                'peak_usage': self.tracker.get_stats()['peak_usage'],
                'recent_alerts_count': len(self.get_recent_alerts(5)),
                'monitoring_overhead_ms': self._performance_stats['monitoring_overhead'] * 1000
            }
        except Exception as e:
            print(f"获取内存摘要时出错: {e}")
            return {
                'system_usage_percent': 0.0,
                'process_usage_mb': 0.0,
                'process_usage_percent': 0.0,
                'trend': 'unknown',
                'peak_usage': 0.0,
                'recent_alerts_count': 0,
                'monitoring_overhead_ms': 0.0,
                'error': str(e)
            }
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        try:
            import gc
            
            # 强制垃圾回收
            collected = gc.collect()
            
            # 生成优化信息警报
            self._generate_alert(
                'info',
                f'内存优化完成，回收了 {collected} 个对象',
                0.0,
                0.0,
                'optimization'
            )
            
            return collected
            
        except Exception as e:
            print(f"优化内存使用时出错: {e}")
            return 0
    
    def cleanup(self):
        """清理资源"""
        try:
            self._destroyed = True
            self.stop_monitoring()

            # 断开所有信号连接
            try:
                self.memory_alert.disconnect()
                self.memory_stats_updated.disconnect()
            except (RuntimeError, TypeError):
                pass

            # 清理定时器 - 更安全的方式
            if hasattr(self, '_monitor_timer') and self._monitor_timer is not None:
                try:
                    # 检查Qt对象是否仍然有效
                    if not sip.isdeleted(self._monitor_timer):
                        self._monitor_timer.stop()
                        try:
                            self._monitor_timer.timeout.disconnect()
                        except (RuntimeError, TypeError):
                            pass
                        self._monitor_timer.deleteLater()
                except (RuntimeError, TypeError, AttributeError):
                    # Qt对象可能已被删除，忽略错误
                    pass
                finally:
                    self._monitor_timer = None

            # 清理进程对象
            self.process = None

            # 清理集合
            if hasattr(self, '_alerts'):
                self._alerts.clear()
            if hasattr(self, '_component_monitors'):
                self._component_monitors.clear()

        except Exception as e:
            print(f"清理内存监控器时出错: {e}")

    def __del__(self):
        """析构函数"""
        try:
            self._destroyed = True
            if hasattr(self, '_monitor_timer') and self._monitor_timer is not None:
                try:
                    # 检查Qt对象是否仍然有效
                    if not sip.isdeleted(self._monitor_timer):
                        self._monitor_timer.stop()
                except (RuntimeError, TypeError, AttributeError):
                    # Qt对象可能已被删除，忽略错误
                    pass
        except Exception:
            # 忽略析构函数中的所有错误
            pass


# 全局内存监控器实例
_global_memory_monitor = None
_monitor_lock = threading.Lock()
_monitor_valid = True


def get_global_memory_monitor() -> IntegratedMemoryMonitor:
    """获取全局内存监控器实例（单例模式）"""
    global _global_memory_monitor, _monitor_valid

    if _global_memory_monitor is None or not _monitor_valid:
        with _monitor_lock:
            if _global_memory_monitor is None or not _monitor_valid:
                try:
                    # 检查是否有QApplication实例
                    from PyQt5.QtWidgets import QApplication
                    if QApplication.instance() is None:
                        print("没有QApplication实例，无法创建内存监控器")
                        return None

                    _global_memory_monitor = IntegratedMemoryMonitor()
                    _monitor_valid = True

                    # 注册应用程序退出时的清理函数
                    import atexit
                    atexit.register(cleanup_global_memory_monitor)

                except Exception as e:
                    print(f"创建全局内存监控器失败: {str(e)}")
                    _monitor_valid = False
                    return None

    return _global_memory_monitor if _monitor_valid else None


def cleanup_global_memory_monitor():
    """清理全局内存监控器"""
    global _global_memory_monitor, _monitor_valid

    with _monitor_lock:
        if _global_memory_monitor:
            try:
                _global_memory_monitor.cleanup()
            except Exception as e:
                print(f"清理全局内存监控器失败: {str(e)}")
            finally:
                _global_memory_monitor = None
                _monitor_valid = False


def get_memory_summary() -> Dict[str, Any]:
    """获取内存使用摘要"""
    monitor = get_global_memory_monitor()
    if monitor:
        try:
            return monitor.get_memory_summary()
        except Exception as e:
            # 如果监控器有问题，直接使用psutil获取基本信息
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                return {
                    'process_usage_mb': memory_info.rss / 1024 / 1024,
                    'system_usage_percent': psutil.virtual_memory().percent,
                    'fallback': True
                }
            except Exception:
                return {
                    'process_usage_mb': 0.0,
                    'system_usage_percent': 0.0,
                    'error': True
                }
    return {
        'process_usage_mb': 0.0,
        'system_usage_percent': 0.0,
        'no_monitor': True
    }


def optimize_system_memory():
    """优化系统内存使用"""
    monitor = get_global_memory_monitor()
    if monitor:
        try:
            return monitor.optimize_memory_usage()
        except Exception as e:
            print(f"优化系统内存失败: {str(e)}")
            return 0
    return 0
