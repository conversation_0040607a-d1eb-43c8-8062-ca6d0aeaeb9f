"""
资源优化器
用于优化应用程序的资源使用，减少内存和CPU占用
"""
import os
import gc
import sys
import time
import threading
import psutil
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

class ResourceOptimizer(QObject):
    """资源优化器，用于优化应用程序的资源使用"""
    
    # 定义信号
    optimization_performed = pyqtSignal(dict)  # 优化执行后的信号，包含优化结果
    
    def __init__(self, parent=None):
        """初始化资源优化器"""
        super().__init__(parent)
        self.process = psutil.Process(os.getpid())
        self.optimization_timer = None
        self.optimization_interval = 60000  # 默认每分钟优化一次
        self.memory_threshold = 500  # 默认内存阈值（MB）
        self.cpu_threshold = 50  # 默认CPU阈值（%）
        self.log_cache_size = 10000  # 默认日志缓存大小（行）
        self.log_refresh_interval = 500  # 默认日志刷新间隔（毫秒）
        self.optimization_enabled = True
        
    def start_optimization(self, interval=None):
        """
        开始定期优化
        
        Args:
            interval (int, optional): 优化间隔（毫秒）
        """
        if interval is not None:
            self.optimization_interval = interval
            
        # 停止现有定时器
        if self.optimization_timer:
            self.optimization_timer.stop()
            
        # 创建新定时器
        self.optimization_timer = QTimer(self)
        self.optimization_timer.timeout.connect(self.optimize_resources)
        self.optimization_timer.start(self.optimization_interval)
        
    def stop_optimization(self):
        """停止定期优化"""
        if self.optimization_timer:
            self.optimization_timer.stop()
            
    def set_thresholds(self, memory_threshold=None, cpu_threshold=None):
        """
        设置优化阈值
        
        Args:
            memory_threshold (int, optional): 内存阈值（MB）
            cpu_threshold (int, optional): CPU阈值（%）
        """
        if memory_threshold is not None:
            self.memory_threshold = memory_threshold
            
        if cpu_threshold is not None:
            self.cpu_threshold = cpu_threshold
            
    def set_log_parameters(self, cache_size=None, refresh_interval=None):
        """
        设置日志参数
        
        Args:
            cache_size (int, optional): 日志缓存大小（行）
            refresh_interval (int, optional): 日志刷新间隔（毫秒）
        """
        if cache_size is not None:
            self.log_cache_size = cache_size
            
        if refresh_interval is not None:
            self.log_refresh_interval = refresh_interval
            
    def optimize_resources(self):
        """优化资源使用"""
        if not self.optimization_enabled:
            return
            
        # 获取当前资源使用情况
        memory_usage = self.process.memory_info().rss / (1024 * 1024)  # 转换为MB
        cpu_usage = self.process.cpu_percent(interval=0.1)
        
        # 初始化优化结果
        result = {
            "memory_before": memory_usage,
            "cpu_before": cpu_usage,
            "memory_after": memory_usage,
            "cpu_after": cpu_usage,
            "gc_collected": 0,
            "optimizations_applied": []
        }
        
        # 检查是否需要优化
        if memory_usage > self.memory_threshold or cpu_usage > self.cpu_threshold:
            # 执行垃圾回收
            gc_count = gc.collect()
            result["gc_collected"] = gc_count
            result["optimizations_applied"].append("垃圾回收")
            
            # 清理未使用的缓存
            self._clear_unused_caches()
            result["optimizations_applied"].append("清理缓存")
            
            # 优化日志处理
            self._optimize_log_handling()
            result["optimizations_applied"].append("优化日志处理")
            
            # 获取优化后的资源使用情况
            memory_usage_after = self.process.memory_info().rss / (1024 * 1024)
            cpu_usage_after = self.process.cpu_percent(interval=0.1)
            
            result["memory_after"] = memory_usage_after
            result["cpu_after"] = cpu_usage_after
            
            # 发送优化执行信号
            self.optimization_performed.emit(result)
            
    def _clear_unused_caches(self):
        """清理未使用的缓存"""
        # 查找并清理应用程序中的缓存
        for obj in gc.get_objects():
            # 清理字典缓存
            if isinstance(obj, dict) and len(obj) > 100:
                if hasattr(obj, 'clear') and callable(obj.clear) and not isinstance(obj, type):
                    # 检查是否是系统字典
                    if not any(obj is getattr(sys, attr) for attr in dir(sys) if isinstance(getattr(sys, attr), dict)):
                        # 检查是否是模块字典
                        if not any(obj is getattr(module, '__dict__', {}) for module in sys.modules.values()):
                            # 检查是否是类字典
                            if not any(obj is getattr(cls, '__dict__', {}) for cls in gc.get_objects() if isinstance(cls, type)):
                                # 如果不是系统、模块或类字典，尝试清理
                                try:
                                    obj.clear()
                                except Exception:
                                    pass
                                    
    def _optimize_log_handling(self):
        """优化日志处理"""
        # 查找并优化日志处理器
        from utils.adaptive_log_handler import AdaptiveLogHandler
        
        for obj in gc.get_objects():
            if isinstance(obj, AdaptiveLogHandler):
                # 设置日志缓存大小
                if hasattr(obj, 'set_max_batch_size'):
                    obj.set_max_batch_size(self.log_cache_size)
                    
                # 设置日志刷新间隔
                if hasattr(obj, 'set_refresh_interval'):
                    obj.set_refresh_interval(self.log_refresh_interval)
                    
    def get_resource_usage(self):
        """
        获取当前资源使用情况
        
        Returns:
            dict: 资源使用情况
        """
        memory_usage = self.process.memory_info().rss / (1024 * 1024)  # 转换为MB
        cpu_usage = self.process.cpu_percent(interval=0.1)
        
        return {
            "memory_usage": memory_usage,
            "cpu_usage": cpu_usage,
            "memory_threshold": self.memory_threshold,
            "cpu_threshold": self.cpu_threshold
        }
        
    def enable_optimization(self, enabled=True):
        """
        启用或禁用优化
        
        Args:
            enabled (bool): 是否启用优化
        """
        self.optimization_enabled = enabled
