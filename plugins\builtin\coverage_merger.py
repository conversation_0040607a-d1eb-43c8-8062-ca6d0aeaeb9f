from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLineEdit, QFileDialog, QLabel, QComboBox, QListWidget,
                           QTextEdit, QMessageBox, QToolTip, QApplication)
from PyQt5.QtCore import Qt, QEvent, QObject, pyqtSignal
from plugins.base import PluginBase
import os
import json
import time
from datetime import datetime
from abc import ABC
from utils.async_task_manager import AsyncTaskManager

# 创建一个新的元类来解决冲突
class CoverageMergerMeta(type(QObject), type(ABC)):
    pass

# 使用新的元类
class CoverageMergerPlugin(QObject, PluginBase, metaclass=CoverageMergerMeta):
    @property
    def name(self):
        return "覆盖率合并工具"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "合并多个覆盖率数据库，支持不同层级的覆盖率合并"

    # 定义信号
    task_completed = pyqtSignal(str, bool)  # 消息, 是否成功
    task_progress = pyqtSignal(str)  # 进度消息

    def __init__(self):
        QObject.__init__(self)
        PluginBase.__init__(self)
        self.merge_databases = []
        # 修改历史记录文件路径为执行目录
        self.history_file = os.path.join(os.getcwd(), "coverage_merge_history.json")
        self.command_history = self.load_history()

        # 创建异步任务管理器
        self.async_task_manager = AsyncTaskManager()
        self.async_task_manager.task_completed.connect(self.handle_task_completed)
        self.async_task_manager.task_error.connect(self.handle_task_error)

        # 对话框引用
        self.current_dialog = None

        # 状态指示器
        self.status_action = None
        self.is_running = False

    def load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"加载历史记录失败: {str(e)}")
            return []

    def save_history(self, command_data):
        """保存命令到历史记录"""
        try:
            # 读取现有历史记录
            history = []
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except Exception:
                    history = []

            # 检查是否存在相同配置，如果存在则删除旧的
            for i, item in enumerate(history):
                if (item.get('base_dir') == command_data['base_dir'] and
                    item.get('merge_hier') == command_data['merge_hier'] and
                    item.get('merge_work') == command_data['merge_work']):
                    del history[i]
                    break

            # 添加新记录到开头
            history.insert(0, command_data)

            # 保持最多20条记录
            if len(history) > 20:
                history = history[:20]

            # 保存到文件
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存历史记录失败: {str(e)}")

    def initialize(self, main_window):
        """初始化插件，添加菜单项"""
        try:
            self.main_window = main_window

            if not hasattr(main_window, 'tools_menu'):
                print("错误: 主窗口没有tools_menu属性")
                return

            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_merger_dialog)

            self.main_window.tools_menu.addSeparator()
            self.main_window.tools_menu.addAction(self.menu_action)

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            from PyQt5.QtWidgets import QLabel, QToolButton
            from PyQt5.QtGui import QIcon

            # 创建状态指示器标签
            self.status_label = QLabel("覆盖率合并工具运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.current_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.current_dialog.isVisible():
                self.current_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.current_dialog.isMinimized():
                self.current_dialog.showNormal()
            # 将对话框置于前台
            self.current_dialog.raise_()
            self.current_dialog.activateWindow()

    def cleanup(self):
        """清理插件资源"""
        # 清理异步任务管理器
        if hasattr(self, 'async_task_manager'):
            try:
                self.async_task_manager.cleanup()
            except Exception as e:
                print(f"清理异步任务管理器失败: {str(e)}")

        # 清理菜单项
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        # 关闭对话框
        if hasattr(self, 'current_dialog') and self.current_dialog:
            try:
                self.current_dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

        # 重置运行状态
        self.is_running = False

    def show_merger_dialog(self):
        """显示覆盖率合并对话框"""
        # 创建非模态对话框
        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("覆盖率合并工具")
        dialog.resize(800, 800)
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 保存对话框引用
        self.current_dialog = dialog

        # 连接信号
        self.task_progress.connect(lambda msg: self.add_log(msg))
        self.task_completed.connect(self.handle_merge_completed)

        layout = QVBoxLayout()

        # 添加历史记录选择
        history_group = QHBoxLayout()
        history_label = QLabel("历史记录:")
        history_label.setFixedWidth(70)  # 固定标签宽度

        self.history_combo = QComboBox()
        self.history_combo.setMaximumWidth(600)  # 设置最大宽度
        self.history_combo.setSizeAdjustPolicy(QComboBox.AdjustToMinimumContentsLengthWithIcon)
        self.history_combo.setMinimumContentsLength(50)  # 设置最小内容长度

        # 添加工具提示功能
        self.history_combo.setToolTipDuration(-1)  # 工具提示一直显示
        self.history_combo.installEventFilter(self)  # 安装事件过滤器

        # 添加历史记录
        self.history_combo.addItem("新建配置")
        for item in self.command_history:
            command = item.get('command', '')
            # 格式化显示文本
            display_text = self.format_command_text(command)
            self.history_combo.addItem(display_text, item)
            # 设置完整命令作为工具提示
            self.history_combo.setItemData(self.history_combo.count()-1, command, Qt.ToolTipRole)

        self.history_combo.currentIndexChanged.connect(self.load_history_item)
        history_group.addWidget(history_label)
        history_group.addWidget(self.history_combo)
        layout.addLayout(history_group)

        # 第一部分：覆盖率基板database输入
        base_group = QHBoxLayout()
        base_label = QLabel("覆盖率基板路径:")
        self.base_input = QLineEdit()
        base_button = QPushButton("浏览...")
        base_button.clicked.connect(lambda: self.browse_directory(self.base_input))
        base_group.addWidget(base_label)
        base_group.addWidget(self.base_input)
        base_group.addWidget(base_button)
        layout.addLayout(base_group)

        # 第二部分：覆盖率database列表
        db_group = QVBoxLayout()
        db_label = QLabel("覆盖率数据库列表:")
        self.db_list = QListWidget()
        db_buttons = QHBoxLayout()
        add_db_button = QPushButton("添加数据库")
        remove_db_button = QPushButton("移除选中")
        add_db_button.clicked.connect(self.add_database)
        remove_db_button.clicked.connect(self.remove_database)
        db_buttons.addWidget(add_db_button)
        db_buttons.addWidget(remove_db_button)
        db_group.addWidget(db_label)
        db_group.addWidget(self.db_list)
        db_group.addLayout(db_buttons)
        layout.addLayout(db_group)

        # 第三部分：覆盖率合并层级
        hier_group = QHBoxLayout()
        hier_label = QLabel("覆盖率合并层级:")
        self.hier_input = QLineEdit()
        hier_group.addWidget(hier_label)
        hier_group.addWidget(self.hier_input)
        layout.addLayout(hier_group)

        # 第四部分：initial_model选择
        model_group = QHBoxLayout()
        model_label = QLabel("Initial Model:")
        self.model_combo = QComboBox()
        self.model_combo.addItems(["primary_run", "empty", "union_all:cov", "自定义路径"])
        self.model_input = QLineEdit()
        self.model_input.setPlaceholderText("输入自定义路径...")
        self.model_input.setEnabled(False)
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        model_group.addWidget(model_label)
        model_group.addWidget(self.model_combo)
        model_group.addWidget(self.model_input)
        layout.addLayout(model_group)

        # 第五部分：merge_work输出路径
        work_group = QHBoxLayout()
        work_label = QLabel("合并结果输出路径:")
        self.work_input = QLineEdit()
        work_button = QPushButton("浏览...")
        work_button.clicked.connect(lambda: self.browse_directory(self.work_input))
        work_group.addWidget(work_label)
        work_group.addWidget(self.work_input)
        work_group.addWidget(work_button)
        layout.addLayout(work_group)

        # 第六部分：merge_cfg配置文件
        cfg_group = QHBoxLayout()
        cfg_label = QLabel("Merge配置文件:")
        self.cfg_input = QLineEdit()
        cfg_button = QPushButton("浏览...")
        cfg_button.clicked.connect(lambda: self.browse_file(self.cfg_input))
        cfg_group.addWidget(cfg_label)
        cfg_group.addWidget(self.cfg_input)
        cfg_group.addWidget(cfg_button)
        layout.addLayout(cfg_group)

        # 添加命令预览区域
        preview_label = QLabel("命令预览:")
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMaximumHeight(100)
        self.preview_text.setStyleSheet("background-color: #f0f0f0;")
        layout.addWidget(preview_label)
        layout.addWidget(self.preview_text)

        # 添加一个预览按钮
        preview_button = QPushButton("预览命令")
        preview_button.clicked.connect(lambda: self.preview_command())
        layout.addWidget(preview_button)

        # 添加日志显示区域
        log_label = QLabel("执行日志:")
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(200)
        layout.addWidget(log_label)
        layout.addWidget(self.log_text)

        # 添加按钮布局
        button_layout = QHBoxLayout()

        # 合并按钮
        self.merge_button = QPushButton("开始合并")
        self.merge_button.clicked.connect(lambda: self.merge_coverage())
        button_layout.addWidget(self.merge_button)

        # 添加最小化按钮
        minimize_button = QPushButton("最小化窗口")
        minimize_button.clicked.connect(dialog.showMinimized)
        button_layout.addWidget(minimize_button)

        # 添加后台运行按钮
        background_button = QPushButton("后台运行")
        background_button.clicked.connect(lambda: self.run_in_background(dialog))
        button_layout.addWidget(background_button)

        layout.addLayout(button_layout)

        dialog.setLayout(layout)

        # 连接对话框关闭事件
        dialog.finished.connect(self.on_dialog_closed)

        # 非模态显示对话框
        dialog.show()

    def format_command_text(self, command):
        """格式化命令文本，使其适合在下拉框中显示"""
        max_length = 80  # 最大显示长度
        if len(command) > max_length:
            # 保留命令开头和结尾，中间用省略号替代
            return f"{command[:40]}...{command[-37:]}"
        return command

    def eventFilter(self, obj, event):
        """事件过滤器，处理鼠标悬停事件"""
        if obj == self.history_combo and event.type() == QEvent.ToolTip:
            index = self.history_combo.currentIndex()
            if index > 0:  # 跳过"新建配置"选项
                # 显示完整命令作为工具提示
                command = self.history_combo.itemData(index, Qt.ToolTipRole)
                if command:
                    QToolTip.showText(event.globalPos(), command)
                    return True
        return super().eventFilter(obj, event)

    def load_history_item(self, index):
        """加载选中的历史记录项"""
        if index == 0:  # 新建配置
            self.clear_all_inputs()
            return

        history_item = self.history_combo.itemData(index)
        if not history_item:
            return

        # 填充所有输入框
        self.base_input.setText(history_item.get('base_dir', ''))
        self.hier_input.setText(history_item.get('merge_hier', ''))
        self.work_input.setText(history_item.get('merge_work', ''))
        self.cfg_input.setText(history_item.get('merge_cfg', ''))

        # 设置initial_model
        initial_model = history_item.get('initial_model', 'primary_run')
        if initial_model in ['primary_run', 'empty', 'union_all:cov']:
            self.model_combo.setCurrentText(initial_model)
            self.model_input.clear()
        else:
            self.model_combo.setCurrentText('自定义路径')
            self.model_input.setText(initial_model)

        # 清空并重新添加数据库列表
        self.db_list.clear()
        for db in history_item.get('databases', []):
            self.db_list.addItem(db)

        # 更新命令预览
        self.preview_command()

    def clear_all_inputs(self):
        """清空所有输入"""
        self.base_input.clear()
        self.db_list.clear()
        self.hier_input.clear()
        self.model_combo.setCurrentText('primary_run')
        self.model_input.clear()
        self.work_input.clear()
        self.cfg_input.clear()
        self.preview_text.clear()

    def preview_command(self):
        """预览将要执行的命令"""
        try:
            # 构建命令
            cmd_parts = ["runsim"]

            # 添加基板路径
            base_dir = self.base_input.text().strip()
            if base_dir:
                cmd_parts.append("-merge_dir")
                cmd_parts.append(base_dir)

            # 添加数据库列表
            for i in range(self.db_list.count()):
                db_path = self.db_list.item(i).text()
                cmd_parts.append(db_path)

            # 添加合并层级
            merge_hier = self.hier_input.text().strip()
            if merge_hier:
                cmd_parts.append("-merge_hier")
                cmd_parts.append(merge_hier)

            # 添加initial_model
            model = self.model_combo.currentText()
            if model == "primary_run":
                cmd_parts.extend(["-initial_model", "primary_run"])
            elif model == "empty":
                cmd_parts.extend(["-initial_model", "empty"])
            elif model == "union_all:cov":
                cmd_parts.extend(["-initial_model", "union_all:cov"])
            elif model == "自定义路径":
                custom_path = self.model_input.text().strip()
                if custom_path:
                    cmd_parts.extend(["-initial_model", custom_path])

            # 添加merge_work
            merge_work = self.work_input.text().strip()
            if merge_work:
                cmd_parts.extend(["-merge_work", merge_work])

            # 添加merge_cfg
            merge_cfg = self.cfg_input.text().strip()
            if merge_cfg:
                cmd_parts.extend(["-merge_cfg", merge_cfg])

            # 更新预览窗口
            command = " ".join(cmd_parts)
            self.preview_text.setText(command)

        except Exception as e:
            self.preview_text.setText(f"生成预览命令时出错：{str(e)}")

    def add_log(self, message):
        """添加日志信息到日志区域"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.append(f"[{timestamp}] {message}")
            # 滚动到底部
            self.log_text.verticalScrollBar().setValue(
                self.log_text.verticalScrollBar().maximum()
            )
            # 处理事件，保持UI响应
            QApplication.processEvents()

    def browse_directory(self, line_edit):
        """浏览并选择目录"""
        directory = QFileDialog.getExistingDirectory(
            self.main_window,
            "选择目录",
            "",
            QFileDialog.ShowDirsOnly
        )
        if directory:
            line_edit.setText(directory)

    def browse_file(self, line_edit):
        """浏览并选择文件"""
        file_name, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择文件",
            "",
            "所有文件 (*.*)"
        )
        if file_name:
            line_edit.setText(file_name)

    def add_database(self):
        """添加覆盖率数据库"""
        directory = QFileDialog.getExistingDirectory(
            self.main_window,
            "选择覆盖率数据库目录",
            "",
            QFileDialog.ShowDirsOnly
        )
        if directory:
            self.db_list.addItem(directory)

    def remove_database(self):
        """移除选中的数据库"""
        current_item = self.db_list.currentItem()
        if current_item:
            self.db_list.takeItem(self.db_list.row(current_item))

    def on_model_changed(self, text):
        """处理initial_model选择变化"""
        self.model_input.setEnabled(text == "自定义路径")

    def merge_coverage(self):
        """执行覆盖率合并命令"""
        try:
            # 获取当前命令
            command = self.preview_text.toPlainText().strip()
            if not command:
                self.preview_command()
                command = self.preview_text.toPlainText().strip()

            if not command:
                self.add_log("错误：未能生成有效的合并命令")
                QMessageBox.critical(self.current_dialog, "错误", "未能生成有效的合并命令")
                return

            # 保存当前配置到历史记录
            current_config = {
                'base_dir': self.base_input.text().strip(),
                'databases': [self.db_list.item(i).text() for i in range(self.db_list.count())],
                'merge_hier': self.hier_input.text().strip(),
                'initial_model': self.model_input.text().strip() if self.model_combo.currentText() == '自定义路径'
                               else self.model_combo.currentText(),
                'merge_work': self.work_input.text().strip(),
                'merge_cfg': self.cfg_input.text().strip(),
                'command': command,  # 保存完整命令
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 执行命令前先保存到历史记录
            self.save_history(current_config)

            # 禁用合并按钮，防止重复点击
            self.merge_button.setEnabled(False)
            self.merge_button.setText("合并中...")

            # 清空日志区域
            self.log_text.clear()

            # 添加开始日志
            self.add_log("开始执行覆盖率合并...")
            self.add_log(f"执行命令：\n{command}")

            # 设置运行状态
            self.is_running = True

            # 显示状态指示器
            self.update_status_indicator(True)

            # 在后台线程中执行合并操作
            self.async_task_manager.run_async(self.execute_merge_command, command)

        except Exception as e:
            error_msg = str(e)
            self.add_log(f"执行覆盖率合并时出错：{error_msg}")
            QMessageBox.critical(self.current_dialog, "错误", f"执行覆盖率合并时出错：{error_msg}")
            self.merge_button.setEnabled(True)
            self.merge_button.setText("开始合并")

            # 重置运行状态
            self.is_running = False

            # 隐藏状态指示器
            self.update_status_indicator(False)

    def execute_merge_command(self, command):
        """在后台线程中执行合并命令"""
        from subprocess import Popen, PIPE, STDOUT

        try:
            # 创建进程，合并标准输出和标准错误
            process = Popen(command, shell=True, stdout=PIPE, stderr=STDOUT, text=True,
                           bufsize=1, universal_newlines=True, encoding='gbk', errors='replace')

            # 实时读取输出并发送到UI
            for line in iter(process.stdout.readline, ''):
                if line.strip():  # 只处理非空行
                    self.task_progress.emit(line.strip())
                    # 处理事件，保持UI响应
                    QApplication.processEvents()
                    # 短暂休眠，避免CPU占用过高
                    time.sleep(0.01)

            # 等待进程完成
            process.stdout.close()
            return_code = process.wait()

            # 返回执行结果
            return {
                'return_code': return_code,
                'success': return_code == 0
            }

        except Exception as e:
            # 返回错误信息
            return {
                'return_code': -1,
                'error': str(e),
                'success': False
            }

    def handle_task_completed(self, result):
        """处理异步任务完成"""
        try:
            if isinstance(result, dict):
                if result.get('success', False):
                    self.task_completed.emit("覆盖率合并成功完成!", True)
                else:
                    error_msg = result.get('error', f"进程返回错误码: {result.get('return_code', -1)}")
                    self.task_completed.emit(f"覆盖率合并失败: {error_msg}", False)
            else:
                self.task_completed.emit(f"收到未知结果: {result}", False)
        except Exception as e:
            self.task_completed.emit(f"处理任务结果时出错: {str(e)}", False)

    def handle_task_error(self, error_msg):
        """处理异步任务错误"""
        self.task_completed.emit(f"任务执行失败: {error_msg}", False)

    def handle_merge_completed(self, message, success):
        """处理合并完成事件"""
        # 恢复按钮状态
        self.merge_button.setEnabled(True)
        self.merge_button.setText("开始合并")

        # 重置运行状态
        self.is_running = False

        # 隐藏状态指示器
        self.update_status_indicator(False)

        # 添加完成日志
        self.add_log(message)

        # 显示结果消息框，但不自动关闭对话框
        if success:
            # 如果窗口被最小化或隐藏，则恢复显示
            if self.current_dialog and (self.current_dialog.isMinimized() or not self.current_dialog.isVisible()):
                self.current_dialog.showNormal()

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("覆盖率合并已完成", 5000)

            QMessageBox.information(self.current_dialog, "成功", message)
            # 不再自动关闭对话框，让用户决定何时关闭
        else:
            # 如果窗口被最小化或隐藏，则恢复显示
            if self.current_dialog and (self.current_dialog.isMinimized() or not self.current_dialog.isVisible()):
                self.current_dialog.showNormal()

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("覆盖率合并失败", 5000)

            QMessageBox.critical(self.current_dialog, "错误", message)

    def update_status_indicator(self, show=True):
        """更新状态指示器"""
        # 更新标签
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(show)

            # 更新状态指示器文本
            if show:
                # 获取当前合并的目标路径
                merge_work = self.work_input.text().strip() if hasattr(self, 'work_input') else ""
                if merge_work:
                    # 提取目录名
                    import os
                    dir_name = os.path.basename(merge_work)
                    self.status_label.setText(f"覆盖率合并中: {dir_name}")
                else:
                    self.status_label.setText("覆盖率合并工具运行中")

        # 更新按钮
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(show)

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if self.is_running:
                self.update_status_indicator(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("覆盖率合并工具正在后台运行", 5000)

            # 添加日志
            self.add_log("窗口已隐藏到后台运行，任务继续执行中...")

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 断开信号连接，避免内存泄漏
        try:
            self.task_progress.disconnect()
            self.task_completed.disconnect()
        except Exception:
            pass  # 忽略断开连接时的错误

        # 如果任务仍在运行，更新状态指示器
        if self.is_running:
            # 保持状态指示器可见，因为任务仍在后台运行
            self.update_status_indicator(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("覆盖率合并工具已关闭，但任务仍在后台运行", 5000)
        else:
            # 隐藏状态指示器
            self.update_status_indicator(False)

        # 清理对话框引用
        self.current_dialog = None