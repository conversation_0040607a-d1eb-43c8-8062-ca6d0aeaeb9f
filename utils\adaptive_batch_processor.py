"""
自适应批处理管理器 - 智能批处理大小和刷新时机优化
根据系统负载和数据特征动态调整批处理策略
"""
import time
import threading
from typing import List, Callable, Any, Dict, Optional
from collections import deque
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
import psutil
from utils.string_pool import intern_string


class SystemLoadMonitor:
    """
    系统负载监控器 - 监控CPU和内存使用情况
    """
    
    def __init__(self, update_interval: float = 1.0):
        self.update_interval = update_interval
        self._cpu_usage = 0.0
        self._memory_usage = 0.0
        self._last_update = 0.0
        self._lock = threading.RLock()
        
        # 历史数据用于计算平均值
        self._cpu_history = deque(maxlen=10)
        self._memory_history = deque(maxlen=10)
    
    def update_metrics(self):
        """更新系统指标"""
        current_time = time.time()
        
        with self._lock:
            if current_time - self._last_update < self.update_interval:
                return  # 避免过于频繁的更新
            
            try:
                # 获取CPU使用率
                cpu_percent = psutil.cpu_percent(interval=None)
                self._cpu_history.append(cpu_percent)
                self._cpu_usage = sum(self._cpu_history) / len(self._cpu_history)
                
                # 获取内存使用率
                memory = psutil.virtual_memory()
                self._memory_history.append(memory.percent)
                self._memory_usage = sum(self._memory_history) / len(self._memory_history)
                
                self._last_update = current_time
                
            except Exception as e:
                print(f"更新系统指标时出错: {e}")
    
    def get_cpu_usage(self) -> float:
        """获取平均CPU使用率"""
        self.update_metrics()
        with self._lock:
            return self._cpu_usage
    
    def get_memory_usage(self) -> float:
        """获取平均内存使用率"""
        self.update_metrics()
        with self._lock:
            return self._memory_usage
    
    def get_system_load_factor(self) -> float:
        """
        获取系统负载因子 (0.0 - 1.0)
        0.0 = 系统空闲，1.0 = 系统繁忙
        """
        cpu = self.get_cpu_usage()
        memory = self.get_memory_usage()
        
        # 综合CPU和内存使用率计算负载因子
        load_factor = (cpu * 0.7 + memory * 0.3) / 100.0
        return min(1.0, max(0.0, load_factor))


class AdaptiveBatchConfig:
    """
    自适应批处理配置 - 根据系统负载动态调整参数
    """
    
    def __init__(self):
        # 基础配置
        self.min_batch_size = 10
        self.max_batch_size = 500
        self.base_batch_size = 100
        
        self.min_timeout_ms = 10
        self.max_timeout_ms = 1000
        self.base_timeout_ms = 100
        
        # 自适应参数
        self.load_threshold_low = 0.3   # 低负载阈值
        self.load_threshold_high = 0.7  # 高负载阈值
        
        # 性能历史
        self._performance_history = deque(maxlen=20)
        self._last_adjustment_time = 0.0
        self._adjustment_interval = 5.0  # 调整间隔（秒）
    
    def calculate_optimal_batch_size(self, system_load: float, 
                                   pending_count: int, 
                                   avg_process_time: float) -> int:
        """
        计算最优批处理大小
        
        Args:
            system_load: 系统负载因子 (0.0-1.0)
            pending_count: 待处理项目数量
            avg_process_time: 平均处理时间
            
        Returns:
            最优批处理大小
        """
        # 基于系统负载调整
        if system_load < self.load_threshold_low:
            # 低负载：增大批处理大小
            load_factor = 1.5
        elif system_load > self.load_threshold_high:
            # 高负载：减小批处理大小
            load_factor = 0.5
        else:
            # 中等负载：保持基础大小
            load_factor = 1.0
        
        # 基于待处理数量调整
        if pending_count > 1000:
            count_factor = 1.5
        elif pending_count < 50:
            count_factor = 0.7
        else:
            count_factor = 1.0
        
        # 基于处理时间调整
        if avg_process_time > 0.1:  # 处理时间过长
            time_factor = 0.8
        elif avg_process_time < 0.01:  # 处理时间很短
            time_factor = 1.2
        else:
            time_factor = 1.0
        
        # 计算最终批处理大小
        optimal_size = int(self.base_batch_size * load_factor * count_factor * time_factor)
        
        # 限制在合理范围内
        return max(self.min_batch_size, min(self.max_batch_size, optimal_size))
    
    def calculate_optimal_timeout(self, system_load: float, batch_size: int) -> int:
        """
        计算最优超时时间
        
        Args:
            system_load: 系统负载因子
            batch_size: 当前批处理大小
            
        Returns:
            最优超时时间（毫秒）
        """
        # 基于系统负载调整
        if system_load < self.load_threshold_low:
            # 低负载：可以等待更长时间
            load_factor = 1.5
        elif system_load > self.load_threshold_high:
            # 高负载：减少等待时间
            load_factor = 0.5
        else:
            load_factor = 1.0
        
        # 基于批处理大小调整
        size_factor = batch_size / self.base_batch_size
        
        # 计算最终超时时间
        optimal_timeout = int(self.base_timeout_ms * load_factor * size_factor)
        
        # 限制在合理范围内
        return max(self.min_timeout_ms, min(self.max_timeout_ms, optimal_timeout))
    
    def record_performance(self, batch_size: int, process_time: float, 
                         items_processed: int):
        """记录性能数据"""
        performance_data = {
            'batch_size': batch_size,
            'process_time': process_time,
            'items_processed': items_processed,
            'throughput': items_processed / max(0.001, process_time),
            'timestamp': time.time()
        }
        
        self._performance_history.append(performance_data)
    
    def get_average_process_time(self) -> float:
        """获取平均处理时间"""
        if not self._performance_history:
            return 0.05  # 默认值
        
        total_time = sum(p['process_time'] for p in self._performance_history)
        return total_time / len(self._performance_history)
    
    def get_average_throughput(self) -> float:
        """获取平均吞吐量"""
        if not self._performance_history:
            return 100.0  # 默认值
        
        total_throughput = sum(p['throughput'] for p in self._performance_history)
        return total_throughput / len(self._performance_history)


class AdaptiveBatchProcessor(QObject):
    """
    自适应批处理器 - 智能批处理管理
    """
    
    # 信号定义
    batch_processed = pyqtSignal(int, float)  # 批处理完成信号 (items_count, process_time)
    
    def __init__(self, process_callback: Callable[[List[Any]], None], 
                 parent=None):
        super().__init__(parent)
        
        self.process_callback = process_callback
        self.system_monitor = SystemLoadMonitor()
        self.config = AdaptiveBatchConfig()
        
        # 批处理数据
        self._pending_items: List[Any] = []
        self._lock = threading.RLock()
        
        # 定时器
        self._batch_timer = QTimer()
        self._batch_timer.timeout.connect(self._process_batch)
        self._batch_timer.setSingleShot(True)
        
        # 性能统计
        self._stats = {
            'total_batches': 0,
            'total_items_processed': 0,
            'total_process_time': 0.0,
            'avg_batch_size': 0.0,
            'avg_process_time': 0.0,
            'adaptive_adjustments': 0
        }
        
        # 自适应调整定时器
        self._adjustment_timer = QTimer()
        self._adjustment_timer.timeout.connect(self._adjust_parameters)
        self._adjustment_timer.start(5000)  # 每5秒调整一次

    def set_batch_size(self, batch_size: int):
        """设置基础批处理大小"""
        self.config.base_batch_size = max(self.config.min_batch_size,
                                         min(self.config.max_batch_size, batch_size))
    
    def add_item(self, item: Any):
        """添加待处理项目"""
        with self._lock:
            self._pending_items.append(item)
            
            # 检查是否需要立即处理
            current_batch_size = self._get_optimal_batch_size()
            
            if len(self._pending_items) >= current_batch_size:
                self._trigger_immediate_processing()
            elif not self._batch_timer.isActive():
                self._schedule_batch_processing()
    
    def add_items(self, items: List[Any]):
        """批量添加待处理项目"""
        if not items:
            return
        
        with self._lock:
            self._pending_items.extend(items)
            
            # 检查是否需要立即处理
            current_batch_size = self._get_optimal_batch_size()
            
            if len(self._pending_items) >= current_batch_size:
                self._trigger_immediate_processing()
            elif not self._batch_timer.isActive():
                self._schedule_batch_processing()
    
    def force_flush(self):
        """强制处理所有待处理项目"""
        if self._batch_timer.isActive():
            self._batch_timer.stop()
        
        self._process_batch()
    
    def _get_optimal_batch_size(self) -> int:
        """获取当前最优批处理大小"""
        system_load = self.system_monitor.get_system_load_factor()
        pending_count = len(self._pending_items)
        avg_process_time = self.config.get_average_process_time()
        
        return self.config.calculate_optimal_batch_size(
            system_load, pending_count, avg_process_time
        )
    
    def _get_optimal_timeout(self) -> int:
        """获取当前最优超时时间"""
        system_load = self.system_monitor.get_system_load_factor()
        batch_size = self._get_optimal_batch_size()
        
        return self.config.calculate_optimal_timeout(system_load, batch_size)
    
    def _schedule_batch_processing(self):
        """安排批处理"""
        if self._batch_timer.isActive():
            return
        
        timeout = self._get_optimal_timeout()
        self._batch_timer.start(timeout)
    
    def _trigger_immediate_processing(self):
        """触发立即处理"""
        if self._batch_timer.isActive():
            self._batch_timer.stop()
        
        self._process_batch()
    
    def _process_batch(self):
        """处理当前批次"""
        with self._lock:
            if not self._pending_items:
                return
            
            # 获取当前批处理大小
            batch_size = self._get_optimal_batch_size()
            
            # 提取要处理的项目
            items_to_process = self._pending_items[:batch_size]
            self._pending_items = self._pending_items[batch_size:]
            
            # 如果还有剩余项目，安排下一次处理
            if self._pending_items:
                self._schedule_batch_processing()
        
        if not items_to_process:
            return
        
        # 处理批次
        start_time = time.perf_counter()
        
        try:
            self.process_callback(items_to_process)
            
        except Exception as e:
            print(f"批处理时出错: {e}")
        
        finally:
            process_time = time.perf_counter() - start_time
            
            # 更新统计信息
            self._update_stats(len(items_to_process), process_time)
            
            # 记录性能数据
            self.config.record_performance(
                len(items_to_process), process_time, len(items_to_process)
            )
            
            # 发出信号
            self.batch_processed.emit(len(items_to_process), process_time)
    
    def _adjust_parameters(self):
        """自适应调整参数"""
        try:
            # 更新系统监控
            self.system_monitor.update_metrics()
            
            # 记录调整
            self._stats['adaptive_adjustments'] += 1
            
        except Exception as e:
            print(f"调整参数时出错: {e}")
    
    def _update_stats(self, items_count: int, process_time: float):
        """更新统计信息"""
        self._stats['total_batches'] += 1
        self._stats['total_items_processed'] += items_count
        self._stats['total_process_time'] += process_time
        
        # 计算平均值
        if self._stats['total_batches'] > 0:
            self._stats['avg_batch_size'] = (
                self._stats['total_items_processed'] / self._stats['total_batches']
            )
            self._stats['avg_process_time'] = (
                self._stats['total_process_time'] / self._stats['total_batches']
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            current_stats = self._stats.copy()
            current_stats.update({
                'pending_items': len(self._pending_items),
                'current_batch_size': self._get_optimal_batch_size(),
                'current_timeout_ms': self._get_optimal_timeout(),
                'system_load': self.system_monitor.get_system_load_factor(),
                'cpu_usage': self.system_monitor.get_cpu_usage(),
                'memory_usage': self.system_monitor.get_memory_usage(),
                'avg_throughput': self.config.get_average_throughput()
            })
            
            return current_stats
    
    def cleanup(self):
        """清理资源"""
        try:
            # 停止定时器
            if self._batch_timer.isActive():
                self._batch_timer.stop()
            
            if self._adjustment_timer.isActive():
                self._adjustment_timer.stop()
            
            # 处理剩余项目
            self.force_flush()
            
        except Exception as e:
            print(f"清理批处理器时出错: {e}")
