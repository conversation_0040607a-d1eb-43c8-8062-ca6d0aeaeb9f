"""
AI API客户端

提供与AI服务的通信功能，兼容OpenAI API格式
"""

import json
import requests
import time
from typing import Dict, List, Any, Optional, Iterator, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QThread


class APIResponse:
    """API响应类"""
    
    def __init__(self, success: bool, data: Any = None, error: str = None):
        self.success = success
        self.data = data
        self.error = error
        self.timestamp = time.time()


class StreamingAPIWorker(QThread):
    """流式API请求工作线程"""
    
    # 信号
    chunk_received = pyqtSignal(str)  # 接收到的文本块
    finished = pyqtSignal(bool, str)  # 完成信号 (success, final_text or error)
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, api_client, messages: List[Dict], config: Dict):
        super().__init__()
        self.api_client = api_client
        self.messages = messages
        self.config = config
        self.is_cancelled = False
    
    def cancel(self):
        """取消请求"""
        self.is_cancelled = True
    
    def run(self):
        """执行流式请求"""
        try:
            full_response = ""
            
            for chunk in self.api_client._stream_chat_completion(self.messages, self.config):
                if self.is_cancelled:
                    break
                
                if chunk:
                    full_response += chunk
                    self.chunk_received.emit(chunk)
            
            if not self.is_cancelled:
                self.finished.emit(True, full_response)
            
        except Exception as e:
            if not self.is_cancelled:
                error_msg = str(e)
                self.error_occurred.emit(error_msg)
                self.finished.emit(False, error_msg)


class APIClient(QObject):
    """AI API客户端"""
    
    def __init__(self):
        super().__init__()
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Chat-Plugin/1.0'
        })
    
    def set_config(self, config: Dict[str, Any]):
        """设置API配置

        Args:
            config: 配置字典，包含api_key, api_url等
        """
        self.config = config

        # 设置认证头
        api_key = config.get('api_key', '')
        if api_key:
            self.session.headers.update({
                'Authorization': f'Bearer {api_key}'
            })

            # 为Open Router添加特殊请求头
            api_url = config.get('api_url', '')
            if 'openrouter.ai' in api_url:
                self.session.headers.update({
                    'HTTP-Referer': 'https://github.com/your-repo',  # 可选：标识应用来源
                    'X-Title': 'AI Chat Assistant'  # 可选：应用名称
                })
    
    def test_connection(self) -> APIResponse:
        """测试API连接
        
        Returns:
            APIResponse: 测试结果
        """
        try:
            # 发送一个简单的测试请求
            test_messages = [{"role": "user", "content": "Hello"}]
            
            response = self.chat_completion(
                messages=test_messages,
                max_tokens=10,
                stream=False
            )
            
            if response.success:
                return APIResponse(True, "连接成功")
            else:
                return APIResponse(False, error=response.error)
                
        except Exception as e:
            return APIResponse(False, error=f"连接测试失败: {str(e)}")
    
    def chat_completion(self, messages: List[Dict], **kwargs) -> APIResponse:
        """聊天完成API调用
        
        Args:
            messages: 消息列表
            **kwargs: 其他参数
            
        Returns:
            APIResponse: API响应
        """
        try:
            # 准备请求数据
            data = {
                "model": self.config.get('model', 'deepseek-chat'),
                "messages": messages,
                "max_tokens": kwargs.get('max_tokens', self.config.get('max_tokens', 2048)),
                "temperature": kwargs.get('temperature', self.config.get('temperature', 0.7)),
                "stream": kwargs.get('stream', False)
            }
            
            # 移除None值
            data = {k: v for k, v in data.items() if v is not None}
            
            # 发送请求
            api_url = self.config.get('api_url', '').rstrip('/')
            url = f"{api_url}/chat/completions"
            
            timeout = self.config.get('request_timeout', 30)
            
            response = self.session.post(
                url,
                json=data,
                timeout=timeout,
                stream=data.get('stream', False)
            )

            # 确保响应编码正确
            response.encoding = 'utf-8'
            response.raise_for_status()
            
            if data.get('stream', False):
                # 流式响应处理
                return self._handle_stream_response(response)
            else:
                # 普通响应处理
                result = response.json()
                
                if 'choices' in result and len(result['choices']) > 0:
                    message = result['choices'][0]['message']
                    content = message.get('content', '')

                    # 对于DeepSeek R1等模型，如果content为空，尝试获取reasoning内容
                    if not content and 'reasoning' in message:
                        content = message['reasoning']

                    return APIResponse(True, content)
                else:
                    return APIResponse(False, error="响应格式错误")
                    
        except requests.exceptions.Timeout:
            return APIResponse(False, error="请求超时")
        except requests.exceptions.ConnectionError:
            return APIResponse(False, error="连接错误，请检查网络和API地址")
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP错误: {e.response.status_code}"
            try:
                # 尝试获取详细错误信息
                error_detail = e.response.json()
                if 'error' in error_detail:
                    if isinstance(error_detail['error'], dict):
                        error_msg += f" - {error_detail['error'].get('message', '未知错误')}"
                    else:
                        error_msg += f" - {error_detail['error']}"

                # 针对402错误的特殊处理
                if e.response.status_code == 402:
                    error_msg += "\n可能的原因：\n1. 账户余额不足\n2. 免费额度已用完\n3. API Key权限不足\n4. 模型不支持免费使用"

            except:
                pass
            return APIResponse(False, error=error_msg)
        except json.JSONDecodeError:
            return APIResponse(False, error="响应解析错误")
        except Exception as e:
            return APIResponse(False, error=f"请求失败: {str(e)}")
    
    def _handle_stream_response(self, response) -> APIResponse:
        """处理流式响应（同步版本）"""
        try:
            content = ""
            # 确保响应编码正确
            response.encoding = 'utf-8'

            for line in response.iter_lines(decode_unicode=True, chunk_size=1024):
                if not line:
                    continue

                line = line.strip()
                if line.startswith('data: '):
                    data_str = line[6:]  # 移除 'data: ' 前缀

                    if data_str.strip() == '[DONE]':
                        break

                    try:
                        data = json.loads(data_str)
                        if 'choices' in data and len(data['choices']) > 0:
                            delta = data['choices'][0].get('delta', {})
                            chunk_content = ""

                            # 优先获取content，如果没有则获取reasoning
                            if 'content' in delta and delta['content']:
                                chunk_content = delta['content']
                            elif 'reasoning' in delta and delta['reasoning']:
                                chunk_content = delta['reasoning']

                            if chunk_content:
                                content += chunk_content
                    except json.JSONDecodeError:
                        continue

            return APIResponse(True, content)

        except Exception as e:
            return APIResponse(False, error=f"流式响应处理失败: {str(e)}")
    
    def _stream_chat_completion(self, messages: List[Dict], config: Dict) -> Iterator[str]:
        """流式聊天完成（生成器版本）
        
        Args:
            messages: 消息列表
            config: 配置字典
            
        Yields:
            str: 文本块
        """
        try:
            # 准备请求数据
            data = {
                "model": config.get('model', 'deepseek-chat'),
                "messages": messages,
                "max_tokens": config.get('max_tokens', 2048),
                "temperature": config.get('temperature', 0.7),
                "stream": True
            }
            
            # 发送请求
            api_url = config.get('api_url', '').rstrip('/')
            url = f"{api_url}/chat/completions"
            
            timeout = config.get('request_timeout', 30)
            
            response = self.session.post(
                url,
                json=data,
                timeout=timeout,
                stream=True
            )

            # 确保响应编码正确
            response.encoding = 'utf-8'
            response.raise_for_status()
            
            # 处理流式响应
            for line in response.iter_lines(decode_unicode=True, chunk_size=1024):
                if not line:
                    continue

                line = line.strip()
                if line.startswith('data: '):
                    data_str = line[6:]  # 移除 'data: ' 前缀

                    if data_str.strip() == '[DONE]':
                        break

                    try:
                        data = json.loads(data_str)
                        if 'choices' in data and len(data['choices']) > 0:
                            delta = data['choices'][0].get('delta', {})
                            chunk_content = ""

                            # 优先获取content，如果没有则获取reasoning
                            if 'content' in delta and delta['content']:
                                chunk_content = delta['content']
                            elif 'reasoning' in delta and delta['reasoning']:
                                chunk_content = delta['reasoning']

                            if chunk_content:
                                yield chunk_content
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            raise Exception(f"流式请求失败: {str(e)}")
    
    def stream_chat_completion_async(self, messages: List[Dict]) -> StreamingAPIWorker:
        """异步流式聊天完成
        
        Args:
            messages: 消息列表
            
        Returns:
            StreamingAPIWorker: 工作线程
        """
        worker = StreamingAPIWorker(self, messages, self.config)
        return worker
    
    def translate_text(self, text: str, target_language: str = "en") -> APIResponse:
        """翻译文本
        
        Args:
            text: 要翻译的文本
            target_language: 目标语言 ('en' 或 'zh')
            
        Returns:
            APIResponse: 翻译结果
        """
        try:
            # 构建翻译提示
            if target_language.lower() == 'en':
                prompt = f"请将以下中文翻译成英文，只返回翻译结果，不要添加任何解释：\n\n{text}"
            else:
                prompt = f"请将以下英文翻译成中文，只返回翻译结果，不要添加任何解释：\n\n{text}"
            
            messages = [{"role": "user", "content": prompt}]
            
            return self.chat_completion(messages, max_tokens=1024, temperature=0.3)
            
        except Exception as e:
            return APIResponse(False, error=f"翻译失败: {str(e)}")
    
    def summarize_document(self, content: str, max_length: int = 500) -> APIResponse:
        """生成文档摘要
        
        Args:
            content: 文档内容
            max_length: 最大摘要长度
            
        Returns:
            APIResponse: 摘要结果
        """
        try:
            prompt = f"""请为以下文档生成一个简洁的摘要，摘要长度不超过{max_length}字：

{content[:4000]}  # 限制输入长度

摘要要求：
1. 概括文档的主要内容和要点
2. 保持简洁明了
3. 突出重要信息
4. 使用中文"""
            
            messages = [{"role": "user", "content": prompt}]
            
            return self.chat_completion(messages, max_tokens=max_length, temperature=0.5)
            
        except Exception as e:
            return APIResponse(False, error=f"生成摘要失败: {str(e)}")
