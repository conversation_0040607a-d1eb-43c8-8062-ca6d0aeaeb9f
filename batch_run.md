我想在RunSim GUI中增加一个BATCH RUN（批量运行）功能。具体需求如下：

**功能概述：**
在GUI界面中添加BATCH RUN功能，包含两个控制选项：
1. "启用BATCH RUN模式"复选框
2. "强制更新编译基板"复选框（仅在BATCH RUN模式启用时可用）
3. 当鼠标悬停在“启用BATCH RUN模式”复选框上时，显示工具提示：
   - 工具提示标题：BATCH RUN模式
   - 工具提示内容：在BATCH RUN模式下，会自动检查基础用例目录是否存在，并根据需要跳过编译步骤，直接执行仿真。若要强制更新编译基板，请勾选“强制更新编译基板”选项。

**执行逻辑：**
当启用BATCH RUN模式并点击"执行仿真和编译"按钮时，根据"强制更新编译基板"选项执行不同的命令序列：

**情况A：未选中"强制更新编译基板"**
1. 检查基础用例目录是否存在：
   - 检查路径：runsim执行目录下 或 环境变量$PROJ_WORK目录下
   - 基础用例名：从case_panel.py中当前选中用例名获取（需要明确获取逻辑）
2. 如果基础用例目录存在：
   - 在原仿真命令基础上添加参数：`-R $PROJ_WORK/基础用例目录`
3. 如果基础用例目录不存在：
   - 第一条命令：将原命令的-case参数值改为基础用例名，并添加-C选项
   - 第二条命令：在原仿真命令基础上添加`-R $PROJ_WORK/基础用例目录`
   - 顺序执行：先执行第一条命令完成后，再执行第二条命令

**情况B：选中"强制更新编译基板"**
- 第一条命令：将原命令的-case参数值改为基础用例名，并添加-C选项
- 第二条命令：在原仿真命令基础上添加`-R $PROJ_WORK/基础用例目录`
- 顺序执行：先执行第一条命令完成后，再执行第二条命令

**技术要求：**
1. 需要实时更新命令预览区域，显示将要执行的命令
2. 需要确定这两个复选框在GUI中的最佳放置位置
3. 需要明确如何从当前用例名提取基础用例名的逻辑
4. 需要处理命令的顺序执行和状态反馈

**实现步骤：**
1. 首先分析RunSim GUI现有GUI布局，建议这两个选项的最佳放置位置
2. 明确基础用例名的提取逻辑和目录检查机制
3. 实现BATCH RUN的命令生成和执行逻辑
4. 更新命令预览功能以支持新的执行模式