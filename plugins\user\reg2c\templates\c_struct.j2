// Register structure definition
typedef struct {
{% set ns = namespace(prev_offset=0) %}
{% for reg in registers|sort(attribute='offset') %}
    {% if reg.offset > ns.prev_offset %}
    uint32_t PAD_0x{{ "%04X" % ns.prev_offset }}[{{ (reg.offset - ns.prev_offset) //4 }}]; // 0x{{ "%04X" % ns.prev_offset }} - 0x{{ "%04X" % (reg.offset -1) }}
    {% endif %}
    volatile uint32_t {{ reg.name }}; // 0x{{ "%04X" % reg.offset }}
    {% set ns.prev_offset = reg.offset +4 %}
{% endfor %}
} {{ module_name }}_t;