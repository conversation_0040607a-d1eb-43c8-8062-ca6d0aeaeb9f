"""
设置对话框

提供AI聊天插件的配置界面
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QPushButton, QLabel, QGroupBox, QTextEdit, QFileDialog,
    QMessageBox, QSlider, QFrame, QWidget
)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QFont


class APIConfigTab(QWidget):
    """API配置标签页"""
    
    def __init__(self, config_model):
        super().__init__()
        self.config_model = config_model
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # API基础配置组
        api_group = QGroupBox("API配置")
        api_layout = QFormLayout(api_group)
        
        # 模型提供商
        self.provider_combo = QComboBox()
        self.provider_combo.addItems(self.config_model.get_all_providers())
        self.provider_combo.currentTextChanged.connect(self._on_provider_changed)
        api_layout.addRow("模型提供商:", self.provider_combo)
        
        # API Key
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        self.api_key_edit.setPlaceholderText("请输入API Key")
        api_layout.addRow("API Key:", self.api_key_edit)
        
        # API URL
        self.api_url_edit = QLineEdit()
        self.api_url_edit.setPlaceholderText("https://api.example.com/v1")
        api_layout.addRow("API URL:", self.api_url_edit)
        
        # 模型选择
        self.model_combo = QComboBox()
        api_layout.addRow("模型:", self.model_combo)
        
        layout.addWidget(api_group)
        
        # 请求参数组
        params_group = QGroupBox("请求参数")
        params_layout = QFormLayout(params_group)
        
        # 最大令牌数
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(1, 8192)
        self.max_tokens_spin.setValue(2048)
        params_layout.addRow("最大令牌数:", self.max_tokens_spin)
        
        # 温度
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setValue(0.7)
        params_layout.addRow("温度:", self.temperature_spin)
        
        # 流式响应
        self.stream_check = QCheckBox("启用流式响应")
        self.stream_check.setChecked(True)
        params_layout.addRow("", self.stream_check)
        
        layout.addWidget(params_group)
        
        # 连接测试
        test_layout = QHBoxLayout()
        self.test_btn = QPushButton("测试连接")
        self.test_btn.clicked.connect(self._test_connection)
        test_layout.addWidget(self.test_btn)
        test_layout.addStretch()
        
        layout.addLayout(test_layout)
        layout.addStretch()
    
    def _on_provider_changed(self, provider):
        """提供商变更处理"""
        # 更新模型列表
        self.model_combo.clear()
        models = self.config_model.PREDEFINED_MODELS.get(provider, [])
        self.model_combo.addItems(models)
        
        # 更新默认API URL
        default_urls = {
            "DeepSeek": "https://api.deepseek.com/v1",
            "通义千问": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "OpenAI": "https://api.openai.com/v1",
            "Open Router": "https://openrouter.ai/api/v1"
        }
        
        if provider in default_urls:
            self.api_url_edit.setText(default_urls[provider])
    
    def _test_connection(self):
        """测试API连接"""
        # 临时保存当前配置
        temp_config = {
            'api_key': self.api_key_edit.text(),
            'api_url': self.api_url_edit.text(),
            'model': self.model_combo.currentText(),
            'max_tokens': self.max_tokens_spin.value(),
            'temperature': self.temperature_spin.value(),
            'request_timeout': 10
        }
        
        # 创建临时API客户端进行测试
        from ..utils.api_client import APIClient
        
        api_client = APIClient()
        api_client.set_config(temp_config)
        
        self.test_btn.setText("测试中...")
        self.test_btn.setEnabled(False)
        
        try:
            response = api_client.test_connection()
            
            if response.success:
                QMessageBox.information(self, "测试成功", "API连接测试成功！")
            else:
                QMessageBox.warning(self, "测试失败", f"API连接测试失败:\n{response.error}")
                
        except Exception as e:
            QMessageBox.critical(self, "测试错误", f"测试过程中发生错误:\n{str(e)}")
        
        finally:
            self.test_btn.setText("测试连接")
            self.test_btn.setEnabled(True)
    
    def load_config(self):
        """加载配置"""
        # 设置提供商
        provider = self.config_model.get('model_provider', 'DeepSeek')
        index = self.provider_combo.findText(provider)
        if index >= 0:
            self.provider_combo.setCurrentIndex(index)
        
        # 设置其他配置
        self.api_key_edit.setText(self.config_model.get('api_key', ''))
        self.api_url_edit.setText(self.config_model.get('api_url', ''))
        
        # 设置模型
        model = self.config_model.get('model', '')
        model_index = self.model_combo.findText(model)
        if model_index >= 0:
            self.model_combo.setCurrentIndex(model_index)
        
        self.max_tokens_spin.setValue(self.config_model.get('max_tokens', 2048))
        self.temperature_spin.setValue(self.config_model.get('temperature', 0.7))
        self.stream_check.setChecked(self.config_model.get('stream', True))
    
    def save_config(self):
        """保存配置"""
        config_updates = {
            'model_provider': self.provider_combo.currentText(),
            'api_key': self.api_key_edit.text(),
            'api_url': self.api_url_edit.text(),
            'model': self.model_combo.currentText(),
            'max_tokens': self.max_tokens_spin.value(),
            'temperature': self.temperature_spin.value(),
            'stream': self.stream_check.isChecked()
        }
        
        self.config_model.update(config_updates)


class UIConfigTab(QWidget):
    """界面配置标签页"""
    
    def __init__(self, config_model):
        super().__init__()
        self.config_model = config_model
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 窗口配置组
        window_group = QGroupBox("窗口设置")
        window_layout = QFormLayout(window_group)
        
        # 窗口大小
        self.width_spin = QSpinBox()
        self.width_spin.setRange(600, 2000)
        self.width_spin.setValue(800)
        window_layout.addRow("窗口宽度:", self.width_spin)
        
        self.height_spin = QSpinBox()
        self.height_spin.setRange(400, 1500)
        self.height_spin.setValue(600)
        window_layout.addRow("窗口高度:", self.height_spin)
        
        # 字体大小
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        window_layout.addRow("字体大小:", self.font_size_spin)
        
        # 主题
        self.theme_combo = QComboBox()
        # 动态加载可用主题
        from ..utils.theme_manager import ThemeManager
        self.theme_manager = ThemeManager()
        available_themes = self.theme_manager.get_available_themes()
        for theme_key, theme_name in available_themes.items():
            self.theme_combo.addItem(theme_name, theme_key)
        self.theme_combo.currentTextChanged.connect(self._on_theme_changed)
        window_layout.addRow("主题:", self.theme_combo)

        # 主题预览按钮
        theme_preview_btn = QPushButton("预览主题")
        theme_preview_btn.clicked.connect(self._preview_theme)
        window_layout.addRow("", theme_preview_btn)
        
        layout.addWidget(window_group)
        
        # 功能配置组
        feature_group = QGroupBox("功能设置")
        feature_layout = QFormLayout(feature_group)
        
        # 翻译功能
        self.translation_check = QCheckBox("启用翻译功能")
        self.translation_check.setChecked(True)
        feature_layout.addRow("", self.translation_check)
        
        # 自动检测语言
        self.auto_detect_check = QCheckBox("自动检测语言")
        self.auto_detect_check.setChecked(True)
        feature_layout.addRow("", self.auto_detect_check)
        
        # 启用日志
        self.logging_check = QCheckBox("启用日志记录")
        self.logging_check.setChecked(True)
        feature_layout.addRow("", self.logging_check)
        
        layout.addWidget(feature_group)
        layout.addStretch()
    
    def _on_theme_changed(self):
        """主题变更处理"""
        # 实时预览主题变化
        current_theme = self.theme_combo.currentData()
        if current_theme:
            self.theme_manager.set_theme(current_theme)

    def _preview_theme(self):
        """预览主题"""
        current_theme = self.theme_combo.currentData()
        if current_theme:
            self.theme_manager.set_theme(current_theme)
            QMessageBox.information(self, "主题预览", f"已应用主题: {self.theme_combo.currentText()}")

    def load_config(self):
        """加载配置"""
        self.width_spin.setValue(self.config_model.get('window_width', 800))
        self.height_spin.setValue(self.config_model.get('window_height', 600))
        self.font_size_spin.setValue(self.config_model.get('font_size', 12))

        # 加载当前主题
        current_theme = self.theme_manager.get_current_theme()
        for i in range(self.theme_combo.count()):
            if self.theme_combo.itemData(i) == current_theme:
                self.theme_combo.setCurrentIndex(i)
                break

        self.translation_check.setChecked(self.config_model.get('translation_enabled', True))
        self.auto_detect_check.setChecked(self.config_model.get('auto_detect_language', True))
        self.logging_check.setChecked(self.config_model.get('enable_logging', True))
    
    def save_config(self):
        """保存配置"""
        # 保存主题设置
        selected_theme = self.theme_combo.currentData()
        if selected_theme:
            self.theme_manager.set_theme(selected_theme)

        config_updates = {
            'window_width': self.width_spin.value(),
            'window_height': self.height_spin.value(),
            'font_size': self.font_size_spin.value(),
            'theme': selected_theme or 'light',
            'translation_enabled': self.translation_check.isChecked(),
            'auto_detect_language': self.auto_detect_check.isChecked(),
            'enable_logging': self.logging_check.isChecked()
        }

        self.config_model.update(config_updates)


class AdvancedConfigTab(QWidget):
    """高级配置标签页"""
    
    def __init__(self, config_model):
        super().__init__()
        self.config_model = config_model
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 网络配置组
        network_group = QGroupBox("网络设置")
        network_layout = QFormLayout(network_group)
        
        # 请求超时
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 120)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" 秒")
        network_layout.addRow("请求超时:", self.timeout_spin)
        
        # 最大重试次数
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(0, 10)
        self.retry_spin.setValue(3)
        network_layout.addRow("最大重试次数:", self.retry_spin)
        
        layout.addWidget(network_group)
        
        # 文档配置组
        doc_group = QGroupBox("文档设置")
        doc_layout = QFormLayout(doc_group)
        
        # 最大文件大小
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(1, 100)
        self.max_size_spin.setValue(10)
        self.max_size_spin.setSuffix(" MB")
        doc_layout.addRow("最大文件大小:", self.max_size_spin)
        
        layout.addWidget(doc_group)
        
        # 配置管理
        config_group = QGroupBox("配置管理")
        config_layout = QVBoxLayout(config_group)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        export_btn = QPushButton("导出配置")
        export_btn.clicked.connect(self._export_config)
        button_layout.addWidget(export_btn)
        
        import_btn = QPushButton("导入配置")
        import_btn.clicked.connect(self._import_config)
        button_layout.addWidget(import_btn)
        
        reset_btn = QPushButton("重置配置")
        reset_btn.clicked.connect(self._reset_config)
        button_layout.addWidget(reset_btn)
        
        config_layout.addLayout(button_layout)
        layout.addWidget(config_group)
        
        layout.addStretch()
    
    def _export_config(self):
        """导出配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出配置",
            "ai_chat_config.json",
            "JSON文件 (*.json)"
        )
        
        if file_path:
            if self.config_model.export_config(file_path):
                QMessageBox.information(self, "导出成功", "配置已成功导出")
            else:
                QMessageBox.warning(self, "导出失败", "配置导出失败")
    
    def _import_config(self):
        """导入配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "导入配置",
            "",
            "JSON文件 (*.json)"
        )
        
        if file_path:
            if self.config_model.import_config(file_path):
                QMessageBox.information(self, "导入成功", "配置已成功导入，请重启应用生效")
            else:
                QMessageBox.warning(self, "导入失败", "配置导入失败")
    
    def _reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要重置所有配置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.config_model.reset_to_default()
            QMessageBox.information(self, "重置成功", "配置已重置为默认值")
    
    def load_config(self):
        """加载配置"""
        self.timeout_spin.setValue(self.config_model.get('request_timeout', 30))
        self.retry_spin.setValue(self.config_model.get('max_retries', 3))
        
        max_size_mb = self.config_model.get('max_file_size', 10 * 1024 * 1024) // (1024 * 1024)
        self.max_size_spin.setValue(max_size_mb)
    
    def save_config(self):
        """保存配置"""
        config_updates = {
            'request_timeout': self.timeout_spin.value(),
            'max_retries': self.retry_spin.value(),
            'max_file_size': self.max_size_spin.value() * 1024 * 1024
        }
        
        self.config_model.update(config_updates)


class LocalModelTab(QWidget):
    """本地模型配置标签页"""

    def __init__(self, config_model):
        super().__init__()
        self.config_model = config_model
        self.setup_ui()
        self.load_config()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(16)

        # 说明文本
        info_label = QLabel("""
本地模型功能允许您使用本地部署的AI模型，无需依赖在线API服务。
支持GGUF、Ollama等格式的模型，提供与在线API相同的使用体验。
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 12px; padding: 8px; background-color: #F0F8FF; border-radius: 6px;")
        layout.addWidget(info_label)

        # 启用本地模型
        self.enable_local_checkbox = QCheckBox("启用本地模型支持")
        layout.addWidget(self.enable_local_checkbox)

        # 模型管理按钮
        button_layout = QHBoxLayout()

        self.manage_models_btn = QPushButton("管理本地模型")
        self.manage_models_btn.clicked.connect(self.open_model_manager)
        button_layout.addWidget(self.manage_models_btn)

        self.download_models_btn = QPushButton("下载推荐模型")
        self.download_models_btn.clicked.connect(self.download_recommended_models)
        button_layout.addWidget(self.download_models_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 当前加载的模型
        current_group = QGroupBox("当前状态")
        current_layout = QVBoxLayout(current_group)

        self.current_model_label = QLabel("当前模型: 无")
        current_layout.addWidget(self.current_model_label)

        self.model_status_label = QLabel("状态: 未加载")
        current_layout.addWidget(self.model_status_label)

        layout.addWidget(current_group)

        # 推荐模型列表
        recommend_group = QGroupBox("推荐模型")
        recommend_layout = QVBoxLayout(recommend_group)

        recommend_text = QTextEdit()
        recommend_text.setReadOnly(True)
        recommend_text.setMaximumHeight(150)
        recommend_text.setPlainText("""
轻量级模型（适合8GB内存）：
• Llama 3.2 3B - 通用对话模型
• Phi-3 Mini - 微软开发的小型模型
• Gemma 2B - Google开发的轻量模型

中等模型（适合16GB内存）：
• Llama 3.1 8B - 平衡性能与资源消耗
• Mistral 7B - 优秀的代码和推理能力
• CodeLlama 7B - 专门的代码生成模型

高性能模型（需要32GB+内存）：
• Llama 3.1 70B - 顶级性能
• Mixtral 8x7B - 混合专家模型
        """)
        recommend_layout.addWidget(recommend_text)

        layout.addWidget(recommend_group)
        layout.addStretch()

    def load_config(self):
        """加载配置"""
        self.enable_local_checkbox.setChecked(
            self.config_model.get('local_model_enabled', False)
        )

        # 更新当前模型状态
        self.update_model_status()

    def save_config(self):
        """保存配置"""
        self.config_model.set('local_model_enabled', self.enable_local_checkbox.isChecked())

    def open_model_manager(self):
        """打开模型管理器"""
        try:
            from .local_model_dialog import LocalModelDialog
            from ..utils.local_model_client import LocalModelManager

            # 创建模型管理器（如果不存在）
            if not hasattr(self, 'model_manager'):
                self.model_manager = LocalModelManager()

            dialog = LocalModelDialog(self.model_manager, self)
            dialog.exec_()

            # 更新状态
            self.update_model_status()

        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载本地模型管理器: {e}")

    def download_recommended_models(self):
        """下载推荐模型"""
        QMessageBox.information(
            self, "下载模型",
            """
推荐的模型下载方式：

1. 使用Ollama（推荐）：
   • 安装Ollama: https://ollama.ai
   • 运行命令: ollama pull llama3.2:3b
   • 运行命令: ollama pull phi3:mini

2. 手动下载GGUF模型：
   • 访问 Hugging Face
   • 搜索模型名称 + "GGUF"
   • 下载.gguf文件到本地

3. 使用模型管理器：
   • 点击"管理本地模型"
   • 添加已下载的模型文件
            """
        )

    def update_model_status(self):
        """更新模型状态"""
        # 这里可以检查当前加载的模型状态
        self.current_model_label.setText("当前模型: 无")
        self.model_status_label.setText("状态: 未加载")


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, config_model, parent=None):
        super().__init__(parent)
        self.config_model = config_model
        self.setup_ui()
        self.setModal(True)
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("AI聊天助手 - 设置")
        self.setFixedSize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # API配置标签页
        self.api_tab = APIConfigTab(self.config_model)
        self.tab_widget.addTab(self.api_tab, "API配置")
        
        # 本地模型标签页
        self.local_model_tab = LocalModelTab(self.config_model)
        self.tab_widget.addTab(self.local_model_tab, "本地模型")

        # 界面配置标签页
        self.ui_tab = UIConfigTab(self.config_model)
        self.tab_widget.addTab(self.ui_tab, "界面设置")

        # 高级配置标签页
        self.advanced_tab = AdvancedConfigTab(self.config_model)
        self.tab_widget.addTab(self.advanced_tab, "高级设置")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self._save_and_close)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
    
    def _save_and_close(self):
        """保存并关闭"""
        # 验证配置
        errors = self.config_model.validate_config()
        if errors:
            error_msg = "配置验证失败:\n" + "\n".join(errors.values())
            QMessageBox.warning(self, "配置错误", error_msg)
            return
        
        # 保存所有标签页的配置
        self.api_tab.save_config()
        self.local_model_tab.save_config()
        self.ui_tab.save_config()
        self.advanced_tab.save_config()
        
        self.accept()
