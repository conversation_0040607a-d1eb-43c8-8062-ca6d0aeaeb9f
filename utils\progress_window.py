"""
进度窗口组件
"""
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QProgressBar
from PyQt5.QtCore import Qt

class ProgressWindow(QDialog):
    """进度窗口，用于显示长时间操作的进度"""
    
    def __init__(self, parent=None, title="处理中", label_text="请稍候...", modal=True):
        """
        初始化进度窗口
        
        Args:
            parent (QWidget, optional): 父窗口
            title (str, optional): 窗口标题
            label_text (str, optional): 标签文本
            modal (bool, optional): 是否为模态窗口
        """
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setWindowModality(Qt.WindowModal if modal else Qt.NonModal)
        self.setMinimumWidth(400)
        self.setMinimumHeight(100)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QLabel {
                font-size: 12px;
                color: #333;
                margin-bottom: 5px;
            }
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4a9eff;
                width: 10px;
                margin: 0px;
            }
        """)
        
        # 创建布局
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # 创建标签
        self.label = QLabel(label_text)
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)
        
        # 创建进度条
        self.progress = QProgressBar()
        self.progress.setRange(0, 100)
        self.progress.setValue(0)
        self.progress.setTextVisible(True)
        layout.addWidget(self.progress)
        
        self.setLayout(layout)
        
    def set_progress(self, value):
        """
        设置进度值
        
        Args:
            value (int): 进度值（0-100）
        """
        self.progress.setValue(value)
        
    def set_label_text(self, text):
        """
        设置标签文本
        
        Args:
            text (str): 标签文本
        """
        self.label.setText(text)
