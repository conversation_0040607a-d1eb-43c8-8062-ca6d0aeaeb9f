"""
Git文件版本比对插件主窗口

提供Git文件版本比对的主用户界面，包括：
- 文件选择区域
- 版本选择区域  
- 差异显示区域
- 工具栏和状态栏
"""

import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, 
    QStatusBar, QLabel, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

from plugins.base import NonModalDialog
# 延迟导入以避免循环导入
# from .file_selector import FileSelector
# from .version_selector import VersionSelector
# from .diff_viewer import DiffViewer
# from .toolbar import GitDiffToolbar


class GitDiffMainWindow(NonModalDialog):
    """Git文件版本比对主窗口"""
    
    # 信号定义
    file_selected = pyqtSignal(str)  # 文件选择信号
    diff_requested = pyqtSignal(str, str, str)  # 差异比对请求信号 (file_path, old_commit, new_commit)
    
    def __init__(self, parent=None):
        super().__init__(parent, "Git文件版本比对工具")
        self.setWindowTitle("Git文件版本比对工具")
        self.resize(1200, 800)
        self.setMinimumSize(1000, 600)

        # 重新设置窗口标志，添加最大化按钮
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        
        # 当前状态
        self.current_file_path = ""
        self.current_repo_path = ""
        
        # 应用RunSim GUI主题样式
        self._apply_runsim_theme()
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
    
    def _apply_runsim_theme(self):
        """应用RunSim GUI主题样式"""
        runsim_style = """
            /* 主窗口样式 - 与 Runsim GUI 保持一致 */
            QMainWindow, QDialog {
                background-color: #f5f5f5;
                color: #444;
            }
            
            /* 分组框样式 */
            QGroupBox {
                font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }
            
            /* 按钮样式 */
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #3d8ced;
            }
            
            QPushButton:pressed {
                background-color: #3274bf;
            }
            
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            
            /* 输入框样式 */
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                selection-background-color: #4a9eff;
                font-family: "Microsoft YaHei";
            }
            
            QLineEdit:focus {
                border: 2px solid #4a9eff;
            }
            
            /* 文本编辑器样式 */
            QTextEdit, QPlainTextEdit {
                background-color: white;
                color: #333;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                font-family: "Microsoft YaHei", Consolas, Courier, monospace;
                selection-background-color: #4a9eff;
                selection-color: white;
                padding: 5px;
            }
            
            /* 列表控件样式 */
            QListWidget, QTreeWidget {
                background-color: white;
                color: #333;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                font-family: "Microsoft YaHei";
                selection-background-color: #4a9eff;
                selection-color: white;
                padding: 5px;
            }
            
            QListWidget::item, QTreeWidget::item {
                padding: 6px;
                border-bottom: 1px solid #e0e0e0;
                border-radius: 2px;
                margin: 1px;
            }
            
            QListWidget::item:hover, QTreeWidget::item:hover {
                background-color: #f0f8ff;
                border: 1px solid #4a9eff;
            }
            
            QListWidget::item:selected, QTreeWidget::item:selected {
                background-color: #4a9eff;
                color: white;
                border: 1px solid #3d8ced;
            }
            
            /* 分割器样式 */
            QSplitter::handle {
                background-color: #d0d0d0;
            }
            
            QSplitter::handle:horizontal {
                width: 3px;
            }
            
            QSplitter::handle:vertical {
                height: 3px;
            }
            
            /* 状态栏样式 */
            QStatusBar {
                background-color: #f0f0f0;
                border-top: 1px solid #d0d0d0;
                color: #666;
                height: 24px;
                max-height: 24px;
                font-size: 11px;
            }
            
            /* 工具栏样式 */
            QToolBar {
                background-color: #f8f8f8;
                border-bottom: 1px solid #d0d0d0;
                spacing: 3px;
                padding: 3px;
            }
            
            QToolBar::separator {
                background-color: #d0d0d0;
                width: 1px;
                margin: 0 5px;
            }
        """
        self.setStyleSheet(runsim_style)
    
    def _init_ui(self):
        """初始化用户界面"""
        # 延迟导入组件
        from .file_selector import FileSelector
        from .version_selector import VersionSelector
        from .diff_viewer import DiffViewer
        from .toolbar import GitDiffToolbar

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 创建工具栏
        self.toolbar = GitDiffToolbar(self)
        main_layout.addWidget(self.toolbar)

        # 创建主分割器（水平分割）
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)

        # 创建左侧分割器（垂直分割）
        left_splitter = QSplitter(Qt.Vertical)

        # 文件选择器
        self.file_selector = FileSelector(self)
        left_splitter.addWidget(self.file_selector)

        # 版本选择器
        self.version_selector = VersionSelector(self)
        left_splitter.addWidget(self.version_selector)

        # 设置左侧分割器的比例（文件选择器:版本选择器 = 2:3）
        left_splitter.setSizes([250, 350])
        left_splitter.setMinimumWidth(320)

        # 差异显示器
        self.diff_viewer = DiffViewer(self)
        ## 设置差异显示器的固定高度，防止其占用过多空间
        #self.diff_viewer.setMaximumHeight(500)  # 限制最大高度为500像素

        # 添加到主分割器
        main_splitter.addWidget(left_splitter)
        main_splitter.addWidget(self.diff_viewer)

        # 设置主分割器的比例，左侧面板占更多空间，差异显示区域紧凑
        main_splitter.setSizes([400, 600])

        # 设置分割器策略，允许两个区域都可以伸缩
        main_splitter.setStretchFactor(0, 1)  # 左侧面板可伸缩
        main_splitter.setStretchFactor(1, 2)  # 差异显示区域也可伸缩，权重更大

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.status_bar.setFixedHeight(24)  # 设置固定高度
        main_layout.addWidget(self.status_bar)

        # 状态栏标签
        self.status_label = QLabel("就绪")
        self.file_label = QLabel("文件: 未选择")
        self.stats_label = QLabel("差异: 无")

        self.status_bar.addWidget(self.status_label)
        self.status_bar.addPermanentWidget(self.file_label)
        self.status_bar.addPermanentWidget(self.stats_label)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 文件选择器信号
        self.file_selector.file_selected.connect(self._on_file_selected)
        self.file_selector.repo_changed.connect(self._on_repo_changed)
        
        # 版本选择器信号
        self.version_selector.version_selected.connect(self._on_version_selected)
        self.version_selector.compare_requested.connect(self._on_compare_requested)
        
        # 工具栏信号
        self.toolbar.refresh_requested.connect(self._on_refresh_requested)
        self.toolbar.export_requested.connect(self._on_export_requested)
        self.toolbar.search_requested.connect(self._on_search_requested)
        self.toolbar.view_mode_changed.connect(self._on_view_mode_changed)
        self.toolbar.settings_requested.connect(self._on_settings_requested)
        self.toolbar.external_tool_requested.connect(self._on_external_tool_requested)

        # 差异导航信号
        self.toolbar.previous_diff_requested.connect(self._on_previous_diff_requested)
        self.toolbar.next_diff_requested.connect(self._on_next_diff_requested)
        self.toolbar.toggle_navigation_bar.connect(self._on_toggle_navigation_bar)

        # 差异显示器信号
        self.diff_viewer.stats_updated.connect(self._on_stats_updated)
    
    def _on_file_selected(self, file_path: str):
        """文件选择事件处理"""
        self.current_file_path = file_path
        self.file_label.setText(f"文件: {os.path.basename(file_path)}")
        
        # 更新版本选择器
        self.version_selector.set_file_path(file_path)

        # 清空差异显示
        self.diff_viewer.clear()
        self.stats_label.setText("差异: 无")

        # 更新外部工具按钮状态
        self._update_external_tools_state()

        self.status_label.setText(f"已选择文件: {os.path.basename(file_path)}")
    
    def _on_repo_changed(self, repo_path: str):
        """仓库变更事件处理"""
        self.current_repo_path = repo_path
        self.status_label.setText(f"仓库: {os.path.basename(repo_path)}")
    
    def _on_version_selected(self, commit_info):
        """版本选择事件处理"""
        self.status_label.setText(f"已选择版本: {commit_info.short_sha}")

        # 更新外部工具按钮状态
        self._update_external_tools_state()
    
    def _on_compare_requested(self, file_path: str, old_commit: str, new_commit: str):
        """比对请求事件处理"""
        try:
            self.status_label.setText("正在计算差异...")
            
            # 执行差异比对
            self.diff_viewer.show_diff(file_path, old_commit, new_commit)
            
            self.status_label.setText("差异计算完成")
            
        except Exception as e:
            self.status_label.setText("差异计算失败")
            QMessageBox.warning(self, "错误", f"差异计算失败: {str(e)}")
    
    def _on_refresh_requested(self):
        """刷新请求事件处理"""
        try:
            self.status_label.setText("正在刷新...")
            
            # 刷新文件选择器
            self.file_selector.refresh()
            
            # 如果有选中的文件，刷新版本选择器
            if self.current_file_path:
                self.version_selector.refresh()
            
            self.status_label.setText("刷新完成")
            
        except Exception as e:
            self.status_label.setText("刷新失败")
            QMessageBox.warning(self, "错误", f"刷新失败: {str(e)}")
    
    def _on_export_requested(self):
        """导出请求事件处理"""
        try:
            self.diff_viewer.export_diff()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败: {str(e)}")
    
    def _on_search_requested(self, search_text: str):
        """搜索请求事件处理"""
        try:
            self.diff_viewer.search_text(search_text)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"搜索失败: {str(e)}")
    
    def _on_view_mode_changed(self, mode: str):
        """视图模式变更事件处理"""
        try:
            self.diff_viewer.set_view_mode(mode)
            self.status_label.setText(f"切换到{mode}视图")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"切换视图模式失败: {str(e)}")
    
    def _on_stats_updated(self, stats_text: str):
        """统计信息更新事件处理"""
        self.stats_label.setText(f"差异: {stats_text}")

    def _on_settings_requested(self):
        """设置请求事件处理"""
        try:
            from .settings_dialog import SettingsDialog

            settings_dialog = SettingsDialog(self)
            if settings_dialog.exec_() == SettingsDialog.Accepted:
                # 获取设置并应用
                settings = settings_dialog.get_settings()
                self._apply_settings(settings)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开设置对话框失败: {str(e)}")

    def _apply_settings(self, settings):
        """应用设置"""
        try:
            # 应用视图模式设置
            if 'default_view_mode' in settings:
                mode = "side-by-side" if settings['default_view_mode'] == "并排视图" else "unified"
                self.diff_viewer.set_view_mode(mode)
                self.toolbar.set_view_mode(mode)

            # 这里可以添加更多设置的应用逻辑
            self.status_label.setText("设置已应用")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"应用设置失败: {str(e)}")

    def _on_external_tool_requested(self, tool_name: str):
        """外部工具请求事件处理"""
        try:
            # 检查是否有选中的文件和版本
            if not self.current_file_path:
                QMessageBox.warning(self, "警告", "请先选择要比较的文件")
                return

            selected_commit = self.version_selector.get_selected_commit()
            if not selected_commit:
                QMessageBox.warning(self, "警告", "请先选择要比较的历史版本")
                return

            self.status_label.setText(f"正在启动 {tool_name}...")

            # 导入外部工具管理器
            from ..models.external_tools import external_tools_manager

            # 获取文件内容
            file_dir = os.path.dirname(self.current_file_path)
            from ..models.git_repository import GitRepository
            git_repo = GitRepository(file_dir)

            if not git_repo.is_valid_repo():
                QMessageBox.warning(self, "错误", "文件不在Git仓库中")
                return

            # 获取历史版本内容
            old_content = git_repo.get_file_content_at_commit(self.current_file_path, selected_commit.sha)
            if not old_content:
                QMessageBox.warning(self, "错误", "无法获取历史版本内容")
                return

            # 创建临时文件
            filename = os.path.basename(self.current_file_path)
            temp_old_file = external_tools_manager.create_temp_file(
                old_content, filename, selected_commit.sha
            )

            # 启动外部工具
            external_tools_manager.launch_tool(tool_name, temp_old_file, self.current_file_path)

            self.status_label.setText(f"已启动 {tool_name}")

        except Exception as e:
            self.status_label.setText("启动外部工具失败")
            QMessageBox.warning(self, "错误", f"启动外部工具失败: {str(e)}")

    def _update_external_tools_state(self):
        """更新外部工具按钮状态"""
        # 只有在选择了文件和版本时才启用外部工具
        has_file = bool(self.current_file_path)
        has_version = bool(self.version_selector.get_selected_commit())

        self.toolbar.enable_external_tools(has_file and has_version)

    def _on_previous_diff_requested(self):
        """上一个差异请求事件处理"""
        self.diff_viewer.handle_navigation_action("previous")

    def _on_next_diff_requested(self):
        """下一个差异请求事件处理"""
        self.diff_viewer.handle_navigation_action("next")

    def _on_toggle_navigation_bar(self, visible: bool):
        """切换导航栏显示事件处理"""
        self.diff_viewer.set_navigation_bar_visible(visible)

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 清理资源
            self.diff_viewer.cleanup()

            # 清理外部工具临时文件
            from ..models.external_tools import external_tools_manager
            external_tools_manager.cleanup_temp_files()

            event.accept()
        except Exception as e:
            print(f"关闭窗口时出错: {e}")
            event.accept()
