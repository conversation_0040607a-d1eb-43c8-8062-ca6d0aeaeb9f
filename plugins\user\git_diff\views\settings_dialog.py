"""
设置对话框

提供Git文件版本比对插件的设置选项。
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QGroupBox, QLabel, QSpinBox, QCheckBox, QPushButton,
    QComboBox, QLineEdit, QMessageBox, QFileDialog, QFormLayout
)
from PyQt5.QtCore import Qt
import os

from ..models.external_tools import external_tools_manager


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Git文件版本比对工具 - 设置")
        self.setModal(True)
        self.resize(500, 400)
        
        # 应用样式
        self._apply_style()
        
        # 初始化UI
        self._init_ui()
        
        # 加载设置
        self._load_settings()
    
    def _apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                color: #444;
            }
            
            QGroupBox {
                font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }
            
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #3d8ced;
            }
            
            QPushButton:pressed {
                background-color: #3274bf;
            }
            
            QLineEdit, QSpinBox, QComboBox {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                font-family: "Microsoft YaHei";
            }
            
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
                border: 2px solid #4a9eff;
            }
        """)
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 常规设置标签页
        general_tab = self._create_general_tab()
        tab_widget.addTab(general_tab, "常规")
        
        # 显示设置标签页
        display_tab = self._create_display_tab()
        tab_widget.addTab(display_tab, "显示")
        
        # 高级设置标签页
        advanced_tab = self._create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级")

        # 外部工具标签页
        external_tools_tab = self._create_external_tools_tab()
        tab_widget.addTab(external_tools_tab, "外部工具")
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        self.apply_button = QPushButton("应用")
        
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.apply_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.ok_button.clicked.connect(self._on_ok_clicked)
        self.cancel_button.clicked.connect(self.reject)
        self.apply_button.clicked.connect(self._on_apply_clicked)
    
    def _create_general_tab(self):
        """创建常规设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 提交历史设置
        history_group = QGroupBox("提交历史")
        history_layout = QVBoxLayout(history_group)
        
        # 最大提交数量
        max_commits_layout = QHBoxLayout()
        max_commits_layout.addWidget(QLabel("最大提交数量:"))
        self.max_commits_spinbox = QSpinBox()
        self.max_commits_spinbox.setRange(10, 1000)
        self.max_commits_spinbox.setValue(100)
        self.max_commits_spinbox.setSuffix(" 个")
        max_commits_layout.addWidget(self.max_commits_spinbox)
        max_commits_layout.addStretch()
        history_layout.addLayout(max_commits_layout)
        
        layout.addWidget(history_group)
        
        # 文件处理设置
        file_group = QGroupBox("文件处理")
        file_layout = QVBoxLayout(file_group)
        
        # 自动检测编码
        self.auto_encoding_checkbox = QCheckBox("自动检测文件编码")
        self.auto_encoding_checkbox.setChecked(True)
        file_layout.addWidget(self.auto_encoding_checkbox)
        
        # 默认编码
        encoding_layout = QHBoxLayout()
        encoding_layout.addWidget(QLabel("默认编码:"))
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["UTF-8", "GBK", "GB2312", "ASCII"])
        encoding_layout.addWidget(self.encoding_combo)
        encoding_layout.addStretch()
        file_layout.addLayout(encoding_layout)
        
        layout.addWidget(file_group)
        layout.addStretch()
        
        return widget
    
    def _create_display_tab(self):
        """创建显示设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 视图设置
        view_group = QGroupBox("视图设置")
        view_layout = QVBoxLayout(view_group)
        
        # 默认视图模式
        view_mode_layout = QHBoxLayout()
        view_mode_layout.addWidget(QLabel("默认视图模式:"))
        self.view_mode_combo = QComboBox()
        self.view_mode_combo.addItems(["并排视图", "统一视图"])
        view_mode_layout.addWidget(self.view_mode_combo)
        view_mode_layout.addStretch()
        view_layout.addLayout(view_mode_layout)
        
        # 语法高亮
        self.syntax_highlight_checkbox = QCheckBox("启用语法高亮")
        self.syntax_highlight_checkbox.setChecked(True)
        view_layout.addWidget(self.syntax_highlight_checkbox)
        
        # 显示行号
        self.show_line_numbers_checkbox = QCheckBox("显示行号")
        self.show_line_numbers_checkbox.setChecked(True)
        view_layout.addWidget(self.show_line_numbers_checkbox)
        
        layout.addWidget(view_group)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QVBoxLayout(font_group)
        
        # 字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(8, 24)
        self.font_size_spinbox.setValue(10)
        self.font_size_spinbox.setSuffix(" pt")
        font_size_layout.addWidget(self.font_size_spinbox)
        font_size_layout.addStretch()
        font_layout.addLayout(font_size_layout)
        
        layout.addWidget(font_group)
        layout.addStretch()
        
        return widget
    
    def _create_advanced_tab(self):
        """创建高级设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 性能设置
        performance_group = QGroupBox("性能设置")
        performance_layout = QVBoxLayout(performance_group)
        
        # 上下文行数
        context_layout = QHBoxLayout()
        context_layout.addWidget(QLabel("差异上下文行数:"))
        self.context_lines_spinbox = QSpinBox()
        self.context_lines_spinbox.setRange(1, 20)
        self.context_lines_spinbox.setValue(3)
        self.context_lines_spinbox.setSuffix(" 行")
        context_layout.addWidget(self.context_lines_spinbox)
        context_layout.addStretch()
        performance_layout.addLayout(context_layout)
        
        # 大文件警告阈值
        large_file_layout = QHBoxLayout()
        large_file_layout.addWidget(QLabel("大文件警告阈值:"))
        self.large_file_spinbox = QSpinBox()
        self.large_file_spinbox.setRange(100, 10000)
        self.large_file_spinbox.setValue(1000)
        self.large_file_spinbox.setSuffix(" KB")
        large_file_layout.addWidget(self.large_file_spinbox)
        large_file_layout.addStretch()
        performance_layout.addLayout(large_file_layout)
        
        layout.addWidget(performance_group)
        
        # 调试设置
        debug_group = QGroupBox("调试设置")
        debug_layout = QVBoxLayout(debug_group)
        
        self.debug_mode_checkbox = QCheckBox("启用调试模式")
        debug_layout.addWidget(self.debug_mode_checkbox)
        
        self.verbose_logging_checkbox = QCheckBox("详细日志输出")
        debug_layout.addWidget(self.verbose_logging_checkbox)
        
        layout.addWidget(debug_group)
        layout.addStretch()
        
        return widget

    def _create_external_tools_tab(self):
        """创建外部工具配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 工具检测状态
        status_group = QGroupBox("工具检测状态")
        status_layout = QVBoxLayout(status_group)

        # 刷新按钮
        refresh_layout = QHBoxLayout()
        refresh_button = QPushButton("刷新检测")
        refresh_button.clicked.connect(self._refresh_external_tools)
        refresh_layout.addWidget(refresh_button)
        refresh_layout.addStretch()
        status_layout.addLayout(refresh_layout)

        # 工具状态列表
        self.tools_status_layout = QVBoxLayout()
        status_layout.addLayout(self.tools_status_layout)

        layout.addWidget(status_group)

        # 自定义路径设置
        custom_group = QGroupBox("自定义工具路径")
        custom_layout = QFormLayout(custom_group)

        # 存储自定义路径控件的字典
        self.custom_path_widgets = {}

        # 为每个工具创建路径设置
        for tool_name, tool in external_tools_manager.tools.items():
            path_layout = QHBoxLayout()

            path_edit = QLineEdit()
            path_edit.setPlaceholderText(f"自定义 {tool.display_name} 路径...")
            if tool.custom_path:
                path_edit.setText(tool.custom_path)

            browse_button = QPushButton("浏览")
            browse_button.clicked.connect(
                lambda checked, name=tool_name, edit=path_edit: self._browse_tool_path(name, edit)
            )

            clear_button = QPushButton("清除")
            clear_button.clicked.connect(
                lambda checked, name=tool_name, edit=path_edit: self._clear_tool_path(name, edit)
            )

            path_layout.addWidget(path_edit)
            path_layout.addWidget(browse_button)
            path_layout.addWidget(clear_button)

            custom_layout.addRow(f"{tool.display_name}:", path_layout)
            self.custom_path_widgets[tool_name] = path_edit

        layout.addWidget(custom_group)

        # 更新工具状态显示
        self._update_tools_status()

        layout.addStretch()
        return widget
    
    def _load_settings(self):
        """加载设置"""
        # 这里可以从配置文件加载设置
        # 目前使用默认值
        pass
    
    def _save_settings(self):
        """保存设置"""
        # 这里可以保存设置到配置文件
        # 目前只是显示消息
        QMessageBox.information(self, "设置", "设置已保存")
    
    def _on_ok_clicked(self):
        """确定按钮点击事件"""
        self._save_settings()
        self.accept()
    
    def _on_apply_clicked(self):
        """应用按钮点击事件"""
        self._save_settings()
    
    def _refresh_external_tools(self):
        """刷新外部工具检测"""
        external_tools_manager._detect_available_tools()
        self._update_tools_status()
        QMessageBox.information(self, "刷新完成", "外部工具检测已刷新")

    def _update_tools_status(self):
        """更新工具状态显示"""
        # 清除现有状态显示
        for i in reversed(range(self.tools_status_layout.count())):
            child = self.tools_status_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 添加工具状态
        for tool in external_tools_manager.tools.values():
            status_text = f"✅ {tool.display_name}" if tool.is_available else f"❌ {tool.display_name}"
            if tool.custom_path:
                status_text += f" (自定义路径)"

            status_label = QLabel(status_text)
            status_label.setStyleSheet("padding: 2px; margin: 1px;")
            self.tools_status_layout.addWidget(status_label)

    def _browse_tool_path(self, tool_name: str, path_edit: QLineEdit):
        """浏览工具路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            f"选择 {external_tools_manager.tools[tool_name].display_name} 可执行文件",
            "",
            "可执行文件 (*.exe);;所有文件 (*.*)" if os.name == 'nt' else "所有文件 (*.*)"
        )

        if file_path:
            path_edit.setText(file_path)
            # 立即应用设置
            if external_tools_manager.set_custom_path(tool_name, file_path):
                self._update_tools_status()
                QMessageBox.information(self, "成功", f"{external_tools_manager.tools[tool_name].display_name} 路径设置成功")
            else:
                QMessageBox.warning(self, "错误", "所选文件不存在或无效")

    def _clear_tool_path(self, tool_name: str, path_edit: QLineEdit):
        """清除工具路径"""
        path_edit.clear()
        external_tools_manager.set_custom_path(tool_name, "")
        self._update_tools_status()

    def get_settings(self):
        """获取当前设置"""
        settings = {
            'max_commits': self.max_commits_spinbox.value(),
            'auto_encoding': self.auto_encoding_checkbox.isChecked(),
            'default_encoding': self.encoding_combo.currentText(),
            'default_view_mode': self.view_mode_combo.currentText(),
            'syntax_highlight': self.syntax_highlight_checkbox.isChecked(),
            'show_line_numbers': self.show_line_numbers_checkbox.isChecked(),
            'font_size': self.font_size_spinbox.value(),
            'context_lines': self.context_lines_spinbox.value(),
            'large_file_threshold': self.large_file_spinbox.value(),
            'debug_mode': self.debug_mode_checkbox.isChecked(),
            'verbose_logging': self.verbose_logging_checkbox.isChecked()
        }

        # 添加外部工具路径设置
        external_tools_paths = {}
        for tool_name, path_edit in self.custom_path_widgets.items():
            path = path_edit.text().strip()
            if path:
                external_tools_paths[tool_name] = path
        settings['external_tools_paths'] = external_tools_paths

        return settings
