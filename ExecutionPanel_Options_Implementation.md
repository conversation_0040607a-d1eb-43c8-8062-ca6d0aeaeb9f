# RunSim GUI 执行日志面板仿真选项控制功能实现

## 功能概述

在RunSim GUI的执行日志面板(ExecutionPanel)中，为每个用例标签页(case tab)添加了便捷的仿真选项控制功能，允许用户直接在执行日志页面修改-fsdb和-R选项并重新执行，简化FAIL用例的调试流程。

## 实现的功能

### 1. 命令解析和修改工具 (utils/command_generator.py)

#### 新增方法：

- **`modify_command_options(command, fsdb_enabled=None, r_enabled=None)`**
  - 修改命令中的-fsdb和-R选项
  - 支持BATCH RUN模式的分号分隔命令
  - 智能处理选项的添加和移除

- **`has_fsdb_option(command)`** / **`has_r_option(command)`**
  - 检查命令中是否包含指定选项
  - 支持BATCH RUN模式检查

- **`_modify_single_command_options(command, fsdb_enabled, r_enabled)`**
  - 修改单个命令中的选项

- **`_modify_fsdb_option(parts, enabled)`** / **`_modify_r_option(parts, enabled)`**
  - 具体的选项修改逻辑
  - 智能处理选项位置和参数

#### 特性：
- ✅ 支持普通命令和BATCH RUN命令
- ✅ 智能选项位置管理
- ✅ 防止重复选项
- ✅ 保持命令结构完整性

### 2. 进程管理器增强 (utils/process_manager.py)

#### 新增方法：

- **`update_command(new_command)`**
  - 更新执行命令
  - 不影响正在运行的进程
  - 新命令在下次start()时生效

### 3. 日志面板UI增强 (views/log_panel.py)

#### UI改进：

- **仿真选项控制区域**
  - 在状态栏中添加-fsdb和-R复选框
  - 红色主题突出显示仿真选项
  - 工具提示说明选项用途

- **复选框样式**
  ```css
  QCheckBox {
      color: #2c3e50;
      font-weight: bold;
      padding: 3px;
      font-size: 10pt;
  }
  QCheckBox::indicator:checked {
      border: 2px solid #e74c3c;
      background-color: #e74c3c;
      border-radius: 3px;
  }
  ```

#### 新增方法：

- **`_init_checkbox_states()`**
  - 根据当前命令初始化复选框状态
  - 自动检测命令中的选项

- **`_on_fsdb_option_changed(state)`** / **`_on_r_option_changed(state)`**
  - 处理复选框状态变更
  - 实时更新命令和预览

- **`_update_command_preview()`**
  - 更新命令预览显示

## 技术实现细节

### 1. 命令修改算法

```python
def modify_command_options(command, fsdb_enabled=None, r_enabled=None):
    # 处理BATCH RUN模式
    if ' ; ' in command:
        commands = command.split(' ; ')
        modified_commands = []
        for cmd in commands:
            modified_cmd = _modify_single_command_options(cmd, fsdb_enabled, r_enabled)
            modified_commands.append(modified_cmd)
        return ' ; '.join(modified_commands)
    else:
        # 普通模式
        return _modify_single_command_options(command, fsdb_enabled, r_enabled)
```

### 2. 选项检测逻辑

```python
def has_fsdb_option(command):
    if ' ; ' in command:
        # BATCH RUN模式：检查任一命令是否包含选项
        commands = command.split(' ; ')
        for cmd in commands:
            if _has_option_in_single_command(cmd.strip(), "-fsdb"):
                return True
        return False
    else:
        return _has_option_in_single_command(command, "-fsdb")
```

### 3. UI集成

```python
def _on_fsdb_option_changed(self, state):
    enabled = state == 2  # Qt.Checked = 2
    self.command = CommandParser.modify_command_options(
        self.command, fsdb_enabled=enabled
    )
    self._update_command_preview()
    if hasattr(self, 'process_manager') and self.process_manager:
        self.process_manager.update_command(self.command)
```

## 使用流程

### 1. 初始执行
1. 用户在基础参数配置页面设置参数并执行用例
2. 系统在ExecutionPanel中创建新的标签页
3. LogPanel自动检测命令中的选项并设置复选框状态

### 2. 调试失败用例
1. 仿真失败后，用户在当前标签页中勾选-fsdb复选框
2. 系统自动更新命令，添加波形输出选项
3. 用户点击"重新执行"按钮，使用新命令重新运行
4. 如需跳过编译，可勾选-R选项

### 3. 选项管理
- 复选框状态与命令同步
- 支持任意组合的选项启用/禁用
- 实时预览命令变化
- 保持与历史记录的一致性

## 兼容性

### 支持的命令格式：
- ✅ 普通runsim命令
- ✅ BATCH RUN分号分隔命令
- ✅ 包含各种参数的复杂命令
- ✅ 已包含-fsdb/-R选项的命令

### 支持的操作：
- ✅ 添加选项到无选项命令
- ✅ 从有选项命令移除选项
- ✅ 选项状态切换
- ✅ 多选项同时修改

## 测试验证

### 1. 命令解析测试 (test_command_parser.py)
- 验证各种命令格式的解析
- 测试选项添加和移除
- 验证BATCH RUN命令处理

### 2. GUI功能测试 (test_simple_gui.py)
- 验证复选框与命令同步
- 测试实时命令更新
- 验证用户交互流程

## 性能优化

### 1. 命令处理优化
- 使用字符串分割而非正则表达式
- 避免不必要的命令重建
- 智能选项位置管理

### 2. UI响应优化
- 信号阻塞防止循环触发
- 延迟更新减少UI刷新
- 内存友好的字符串处理

## 错误处理

### 1. 命令解析错误
- 异常捕获和日志记录
- 优雅降级到原始命令
- 用户友好的错误提示

### 2. UI状态错误
- 复选框状态同步检查
- 命令预览更新保护
- 进程管理器状态验证

## 扩展性

### 1. 新选项支持
- 模块化的选项处理架构
- 易于添加新的仿真选项
- 统一的选项管理接口

### 2. UI扩展
- 灵活的布局设计
- 可配置的选项显示
- 主题适配支持

## 总结

本实现成功为RunSim GUI的ExecutionPanel添加了便捷的仿真选项控制功能，显著简化了FAIL用例的调试流程。用户现在可以直接在执行日志页面修改-fsdb和-R选项并重新执行，无需在不同页面间切换，大大提高了调试效率。

实现采用了模块化设计，具有良好的扩展性和维护性，同时保持了与现有代码的兼容性。
