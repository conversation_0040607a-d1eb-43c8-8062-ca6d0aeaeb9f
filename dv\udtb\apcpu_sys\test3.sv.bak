class test;

	force tb.aaa = 1; // FORCE_CHECK (Confirmed by jiadong.he)
	
	// force tb.bbb = 0;
	
	/*
	force tb.ccc = 0;
	force tb.ddd = 0;
	wait(tb.bbb == 1'b1);
	*/
	
	wait(tb.ccc == 1'b0); // WAIT_CHECK
	
	force tb.ddd = 32'h0; // FORCE_CHECK (Confirmed by jiadong.he)
	
	wait(tb.ddd == 'h4); // WAIT_CHECK (Confirmed by jiadong.he)
	
	void'(sprd_hld_force("tb.ccc", 1)); // FORCE_CHECK (Confirmed by jiadong.he)
	
	uvm_hld_force("tb.ddd", 0); // FORCE_CHECK (Confirmed by jiadong.he)
	
	force tb.aaa = 0; // FORCE_CHECK (Confirmed by jiadong.he)
	
	force tb.ccc = 0; // FORCE_CHECK (Confirmed by jiadong.he)

endclass