"""
终端选择对话框
用于让用户选择要发送命令的终端
"""
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QMessageBox, QGroupBox, QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QFont
from utils.terminal_detector import TerminalInfo
from utils.terminal_manager import TerminalManager
import os


class TerminalSelectorDialog(QDialog):
    """终端选择对话框"""
    
    # 定义信号
    terminal_selected = pyqtSignal(TerminalInfo, str)  # 选中的终端和命令
    
    def __init__(self, command: str, parent=None):
        """
        初始化对话框
        
        Args:
            command: 要发送的命令
            parent: 父窗口
        """
        super().__init__(parent)
        self.command = command
        self.selected_terminal = None
        self.terminals = []
        
        self.init_ui()
        self.load_terminals()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("选择终端执行")
        self.setModal(True)
        self.resize(600, 500)
        
        # 设置窗口图标
        self.setWindowIcon(QIcon.fromTheme("utilities-terminal"))
        
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 标题
        title_label = QLabel("选择要执行命令的终端:")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 命令预览组
        command_group = QGroupBox("要执行的命令:")
        command_layout = QVBoxLayout()
        
        self.command_preview = QTextEdit()
        self.command_preview.setPlainText(self.command)
        self.command_preview.setReadOnly(True)
        self.command_preview.setMaximumHeight(80)
        self.command_preview.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
                background-color: #f5f5f5;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
            }
        """)
        
        command_layout.addWidget(self.command_preview)
        command_group.setLayout(command_layout)
        layout.addWidget(command_group)
        
        # 终端列表组
        terminal_group = QGroupBox("可用终端:")
        terminal_layout = QVBoxLayout()
        
        self.terminal_list = QListWidget()
        self.terminal_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
                selection-background-color: #4a9eff;
                selection-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:hover {
                background-color: #f0f8ff;
            }
            QListWidget::item:selected {
                background-color: #4a9eff;
                color: white;
            }
        """)
        self.terminal_list.itemDoubleClicked.connect(self.on_terminal_double_clicked)
        self.terminal_list.itemSelectionChanged.connect(self.on_selection_changed)
        
        terminal_layout.addWidget(self.terminal_list)
        
        # 刷新按钮
        refresh_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新终端列表")
        self.refresh_btn.setIcon(QIcon.fromTheme("view-refresh"))
        self.refresh_btn.clicked.connect(self.load_terminals)
        refresh_layout.addWidget(self.refresh_btn)
        refresh_layout.addStretch()
        
        terminal_layout.addLayout(refresh_layout)
        terminal_group.setLayout(terminal_layout)
        layout.addWidget(terminal_group)
        
        # 状态标签
        self.status_label = QLabel("正在检测终端...")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)
        
        # 新建终端按钮（单独放置，使其更加明显）
        new_terminal_layout = QHBoxLayout()
        self.new_terminal_btn = QPushButton("打开新终端")
        self.new_terminal_btn.setIcon(QIcon.fromTheme("utilities-terminal"))
        self.new_terminal_btn.clicked.connect(self.open_new_terminal)
        self.new_terminal_btn.setMinimumHeight(40)  # 增加按钮高度
        new_terminal_layout.addWidget(self.new_terminal_btn)
        new_terminal_layout.addStretch()
        layout.addLayout(new_terminal_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setIcon(QIcon.fromTheme("dialog-cancel"))
        self.cancel_btn.clicked.connect(self.reject)
        
        # 确定按钮
        self.ok_btn = QPushButton("发送命令")
        self.ok_btn.setIcon(QIcon.fromTheme("dialog-ok"))
        self.ok_btn.clicked.connect(self.send_command)
        self.ok_btn.setEnabled(False)
        self.ok_btn.setDefault(True)
        
        # 设置按钮样式
        button_style = """
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
                color: #333;  /* 深色文字，确保可见 */
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
                border-color: #999;
            }
            QPushButton:pressed {
                background-color: #e0e0e0;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999;
                border-color: #ddd;
            }
        """
        
        ok_button_style = """
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #4a9eff;
                border-radius: 4px;
                background-color: #4a9eff;
                color: white;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3d8bdb;
                border-color: #3d8bdb;
            }
            QPushButton:pressed {
                background-color: #2c6cb7;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
                border-color: #cccccc;
            }
        """
        
        # 自定义新建终端按钮样式，让它更加突出
        new_terminal_button_style = """
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #28a745;
                border-radius: 4px;
                background-color: #28a745;
                color: white;
                font-weight: bold;
                min-width: 150px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
                border-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """
        
        self.new_terminal_btn.setStyleSheet(new_terminal_button_style)
        self.cancel_btn.setStyleSheet(button_style)
        self.ok_btn.setStyleSheet(ok_button_style)
        
        # 不再需要在button_layout中添加new_terminal_btn，因为它已经单独添加了
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def load_terminals(self):
        """加载终端列表"""
        self.status_label.setText("正在检测终端...")
        self.terminal_list.clear()
        self.terminals.clear()
        
        try:
            # 检查依赖
            if not TerminalManager.is_available():
                self.status_label.setText("终端管理功能不可用，请安装 wmctrl 和 xdotool")
                return
            
            # 获取终端列表
            terminals = TerminalManager.get_available_terminals()
            
            if not terminals:
                self.status_label.setText("未找到打开的终端窗口")
                return
            
            # 添加到列表
            for terminal in terminals:
                self.terminals.append(terminal)
                
                item = QListWidgetItem()
                item.setText(f"{terminal.terminal_type}: {terminal.title}")
                item.setData(Qt.UserRole, terminal)
                
                # 设置图标
                if terminal.terminal_type == 'gnome-terminal':
                    item.setIcon(QIcon.fromTheme("utilities-terminal"))
                elif terminal.terminal_type == 'konsole':
                    item.setIcon(QIcon.fromTheme("utilities-terminal"))
                else:
                    item.setIcon(QIcon.fromTheme("applications-system"))
                
                self.terminal_list.addItem(item)
            
            self.status_label.setText(f"找到 {len(terminals)} 个终端窗口")
            
        except Exception as e:
            self.status_label.setText(f"检测终端时出错: {str(e)}")
    
    def on_selection_changed(self):
        """处理选择变更"""
        current_item = self.terminal_list.currentItem()
        if current_item:
            self.selected_terminal = current_item.data(Qt.UserRole)
            self.ok_btn.setEnabled(True)
        else:
            self.selected_terminal = None
            self.ok_btn.setEnabled(False)
    
    def on_terminal_double_clicked(self, item):
        """处理双击事件"""
        self.selected_terminal = item.data(Qt.UserRole)
        self.send_command()
    
    def send_command(self):
        """发送命令到选中的终端"""
        if not self.selected_terminal:
            QMessageBox.warning(self, "警告", "请先选择一个终端")
            return
        
        try:
            # 发送命令
            success = TerminalManager.send_command_to_terminal(self.selected_terminal, self.command)
            
            if success:
                QMessageBox.information(self, "成功", f"命令已发送到终端: {self.selected_terminal.title}")
                self.accept()
            else:
                QMessageBox.warning(self, "失败", "发送命令失败，请检查终端是否仍然打开")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"发送命令时出错: {str(e)}")
    
    def open_new_terminal(self):
        """打开新终端"""
        try:
            # 获取当前工作目录（RunSim GUI执行的目录）
            work_dir = os.getcwd()
            
            # 打开新终端并自动执行需要的命令
            success = TerminalManager.open_new_terminal(
                command=self.command,
                work_dir=work_dir,  
                execute_fee=True,         # 自动执行fee命令
                execute_runsim=True,      # 自动执行runsim命令
                execute_sshnew7=True      # 自动执行sshnew7命令，回车选择默认服务器
            )
            
            if success:
                QMessageBox.information(self, "成功", "新终端已打开，请刷新列表")
                # 延迟刷新，给新终端一些时间启动
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(1000, self.load_terminals)
            else:
                QMessageBox.warning(self, "失败", "无法打开新终端")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开新终端时出错: {str(e)}")


if __name__ == "__main__":
    # 测试代码
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    test_command = "echo 'Hello from RunSim!'"
    dialog = TerminalSelectorDialog(test_command)
    
    if dialog.exec_() == QDialog.Accepted:
        print("用户选择了终端并发送了命令")
    else:
        print("用户取消了操作")
    
    sys.exit()
