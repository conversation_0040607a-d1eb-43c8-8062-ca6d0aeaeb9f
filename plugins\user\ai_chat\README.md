# AI聊天助手插件

一个功能强大的AI聊天插件，为RunSim GUI应用程序提供智能对话、文档解析和翻译功能。

## 功能特性

### 🤖 AI聊天对话
- **现代化聊天界面**：采用消息气泡设计，支持用户和AI消息的清晰区分
- **流式响应**：实时显示AI生成的回复，提供流畅的对话体验
- **多会话管理**：支持创建、切换和删除多个聊天会话
- **消息历史**：自动保存聊天记录，支持会话恢复

### 📄 文档上传与解析
- **多格式支持**：支持PDF、Word（.docx/.doc）、TXT、Markdown等常见文档格式
- **智能解析**：自动提取文档内容并生成摘要
- **上下文集成**：AI可基于上传的文档内容回答问题
- **文档管理**：可视化文档列表，支持添加和删除文档

### 🌐 中英文互译
- **智能语言检测**：自动识别输入文本的语言
- **双向翻译**：支持中文到英文、英文到中文的互译
- **翻译集成**：可直接翻译输入框内容或聊天消息
- **结果展示**：清晰显示原文和译文对比

### ⚙️ 灵活配置管理
- **多模型支持**：
  - DeepSeek系列（deepseek-chat、deepseek-coder、deepseek-reasoner）
  - 通义千问系列（qwen-turbo、qwen-plus、qwen-max等）
  - OpenAI系列（gpt-3.5-turbo、gpt-4等）
  - Open Router（支持多种模型提供商的统一接口）
- **API配置**：支持自定义API Key、API URL和模型选择
- **参数调节**：可调整温度、最大令牌数等生成参数
- **界面定制**：支持主题、字体大小等界面个性化设置

## 安装要求

### 基础依赖
- Python 3.8+
- PyQt5 5.15+
- requests

### 可选依赖（用于文档解析）
```bash
# PDF支持
pip install PyPDF2

# Word文档支持
pip install python-docx

# DOC文件支持（Windows）
pip install pywin32

# 或者使用跨平台方案
pip install docx2txt

# RTF文件支持
pip install striprtf
```

## 快速开始

### 1. 启动插件
1. 启动RunSim GUI应用程序
2. 在菜单栏中选择"工具" → "AI聊天助手"
3. AI聊天窗口将会打开

### 2. 配置API
1. 点击工具栏中的"设置"按钮
2. 在"API配置"标签页中：
   - 选择模型提供商（DeepSeek、通义千问、OpenAI或Open Router）
   - 输入对应的API Key
   - 确认API URL（通常会自动填充）
   - 选择要使用的具体模型
3. 点击"测试连接"验证配置
4. 点击"确定"保存设置

### 3. 开始聊天
1. 在输入框中输入您的问题或消息
2. 点击"发送"按钮或按Enter键
3. AI将会实时生成回复

### 4. 上传文档
1. 在左侧文档面板中点击"上传文档"
2. 选择要上传的文档文件
3. 等待文档解析完成（显示✓标记）
4. 现在可以基于文档内容提问

### 5. 使用翻译功能
1. 在输入框中输入要翻译的文本
2. 点击"翻译"按钮
3. 选择是否将译文替换到输入框

## 配置说明

### API配置
- **DeepSeek**：
  - API URL: `https://api.deepseek.com/v1`
  - 需要DeepSeek API Key
  - 推荐模型：`deepseek-chat`

- **通义千问**：
  - API URL: `https://dashscope.aliyuncs.com/compatible-mode/v1`
  - 需要阿里云API Key
  - 推荐模型：`qwen-turbo`

- **OpenAI**：
  - API URL: `https://api.openai.com/v1`
  - 需要OpenAI API Key
  - 推荐模型：`gpt-3.5-turbo`

- **Open Router**：
  - API URL: `https://openrouter.ai/api/v1`
  - 需要Open Router API Key（支持免费模型）
  - 支持多种模型：OpenAI、Anthropic、Google、Meta等
  - **免费模型推荐**：
    - `deepseek/deepseek-r1:free` - DeepSeek最新推理模型
    - `google/gemini-2.0-flash-exp:free` - Google Gemini 2.0实验版
    - `meta-llama/llama-3.2-3b-instruct:free` - Meta Llama轻量版
  - **付费模型推荐**：`openai/gpt-4o`、`anthropic/claude-3.5-sonnet`

### 高级设置
- **请求超时**：API请求的超时时间（默认30秒）
- **最大重试次数**：请求失败时的重试次数（默认3次）
- **最大文件大小**：上传文档的大小限制（默认10MB）

## 使用技巧

### 1. 文档问答
- 上传相关文档后，可以询问："请总结这个文档的主要内容"
- 或者："根据文档内容，如何解决XX问题？"

### 2. 代码相关
- 可以询问编程问题："如何用Python实现XX功能？"
- 或者请求代码审查："请帮我检查这段代码的问题"

### 3. 翻译使用
- 支持技术文档翻译
- 可以翻译代码注释
- 支持学术论文摘要翻译

### 4. 多会话管理
- 为不同主题创建不同的会话
- 可以同时进行多个独立的对话
- 会话标题会自动根据首条消息生成

## 故障排除

### 常见问题

**Q: 插件无法启动**
A: 检查是否安装了所有必需的依赖，特别是PyQt5和requests

**Q: API连接失败**
A: 
1. 检查网络连接
2. 验证API Key是否正确
3. 确认API URL格式正确
4. 检查API服务是否可用

**Q: 文档上传失败**
A:
1. 检查文件格式是否支持
2. 确认文件大小未超过限制
3. 安装对应的文档解析库

**Q: 翻译功能不工作**
A:
1. 确保API配置正确
2. 检查翻译功能是否在设置中启用
3. 验证网络连接

### 日志查看
插件会在控制台输出详细的日志信息，包括：
- 插件初始化状态
- API请求和响应
- 文档处理进度
- 错误信息和堆栈跟踪

## 开发信息

### 项目结构
```
ai_chat/
├── models/              # 数据模型
│   ├── chat_model.py   # 聊天数据管理
│   ├── config_model.py # 配置管理
│   └── document_model.py # 文档管理
├── views/              # 界面组件
│   ├── chat_window.py  # 主聊天窗口
│   └── settings_dialog.py # 设置对话框
├── controllers/        # 控制器
│   └── chat_controller.py # 聊天控制逻辑
└── utils/              # 工具类
    ├── api_client.py   # API客户端
    ├── document_parser.py # 文档解析
    └── translator.py   # 翻译功能
```

### 扩展开发
插件采用模块化设计，支持功能扩展：
- 添加新的AI模型支持
- 扩展文档格式支持
- 增加新的翻译服务
- 自定义界面主题

## 版本历史

### v1.0.0
- 初始版本发布
- 基础聊天功能
- 文档上传解析
- 中英文翻译
- 多模型支持
- 配置管理界面

## 许可证

本插件遵循与主项目相同的许可证。

## 支持与反馈

如有问题或建议，请通过以下方式联系：
- 在主项目中提交Issue
- 发送邮件至开发团队
- 参与项目讨论
