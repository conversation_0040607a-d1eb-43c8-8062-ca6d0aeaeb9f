#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应日志刷新管理器

根据系统负载和日志活动动态调整刷新间隔，优化性能
"""

import time
import psutil
from utils.performance_monitor import PerformanceMonitor

class AdaptiveRefreshManager:
    """自适应日志刷新管理器"""
    
    def __init__(self):
        """初始化自适应刷新管理器"""
        # 基础刷新间隔（毫秒）
        self.base_visible_interval = 300  # 可见标签页基础间隔
        self.base_hidden_interval = 2000  # 隐藏标签页基础间隔
        
        # 性能监控器
        self.performance_monitor = PerformanceMonitor.instance()
        
        # 系统负载缓存
        self._last_load_check = 0
        self._cached_cpu_load = 0.0
        self._cached_memory_load = 0.0
        self._load_cache_duration = 2.0  # 负载缓存持续时间（秒）
        
        # 自适应参数
        self.load_factor_cache = {}
        self.activity_factor_cache = {}
        
    def get_refresh_interval(self, is_visible, log_activity=0, case_name=""):
        """
        根据系统负载和日志活动动态调整刷新间隔
        
        Args:
            is_visible (bool): 标签页是否可见
            log_activity (int): 日志活动量（字节数或行数）
            case_name (str): 用例名称（用于缓存）
            
        Returns:
            int: 刷新间隔（毫秒）
        """
        base_interval = self.base_visible_interval if is_visible else self.base_hidden_interval
        
        # 获取系统负载（使用缓存减少开销）
        cpu_load, memory_load = self._get_system_load()
        
        # 计算负载调整因子
        load_multiplier = self._calculate_load_multiplier(cpu_load, memory_load)
        
        # 计算活动调整因子
        activity_multiplier = self._calculate_activity_multiplier(log_activity, is_visible)
        
        # 计算最终间隔
        final_interval = int(base_interval * load_multiplier * activity_multiplier)
        
        # 限制在合理范围内
        if is_visible:
            return max(100, min(final_interval, 1000))  # 100ms - 1s
        else:
            return max(1000, min(final_interval, 5000))  # 1s - 5s
    
    def _get_system_load(self):
        """
        获取系统负载（带缓存）
        
        Returns:
            tuple: (CPU负载, 内存负载)
        """
        current_time = time.time()
        
        # 检查缓存是否有效
        if current_time - self._last_load_check < self._load_cache_duration:
            return self._cached_cpu_load, self._cached_memory_load
        
        try:
            # 尝试从性能监控器获取
            metrics = self.performance_monitor.get_current_metrics()
            if metrics and hasattr(metrics, 'cpu_usage') and hasattr(metrics, 'memory_usage'):
                self._cached_cpu_load = metrics.cpu_usage
                self._cached_memory_load = metrics.memory_usage
            else:
                # 直接获取系统负载
                self._cached_cpu_load = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                self._cached_memory_load = memory.percent
        except Exception:
            # 如果获取失败，使用默认值
            self._cached_cpu_load = 0.0
            self._cached_memory_load = 0.0
        
        self._last_load_check = current_time
        return self._cached_cpu_load, self._cached_memory_load
    
    def _calculate_load_multiplier(self, cpu_load, memory_load):
        """
        计算负载调整因子
        
        Args:
            cpu_load (float): CPU负载百分比
            memory_load (float): 内存负载百分比
            
        Returns:
            float: 负载调整因子
        """
        # 使用最高负载作为主要指标
        max_load = max(cpu_load, memory_load)
        
        if max_load > 85:
            return 3.0  # 极高负载：大幅降低刷新频率
        elif max_load > 70:
            return 2.0  # 高负载：显著降低刷新频率
        elif max_load > 50:
            return 1.5  # 中等负载：适度降低刷新频率
        elif max_load > 30:
            return 1.0  # 正常负载：保持默认频率
        else:
            return 0.8  # 低负载：可以提高刷新频率
    
    def _calculate_activity_multiplier(self, log_activity, is_visible):
        """
        计算活动调整因子
        
        Args:
            log_activity (int): 日志活动量
            is_visible (bool): 是否可见
            
        Returns:
            float: 活动调整因子
        """
        if not is_visible:
            # 隐藏标签页总是降低频率
            return 1.5
        
        # 根据日志活动量调整
        if log_activity > 10000:  # 高活动（>10KB）
            return 0.7  # 提高刷新频率
        elif log_activity > 5000:  # 中等活动（5-10KB）
            return 0.9  # 略微提高刷新频率
        elif log_activity > 1000:  # 正常活动（1-5KB）
            return 1.0  # 保持默认频率
        elif log_activity > 100:   # 低活动（100B-1KB）
            return 1.2  # 略微降低刷新频率
        else:  # 极低活动（<100B）
            return 1.5  # 显著降低刷新频率
    
    def get_adaptive_batch_size(self, is_visible, system_load=None):
        """
        获取自适应批处理大小
        
        Args:
            is_visible (bool): 标签页是否可见
            system_load (float, optional): 系统负载
            
        Returns:
            int: 批处理大小（字节）
        """
        if system_load is None:
            cpu_load, memory_load = self._get_system_load()
            system_load = max(cpu_load, memory_load)
        
        base_size = 8192 if is_visible else 16384  # 基础批处理大小
        
        if system_load > 80:
            return base_size * 4  # 高负载时增加批处理大小
        elif system_load > 60:
            return base_size * 2  # 中等负载时适度增加
        elif system_load < 30:
            return base_size // 2  # 低负载时减少批处理大小
        else:
            return base_size  # 正常负载保持默认大小
    
    def should_force_flush(self, last_flush_time, buffer_size, is_visible):
        """
        判断是否应该强制刷新
        
        Args:
            last_flush_time (float): 上次刷新时间
            buffer_size (int): 缓冲区大小
            is_visible (bool): 标签页是否可见
            
        Returns:
            bool: 是否应该强制刷新
        """
        current_time = time.time()
        time_since_flush = current_time - last_flush_time
        
        # 获取系统负载
        cpu_load, memory_load = self._get_system_load()
        max_load = max(cpu_load, memory_load)
        
        # 根据负载调整强制刷新阈值
        if max_load > 80:
            # 高负载时更保守
            max_wait_time = 5.0 if is_visible else 10.0
            max_buffer_size = 65536
        elif max_load > 50:
            # 中等负载时适中
            max_wait_time = 3.0 if is_visible else 6.0
            max_buffer_size = 32768
        else:
            # 低负载时更积极
            max_wait_time = 1.0 if is_visible else 3.0
            max_buffer_size = 16384
        
        # 检查是否需要强制刷新
        return (time_since_flush > max_wait_time or 
                buffer_size > max_buffer_size)
    
    def get_performance_stats(self):
        """
        获取性能统计信息
        
        Returns:
            dict: 性能统计信息
        """
        cpu_load, memory_load = self._get_system_load()
        
        return {
            'cpu_load': cpu_load,
            'memory_load': memory_load,
            'max_load': max(cpu_load, memory_load),
            'cache_valid': time.time() - self._last_load_check < self._load_cache_duration,
            'base_visible_interval': self.base_visible_interval,
            'base_hidden_interval': self.base_hidden_interval
        }
    
    def reset_cache(self):
        """重置所有缓存"""
        self._last_load_check = 0
        self._cached_cpu_load = 0.0
        self._cached_memory_load = 0.0
        self.load_factor_cache.clear()
        self.activity_factor_cache.clear()

# 全局实例
_global_refresh_manager = None

def get_adaptive_refresh_manager():
    """获取全局自适应刷新管理器实例"""
    global _global_refresh_manager
    if _global_refresh_manager is None:
        _global_refresh_manager = AdaptiveRefreshManager()
    return _global_refresh_manager
