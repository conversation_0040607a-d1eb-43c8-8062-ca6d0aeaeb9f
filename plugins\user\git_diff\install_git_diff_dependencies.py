#!/usr/bin/env python3
"""
Git文件版本比对插件依赖安装脚本

自动安装Git文件版本比对插件所需的Python包
"""

import subprocess
import sys
import os


def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr


def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False


def install_package(package_name, pip_name=None):
    """安装Python包"""
    if pip_name is None:
        pip_name = package_name
    
    print(f"正在安装 {package_name}...")
    
    success, output = run_command(f"pip install {pip_name}")
    
    if success:
        print(f"✓ {package_name} 安装成功")
        return True
    else:
        print(f"✗ {package_name} 安装失败: {output}")
        return False


def main():
    """主安装函数"""
    print("Git文件版本比对插件依赖安装程序")
    print("=" * 50)
    
    # 基础依赖包
    basic_packages = [
        ("git", "GitPython"),
        ("chardet", "chardet"),
    ]
    
    # 可选依赖包
    optional_packages = [
        ("pygments", "Pygments"),
    ]
    
    print("\n检查基础依赖...")
    basic_success = True
    
    for import_name, pip_name in basic_packages:
        if check_package(import_name):
            print(f"✓ {pip_name} 已安装")
        else:
            if not install_package(pip_name, pip_name):
                basic_success = False
    
    if not basic_success:
        print("\n❌ 基础依赖安装失败，插件无法正常工作")
        return False
    
    print("\n检查可选依赖（用于语法高亮）...")
    
    # 询问是否安装可选依赖
    install_optional = input("\n是否安装语法高亮依赖？(y/n): ").lower().strip()
    
    if install_optional in ['y', 'yes', '是']:
        optional_success = 0
        total_optional = len(optional_packages)
        
        for import_name, pip_name in optional_packages:
            if check_package(import_name):
                print(f"✓ {pip_name} 已安装")
                optional_success += 1
            else:
                if install_package(pip_name, pip_name):
                    optional_success += 1
        
        print(f"\n可选依赖安装完成: {optional_success}/{total_optional}")
        
        if optional_success < total_optional:
            print("\n⚠️  部分可选依赖安装失败，但不影响基本功能")
            print("您仍然可以使用文件比对功能")
            print("如需语法高亮功能，请手动安装失败的包")
    else:
        print("跳过可选依赖安装")
    
    print("\n" + "=" * 50)
    print("🎉 依赖安装完成！")
    print("\n使用说明:")
    print("1. 启动RunSim GUI应用程序")
    print("2. 在菜单栏选择 '工具' → 'Git文件版本比对工具'")
    print("3. 选择Git仓库目录或具体文件")
    print("4. 选择要比对的历史版本")
    print("5. 查看差异结果")
    
    print("\n支持的功能:")
    features = ["文件版本比对", "历史版本选择", "并排/统一差异显示"]
    
    if check_package("pygments"):
        features.append("语法高亮")
    
    print("- " + "\n- ".join(features))
    
    print("\n支持的文件类型:")
    print("- Python (.py, .pyw)")
    print("- C/C++ (.c, .cpp, .h, .hpp)")
    print("- SystemVerilog (.sv, .v, .vh)")
    print("- 文本文件 (.txt, .md, .log)")
    print("- 其他文本格式文件")
    
    return True


if __name__ == "__main__":
    main()
