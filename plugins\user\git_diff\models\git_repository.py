"""
Git仓库操作模型

提供Git仓库的基本操作功能，包括：
- 仓库检测和初始化
- 文件历史获取
- 文件内容读取
- 分支和标签操作
"""

import os
import sys
from typing import List, Optional, Dict, Tuple
from datetime import datetime

try:
    import git
    from git import Repo, Commit
    GIT_AVAILABLE = True
except ImportError:
    GIT_AVAILABLE = False
    print("警告: GitPython未安装，Git功能将不可用")


class CommitInfo:
    """提交信息数据类"""
    
    def __init__(self, commit):
        if GIT_AVAILABLE and isinstance(commit, Commit):
            self.sha = commit.hexsha
            self.short_sha = commit.hexsha[:8]
            self.author = commit.author.name
            self.author_email = commit.author.email
            self.date = datetime.fromtimestamp(commit.committed_date)
            self.message = commit.message.strip()
            self.summary = commit.summary
        else:
            # 默认值
            self.sha = ""
            self.short_sha = ""
            self.author = ""
            self.author_email = ""
            self.date = datetime.now()
            self.message = ""
            self.summary = ""
    
    def __str__(self):
        return f"{self.short_sha} - {self.summary} ({self.author}, {self.date.strftime('%Y-%m-%d %H:%M')})"


class GitRepository:
    """Git仓库操作类"""
    
    def __init__(self, repo_path: str = None):
        """初始化Git仓库
        
        Args:
            repo_path: 仓库路径，如果为None则使用当前目录
        """
        self.repo_path = repo_path or os.getcwd()
        self.repo = None
        self._is_valid_repo = False
        
        if GIT_AVAILABLE:
            self._initialize_repo()
    
    def _initialize_repo(self):
        """初始化Git仓库对象"""
        try:
            self.repo = Repo(self.repo_path, search_parent_directories=True)
            self._is_valid_repo = True
            self.repo_path = self.repo.working_dir
        except git.exc.InvalidGitRepositoryError:
            self._is_valid_repo = False
        except Exception as e:
            print(f"初始化Git仓库失败: {e}")
            self._is_valid_repo = False
    
    def is_valid_repo(self) -> bool:
        """检查是否为有效的Git仓库"""
        return GIT_AVAILABLE and self._is_valid_repo
    
    def get_repo_root(self) -> str:
        """获取仓库根目录"""
        if self.is_valid_repo():
            return self.repo.working_dir
        return self.repo_path
    
    def get_current_branch(self) -> str:
        """获取当前分支名"""
        if not self.is_valid_repo():
            return "未知"
        
        try:
            return self.repo.active_branch.name
        except Exception:
            return "分离HEAD"
    
    def get_branches(self) -> List[str]:
        """获取所有分支列表"""
        if not self.is_valid_repo():
            return []
        
        try:
            branches = []
            for branch in self.repo.branches:
                branches.append(branch.name)
            return branches
        except Exception as e:
            print(f"获取分支列表失败: {e}")
            return []
    
    def get_file_commits(self, file_path: str, max_count: int = 50) -> List[CommitInfo]:
        """获取文件的提交历史

        Args:
            file_path: 文件路径（相对于仓库根目录）
            max_count: 最大提交数量

        Returns:
            提交信息列表
        """
        if not self.is_valid_repo():
            return []

        try:
            # 确保文件路径是相对于仓库根目录的
            if os.path.isabs(file_path):
                rel_path = os.path.relpath(file_path, self.repo.working_dir)
            else:
                rel_path = file_path

            # 标准化路径分隔符（Git使用正斜杠）
            rel_path = rel_path.replace('\\', '/')

            commits = list(self.repo.iter_commits(paths=rel_path, max_count=max_count))
            return [CommitInfo(commit) for commit in commits]
        except Exception as e:
            print(f"获取文件提交历史失败: {e}")
            return []
    
    def get_file_content_at_commit(self, file_path: str, commit_sha: str) -> str:
        """获取指定提交中的文件内容

        Args:
            file_path: 文件路径（相对于仓库根目录）
            commit_sha: 提交哈希值

        Returns:
            文件内容字符串
        """
        if not self.is_valid_repo():
            return ""

        try:
            # 确保文件路径是相对于仓库根目录的
            if os.path.isabs(file_path):
                rel_path = os.path.relpath(file_path, self.repo.working_dir)
            else:
                rel_path = file_path

            # 标准化路径分隔符（Git使用正斜杠）
            rel_path = rel_path.replace('\\', '/')

            commit = self.repo.commit(commit_sha)
            blob = commit.tree[rel_path]
            
            # 尝试解码文件内容
            content_bytes = blob.data_stream.read()
            
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    return content_bytes.decode(encoding)
                except UnicodeDecodeError:
                    continue
            
            # 如果所有编码都失败，使用错误处理
            return content_bytes.decode('utf-8', errors='replace')
            
        except Exception as e:
            print(f"获取文件内容失败: {e}")
            return ""
    
    def get_current_file_content(self, file_path: str) -> str:
        """获取当前工作目录中的文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容字符串
        """
        try:
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.get_repo_root(), file_path)
            
            # 检测文件编码
            try:
                import chardet
                with open(file_path, 'rb') as f:
                    raw_data = f.read()
                    result = chardet.detect(raw_data)
                    encoding = result['encoding'] or 'utf-8'
            except ImportError:
                encoding = 'utf-8'
            
            # 读取文件内容
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                return f.read()
                
        except Exception as e:
            print(f"读取当前文件失败: {e}")
            return ""
    
    def get_tracked_files(self, directory: str = None) -> List[str]:
        """获取Git跟踪的文件列表

        Args:
            directory: 目录路径，如果为None则获取整个仓库的文件

        Returns:
            文件路径列表（相对于仓库根目录）
        """
        if not self.is_valid_repo():
            return []

        try:
            # 获取所有跟踪的文件
            tracked_files = []

            for item in self.repo.index.entries:
                file_path = item[0]  # 文件路径（Git内部使用正斜杠）

                # 如果指定了目录，只返回该目录下的文件
                if directory:
                    rel_dir = os.path.relpath(directory, self.repo.working_dir)
                    # 标准化路径分隔符进行比较
                    rel_dir = rel_dir.replace('\\', '/')
                    if not file_path.startswith(rel_dir):
                        continue

                tracked_files.append(file_path)

            return sorted(tracked_files)

        except Exception as e:
            print(f"获取跟踪文件列表失败: {e}")
            return []
    
    def file_exists_in_commit(self, file_path: str, commit_sha: str) -> bool:
        """检查文件在指定提交中是否存在

        Args:
            file_path: 文件路径（可以是绝对路径或相对于仓库根目录的路径）
            commit_sha: 提交哈希值

        Returns:
            文件是否存在
        """
        if not self.is_valid_repo():
            return False

        try:
            # 确保路径是相对于仓库根目录的
            if os.path.isabs(file_path):
                rel_path = os.path.relpath(file_path, self.repo.working_dir)
            else:
                rel_path = file_path

            # 标准化路径分隔符（Windows兼容性）
            rel_path = rel_path.replace('\\', '/')

            commit = self.repo.commit(commit_sha)

            # 检查文件是否在提交的树中
            try:
                commit.tree[rel_path]
                return True
            except KeyError:
                return False

        except Exception as e:
            print(f"检查文件存在性失败: {e}")
            return False
