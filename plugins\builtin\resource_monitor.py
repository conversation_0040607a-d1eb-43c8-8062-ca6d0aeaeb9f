from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QProgressBar, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, QTimer, QRectF
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush
import psutil
import time
from datetime import datetime
from plugins.base import PluginBase

class TrendChart(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data = []  # [(timestamp, cpu, mem, disk)]
        self.setMinimumHeight(200)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制背景
        painter.fillRect(self.rect(), QColor(255, 255, 255))

        # 如果没有数据，返回
        if not self.data:
            return

        # 计算绘图区域
        margin = 30
        chart_rect = QRectF(
            margin, margin,
            self.width() - 2 * margin,
            self.height() - 2 * margin
        )

        # 绘制网格
        painter.setPen(QPen(QColor(200, 200, 200)))
        for i in range(0, 101, 20):  # 每20%画一条线
            y = int(chart_rect.bottom() - (i / 100.0) * chart_rect.height())
            painter.drawLine(
                int(chart_rect.left()), y,
                int(chart_rect.right()), y
            )

        # 绘制数据
        start_time = self.data[0][0]
        time_range = self.data[-1][0] - start_time

        def get_coordinates(timestamp, value):
            """计算数据点的坐标"""
            x = int(chart_rect.left() + ((timestamp - start_time) / time_range) * chart_rect.width())
            y = int(chart_rect.bottom() - (value / 100.0) * chart_rect.height())
            return x, y

        # 绘制CPU使用率
        painter.setPen(QPen(QColor(76, 175, 80), 2))
        for i in range(1, len(self.data)):
            x1, y1 = get_coordinates(self.data[i-1][0], self.data[i-1][1])
            x2, y2 = get_coordinates(self.data[i][0], self.data[i][1])
            painter.drawLine(x1, y1, x2, y2)

        # 绘制内存使用率
        painter.setPen(QPen(QColor(33, 150, 243), 2))
        for i in range(1, len(self.data)):
            x1, y1 = get_coordinates(self.data[i-1][0], self.data[i-1][2])
            x2, y2 = get_coordinates(self.data[i][0], self.data[i][2])
            painter.drawLine(x1, y1, x2, y2)

        # 绘制磁盘使用率
        painter.setPen(QPen(QColor(255, 152, 0), 2))
        for i in range(1, len(self.data)):
            x1, y1 = get_coordinates(self.data[i-1][0], self.data[i-1][3])
            x2, y2 = get_coordinates(self.data[i][0], self.data[i][3])
            painter.drawLine(x1, y1, x2, y2)

        # 绘制坐标轴
        painter.setPen(QPen(QColor(0, 0, 0)))
        painter.drawLine(
            int(chart_rect.left()), int(chart_rect.top()),
            int(chart_rect.left()), int(chart_rect.bottom())
        )
        painter.drawLine(
            int(chart_rect.left()), int(chart_rect.bottom()),
            int(chart_rect.right()), int(chart_rect.bottom())
        )

        # 绘制标签
        painter.drawText(5, margin, "100%")
        painter.drawText(5, int(chart_rect.bottom()), "0%")

        # 绘制图例
        legend_margin = 10
        legend_height = 20
        legend_width = 15
        text_margin = 5

        x = int(chart_rect.right() - 200)  # 图例位置
        y = int(chart_rect.top())

        # CPU图例
        painter.setPen(QPen(QColor(76, 175, 80), 2))
        painter.drawLine(x, y + legend_height//2, x + legend_width, y + legend_height//2)
        painter.drawText(x + legend_width + text_margin, y + legend_height, "CPU使用率")

        # 内存图例
        y += legend_height + 5
        painter.setPen(QPen(QColor(33, 150, 243), 2))
        painter.drawLine(x, y + legend_height//2, x + legend_width, y + legend_height//2)
        painter.drawText(x + legend_width + text_margin, y + legend_height, "内存使用率")

        # 磁盘图例
        y += legend_height + 5
        painter.setPen(QPen(QColor(255, 152, 0), 2))
        painter.drawLine(x, y + legend_height//2, x + legend_width, y + legend_height//2)
        painter.drawText(x + legend_width + text_margin, y + legend_height, "磁盘使用率")

        # 绘制时间轴标签
        painter.setPen(QPen(QColor(0, 0, 0)))
        time_text = "时间轴 (最近5分钟)"
        painter.drawText(
            int(chart_rect.left() + (chart_rect.width() - painter.fontMetrics().width(time_text)) / 2),
            int(chart_rect.bottom() + 25),
            time_text
        )

    def add_data(self, cpu, mem, disk):
        """添加新的数据点"""
        now = time.time()
        self.data.append((now, cpu, mem, disk))
        if len(self.data) > 300:  # 保持最近5分钟的数据
            self.data = self.data[-300:]
        self.update()

class ResourceMonitorDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("系统资源监控")
        self.resize(800, 600)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 创建选项卡
        tab_widget = QTabWidget()

        # 实时监控选项卡
        realtime_tab = QWidget()
        realtime_layout = QVBoxLayout()

        # CPU使用率
        cpu_layout = QHBoxLayout()
        cpu_label = QLabel("CPU使用率:")
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
            }
        """)
        cpu_layout.addWidget(cpu_label)
        cpu_layout.addWidget(self.cpu_bar)

        # 内存使用率
        memory_layout = QHBoxLayout()
        memory_label = QLabel("内存使用率:")
        self.memory_bar = QProgressBar()
        self.memory_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #2196F3;
            }
        """)
        memory_layout.addWidget(memory_label)
        memory_layout.addWidget(self.memory_bar)

        # 磁盘使用率
        disk_layout = QHBoxLayout()
        disk_label = QLabel("磁盘使用率:")
        self.disk_bar = QProgressBar()
        self.disk_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #FF9800;
            }
        """)
        disk_layout.addWidget(disk_label)
        disk_layout.addWidget(self.disk_bar)

        realtime_layout.addLayout(cpu_layout)
        realtime_layout.addLayout(memory_layout)
        realtime_layout.addLayout(disk_layout)
        realtime_tab.setLayout(realtime_layout)

        # 趋势图选项卡
        trend_tab = QWidget()
        trend_layout = QVBoxLayout()

        # 使用自定义趋势图控件
        self.trend_chart = TrendChart()
        trend_layout.addWidget(self.trend_chart)

        trend_tab.setLayout(trend_layout)

        # 添加选项卡
        tab_widget.addTab(realtime_tab, "实时监控")
        tab_widget.addTab(trend_tab, "趋势图")

        layout.addWidget(tab_widget)
        self.setLayout(layout)

        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.start(1000)  # 每秒更新一次

    def update_data(self):
        """更新资源使用数据"""
        try:
            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.cpu_bar.setValue(int(cpu_percent))

            # 获取内存使用率
            memory = psutil.virtual_memory()
            self.memory_bar.setValue(int(memory.percent))

            # 获取磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            self.disk_bar.setValue(int(disk_percent))

            # 更新趋势图
            self.trend_chart.add_data(cpu_percent, memory.percent, disk_percent)

        except Exception as e:
            print(f"更新资源数据失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件处理"""
        self.update_timer.stop()
        event.accept()

class ResourceMonitorPlugin(PluginBase):
    """资源监控插件，实时监控CPU、内存等系统资源使用情况"""

    @property
    def name(self):
        return "资源监控器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "监控系统资源使用情况"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_monitor)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addSeparator()
                main_window.tools_menu.addAction(self.menu_action)

            self.monitor_dialog = None

        except Exception as e:
            print(f"初始化资源监控插件失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
                if self.monitor_dialog:
                    self.monitor_dialog.close()
            except Exception as e:
                print(f"清理资源监控插件失败: {str(e)}")

    def show_monitor(self):
        """显示资源监控对话框"""
        if not self.monitor_dialog:
            self.monitor_dialog = ResourceMonitorDialog(self.main_window)
            # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
            self.monitor_dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
        self.monitor_dialog.show()