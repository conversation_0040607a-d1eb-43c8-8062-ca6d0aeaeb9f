"""
AI聊天主窗口

提供现代化的聊天界面
"""

import os
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTextEdit, QPushButton, QListWidget, QListWidgetItem, QLabel,
    QFrame, QScrollArea, QMessageBox, QFileDialog, QProgressBar,
    QToolButton, QMenu, QAction, QStatusBar, QTabWidget, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap, QPainter, QKeyEvent


class ChatInputEdit(QTextEdit):
    """自定义输入框，支持回车发送和Shift+回车换行"""

    # 信号
    send_message = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMaximumHeight(100)
        self.setPlaceholderText("输入消息... (回车发送，Shift+回车换行)")

    def keyPressEvent(self, event: QKeyEvent):
        """处理键盘事件"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # 检查是否按住了Shift键
            if event.modifiers() & Qt.ShiftModifier:
                # Shift+回车：插入换行
                super().keyPressEvent(event)
            else:
                # 单独回车：发送消息
                if self.toPlainText().strip():
                    self.send_message.emit()
                # 阻止默认的回车行为
                return
        else:
            # 其他按键正常处理
            super().keyPressEvent(event)


class MessageBubble(QFrame):
    """消息气泡组件"""
    
    def __init__(self, message, is_user=True):
        super().__init__()
        self.message = message
        self.is_user = is_user
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.NoFrame)
        
        # 主布局 - 增加间距减少拥挤感
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(20, 8, 20, 8)
        
        # 消息内容
        self.content_label = QLabel()
        self.content_label.setWordWrap(True)
        self.content_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        # 确保正确显示中文
        self.content_label.setText(str(self.message.content))
        
        # 设置现代化样式
        if self.is_user:
            # 用户消息 - 右对齐，现代蓝色背景
            main_layout.addStretch()
            main_layout.addWidget(self.content_label)
            self.content_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1DA1F2, stop:1 #1991DB);
                    color: white;
                    padding: 12px 16px;
                    border-radius: 20px;
                    max-width: 400px;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 1.4;
                    border: 2px solid #1DA1F2;
                }
            """)
        else:
            # AI消息 - 左对齐，现代灰色背景
            main_layout.addWidget(self.content_label)
            main_layout.addStretch()
            self.content_label.setStyleSheet("""
                QLabel {
                    background-color: #F8F9FA;
                    color: #2C3E50;
                    padding: 12px 16px;
                    border-radius: 20px;
                    max-width: 400px;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 1.4;
                    border: 2px solid #E1E8ED;
                }
            """)
    
    def update_content(self, content):
        """更新消息内容"""
        self.message.content = content
        # 确保正确显示中文
        self.content_label.setText(str(content))


class ChatListWidget(QListWidget):
    """聊天列表组件"""
    
    chat_selected = pyqtSignal(str)  # session_id
    chat_deleted = pyqtSignal(str)   # session_id
    clear_all_chats_requested = pyqtSignal()  # 清空所有聊天
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setMaximumWidth(250)
        self.setMinimumWidth(200)

        # 启用右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)

        # 连接信号
        self.itemClicked.connect(self._on_item_clicked)
    
    def add_chat_session(self, session):
        """添加聊天会话"""
        item = QListWidgetItem()
        item.setText(session.title)
        item.setData(Qt.UserRole, session.session_id)
        self.addItem(item)
    
    def update_chat_session(self, session):
        """更新聊天会话"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.UserRole) == session.session_id:
                item.setText(session.title)
                break
    
    def remove_chat_session(self, session_id):
        """移除聊天会话"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.UserRole) == session_id:
                self.takeItem(i)
                break

    def has_session(self, session_id):
        """检查是否已存在指定会话"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.UserRole) == session_id:
                return True
        return False

    def _on_item_clicked(self, item):
        """处理项目点击"""
        session_id = item.data(Qt.UserRole)
        self.chat_selected.emit(session_id)

    def _show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        if not item:
            return

        session_id = item.data(Qt.UserRole)
        if not session_id:
            return

        # 创建右键菜单
        menu = QMenu(self)

        # 删除聊天记录
        delete_action = QAction("删除聊天记录", self)
        delete_action.triggered.connect(lambda: self._delete_chat_session(session_id))
        menu.addAction(delete_action)

        # 重命名聊天记录
        rename_action = QAction("重命名", self)
        rename_action.triggered.connect(lambda: self._rename_chat_session(session_id, item))
        menu.addAction(rename_action)

        menu.addSeparator()

        # 清空所有聊天记录
        clear_all_action = QAction("清空所有聊天记录", self)
        clear_all_action.triggered.connect(self._clear_all_chats)
        menu.addAction(clear_all_action)

        # 显示菜单
        menu.exec_(self.mapToGlobal(position))

    def _delete_chat_session(self, session_id):
        """删除聊天会话"""
        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除这个聊天记录吗？删除后无法恢复。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.chat_deleted.emit(session_id)

    def _rename_chat_session(self, session_id, item):
        """重命名聊天会话"""
        from PyQt5.QtWidgets import QInputDialog

        current_title = item.text()
        new_title, ok = QInputDialog.getText(
            self, "重命名聊天记录",
            "请输入新的标题:", text=current_title
        )

        if ok and new_title.strip():
            # 发送重命名信号（需要在控制器中处理）
            # 这里暂时直接更新显示
            item.setText(new_title.strip())

    def _clear_all_chats(self):
        """清空所有聊天记录"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有聊天记录吗？此操作无法恢复。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 发送清空所有聊天的信号
            self.clear_all_chats_requested.emit()


class DocumentItemWidget(QWidget):
    """文档项组件"""

    selection_changed = pyqtSignal(str, bool)  # doc_id, selected
    remove_requested = pyqtSignal(str)  # doc_id

    def __init__(self, doc_info):
        super().__init__()
        self.doc_info = doc_info
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 2, 4, 2)
        layout.setSpacing(8)

        # 选择复选框
        self.checkbox = QCheckBox()
        self.checkbox.setChecked(getattr(self.doc_info, 'selected', True))
        self.checkbox.stateChanged.connect(self._on_selection_changed)
        layout.addWidget(self.checkbox)

        # 文档信息
        self.info_label = QLabel()
        self.update_display()
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label, 1)

        # 删除按钮
        self.delete_btn = QPushButton("❌")
        self.delete_btn.setMaximumSize(24, 24)
        self.delete_btn.setToolTip("删除文档")
        self.delete_btn.clicked.connect(self._on_remove_clicked)
        layout.addWidget(self.delete_btn)

        # 设置样式
        self.setStyleSheet("""
            DocumentItemWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                margin: 1px;
            }
            DocumentItemWidget:hover {
                background-color: #F3F4F6;
                border-color: #D1D5DB;
            }
            QCheckBox {
                spacing: 4px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid #D1D5DB;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #10B981;
                border-color: #10B981;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QPushButton {
                background-color: transparent;
                border: none;
                font-size: 12px;
                border-radius: 12px;
            }
            QPushButton:hover {
                background-color: #FEE2E2;
            }
            QLabel {
                color: #374151;
                font-size: 11px;
                border: none;
                background: transparent;
                padding: 2px;
            }
        """)

    def update_display(self):
        """更新显示"""
        if self.doc_info.processed:
            status = "✅"
            tooltip = f"文档已处理完成\n路径: {self.doc_info.file_path}"
        elif self.doc_info.error_message:
            status = "❌"
            tooltip = f"处理失败: {self.doc_info.error_message}\n路径: {self.doc_info.file_path}"
        else:
            status = "⏳"
            tooltip = f"正在处理中...\n路径: {self.doc_info.file_path}"

        text = f"{status} {self.doc_info.name}\n({self.doc_info.get_size_str()})"
        self.info_label.setText(text)
        self.setToolTip(tooltip)

    def _on_selection_changed(self, state):
        """选择状态改变"""
        selected = state == Qt.Checked
        self.doc_info.selected = selected
        self.selection_changed.emit(self.doc_info.doc_id, selected)

    def _on_remove_clicked(self):
        """删除按钮点击"""
        self.remove_requested.emit(self.doc_info.doc_id)

    def set_selected(self, selected):
        """设置选中状态"""
        self.checkbox.setChecked(selected)


class DocumentPanel(QWidget):
    """文档面板"""

    document_uploaded = pyqtSignal(str)  # file_path
    document_removed = pyqtSignal(str)   # doc_id
    document_selection_changed = pyqtSignal()  # 文档选择状态改变

    def __init__(self):
        super().__init__()
        self.documents = {}
        self.document_widgets = {}  # doc_id -> DocumentItemWidget
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 标题和状态
        header_layout = QHBoxLayout()
        title_label = QLabel("文档")
        title_label.setFont(QFont("", 10, QFont.Bold))
        header_layout.addWidget(title_label)

        self.status_label = QLabel("(0个)")
        self.status_label.setStyleSheet("color: #666; font-size: 10px;")
        header_layout.addWidget(self.status_label)
        header_layout.addStretch()
        layout.addLayout(header_layout)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 上传按钮
        upload_btn = QPushButton("上传文档")
        upload_btn.clicked.connect(self._upload_document)
        button_layout.addWidget(upload_btn)

        # 全选/取消全选按钮
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self._toggle_select_all)
        self.select_all_btn.setMaximumWidth(60)
        button_layout.addWidget(self.select_all_btn)

        layout.addLayout(button_layout)

        # 文档列表（使用滚动区域）
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setMaximumHeight(200)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.doc_container = QWidget()
        self.doc_layout = QVBoxLayout(self.doc_container)
        self.doc_layout.setContentsMargins(0, 0, 0, 0)
        self.doc_layout.setSpacing(2)
        self.doc_layout.addStretch()

        self.scroll_area.setWidget(self.doc_container)
        layout.addWidget(self.scroll_area)
        
        # 设置现代化样式
        self.setStyleSheet("""
            QWidget {
                background-color: #FFFFFF;
                border: 1px solid #E1E8ED;
                border-radius: 12px;
                padding: 8px;
            }
            QLabel {
                color: #2C3E50;
                font-weight: 600;
                font-size: 14px;
                margin-bottom: 8px;
            }
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
            QListWidget {
                border: 1px solid #E1E8ED;
                border-radius: 8px;
                background-color: #F9FAFB;
                outline: none;
            }
            QListWidget::item {
                padding: 8px 12px;
                border: none;
                border-radius: 6px;
                margin: 2px;
                background-color: transparent;
                color: #374151;
                font-size: 12px;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #DBEAFE;
                color: #1E40AF;
            }
        """)
    
    def _upload_document(self):
        """上传文档"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择文档",
            "",
            "支持的文档 (*.pdf *.docx *.doc *.txt *.md);;所有文件 (*)"
        )
        
        if file_path:
            self.document_uploaded.emit(file_path)
    
    def add_document(self, doc_info):
        """添加文档"""
        self.documents[doc_info.doc_id] = doc_info

        # 创建文档项组件
        doc_widget = DocumentItemWidget(doc_info)
        doc_widget.selection_changed.connect(self._on_document_selection_changed)
        doc_widget.remove_requested.connect(self._on_document_remove_requested)

        # 添加到布局（在stretch之前）
        self.doc_layout.insertWidget(self.doc_layout.count() - 1, doc_widget)
        self.document_widgets[doc_info.doc_id] = doc_widget

        # 更新状态
        self._update_status()

    def update_document_status(self, doc_info):
        """更新文档状态"""
        # 更新文档信息
        if doc_info.doc_id in self.documents:
            self.documents[doc_info.doc_id] = doc_info

        # 更新对应的组件显示
        if doc_info.doc_id in self.document_widgets:
            widget = self.document_widgets[doc_info.doc_id]
            widget.doc_info = doc_info
            widget.update_display()

    def show_upload_progress(self, doc_id, status):
        """显示上传进度"""
        if doc_id in self.document_widgets:
            widget = self.document_widgets[doc_id]
            # 临时更新显示状态
            widget.info_label.setText(f"⏳ {widget.doc_info.name}\n{status}")

    def hide_upload_progress(self, doc_id):
        """隐藏上传进度"""
        doc_info = self.documents.get(doc_id)
        if doc_info:
            self.update_document_status(doc_info)

    def remove_document(self, doc_id):
        """移除文档"""
        # 移除组件
        if doc_id in self.document_widgets:
            widget = self.document_widgets[doc_id]
            self.doc_layout.removeWidget(widget)
            widget.deleteLater()
            del self.document_widgets[doc_id]

        # 移除数据
        if doc_id in self.documents:
            del self.documents[doc_id]

        # 更新状态
        self._update_status()

    def _toggle_select_all(self):
        """切换全选状态"""
        # 检查当前是否全部选中
        all_selected = all(getattr(doc, 'selected', True) for doc in self.documents.values())

        # 设置新状态
        new_state = not all_selected

        for doc_id, widget in self.document_widgets.items():
            widget.set_selected(new_state)
            self.documents[doc_id].selected = new_state

        # 更新按钮文本
        self.select_all_btn.setText("取消全选" if new_state else "全选")

        # 发送选择改变信号
        self.document_selection_changed.emit()

    def _on_document_selection_changed(self, doc_id, selected):
        """文档选择状态改变"""
        if doc_id in self.documents:
            self.documents[doc_id].selected = selected

        # 更新全选按钮状态
        all_selected = all(getattr(doc, 'selected', True) for doc in self.documents.values())
        self.select_all_btn.setText("取消全选" if all_selected else "全选")

        # 更新状态显示
        self._update_status()

        # 发送选择改变信号
        self.document_selection_changed.emit()

    def _on_document_remove_requested(self, doc_id):
        """文档删除请求"""
        # 确认删除
        doc_info = self.documents.get(doc_id)
        if doc_info:
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除文档 '{doc_info.name}' 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.document_removed.emit(doc_id)

    def _update_status(self):
        """更新状态显示"""
        total_count = len(self.documents)
        selected_count = sum(1 for doc in self.documents.values() if getattr(doc, 'selected', True))

        if total_count == 0:
            self.status_label.setText("(0个)")
        else:
            self.status_label.setText(f"({selected_count}/{total_count}个选中)")

    def get_selected_documents(self):
        """获取选中的文档"""
        return [doc for doc in self.documents.values() if getattr(doc, 'selected', True)]


class ChatWindow(QMainWindow):
    """AI聊天主窗口"""
    
    # 信号定义
    message_sent = pyqtSignal(str)
    new_chat_requested = pyqtSignal()
    chat_selected = pyqtSignal(str)
    chat_deleted = pyqtSignal(str)
    clear_all_chats_requested = pyqtSignal()
    settings_requested = pyqtSignal()
    document_uploaded = pyqtSignal(str)
    document_removed = pyqtSignal(str)
    translation_requested = pyqtSignal(str, str)
    stop_generation_requested = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.message_bubbles = {}  # message_id -> MessageBubble
        self.is_generating = False
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("AI聊天助手")
        self.setGeometry(100, 100, 1000, 700)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        self._create_left_panel(splitter)
        
        # 右侧聊天区域
        self._create_chat_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([250, 750])
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 创建工具栏
        self._create_toolbar()
    
    def _create_left_panel(self, parent):
        """创建左侧面板"""
        left_panel = QWidget()
        left_panel.setObjectName("left_panel")
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(12, 12, 12, 12)
        left_layout.setSpacing(12)  # 增加组件间距
        
        # 新建聊天按钮
        new_chat_btn = QPushButton("新建聊天")
        new_chat_btn.clicked.connect(self.new_chat_requested.emit)
        left_layout.addWidget(new_chat_btn)
        
        # 聊天列表
        self.chat_list = ChatListWidget()
        self.chat_list.chat_selected.connect(self.chat_selected.emit)
        self.chat_list.chat_deleted.connect(self.chat_deleted.emit)
        self.chat_list.clear_all_chats_requested.connect(self.clear_all_chats_requested.emit)
        left_layout.addWidget(self.chat_list)
        
        # 文档面板
        self.document_panel = DocumentPanel()
        self.document_panel.document_uploaded.connect(self.document_uploaded.emit)
        self.document_panel.document_removed.connect(self.document_removed.emit)
        left_layout.addWidget(self.document_panel)
        
        parent.addWidget(left_panel)
    
    def _create_chat_area(self, parent):
        """创建聊天区域"""
        chat_widget = QWidget()
        chat_widget.setObjectName("chat_area")
        chat_layout = QVBoxLayout(chat_widget)
        chat_layout.setContentsMargins(20, 16, 20, 16)
        chat_layout.setSpacing(16)  # 增加组件间距

        # 消息显示区域
        self.messages_scroll = QScrollArea()
        self.messages_scroll.setObjectName("messages_scroll")
        self.messages_scroll.setWidgetResizable(True)
        self.messages_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.messages_widget = QWidget()
        self.messages_layout = QVBoxLayout(self.messages_widget)
        self.messages_layout.setSpacing(12)  # 消息间距
        self.messages_layout.setContentsMargins(8, 8, 8, 8)
        self.messages_layout.addStretch()

        self.messages_scroll.setWidget(self.messages_widget)
        chat_layout.addWidget(self.messages_scroll)

        # 输入区域
        self._create_input_area(chat_layout)

        parent.addWidget(chat_widget)

    def _create_input_area(self, parent_layout):
        """创建输入区域"""
        input_frame = QFrame()
        input_frame.setObjectName("input_frame")
        input_layout = QVBoxLayout(input_frame)
        input_layout.setContentsMargins(0, 5, 0, 0)

        # 主输入行 - 水平布局：输入框 + 按钮
        main_input_layout = QHBoxLayout()
        main_input_layout.setSpacing(8)

        # 输入框
        self.input_text = ChatInputEdit()
        self.input_text.send_message.connect(self._send_message)
        main_input_layout.addWidget(self.input_text)

        # 发送按钮
        self.send_btn = QPushButton("发送")
        self.send_btn.clicked.connect(self._send_message)
        self.send_btn.setMinimumWidth(80)
        self.send_btn.setMaximumWidth(80)
        main_input_layout.addWidget(self.send_btn)

        # 停止生成按钮
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_generation_requested.emit)
        self.stop_btn.setVisible(False)
        self.stop_btn.setMinimumWidth(80)
        self.stop_btn.setMaximumWidth(80)
        main_input_layout.addWidget(self.stop_btn)

        input_layout.addLayout(main_input_layout)
        parent_layout.addWidget(input_frame)

    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")

        # 设置按钮
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.settings_requested.emit)
        toolbar.addAction(settings_action)

        toolbar.addSeparator()

        # 翻译助手按钮
        translate_action = QAction("翻译助手", self)
        translate_action.triggered.connect(self._open_translation_dialog)
        toolbar.addAction(translate_action)

        # 清空聊天按钮
        clear_action = QAction("清空聊天", self)
        clear_action.triggered.connect(self._clear_chat)
        toolbar.addAction(clear_action)

    def apply_styles(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            /* 主窗口样式 */
            QMainWindow {
                background-color: #FAFAFA;
                color: #2C3E50;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }

            /* 分割器样式 */
            QSplitter::handle {
                background-color: #E1E8ED;
                width: 3px;
                height: 3px;
                border: 1px solid #D1D5DB;
            }
            QSplitter::handle:hover {
                background-color: #1DA1F2;
                border-color: #1991DB;
            }

            /* 文本编辑框样式 */
            QTextEdit {
                border: 2px solid #E1E8ED;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 14px;
                background-color: #FFFFFF;
                color: #2C3E50;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #1DA1F2;
                outline: none;
            }

            /* 列表控件样式 */
            QListWidget {
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                background-color: #FFFFFF;
                outline: none;
                padding: 4px;
            }
            QListWidget::item {
                border: 1px solid #F0F0F0;
                border-radius: 6px;
                padding: 8px 12px;
                margin: 2px;
                background-color: #FAFAFA;
            }
            QListWidget::item:selected {
                background-color: #E3F2FD;
                border-color: #1DA1F2;
                color: #1565C0;
                font-weight: 600;
            }
            QListWidget::item:hover {
                background-color: #F5F5F5;
                border-color: #CCCCCC;
            }

            /* 主要按钮样式 */
            QPushButton {
                background-color: #1DA1F2;
                color: white;
                border: 2px solid #1DA1F2;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #1991DB;
                border-color: #1991DB;
            }
            QPushButton:pressed {
                background-color: #1681C4;
                border-color: #1681C4;
            }
            QPushButton:disabled {
                background-color: #AAB8C2;
                border-color: #AAB8C2;
                color: #FFFFFF;
            }

            /* 次要按钮样式 */
            QPushButton[class="secondary"] {
                background-color: #F7F9FA;
                color: #1DA1F2;
                border: 2px solid #E1E8ED;
            }
            QPushButton[class="secondary"]:hover {
                background-color: #E1E8ED;
                border-color: #1DA1F2;
            }

            /* 滚动区域样式 */
            QScrollArea {
                border: 2px solid #E1E8ED;
                background-color: #FFFFFF;
                border-radius: 8px;
            }
            QScrollBar:vertical {
                background-color: #F7F9FA;
                width: 10px;
                border-radius: 5px;
                border: 1px solid #E1E8ED;
            }
            QScrollBar::handle:vertical {
                background-color: #AAB8C2;
                border-radius: 5px;
                min-height: 20px;
                border: 1px solid #95A5A6;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #657786;
                border-color: #34495E;
            }

            /* 框架和面板样式 */
            QFrame {
                border: 1px solid #E1E8ED;
                border-radius: 8px;
                background-color: #FFFFFF;
            }
            QWidget[objectName="left_panel"] {
                background-color: #FFFFFF;
                border: 2px solid #E1E8ED;
                border-radius: 12px;
                margin: 4px;
            }
            QWidget[objectName="chat_area"] {
                background-color: #FFFFFF;
                border: 2px solid #E1E8ED;
                border-radius: 12px;
                margin: 4px;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #FFFFFF;
                border-top: 2px solid #E1E8ED;
                color: #657786;
                font-size: 12px;
                padding: 4px 8px;
            }

            /* 工具栏样式 */
            QToolBar {
                background-color: #FFFFFF;
                border: 1px solid #E1E8ED;
                border-bottom: 2px solid #E1E8ED;
                spacing: 8px;
                padding: 8px;
            }
            QToolBar QToolButton {
                background-color: transparent;
                color: #1DA1F2;
                border: 1px solid transparent;
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: 500;
            }
            QToolBar QToolButton:hover {
                background-color: #F7F9FA;
                border-color: #E1E8ED;
            }
            QToolBar QToolButton:pressed {
                background-color: #E1E8ED;
                border-color: #1DA1F2;
            }

            /* 输入框架样式 */
            QFrame#input_frame {
                background-color: #FFFFFF;
                border: 2px solid #E1E8ED;
                border-radius: 12px;
                padding: 8px;
            }
        """)

    def _send_message(self):
        """发送消息"""
        text = self.input_text.toPlainText().strip()
        if text:
            self.message_sent.emit(text)
            self.input_text.clear()

    def _open_translation_dialog(self):
        """打开翻译对话框"""
        from .translation_dialog import TranslationDialog

        dialog = TranslationDialog(self)

        # 如果输入框有内容，预填充到翻译对话框
        text = self.input_text.toPlainText().strip()
        if text:
            dialog.input_text.setPlainText(text)

        # 连接信号
        dialog.translation_requested.connect(self.translation_requested.emit)
        dialog.document_translation_requested.connect(
            lambda file_path, target_lang: self.document_uploaded.emit(file_path)
        )

        # 显示对话框
        dialog.exec_()

    def _clear_chat(self):
        """清空当前聊天"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空当前聊天记录吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 安全地清空消息显示
            self._clear_messages_safely()

    def add_chat_session(self, session):
        """添加聊天会话"""
        self.chat_list.add_chat_session(session)

    def update_chat_session(self, session):
        """更新聊天会话"""
        self.chat_list.update_chat_session(session)

    def remove_chat_session(self, session_id):
        """移除聊天会话"""
        self.chat_list.remove_chat_session(session_id)

    def switch_to_session(self, session_id):
        """切换到指定会话"""
        # 选中对应的列表项
        for i in range(self.chat_list.count()):
            item = self.chat_list.item(i)
            if item.data(Qt.UserRole) == session_id:
                self.chat_list.setCurrentItem(item)
                break

    def load_chat_messages(self, messages):
        """加载聊天消息"""
        # 安全地清空当前消息
        self._clear_messages_safely()

        # 添加消息
        for message in messages:
            self.add_message(message)

        # 滚动到底部
        QTimer.singleShot(100, self._scroll_to_bottom)

    def _clear_messages_safely(self):
        """安全地清空消息"""
        try:
            # 清空消息气泡
            for bubble in self.message_bubbles.values():
                bubble.deleteLater()
            self.message_bubbles.clear()

            # 清空布局中的所有项目，但不删除布局本身
            while self.messages_layout.count():
                child = self.messages_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

            # 重新添加stretch
            self.messages_layout.addStretch()

        except Exception as e:
            print(f"清空消息时发生错误: {e}")
            # 如果出现问题，尝试重新创建布局
            self._recreate_messages_layout()

    def _recreate_messages_layout(self):
        """重新创建消息布局（作为备用方案）"""
        try:
            # 获取当前布局
            old_layout = self.messages_widget.layout()
            if old_layout:
                # 清空旧布局
                while old_layout.count():
                    child = old_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
                # 删除旧布局
                old_layout.deleteLater()

            # 创建新布局
            self.messages_layout = QVBoxLayout(self.messages_widget)
            self.messages_layout.setSpacing(12)
            self.messages_layout.setContentsMargins(8, 8, 8, 8)
            self.messages_layout.addStretch()

        except Exception as e:
            print(f"重新创建消息布局时发生错误: {e}")

    def add_message(self, message):
        """添加消息"""
        is_user = message.role == 'user'
        bubble = MessageBubble(message, is_user)

        # 插入到stretch之前
        self.messages_layout.insertWidget(self.messages_layout.count() - 1, bubble)
        self.message_bubbles[message.id] = bubble

        # 滚动到底部
        QTimer.singleShot(100, self._scroll_to_bottom)

    def update_message_content(self, message_id, content):
        """更新消息内容"""
        if message_id in self.message_bubbles:
            # 确保content是正确的字符串格式
            if isinstance(content, bytes):
                content = content.decode('utf-8', errors='ignore')
            elif not isinstance(content, str):
                content = str(content)

            self.message_bubbles[message_id].update_content(content)
            # 滚动到底部
            QTimer.singleShot(50, self._scroll_to_bottom)

    def _scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.messages_scroll.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def set_generating(self, generating):
        """设置生成状态"""
        self.is_generating = generating
        self.stop_btn.setVisible(generating)

        if generating:
            self.status_bar.showMessage("AI正在生成回复...")
        else:
            self.status_bar.clearMessage()

    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.warning(self, "错误", message)
        self.status_bar.showMessage(f"错误: {message}", 5000)

    def show_translation_status(self, status):
        """显示翻译状态"""
        self.status_bar.showMessage(status)

    def show_translation_result(self, original, translated, target_lang):
        """显示翻译结果"""
        # 在输入框中显示翻译结果
        if target_lang == 'zh':
            result_text = f"原文: {original}\n译文: {translated}"
        else:
            result_text = f"中文: {original}\nEnglish: {translated}"

        # 可以选择替换输入框内容或者显示在对话框中
        reply = QMessageBox.question(
            self,
            "翻译结果",
            f"{result_text}\n\n是否将译文替换到输入框？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            self.input_text.setPlainText(translated)

        self.status_bar.clearMessage()

    def add_document(self, doc_info):
        """添加文档"""
        self.document_panel.add_document(doc_info)

    def update_document_status(self, doc_info):
        """更新文档状态"""
        self.document_panel.update_document_status(doc_info)

    def remove_document(self, doc_id):
        """移除文档"""
        self.document_panel.remove_document(doc_id)

    def show_document_error(self, doc_id, error):
        """显示文档错误"""
        self.show_error(f"文档处理失败: {error}")

    def show_document_upload_progress(self, doc_id, status):
        """显示文档上传进度"""
        self.document_panel.show_upload_progress(doc_id, status)
        self.status_bar.showMessage(f"文档处理: {status}")

    def update_document_upload_progress(self, doc_id, status):
        """更新文档上传进度"""
        self.document_panel.show_upload_progress(doc_id, status)
        self.status_bar.showMessage(f"文档处理: {status}")

    def hide_document_upload_progress(self, doc_id):
        """隐藏文档上传进度"""
        self.document_panel.hide_upload_progress(doc_id)
        self.status_bar.showMessage("文档处理完成", 3000)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 可以在这里保存窗口状态
        event.accept()
