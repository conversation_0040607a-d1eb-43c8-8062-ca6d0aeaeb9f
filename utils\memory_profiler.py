"""
内存分析工具 - 用于详细分析内存使用情况
"""
import gc
import time
import psutil
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest


class MemoryProfiler:
    """内存分析器，提供详细的内存监控功能"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.snapshots = []
        self.gc_stats = []
    
    def take_snapshot(self, label=""):
        """拍摄内存快照"""
        # 获取内存信息
        memory_info = self.process.memory_info()
        
        # 获取垃圾回收统计
        gc_counts = gc.get_count()
        
        snapshot = {
            'label': label,
            'timestamp': time.time(),
            'rss': memory_info.rss,  # 物理内存
            'vms': memory_info.vms,  # 虚拟内存
            'gc_counts': gc_counts,
            'gc_objects': len(gc.get_objects())
        }
        
        self.snapshots.append(snapshot)
        return snapshot
    
    def force_cleanup(self, iterations=3, wait_time=100):
        """强制清理内存"""
        print(f"执行 {iterations} 轮强制清理...")
        
        for i in range(iterations):
            print(f"  第 {i+1} 轮清理:")
            
            # Python垃圾回收
            collected = gc.collect()
            print(f"    Python GC回收对象: {collected}")
            
            # Qt事件处理
            if QApplication.instance():
                QApplication.processEvents()
                print(f"    Qt事件处理完成")
            
            # 等待
            if wait_time > 0:
                QTest.qWait(wait_time)
                print(f"    等待 {wait_time}ms")
            
            # 拍摄快照
            snapshot = self.take_snapshot(f"cleanup_round_{i+1}")
            print(f"    内存: {snapshot['rss'] / 1024 / 1024:.2f}MB")
    
    def analyze_memory_usage(self):
        """分析内存使用情况"""
        if len(self.snapshots) < 2:
            print("快照数量不足，无法分析")
            return
        
        print("\n=== 内存使用分析 ===")
        
        for i, snapshot in enumerate(self.snapshots):
            label = snapshot['label'] or f"快照_{i}"
            rss_mb = snapshot['rss'] / 1024 / 1024
            vms_mb = snapshot['vms'] / 1024 / 1024
            gc_objects = snapshot['gc_objects']
            
            print(f"{label}:")
            print(f"  物理内存: {rss_mb:.2f}MB")
            print(f"  虚拟内存: {vms_mb:.2f}MB")
            print(f"  GC对象数: {gc_objects}")
            
            if i > 0:
                prev = self.snapshots[i-1]
                rss_diff = (snapshot['rss'] - prev['rss']) / 1024 / 1024
                vms_diff = (snapshot['vms'] - prev['vms']) / 1024 / 1024
                obj_diff = snapshot['gc_objects'] - prev['gc_objects']
                
                print(f"  变化: RSS {rss_diff:+.2f}MB, VMS {vms_diff:+.2f}MB, 对象 {obj_diff:+d}")
            print()
    
    def get_memory_change(self, start_label, end_label):
        """获取两个快照之间的内存变化"""
        start_snapshot = None
        end_snapshot = None
        
        for snapshot in self.snapshots:
            if snapshot['label'] == start_label:
                start_snapshot = snapshot
            elif snapshot['label'] == end_label:
                end_snapshot = snapshot
        
        if not start_snapshot or not end_snapshot:
            return None
        
        return {
            'rss_change': end_snapshot['rss'] - start_snapshot['rss'],
            'vms_change': end_snapshot['vms'] - start_snapshot['vms'],
            'objects_change': end_snapshot['gc_objects'] - start_snapshot['gc_objects'],
            'time_elapsed': end_snapshot['timestamp'] - start_snapshot['timestamp']
        }
    
    def clear_snapshots(self):
        """清空快照"""
        self.snapshots.clear()
        self.gc_stats.clear()


def test_memory_with_profiler():
    """使用内存分析器测试LogPanel内存使用"""
    print("=== 使用内存分析器测试LogPanel ===\n")
    
    try:
        from views.refactored_log_panel import RefactoredLogPanel
        
        # 创建内存分析器
        profiler = MemoryProfiler()
        
        # 创建QApplication（如果不存在）
        if not QApplication.instance():
            app = QApplication([])
        
        # 基线快照
        profiler.force_cleanup()
        profiler.take_snapshot("baseline")
        
        # 创建LogPanel实例
        print("创建LogPanel实例...")
        panels = []
        for i in range(3):
            panel = RefactoredLogPanel(f"memory_test_{i}", f"echo test_{i}")
            panels.append(panel)
        
        profiler.take_snapshot("after_creation")
        
        # 清理实例
        print("清理LogPanel实例...")
        for panel in panels:
            try:
                panel.cleanup()
                panel.deleteLater()
            except Exception as e:
                print(f"清理面板时出错: {e}")
        
        # 清空引用
        panels.clear()
        
        profiler.take_snapshot("after_cleanup")
        
        # 强制清理
        profiler.force_cleanup()
        profiler.take_snapshot("after_force_cleanup")
        
        # 分析结果
        profiler.analyze_memory_usage()
        
        # 计算内存变化
        creation_change = profiler.get_memory_change("baseline", "after_creation")
        cleanup_change = profiler.get_memory_change("after_creation", "after_force_cleanup")
        
        if creation_change and cleanup_change:
            print("=== 内存变化总结 ===")
            print(f"创建阶段内存增加: {creation_change['rss_change'] / 1024 / 1024:.2f}MB")
            print(f"清理阶段内存变化: {cleanup_change['rss_change'] / 1024 / 1024:.2f}MB")
            
            if creation_change['rss_change'] > 0:
                leak_ratio = max(0, -cleanup_change['rss_change'] / creation_change['rss_change'])
                print(f"内存释放比例: {leak_ratio:.1%}")
                
                if leak_ratio > 0.3:
                    print("✅ 内存管理良好")
                    return True
                else:
                    print("⚠️ 内存释放不充分")
                    return False
            else:
                print("✅ 内存使用稳定")
                return True
        
        return False
        
    except Exception as e:
        print(f"内存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    test_memory_with_profiler()
