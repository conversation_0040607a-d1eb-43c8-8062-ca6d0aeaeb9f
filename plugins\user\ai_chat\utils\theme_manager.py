"""
主题管理器

支持多种UI主题的切换，包括：
- 浅色主题
- 深色主题
- 高对比度主题
- 自定义主题
"""

import os
import json
import time
from typing import Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QApplication


class ThemeManager(QObject):
    """主题管理器"""
    
    # 信号
    theme_changed = pyqtSignal(str)  # theme_name
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes_file = "ai_chat_themes.json"
        self.custom_themes_dir = "themes"
        
        # 创建主题目录
        os.makedirs(self.custom_themes_dir, exist_ok=True)
        
        # 内置主题
        self.builtin_themes = {
            "light": self._get_light_theme(),
            "dark": self._get_dark_theme(),
            "high_contrast": self._get_high_contrast_theme(),
            "blue": self._get_blue_theme(),
            "green": self._get_green_theme()
        }
        
        # 加载自定义主题
        self.custom_themes = self._load_custom_themes()
        
        # 加载当前主题设置
        self._load_theme_settings()
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        themes = {}
        
        # 内置主题
        themes.update({
            "light": "浅色主题",
            "dark": "深色主题", 
            "high_contrast": "高对比度",
            "blue": "蓝色主题",
            "green": "绿色主题"
        })
        
        # 自定义主题
        for name in self.custom_themes:
            themes[name] = self.custom_themes[name].get('display_name', name)
        
        return themes
    
    def set_theme(self, theme_name: str) -> bool:
        """设置主题"""
        try:
            theme_data = self._get_theme_data(theme_name)
            if not theme_data:
                return False
            
            # 应用主题
            self._apply_theme(theme_data)
            
            # 保存当前主题
            self.current_theme = theme_name
            self._save_theme_settings()
            
            # 发送信号
            self.theme_changed.emit(theme_name)
            
            return True
            
        except Exception as e:
            print(f"设置主题失败: {e}")
            return False
    
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme
    
    def _get_theme_data(self, theme_name: str) -> Optional[Dict[str, Any]]:
        """获取主题数据"""
        if theme_name in self.builtin_themes:
            return self.builtin_themes[theme_name]
        elif theme_name in self.custom_themes:
            return self.custom_themes[theme_name]
        return None
    
    def _apply_theme(self, theme_data: Dict[str, Any]):
        """应用主题"""
        app = QApplication.instance()
        if app:
            stylesheet = theme_data.get('stylesheet', '')
            app.setStyleSheet(stylesheet)
    
    def _get_light_theme(self) -> Dict[str, Any]:
        """浅色主题"""
        return {
            'name': 'light',
            'display_name': '浅色主题',
            'stylesheet': """
                QWidget {
                    background-color: #FFFFFF;
                    color: #2C3E50;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                }
                
                QMainWindow {
                    background-color: #FAFAFA;
                }
                
                QPushButton {
                    background-color: #1DA1F2;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: 13px;
                    min-width: 80px;
                }
                
                QPushButton:hover {
                    background-color: #1991DB;
                }
                
                QPushButton:pressed {
                    background-color: #1681C7;
                }
                
                QPushButton:disabled {
                    background-color: #BDC3C7;
                    color: #7F8C8D;
                }
                
                QTextEdit {
                    border: 2px solid #E1E8ED;
                    border-radius: 8px;
                    padding: 12px;
                    background-color: #FAFBFC;
                    selection-background-color: #1DA1F2;
                    selection-color: white;
                    font-size: 14px;
                    line-height: 1.5;
                }
                
                QTextEdit:focus {
                    border-color: #1DA1F2;
                    background-color: #FFFFFF;
                }
                
                QListWidget {
                    border: 1px solid #E1E8ED;
                    border-radius: 8px;
                    background-color: #FFFFFF;
                    alternate-background-color: #F8F9FA;
                }
                
                QListWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #F1F3F4;
                }
                
                QListWidget::item:selected {
                    background-color: #E3F2FD;
                    color: #1976D2;
                }
                
                QListWidget::item:hover {
                    background-color: #F5F5F5;
                }
                
                QScrollBar:vertical {
                    border: none;
                    background-color: #F1F3F4;
                    width: 12px;
                    border-radius: 6px;
                }
                
                QScrollBar::handle:vertical {
                    background-color: #BDC3C7;
                    border-radius: 6px;
                    min-height: 20px;
                }
                
                QScrollBar::handle:vertical:hover {
                    background-color: #95A5A6;
                }

                QListWidget {
                    border: none;
                    background-color: #FFFFFF;
                    outline: none;
                    border-radius: 8px;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                }

                QListWidget::item {
                    padding: 12px 16px;
                    border: none;
                    border-radius: 8px;
                    margin: 2px 4px;
                    background-color: transparent;
                    color: #2C3E50;
                    font-size: 14px;
                    font-weight: 500;
                }

                QListWidget::item:selected {
                    background-color: #1DA1F2;
                    color: white;
                }

                QListWidget::item:hover {
                    background-color: #F7F9FA;
                }

                QListWidget::item:selected:hover {
                    background-color: #1991DB;
                }
            """
        }
    
    def _get_dark_theme(self) -> Dict[str, Any]:
        """深色主题"""
        return {
            'name': 'dark',
            'display_name': '深色主题',
            'stylesheet': """
                QWidget {
                    background-color: #2B2B2B;
                    color: #FFFFFF;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                }
                
                QMainWindow {
                    background-color: #1E1E1E;
                }
                
                QPushButton {
                    background-color: #0D7377;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: 13px;
                    min-width: 80px;
                }
                
                QPushButton:hover {
                    background-color: #14A085;
                }
                
                QPushButton:pressed {
                    background-color: #0A5D61;
                }
                
                QPushButton:disabled {
                    background-color: #404040;
                    color: #808080;
                }
                
                QTextEdit {
                    border: 2px solid #404040;
                    border-radius: 8px;
                    padding: 12px;
                    background-color: #3C3C3C;
                    selection-background-color: #0D7377;
                    selection-color: white;
                    color: #FFFFFF;
                    font-size: 14px;
                    line-height: 1.5;
                }
                
                QTextEdit:focus {
                    border-color: #0D7377;
                    background-color: #404040;
                }
                
                QListWidget {
                    border: 1px solid #404040;
                    border-radius: 8px;
                    background-color: #2B2B2B;
                    alternate-background-color: #353535;
                }
                
                QListWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #404040;
                    color: #FFFFFF;
                }
                
                QListWidget::item:selected {
                    background-color: #0D7377;
                    color: #FFFFFF;
                }
                
                QListWidget::item:hover {
                    background-color: #404040;
                }
                
                QScrollBar:vertical {
                    border: none;
                    background-color: #404040;
                    width: 12px;
                    border-radius: 6px;
                }
                
                QScrollBar::handle:vertical {
                    background-color: #606060;
                    border-radius: 6px;
                    min-height: 20px;
                }
                
                QScrollBar::handle:vertical:hover {
                    background-color: #808080;
                }
            """
        }
    
    def _get_high_contrast_theme(self) -> Dict[str, Any]:
        """高对比度主题"""
        return {
            'name': 'high_contrast',
            'display_name': '高对比度',
            'stylesheet': """
                QWidget {
                    background-color: #000000;
                    color: #FFFFFF;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                    font-weight: bold;
                }
                
                QPushButton {
                    background-color: #FFFF00;
                    color: #000000;
                    border: 3px solid #FFFFFF;
                    padding: 12px 24px;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 14px;
                }
                
                QPushButton:hover {
                    background-color: #FFFFFF;
                    color: #000000;
                }
                
                QTextEdit {
                    border: 3px solid #FFFFFF;
                    border-radius: 4px;
                    padding: 12px;
                    background-color: #000000;
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: bold;
                }
                
                QListWidget {
                    border: 3px solid #FFFFFF;
                    background-color: #000000;
                    color: #FFFFFF;
                    border-radius: 4px;
                    outline: none;
                }

                QListWidget::item {
                    padding: 12px 16px;
                    border: none;
                    border-radius: 4px;
                    margin: 2px 4px;
                    background-color: transparent;
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: bold;
                }

                QListWidget::item:selected {
                    background-color: #FFFF00;
                    color: #000000;
                }

                QListWidget::item:hover {
                    background-color: #333333;
                    color: #FFFFFF;
                }

                QListWidget::item:selected:hover {
                    background-color: #FFFFFF;
                    color: #000000;
                }
            """
        }
    
    def _get_blue_theme(self) -> Dict[str, Any]:
        """蓝色主题"""
        return {
            'name': 'blue',
            'display_name': '蓝色主题',
            'stylesheet': """
                QWidget {
                    background-color: #F0F8FF;
                    color: #1E3A8A;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                }
                
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    font-weight: 600;
                }
                
                QPushButton:hover {
                    background-color: #2563EB;
                }
                
                QTextEdit {
                    border: 2px solid #BFDBFE;
                    border-radius: 8px;
                    padding: 12px;
                    background-color: #FFFFFF;
                    selection-background-color: #3B82F6;
                }
                
                QTextEdit:focus {
                    border-color: #3B82F6;
                }

                QListWidget {
                    border: none;
                    background-color: #FFFFFF;
                    outline: none;
                    border-radius: 8px;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                }

                QListWidget::item {
                    padding: 12px 16px;
                    border: none;
                    border-radius: 8px;
                    margin: 2px 4px;
                    background-color: transparent;
                    color: #1E3A8A;
                    font-size: 14px;
                    font-weight: 500;
                }

                QListWidget::item:selected {
                    background-color: #3B82F6;
                    color: white;
                }

                QListWidget::item:hover {
                    background-color: #EBF8FF;
                }

                QListWidget::item:selected:hover {
                    background-color: #2563EB;
                }
            """
        }
    
    def _get_green_theme(self) -> Dict[str, Any]:
        """绿色主题"""
        return {
            'name': 'green',
            'display_name': '绿色主题',
            'stylesheet': """
                QWidget {
                    background-color: #F0FDF4;
                    color: #14532D;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                }
                
                QPushButton {
                    background-color: #22C55E;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    font-weight: 600;
                }
                
                QPushButton:hover {
                    background-color: #16A34A;
                }
                
                QTextEdit {
                    border: 2px solid #BBF7D0;
                    border-radius: 8px;
                    padding: 12px;
                    background-color: #FFFFFF;
                    selection-background-color: #22C55E;
                }
                
                QTextEdit:focus {
                    border-color: #22C55E;
                }

                QListWidget {
                    border: none;
                    background-color: #FFFFFF;
                    outline: none;
                    border-radius: 8px;
                    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                }

                QListWidget::item {
                    padding: 12px 16px;
                    border: none;
                    border-radius: 8px;
                    margin: 2px 4px;
                    background-color: transparent;
                    color: #14532D;
                    font-size: 14px;
                    font-weight: 500;
                }

                QListWidget::item:selected {
                    background-color: #22C55E;
                    color: white;
                }

                QListWidget::item:hover {
                    background-color: #F0FDF4;
                }

                QListWidget::item:selected:hover {
                    background-color: #16A34A;
                }
            """
        }
    
    def _load_custom_themes(self) -> Dict[str, Dict[str, Any]]:
        """加载自定义主题"""
        themes = {}
        try:
            for file in os.listdir(self.custom_themes_dir):
                if file.endswith('.json'):
                    theme_path = os.path.join(self.custom_themes_dir, file)
                    with open(theme_path, 'r', encoding='utf-8') as f:
                        theme_data = json.load(f)
                        theme_name = theme_data.get('name', os.path.splitext(file)[0])
                        themes[theme_name] = theme_data
        except Exception as e:
            print(f"加载自定义主题失败: {e}")
        
        return themes
    
    def _save_theme_settings(self):
        """保存主题设置"""
        try:
            settings = {
                'current_theme': self.current_theme
            }
            with open(self.themes_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存主题设置失败: {e}")
    
    def _load_theme_settings(self):
        """加载主题设置"""
        try:
            if os.path.exists(self.themes_file):
                with open(self.themes_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.current_theme = settings.get('current_theme', 'light')
        except Exception as e:
            print(f"加载主题设置失败: {e}")
            self.current_theme = 'light'
    
    def create_custom_theme(self, name: str, display_name: str, stylesheet: str) -> bool:
        """创建自定义主题"""
        try:
            theme_data = {
                'name': name,
                'display_name': display_name,
                'stylesheet': stylesheet,
                'created_time': time.time()
            }
            
            theme_file = os.path.join(self.custom_themes_dir, f"{name}.json")
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, ensure_ascii=False, indent=2)
            
            # 添加到自定义主题列表
            self.custom_themes[name] = theme_data
            
            return True
            
        except Exception as e:
            print(f"创建自定义主题失败: {e}")
            return False
