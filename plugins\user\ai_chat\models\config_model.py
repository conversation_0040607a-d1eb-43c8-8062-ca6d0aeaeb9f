"""
配置数据模型

管理AI聊天插件的配置信息
"""

import json
import os
from typing import Dict, Any, List
from PyQt5.QtCore import QObject, pyqtSignal


class ConfigModel(QObject):
    """配置数据模型"""
    
    # 信号
    config_changed = pyqtSignal(str, object)  # key, value
    
    # 预定义的AI模型
    PREDEFINED_MODELS = {
        "DeepSeek": [
            "deepseek-chat",
            "deepseek-coder",
            "deepseek-reasoner"
        ],
        "通义千问": [
            "qwen-turbo",
            "qwen-plus",
            "qwen-max",
            "qwen-max-longcontext"
        ],
        "OpenAI": [
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo",
            "gpt-4o"
        ],
        "Open Router": [
            # 免费模型 (Free Models) - 基于实际API测试结果
            "deepseek/deepseek-r1:free",
            "deepseek/deepseek-r1-0528:free",
            "deepseek/deepseek-r1-0528-qwen3-8b:free",
            "meta-llama/llama-3.3-8b-instruct:free",
            "meta-llama/llama-3.2-3b-instruct:free",
            "meta-llama/llama-3.2-1b-instruct:free",
            "meta-llama/llama-3.1-8b-instruct:free",
            "microsoft/phi-4-reasoning:free",
            "microsoft/phi-4-reasoning-plus:free",
            "microsoft/phi-3-mini-128k-instruct:free",
            "microsoft/phi-3-medium-128k-instruct:free",
            "google/gemma-3n-e4b-it:free",
            "mistralai/devstral-small:free",
            "nousresearch/deephermes-3-mistral-24b-preview:free",
            "opengvlab/internvl3-14b:free",
            "sarvamai/sarvam-m:free",
            "qwen/qwen-2.5-7b-instruct:free",
            "huggingface/zephyr-7b-beta:free",
            "openchat/openchat-7b:free",
            "openrouter/auto",

            # 付费模型 (Paid Models)
            "openai/gpt-4o",
            "openai/gpt-4o-mini",
            "openai/gpt-4-turbo",
            "openai/gpt-3.5-turbo",
            "anthropic/claude-3.5-sonnet",
            "anthropic/claude-3-haiku",
            "google/gemini-pro-1.5",
            "google/gemini-flash-1.5",
            "meta-llama/llama-3.1-405b-instruct",
            "meta-llama/llama-3.1-70b-instruct",
            "meta-llama/llama-3.1-8b-instruct",
            "mistralai/mistral-large",
            "mistralai/mistral-medium",
            "cohere/command-r-plus",
            "perplexity/llama-3.1-sonar-large-128k-online"
        ]
    }
    
    def __init__(self):
        """初始化配置模型"""
        super().__init__()
        self.config_file = "ai_chat_config.json"
        self._config = self._get_default_config()
        
        # 加载配置
        self.load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # API配置
            "api_key": "",
            "api_url": "https://api.deepseek.com/v1",
            "model": "deepseek-chat",
            "model_provider": "DeepSeek",
            
            # 聊天配置
            "max_tokens": 2048,
            "temperature": 0.7,
            "stream": True,
            
            # 界面配置
            "window_width": 800,
            "window_height": 600,
            "font_size": 12,
            "theme": "light",
            
            # 文档配置
            "max_file_size": 10 * 1024 * 1024,  # 10MB
            "supported_formats": [".pdf", ".docx", ".doc", ".txt", ".md"],
            
            # 翻译配置
            "translation_enabled": True,
            "auto_detect_language": True,
            
            # 高级配置
            "request_timeout": 30,
            "max_retries": 3,
            "enable_logging": True
        }
    
    def get(self, key: str, default=None):
        """获取配置值"""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        old_value = self._config.get(key)
        self._config[key] = value
        
        if old_value != value:
            self.config_changed.emit(key, value)
            self.save_config()
    
    def update(self, config_dict: Dict[str, Any]):
        """批量更新配置"""
        for key, value in config_dict.items():
            self._config[key] = value
            self.config_changed.emit(key, value)
        
        self.save_config()
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def reset_to_default(self):
        """重置为默认配置"""
        self._config = self._get_default_config()
        self.save_config()
        
        # 发送所有配置变更信号
        for key, value in self._config.items():
            self.config_changed.emit(key, value)
    
    def get_available_models(self) -> List[str]:
        """获取当前提供商的可用模型"""
        provider = self.get("model_provider", "DeepSeek")
        return self.PREDEFINED_MODELS.get(provider, [])
    
    def get_all_providers(self) -> List[str]:
        """获取所有模型提供商"""
        return list(self.PREDEFINED_MODELS.keys())
    
    def set_model_provider(self, provider: str):
        """设置模型提供商并更新相关配置"""
        if provider not in self.PREDEFINED_MODELS:
            return
        
        self.set("model_provider", provider)
        
        # 更新默认API URL
        default_urls = {
            "DeepSeek": "https://api.deepseek.com/v1",
            "通义千问": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "OpenAI": "https://api.openai.com/v1",
            "Open Router": "https://openrouter.ai/api/v1"
        }
        
        if provider in default_urls:
            self.set("api_url", default_urls[provider])
        
        # 设置默认模型
        available_models = self.get_available_models()
        if available_models:
            # 对于Open Router，优先选择免费模型
            if provider == "Open Router":
                # 查找免费模型，优先选择性能较好的
                free_models = [m for m in available_models if ":free" in m or "gemini-flash" in m or "deepseek" in m]
                if free_models:
                    # 优先选择 deepseek-r1:free 或 gemini-2.0-flash-exp:free
                    if "deepseek/deepseek-r1:free" in free_models:
                        self.set("model", "deepseek/deepseek-r1:free")
                    elif "google/gemini-2.0-flash-exp:free" in free_models:
                        self.set("model", "google/gemini-2.0-flash-exp:free")
                    else:
                        self.set("model", free_models[0])
                else:
                    self.set("model", available_models[0])
            else:
                self.set("model", available_models[0])
    
    def validate_config(self) -> Dict[str, str]:
        """验证配置，返回错误信息"""
        errors = {}
        
        # 验证API Key
        if not self.get("api_key"):
            errors["api_key"] = "API Key不能为空"
        
        # 验证API URL
        api_url = self.get("api_url")
        if not api_url:
            errors["api_url"] = "API URL不能为空"
        elif not api_url.startswith(("http://", "https://")):
            errors["api_url"] = "API URL格式不正确"
        
        # 验证模型
        if not self.get("model"):
            errors["model"] = "必须选择一个模型"
        
        # 验证数值配置
        max_tokens = self.get("max_tokens")
        if not isinstance(max_tokens, int) or max_tokens <= 0:
            errors["max_tokens"] = "最大令牌数必须是正整数"
        
        temperature = self.get("temperature")
        if not isinstance(temperature, (int, float)) or not (0 <= temperature <= 2):
            errors["temperature"] = "温度值必须在0-2之间"
        
        timeout = self.get("request_timeout")
        if not isinstance(timeout, int) or timeout <= 0:
            errors["request_timeout"] = "请求超时时间必须是正整数"
        
        return errors
    
    def is_valid(self) -> bool:
        """检查配置是否有效"""
        return len(self.validate_config()) == 0
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
    
    def load_config(self):
        """从文件加载配置"""
        try:
            if not os.path.exists(self.config_file):
                return
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
            
            # 合并配置（保留默认值）
            for key, value in loaded_config.items():
                if key in self._config:
                    self._config[key] = value
                    
        except Exception as e:
            print(f"加载配置失败: {str(e)}")
    
    def export_config(self, file_path: str):
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"导出配置失败: {str(e)}")
            return False
    
    def import_config(self, file_path: str):
        """从指定文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证并更新配置
            for key, value in imported_config.items():
                if key in self._config:
                    self._config[key] = value
            
            self.save_config()
            
            # 发送配置变更信号
            for key, value in self._config.items():
                self.config_changed.emit(key, value)
            
            return True
        except Exception as e:
            print(f"导入配置失败: {str(e)}")
            return False
