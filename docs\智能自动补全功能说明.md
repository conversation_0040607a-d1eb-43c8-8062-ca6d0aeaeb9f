# RunSim GUI 智能自动补全功能说明

## 功能概述

RunSim GUI 现已集成智能自动补全功能，为运行参数配置页面的关键输入框提供智能建议，大幅提升用户输入效率和准确性。

## 主要特性

### 1. 智能建议
- **历史记录集成**：自动从用户的历史运行记录中提取常用参数值
- **回归列表解析**：支持从回归列表文件中解析常用配置项
- **频率统计**：根据使用频率智能排序建议项
- **模糊匹配**：支持前缀匹配、包含匹配和模糊匹配

### 2. 支持的输入框
以下输入框已集成自动补全功能：
- **BASE参数**：base环境配置
- **BLOCK路径**：block路径配置
- **用例名**：测试用例名称
- **提交服务器**：bq服务器名称
- **工作目录**：rundir路径
- **CFG定义**：cfg_def配置
- **仿真参数**：simarg参数
- **波形时间**：wdd时间配置
- **其他选项**：其他runsim选项

### 3. 用户体验
- **实时建议**：输入时实时显示匹配的建议列表
- **键盘导航**：支持上下箭头键选择，Tab/Enter确认
- **鼠标操作**：支持鼠标点击选择建议项
- **智能排序**：按使用频率和相关性排序
- **防抖动**：150ms延迟，避免频繁查询

## 使用方法

### 基本操作

1. **开始输入**：在支持自动补全的输入框中开始输入
2. **查看建议**：系统会自动显示匹配的建议列表
3. **选择建议**：
   - 使用上下箭头键导航
   - 按Tab或Enter键确认选择
   - 直接点击鼠标选择
4. **关闭建议**：按Esc键或点击其他区域关闭建议列表

### 高级功能

#### 强制显示建议
在空输入框中按下特定快捷键（如Ctrl+Space）可强制显示所有历史建议。

#### 手动添加历史
系统会自动记录用户的输入，也可以通过以下方式手动管理：
```python
# 在配置面板中调用
config_panel.save_current_values_to_autocomplete()
```

## 数据来源

### 1. 命令历史文件
- **文件位置**：`runsim_command_history.json`
- **解析内容**：历史执行的runsim命令
- **提取参数**：-base、-block、-case、-bq、-rundir、-cfg_def等

### 2. 配置文件
- **文件位置**：`runsim_config.json`
- **解析内容**：当前保存的配置参数
- **提取参数**：所有配置字段的当前值

### 3. 回归列表文件
- **文件格式**：`.list`、`.lst`文件
- **搜索目录**：当前目录、dv目录、test目录
- **解析规则**：按照回归列表文件格式解析block、case、cfg_def、base等参数

## 性能优化

### 1. 异步加载
- 历史数据在后台异步加载，不阻塞UI启动
- 数据加载完成后自动启用补全功能

### 2. 智能缓存
- 查询结果缓存5分钟，减少重复计算
- 数据更新时自动清理相关缓存

### 3. 防抖动机制
- 用户输入150ms后才触发查询
- 避免频繁的匹配计算

### 4. 数量限制
- 每个字段最多保存100个历史项目
- 建议列表最多显示10个项目
- 自动清理过期和低频使用的项目

## 配置管理

### 统计信息查看
```python
# 获取统计信息
stats = config_panel.get_autocomplete_stats()
print(f"总项目数: {stats['base']['total_items']}")
print(f"总使用次数: {stats['base']['total_usage']}")
```

### 数据清理
```python
# 清除特定字段的历史
config_panel.clear_autocomplete_history('base')

# 清除所有历史
config_panel.clear_autocomplete_history()
```

### 数据导入导出
```python
# 导出数据
config_panel.export_autocomplete_data('backup.json')

# 导入数据（合并模式）
config_panel.import_autocomplete_data('backup.json', merge=True)

# 导入数据（替换模式）
config_panel.import_autocomplete_data('backup.json', merge=False)
```

## 文件结构

```
models/
├── autocomplete_model.py      # 数据模型
utils/
├── autocomplete_engine.py     # 补全引擎
├── autocomplete_widget.py     # UI组件
├── autocomplete_manager.py    # 管理器
└── history_parser.py          # 历史解析器
tests/
└── test_autocomplete.py       # 测试用例
docs/
└── 智能自动补全功能说明.md    # 本文档
```

## 数据存储格式

### 自动补全数据文件 (`autocomplete_data.json`)
```json
{
  "base": {
    "test_base": {
      "count": 5,
      "last_used": "2024-01-15T10:30:00"
    }
  },
  "block": {
    "udtb/test": {
      "count": 3,
      "last_used": "2024-01-15T09:15:00"
    }
  }
}
```

## 故障排除

### 常见问题

1. **建议不显示**
   - 检查数据是否加载完成
   - 确认输入长度是否达到最小查询长度
   - 验证字段类型是否支持

2. **建议不准确**
   - 清除缓存重新加载数据
   - 检查历史数据文件是否损坏
   - 重新解析历史记录

3. **性能问题**
   - 检查历史数据量是否过大
   - 调整缓存超时时间
   - 增加防抖动延迟

### 调试模式
在开发环境中，可以启用调试输出：
```python
# 在控制台查看调试信息
print("自动补全数据加载完成")
print(f"解析历史数据失败: {error}")
```

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持9种参数类型的自动补全
- 集成历史记录和回归列表解析
- 实现智能匹配和排序算法
- 添加性能优化机制
- 提供配置管理功能

## 技术支持

如遇到问题或需要功能改进，请联系开发团队或提交Issue。
