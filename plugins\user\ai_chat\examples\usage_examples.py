"""
AI聊天插件使用示例

演示如何使用AI聊天插件的各种功能
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from plugins.user.ai_chat.models.chat_model import ChatModel, ChatMessage
from plugins.user.ai_chat.models.config_model import ConfigModel
from plugins.user.ai_chat.models.document_model import DocumentModel
from plugins.user.ai_chat.utils.api_client import APIClient
from plugins.user.ai_chat.utils.document_parser import DocumentParser
from plugins.user.ai_chat.utils.translator import Translator


def example_chat_model():
    """聊天模型使用示例"""
    print("=== 聊天模型示例 ===")
    
    # 创建聊天模型
    chat_model = ChatModel()
    
    # 创建新会话
    session = chat_model.create_new_session("示例对话")
    print(f"创建会话: {session.title} (ID: {session.session_id})")
    
    # 添加消息
    user_msg = chat_model.add_message('user', '你好，请介绍一下自己')
    print(f"用户消息: {user_msg.content}")
    
    ai_msg = chat_model.add_message('assistant', '你好！我是AI助手，很高兴为您服务。')
    print(f"AI回复: {ai_msg.content}")
    
    # 获取API格式的消息
    api_messages = session.get_messages_for_api()
    print(f"API消息格式: {api_messages}")
    
    print()


def example_config_model():
    """配置模型使用示例"""
    print("=== 配置模型示例 ===")
    
    # 创建配置模型
    config_model = ConfigModel()
    
    # 设置配置
    config_model.set('api_key', 'your-api-key-here')
    config_model.set('model', 'deepseek-chat')
    config_model.set('temperature', 0.7)
    
    # 获取配置
    api_key = config_model.get('api_key')
    model = config_model.get('model')
    temperature = config_model.get('temperature')
    
    print(f"API Key: {api_key}")
    print(f"模型: {model}")
    print(f"温度: {temperature}")
    
    # 获取可用模型
    providers = config_model.get_all_providers()
    print(f"支持的提供商: {providers}")
    
    deepseek_models = config_model.PREDEFINED_MODELS['DeepSeek']
    print(f"DeepSeek模型: {deepseek_models}")

    openrouter_models = config_model.PREDEFINED_MODELS['Open Router']
    print(f"Open Router模型: {openrouter_models[:3]}...")  # 只显示前3个
    
    # 验证配置
    errors = config_model.validate_config()
    if errors:
        print(f"配置错误: {errors}")
    else:
        print("配置验证通过")
    
    print()


def example_document_model():
    """文档模型使用示例"""
    print("=== 文档模型示例 ===")
    
    # 创建文档模型
    doc_model = DocumentModel()
    
    # 创建示例文档文件
    example_file = "example.txt"
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write("这是一个示例文档。\n包含一些测试内容。\n用于演示文档处理功能。")
    
    try:
        # 添加文档
        doc_info = doc_model.add_document(example_file, "示例文档")
        if doc_info:
            print(f"添加文档: {doc_info.name}")
            print(f"文件大小: {doc_info.get_size_str()}")
            print(f"文档ID: {doc_info.doc_id}")
            
            # 模拟文档处理
            success, content, error = DocumentParser.parse_document(example_file)
            if success:
                doc_model.update_document_content(doc_info.doc_id, content, "这是一个示例文档摘要")
                print(f"文档内容: {content}")
                print(f"处理状态: {'已处理' if doc_info.processed else '未处理'}")
            else:
                print(f"文档处理失败: {error}")
            
            # 获取上下文内容
            context = doc_model.get_content_for_context()
            print(f"上下文内容: {context[:100]}...")
        
    finally:
        # 清理示例文件
        if os.path.exists(example_file):
            os.remove(example_file)
    
    print()


def example_api_client():
    """API客户端使用示例"""
    print("=== API客户端示例 ===")
    
    # 创建API客户端
    api_client = APIClient()
    
    # 设置配置（示例配置，实际使用时需要真实的API Key）
    config = {
        'api_key': 'your-api-key-here',
        'api_url': 'https://openrouter.ai/api/v1',  # 使用Open Router作为示例
        'model': 'openai/gpt-4o',
        'max_tokens': 100,
        'temperature': 0.7,
        'request_timeout': 30
    }
    
    api_client.set_config(config)
    
    # 准备测试消息
    messages = [
        {"role": "user", "content": "请简单介绍一下人工智能"}
    ]
    
    print("API配置已设置")
    print(f"API URL: {config['api_url']}")
    print(f"模型: {config['model']}")
    print("注意: 需要有效的API Key才能进行实际请求")
    
    # 注意: 这里不进行实际的API调用，因为需要真实的API Key
    # 实际使用时的代码示例:
    # response = api_client.chat_completion(messages)
    # if response.success:
    #     print(f"AI回复: {response.data}")
    # else:
    #     print(f"请求失败: {response.error}")
    
    print()


def example_document_parser():
    """文档解析器使用示例"""
    print("=== 文档解析器示例 ===")
    
    # 创建示例文本文件
    example_files = {
        "example.txt": "这是一个文本文件示例。\n包含多行内容。\n用于测试文档解析功能。",
        "example.md": "# Markdown示例\n\n这是一个**Markdown**文档。\n\n- 列表项1\n- 列表项2"
    }
    
    for filename, content in example_files.items():
        # 创建文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        try:
            # 解析文档
            success, parsed_content, error = DocumentParser.parse_document(filename)
            
            print(f"文件: {filename}")
            if success:
                print(f"解析成功")
                print(f"内容: {parsed_content[:100]}...")
                
                # 清理文本
                clean_content = DocumentParser.clean_text(parsed_content)
                print(f"清理后: {clean_content[:100]}...")
                
                # 提取摘要
                summary = DocumentParser.extract_summary(clean_content, 50)
                print(f"摘要: {summary}")
            else:
                print(f"解析失败: {error}")
            
            print()
            
        finally:
            # 清理文件
            if os.path.exists(filename):
                os.remove(filename)


def example_translator():
    """翻译器使用示例"""
    print("=== 翻译器示例 ===")
    
    # 创建API客户端和翻译器
    api_client = APIClient()
    translator = Translator(api_client)
    
    # 测试语言检测
    chinese_text = "你好，世界！这是一段中文文本。"
    english_text = "Hello, world! This is an English text."
    
    chinese_lang = translator.detect_language(chinese_text)
    english_lang = translator.detect_language(english_text)
    
    print(f"中文文本检测结果: {chinese_lang}")
    print(f"英文文本检测结果: {english_lang}")
    
    # 测试目标语言确定
    chinese_target = translator.get_target_language(chinese_text)
    english_target = translator.get_target_language(english_text)
    
    print(f"中文文本目标语言: {chinese_target}")
    print(f"英文文本目标语言: {english_target}")
    
    # 测试文本分割
    long_text = "这是一段很长的文本。" * 100
    segments = Translator.split_text_for_translation(long_text, 200)
    print(f"长文本分割为 {len(segments)} 段")
    
    # 测试翻译结果清理
    raw_translation = "翻译结果：Hello, world!"
    clean_translation = Translator.clean_translation_result(raw_translation)
    print(f"清理前: {raw_translation}")
    print(f"清理后: {clean_translation}")
    
    print()


def main():
    """主示例函数"""
    print("AI聊天插件功能示例")
    print("=" * 50)
    
    examples = [
        example_chat_model,
        example_config_model,
        example_document_model,
        example_api_client,
        example_document_parser,
        example_translator
    ]
    
    for example_func in examples:
        try:
            example_func()
        except Exception as e:
            print(f"示例执行出错: {str(e)}")
            import traceback
            traceback.print_exc()
            print()
    
    print("=" * 50)
    print("所有示例执行完成")
    print("\n使用提示:")
    print("1. 在实际使用中，需要配置有效的API Key")
    print("2. 文档解析功能需要安装相应的依赖包")
    print("3. 翻译功能依赖于API服务的可用性")
    print("4. 建议先运行测试脚本验证环境配置")


if __name__ == "__main__":
    main()
