"""
数据看板控制器
负责管理数据看板的业务逻辑和与主GUI的集成
"""
import os
import re
from datetime import datetime
from typing import Optional, Dict, Any

from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QTimer, QThread, Qt
try:
    from PyQt5.QtWidgets import QAction, QMessageBox, QProgressDialog, QApplication
except ImportError as e:
    print(f"导入PyQt5组件失败: {e}")
    # 提供备用导入
    from PyQt5.QtWidgets import QAction, QMessageBox, QProgressDialog
    QApplication = None

from views.dashboard_window import DashboardWindow
from models.dashboard_model import DashboardModel
from utils.adaptive_refresh_manager import AdaptiveRefreshManager


class DashboardController(QObject):
    """数据看板控制器"""
    
    # 信号定义
    simulation_started = pyqtSignal(str)  # case_name
    simulation_completed = pyqtSignal(str, str)  # case_name, status
    
    def __init__(self, main_window):
        """初始化数据看板控制器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.dashboard_window = None
        self.dashboard_model = DashboardModel()
        self._cleaned_up = False  # 清理状态标志
        
        # 创建菜单项
        self.create_menu_items()

        # 性能监控变量（需要在其他初始化之前设置）
        self.last_log_activity = 0
        self.dashboard_visible = True

        # 初始化自适应刷新管理器
        self.adaptive_refresh = AdaptiveRefreshManager()

        # 当前监控的用例
        self.monitoring_cases = {}  # {case_name: {'log_path': str, 'start_time': datetime}}

        # 监控定时器（使用自适应频率）
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.monitor_simulation_logs)
        self.start_adaptive_monitoring()

        # 手动检查定时器（使用自适应频率）
        self.manual_check_timer = QTimer()
        self.manual_check_timer.timeout.connect(self.manual_check_ongoing_cases)
        self.start_adaptive_manual_check()
    
    def create_menu_items(self):
        """创建菜单项"""
        try:
            # 获取数据看板菜单
            dashboard_menu = getattr(self.main_window, 'dashboard_menu', None)
            if not dashboard_menu:
                print("未找到数据看板菜单")
                return
            
            # 创建打开数据看板菜单项
            self.open_dashboard_action = QAction("打开数据看板", self.main_window)
            self.open_dashboard_action.setStatusTip("打开仿真数据看板")
            self.open_dashboard_action.triggered.connect(self.show_dashboard)
            dashboard_menu.addAction(self.open_dashboard_action)
            
            # 添加分隔符
            dashboard_menu.addSeparator()
            
            # 创建数据管理菜单项
            self.clear_data_action = QAction("清除历史数据", self.main_window)
            self.clear_data_action.setStatusTip("清除所有仿真历史记录")
            self.clear_data_action.triggered.connect(self.clear_historical_data)
            dashboard_menu.addAction(self.clear_data_action)
            
            print("数据看板菜单项创建成功")
            
        except Exception as e:
            print(f"创建数据看板菜单项失败: {str(e)}")
    
    @pyqtSlot()
    def show_dashboard(self):
        """显示数据看板窗口"""
        try:
            if self.dashboard_window is None:
                # 创建数据看板窗口
                self.dashboard_window = DashboardWindow(self.main_window)
                self.dashboard_window.window_closed.connect(self.on_dashboard_closed)
            
            # 显示窗口
            self.dashboard_window.show()
            self.dashboard_window.raise_()
            self.dashboard_window.activateWindow()
            
        except Exception as e:
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开数据看板: {str(e)}"
            )
    
    @pyqtSlot()
    def on_dashboard_closed(self):
        """数据看板窗口关闭时的处理"""
        self.dashboard_window = None
    
    @pyqtSlot()
    def clear_historical_data(self):
        """清除历史数据"""
        try:
            # 获取当前记录数量
            total_records = self.dashboard_model.get_total_records_count()

            reply = QMessageBox.question(
                self.main_window,
                "确认清除",
                f"确定要清除所有仿真历史记录吗？\n\n"
                f"当前共有 {total_records} 条记录\n"
                f"此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 显示进度对话框
                progress = QProgressDialog("正在清除历史数据...", "取消", 0, 100, self.main_window)
                progress.setWindowModality(Qt.WindowModal)
                progress.setAutoClose(True)
                progress.setAutoReset(True)
                progress.show()

                try:
                    # 执行清除操作
                    progress.setValue(20)
                    if QApplication:
                        try:
                            QApplication.processEvents()
                        except:
                            pass  # 忽略processEvents错误

                    success = self.dashboard_model.clear_all_historical_data()

                    progress.setValue(80)
                    if QApplication:
                        try:
                            QApplication.processEvents()
                        except:
                            pass  # 忽略processEvents错误

                    if success:
                        # 刷新dashboard窗口
                        if self.dashboard_window:
                            self.dashboard_window.refresh_data()

                        progress.setValue(100)
                        if QApplication:
                            try:
                                QApplication.processEvents()
                            except:
                                pass  # 忽略processEvents错误

                        QMessageBox.information(
                            self.main_window,
                            "清除完成",
                            f"成功清除了 {total_records} 条历史记录"
                        )
                    else:
                        QMessageBox.warning(
                            self.main_window,
                            "清除失败",
                            "清除历史数据时发生错误，请检查数据库连接"
                        )

                except Exception as e:
                    QMessageBox.warning(
                        self.main_window,
                        "清除失败",
                        f"清除历史数据时发生错误: {str(e)}"
                    )
                finally:
                    progress.close()

        except Exception as e:
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"清除历史数据失败: {str(e)}"
            )
    
    def start_simulation_monitoring(self, case_name: str, command_line: str = "",
                                  simulation_stage: str = "DVR1"):
        """开始监控仿真执行（异步执行）

        Args:
            case_name (str): 用例名称
            command_line (str): 执行命令行
            simulation_stage (str): 仿真阶段
        """
        try:
            # 异步添加仿真记录
            success = self.dashboard_model.add_simulation_record(
                case_name, command_line, simulation_stage
            )

            if success:
                # 异步更新状态为On-Going
                self.dashboard_model.update_simulation_status(
                    case_name, "On-Going", start_time=datetime.now()
                )

                # 添加到监控列表
                self.monitoring_cases[case_name] = {
                    'log_path': self.get_simulation_log_path(case_name),
                    'start_time': datetime.now(),
                    'command_line': command_line
                }

                # 发送信号
                self.simulation_started.emit(case_name)



        except Exception as e:
            print(f"开始仿真监控失败: {str(e)}")
    
    def get_simulation_log_path(self, case_name: str) -> str:
        """获取仿真日志路径

        Args:
            case_name (str): 用例名称

        Returns:
            str: 日志文件路径
        """
        # 尝试多种可能的日志路径
        current_dir = os.getcwd()
        possible_paths = [
            os.path.join(current_dir, case_name, "log", "irun_sim.log"),
            os.path.join(current_dir, case_name, "irun_sim.log"),
            os.path.join(current_dir, "log", f"{case_name}_sim.log"),
            os.path.join(current_dir, "log", "irun_sim.log"),
            os.path.join(current_dir, f"{case_name}.log"),
            os.path.join(current_dir, "sim.log")
        ]

        # 返回第一个存在的路径，如果都不存在则返回默认路径
        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 如果都不存在，返回默认路径
        default_path = os.path.join(current_dir, case_name, "log", "irun_sim.log")
        return default_path
    
    def get_compile_log_path(self, case_name: str) -> str:
        """获取编译日志路径

        Args:
            case_name (str): 用例名称

        Returns:
            str: 编译日志文件路径
        """
        # 尝试多种可能的编译日志路径
        current_dir = os.getcwd()
        possible_paths = [
            os.path.join(current_dir, case_name, "log", "irun_compile.log"),
            os.path.join(current_dir, case_name, "irun_compile.log"),
            os.path.join(current_dir, "log", f"{case_name}_compile.log"),
            os.path.join(current_dir, "log", "irun_compile.log"),
            os.path.join(current_dir, f"{case_name}_compile.log"),
            os.path.join(current_dir, "compile.log")
        ]

        # 返回第一个存在的路径，如果都不存在则返回默认路径
        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 如果都不存在，返回默认路径
        default_path = os.path.join(current_dir, case_name, "log", "irun_compile.log")
        return default_path
    
    @pyqtSlot()
    def monitor_simulation_logs(self):
        """监控仿真日志文件"""
        # 更新日志活动量
        self.last_log_activity = self.calculate_log_activity()

        # 如果没有监控的用例，降低检查频率
        if not self.monitoring_cases:
            self.update_monitoring_frequency()
            return

        completed_cases = []
        batch_updates = []  # 批量更新列表
        
        for case_name, info in self.monitoring_cases.items():
            try:
                log_path = info['log_path']
                
                # 检查日志文件是否存在
                if not os.path.exists(log_path):
                    continue
                
                # 检查仿真是否完成
                status = self.check_simulation_status(log_path)
                
                if status in ['PASS', 'FAIL']:
                    # 仿真完成，解析时间信息
                    compile_time = self.parse_compile_time(case_name)
                    sim_time = self.parse_simulation_time(case_name)

                    # 添加到批量更新列表
                    batch_updates.append({
                        'case_name': case_name,
                        'status': status,
                        'compile_time': compile_time,
                        'simulation_time': sim_time,
                        'log_path': log_path
                    })

                    # 发送完成信号
                    self.simulation_completed.emit(case_name, status)

                    # 标记为已完成
                    completed_cases.append(case_name)
            
            except Exception as e:
                print(f"监控仿真日志失败 {case_name}: {str(e)}")
        
        # 批量更新数据库
        if batch_updates:
            self.batch_update_database(batch_updates)

        # 移除已完成的用例
        for case_name in completed_cases:
            del self.monitoring_cases[case_name]

        # 更新监控频率
        self.update_monitoring_frequency()

    @pyqtSlot()
    def manual_check_ongoing_cases(self):
        """手动检查所有On-Going状态的用例"""
        try:
            # 直接获取On-Going状态的记录，使用索引优化
            ongoing_cases = self.dashboard_model.get_simulation_records_by_status('On-Going')

            # 限制检查数量，避免一次处理过多用例
            max_check_count = 50  # 每次最多检查50个用例
            if len(ongoing_cases) > max_check_count:
                ongoing_cases = ongoing_cases[:max_check_count]

            batch_updates = []  # 批量更新列表



            for record in ongoing_cases:
                case_name = record.get('case_name')
                if not case_name:
                    continue



                # 检查仿真日志
                sim_log_path = self.get_simulation_log_path(case_name)
                status = self.check_simulation_status(sim_log_path)

                if status in ['PASS', 'FAIL']:
                    # 解析时间信息
                    compile_time = self.parse_compile_time(case_name)
                    sim_time = self.parse_simulation_time(case_name)

                    # 添加到批量更新列表
                    batch_updates.append({
                        'case_name': case_name,
                        'status': status,
                        'compile_time': compile_time,
                        'simulation_time': sim_time,
                        'log_path': sim_log_path
                    })

                    # 发送完成信号
                    self.simulation_completed.emit(case_name, status)

            # 批量更新数据库
            if batch_updates:
                self.batch_update_database(batch_updates)
                print(f"手动检查完成，更新了 {len(batch_updates)} 个用例状态")

        except Exception as e:
            print(f"手动检查On-Going用例失败: {str(e)}")

    def start_adaptive_monitoring(self):
        """启动自适应监控"""
        # 计算初始监控间隔
        interval = self.adaptive_refresh.get_refresh_interval(
            is_visible=self.dashboard_visible,
            log_activity=self.last_log_activity
        )
        self.monitor_timer.start(interval)

    def start_adaptive_manual_check(self):
        """启动自适应手动检查"""
        # 手动检查频率较低，基础间隔更长
        base_interval = 30000 if self.dashboard_visible else 60000
        interval = max(base_interval, self.adaptive_refresh.get_refresh_interval(
            is_visible=self.dashboard_visible,
            log_activity=self.last_log_activity
        ) * 6)  # 手动检查频率是监控频率的6倍
        self.manual_check_timer.start(interval)

    def update_monitoring_frequency(self):
        """更新监控频率"""
        if not hasattr(self, 'adaptive_refresh'):
            return

        # 计算新的监控间隔
        monitor_interval = self.adaptive_refresh.get_refresh_interval(
            is_visible=self.dashboard_visible,
            log_activity=self.last_log_activity
        )

        # 更新监控定时器
        if self.monitor_timer.isActive():
            self.monitor_timer.stop()
            self.monitor_timer.start(monitor_interval)

        # 更新手动检查定时器
        manual_check_interval = max(30000, monitor_interval * 6)
        if self.manual_check_timer.isActive():
            self.manual_check_timer.stop()
            self.manual_check_timer.start(manual_check_interval)

    def set_dashboard_visibility(self, visible: bool):
        """设置dashboard可见性"""
        self.dashboard_visible = visible
        self.update_monitoring_frequency()

    def calculate_log_activity(self) -> int:
        """计算日志活动量"""
        total_activity = 0

        for case_name, info in self.monitoring_cases.items():
            try:
                log_path = info['log_path']
                if os.path.exists(log_path):
                    # 获取文件大小作为活动量指标
                    file_size = os.path.getsize(log_path)
                    total_activity += file_size
            except Exception:
                continue

        return total_activity

    def batch_update_database(self, updates: list):
        """批量更新数据库

        Args:
            updates (list): 更新列表，每个元素包含case_name, status等信息
        """
        try:
            for update in updates:
                # 异步更新数据库记录
                success = self.dashboard_model.update_simulation_status(
                    case_name=update['case_name'],
                    status=update['status'],
                    compile_time=update.get('compile_time'),
                    simulation_time=update.get('simulation_time'),
                    log_path=update.get('log_path')
                )

                if not success:
                    print(f"批量数据库更新失败: {update['case_name']}")

        except Exception as e:
            print(f"批量更新数据库失败: {str(e)}")
    
    def check_simulation_status(self, log_path: str) -> str:
        """检查仿真状态

        Args:
            log_path (str): 日志文件路径

        Returns:
            str: 仿真状态 (PASS/FAIL/On-Going)
        """
        try:
            if not os.path.exists(log_path):
                return "On-Going"

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

                # 读取最后100行，增加检查范围
                lines = content.split('\n')[-100:]
                last_lines = '\n'.join(lines)



                # 检查多种PASS标识
                pass_patterns = [
                    "SPRD_PASSED",
                    "TEST PASSED",
                    "PASSED",
                    "Simulation completed successfully",
                    "SUCCESS",
                    "FINISH",
                    "simulation finished",
                    "Simulation PASSED"
                    "Test completed successfully"
                ]

                for pattern in pass_patterns:
                    if pattern in last_lines:
                        return "PASS"

                # 检查多种FAIL标识
                fail_patterns = [
                    "SPRD_FAILED",
                    "TEST FAILED",
                    "FAILED",
                    "Error", "ERROR",
                    "FATAL", "Fatal",
                    "Simulation FAILED",
                    "SIMULATION FAILED",
                    "simulation failed",
                    "Test failed",
                    "ABORT",
                    "TIMEOUT"
                ]

                for pattern in fail_patterns:
                    if pattern in last_lines:
                        return "FAIL"

                # 检查文件修改时间
                file_mtime = os.path.getmtime(log_path)
                current_time = datetime.now().timestamp()
                time_diff = current_time - file_mtime

                # 如果文件超过5分钟没有更新，可能已经完成但没有明确标识
                if time_diff > 300:  # 5分钟
                    # 检查是否有仿真结束的其他标识
                    end_patterns = [
                        "exit",
                        "quit",
                        "stop",
                        "end",
                        "finish",
                        "done",
                        "completed",
                        "terminated"
                    ]

                    for pattern in end_patterns:
                        if pattern.lower() in last_lines.lower():
                            # 只在检测到结束标识时输出关键信息
                            print(f"检测到结束标识: {pattern}，但无明确PASS/FAIL，默认为PASS")
                            return "PASS"

                    # 只在文件长时间未更新时输出警告
                    print("文件长时间未更新，可能已失败")
                    return "FAIL"

                # 移除频繁的"仿真仍在进行中"调试输出
                return "On-Going"

        except Exception as e:
            print(f"检查仿真状态失败: {str(e)}")
            return "On-Going"
    
    def parse_compile_time(self, case_name: str) -> Optional[float]:
        """解析编译时间

        Args:
            case_name (str): 用例名称

        Returns:
            Optional[float]: 编译时间(分钟)，失败返回None
        """
        try:
            compile_log = self.get_compile_log_path(case_name)

            # 首先尝试复用time_analyzer的逻辑
            try:
                from plugins.builtin.time_analyzer import TimeAnalyzerPlugin
                analyzer = TimeAnalyzerPlugin()
                result = analyzer.get_time_from_log(compile_log, False)
                if result is not None:
                    print(f"time_analyzer解析编译时间成功: {result}")
                    return result
            except Exception as e:
                print(f"time_analyzer解析编译时间失败: {str(e)}")

            # 备用解析逻辑：直接解析日志文件
            if os.path.exists(compile_log):
                with open(compile_log, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # 查找编译时间相关的模式
                import re
                time_patterns = [
                    r'Total time:\s*(\d+\.?\d*)\s*minutes',
                    r'Compile time:\s*(\d+\.?\d*)\s*min',
                    r'Elapsed time:\s*(\d+):(\d+):(\d+)',  # HH:MM:SS格式
                ]

                for pattern in time_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        if len(matches[0]) == 3:  # HH:MM:SS格式
                            hours, minutes, seconds = map(int, matches[0])
                            total_minutes = hours * 60 + minutes + seconds / 60
                            return total_minutes
                        else:
                            time_value = float(matches[0])
                            return time_value

            return None

        except Exception as e:
            print(f"解析编译时间失败 {case_name}: {str(e)}")
            return None
    
    def parse_simulation_time(self, case_name: str) -> Optional[float]:
        """解析仿真时间

        Args:
            case_name (str): 用例名称

        Returns:
            Optional[float]: 仿真时间(分钟)，失败返回None
        """
        try:
            sim_log = self.get_simulation_log_path(case_name)

            # 首先尝试复用time_analyzer的逻辑
            try:
                from plugins.builtin.time_analyzer import TimeAnalyzerPlugin
                analyzer = TimeAnalyzerPlugin()
                result = analyzer.get_time_from_log(sim_log, True)
                if result is not None:
                    print(f"time_analyzer解析仿真时间成功: {result}")
                    return result
            except Exception as e:
                print(f"time_analyzer解析仿真时间失败: {str(e)}")

            # 备用解析逻辑：直接解析日志文件
            if os.path.exists(sim_log):
                with open(sim_log, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # 查找仿真时间相关的模式
                import re
                time_patterns = [
                    r'Simulation time:\s*(\d+\.?\d*)\s*minutes',
                    r'Total simulation time:\s*(\d+\.?\d*)\s*min',
                    r'Simulation completed in\s*(\d+):(\d+):(\d+)',  # HH:MM:SS格式
                    r'CPU time:\s*(\d+\.?\d*)\s*seconds',  # 秒转分钟
                ]

                for pattern in time_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        if len(matches[0]) == 3:  # HH:MM:SS格式
                            hours, minutes, seconds = map(int, matches[0])
                            total_minutes = hours * 60 + minutes + seconds / 60
                            return total_minutes
                        else:
                            time_value = float(matches[0])
                            # 如果是秒，转换为分钟
                            if 'seconds' in pattern:
                                time_value = time_value / 60
                            return time_value

            return None

        except Exception as e:
            print(f"解析仿真时间失败 {case_name}: {str(e)}")
            return None
    
    def extract_case_name_from_command(self, command: str) -> Optional[str]:
        """从命令行中提取用例名称

        Args:
            command (str): 执行命令

        Returns:
            Optional[str]: 用例名称，失败返回None
        """
        try:
            # 检查是否是BATCH RUN模式的合并命令（包含分号分隔的多个命令）
            if ' ; ' in command:
                # BATCH RUN模式：分号分隔的多个命令
                # 分割命令并获取最后一个命令（这是实际用户选中的case的仿真命令）
                commands = command.split(' ; ')
                if commands:
                    # 使用最后一个命令来提取用例名称
                    last_command = commands[-1].strip()
                    pattern = r'-case\s+(\S+)'
                    match = re.search(pattern, last_command)

                    if match:
                        case_name = match.group(1)
                        return case_name
            else:
                # 普通模式：单个命令
                pattern = r'-case\s+(\S+)'
                match = re.search(pattern, command)

                if match:
                    case_name = match.group(1)
                    return case_name

            return None

        except Exception as e:
            print(f"提取用例名称失败: {str(e)}")
            return None
    
    def on_simulation_command_executed(self, command: str):
        """当仿真命令被执行时调用（异步处理）

        Args:
            command (str): 执行的命令
        """
        try:
            # 使用QTimer异步处理，避免阻塞主线程
            QTimer.singleShot(0, lambda: self._process_simulation_command_async(command))

        except Exception as e:
            print(f"处理仿真命令执行失败: {str(e)}")

    def _process_simulation_command_async(self, command: str):
        """异步处理仿真命令"""
        try:
            # 从命令中提取用例名称
            case_name = self.extract_case_name_from_command(command)

            if case_name:
                # 开始监控仿真
                self.start_simulation_monitoring(case_name, command)

        except Exception as e:
            print(f"异步处理仿真命令失败: {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        # 防止重复清理
        if hasattr(self, '_cleaned_up') and self._cleaned_up:
            return

        try:
            # 标记为已清理，防止重复调用
            self._cleaned_up = True

            # 停止监控定时器
            if hasattr(self, 'monitor_timer'):
                self.monitor_timer.stop()

            # 停止手动检查定时器
            if hasattr(self, 'manual_check_timer'):
                self.manual_check_timer.stop()

            # 清理数据库模型
            if hasattr(self, 'dashboard_model') and self.dashboard_model:
                self.dashboard_model.cleanup()
                self.dashboard_model = None

            # 关闭数据看板窗口
            if self.dashboard_window:
                self.dashboard_window.close()
                self.dashboard_window = None

            print("数据看板控制器清理完成")

        except Exception as e:
            print(f"数据看板控制器清理失败: {str(e)}")
