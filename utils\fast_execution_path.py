#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速执行路径优化模块

提供绕过多层控制器的快速执行路径，减少调用开销
"""

import time
import threading
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from collections import deque

class FastExecutionPath(QObject):
    """快速执行路径管理器"""
    
    # 定义信号
    execution_started = pyqtSignal(str, str)  # case_name, command
    execution_finished = pyqtSignal(str, bool)  # case_name, success
    
    def __init__(self):
        super().__init__()
        self._execution_queue = deque()
        self._active_executions = {}
        self._execution_lock = threading.Lock()
        
        # 性能统计
        self._stats = {
            'total_executions': 0,
            'fast_path_executions': 0,
            'avg_execution_time': 0.0,
            'queue_size': 0
        }
        
        # 队列处理定时器
        self._queue_timer = QTimer()
        self._queue_timer.timeout.connect(self._process_queue)
        self._queue_timer.start(100)  # 每100ms处理一次队列
    
    def execute_fast(self, case_name, command, log_panel=None, execution_panel=None):
        """
        快速执行路径，绕过多层控制器
        
        Args:
            case_name (str): 用例名称
            command (str): 执行命令
            log_panel: 日志面板（可选）
            execution_panel: 执行面板（可选）
            
        Returns:
            bool: 是否成功启动执行
        """
        try:
            start_time = time.time()
            
            # 检查是否已经在执行
            with self._execution_lock:
                if case_name in self._active_executions:
                    print(f"用例 {case_name} 已在执行中，跳过")
                    return False
                
                # 标记为执行中
                self._active_executions[case_name] = {
                    'start_time': start_time,
                    'command': command,
                    'log_panel': log_panel,
                    'execution_panel': execution_panel
                }
            
            # 发射执行开始信号
            self.execution_started.emit(case_name, command)
            
            # 如果提供了日志面板，直接启动执行
            if log_panel:
                self._start_direct_execution(case_name, command, log_panel)
            else:
                # 添加到执行队列
                self._execution_queue.append({
                    'case_name': case_name,
                    'command': command,
                    'execution_panel': execution_panel
                })
            
            self._stats['total_executions'] += 1
            self._stats['fast_path_executions'] += 1
            
            return True
            
        except Exception as e:
            print(f"快速执行路径错误: {e}")
            return False
    
    def _start_direct_execution(self, case_name, command, log_panel):
        """直接启动执行，绕过控制器"""
        try:
            # 直接调用日志面板的执行方法
            if hasattr(log_panel, 'start_execution'):
                log_panel.start_execution()
            elif hasattr(log_panel, 'execute_command'):
                log_panel.execute_command(command)
            else:
                print(f"日志面板不支持直接执行: {case_name}")
                
        except Exception as e:
            print(f"直接执行错误: {e}")
            self._finish_execution(case_name, False)
    
    def _process_queue(self):
        """处理执行队列"""
        if not self._execution_queue:
            return
        
        try:
            # 处理队列中的执行请求
            while self._execution_queue and len(self._active_executions) < 5:  # 限制并发数
                execution_request = self._execution_queue.popleft()
                self._process_execution_request(execution_request)
                
        except Exception as e:
            print(f"处理执行队列错误: {e}")
    
    def _process_execution_request(self, request):
        """处理单个执行请求"""
        case_name = request['case_name']
        command = request['command']
        execution_panel = request['execution_panel']
        
        try:
            if execution_panel and hasattr(execution_panel, 'create_log_panel_fast'):
                # 使用快速创建方法
                log_panel = execution_panel.create_log_panel_fast(case_name, command)
                if log_panel:
                    self._start_direct_execution(case_name, command, log_panel)
                else:
                    self._finish_execution(case_name, False)
            else:
                # 回退到标准路径
                print(f"回退到标准执行路径: {case_name}")
                self._finish_execution(case_name, False)
                
        except Exception as e:
            print(f"处理执行请求错误: {e}")
            self._finish_execution(case_name, False)
    
    def _finish_execution(self, case_name, success):
        """完成执行"""
        with self._execution_lock:
            if case_name in self._active_executions:
                execution_info = self._active_executions.pop(case_name)
                
                # 计算执行时间
                execution_time = time.time() - execution_info['start_time']
                
                # 更新统计信息
                self._update_stats(execution_time)
                
                # 发射完成信号
                self.execution_finished.emit(case_name, success)
    
    def _update_stats(self, execution_time):
        """更新统计信息"""
        total = self._stats['total_executions']
        if total > 0:
            self._stats['avg_execution_time'] = (
                (self._stats['avg_execution_time'] * (total - 1) + execution_time) / total
            )
        
        self._stats['queue_size'] = len(self._execution_queue)
    
    def get_stats(self):
        """获取统计信息"""
        with self._execution_lock:
            return {
                'total_executions': self._stats['total_executions'],
                'fast_path_executions': self._stats['fast_path_executions'],
                'fast_path_ratio': (self._stats['fast_path_executions'] / max(1, self._stats['total_executions'])) * 100,
                'avg_execution_time': self._stats['avg_execution_time'],
                'active_executions': len(self._active_executions),
                'queue_size': len(self._execution_queue)
            }
    
    def is_executing(self, case_name):
        """检查用例是否正在执行"""
        with self._execution_lock:
            return case_name in self._active_executions
    
    def cancel_execution(self, case_name):
        """取消执行"""
        with self._execution_lock:
            if case_name in self._active_executions:
                execution_info = self._active_executions.pop(case_name)
                
                # 尝试停止日志面板
                log_panel = execution_info.get('log_panel')
                if log_panel and hasattr(log_panel, 'stop_execution'):
                    try:
                        log_panel.stop_execution()
                    except Exception as e:
                        print(f"停止执行错误: {e}")
                
                return True
            return False
    
    def clear_queue(self):
        """清空执行队列"""
        self._execution_queue.clear()
    
    def cleanup(self):
        """清理资源"""
        self._queue_timer.stop()
        self.clear_queue()
        
        with self._execution_lock:
            # 尝试停止所有活动执行
            for case_name in list(self._active_executions.keys()):
                self.cancel_execution(case_name)

class BatchExecutionOptimizer:
    """批量执行优化器"""
    
    def __init__(self, fast_execution_path):
        self.fast_path = fast_execution_path
        self._batch_queue = deque()
        self._batch_timer = QTimer()
        self._batch_timer.timeout.connect(self._process_batch)
        self._batch_timer.setSingleShot(True)
        self._batch_size = 5
        self._batch_timeout = 200  # 200ms
    
    def add_to_batch(self, case_name, command, log_panel=None, execution_panel=None):
        """添加到批量执行队列"""
        self._batch_queue.append({
            'case_name': case_name,
            'command': command,
            'log_panel': log_panel,
            'execution_panel': execution_panel
        })
        
        # 如果达到批量大小，立即处理
        if len(self._batch_queue) >= self._batch_size:
            self._process_batch()
        else:
            # 否则启动批量定时器
            if not self._batch_timer.isActive():
                self._batch_timer.start(self._batch_timeout)
    
    def _process_batch(self):
        """处理批量执行"""
        if not self._batch_queue:
            return
        
        self._batch_timer.stop()
        
        # 批量处理所有请求
        batch_items = []
        while self._batch_queue and len(batch_items) < self._batch_size:
            batch_items.append(self._batch_queue.popleft())
        
        # 并行启动执行
        for item in batch_items:
            self.fast_path.execute_fast(
                item['case_name'],
                item['command'],
                item['log_panel'],
                item['execution_panel']
            )

# 全局实例
_global_fast_path = None
_global_batch_optimizer = None

def get_fast_execution_path():
    """获取全局快速执行路径实例"""
    global _global_fast_path
    if _global_fast_path is None:
        _global_fast_path = FastExecutionPath()
    return _global_fast_path

def get_batch_execution_optimizer():
    """获取全局批量执行优化器实例"""
    global _global_batch_optimizer, _global_fast_path
    if _global_batch_optimizer is None:
        if _global_fast_path is None:
            _global_fast_path = FastExecutionPath()
        _global_batch_optimizer = BatchExecutionOptimizer(_global_fast_path)
    return _global_batch_optimizer

def cleanup_fast_execution():
    """清理快速执行路径资源"""
    global _global_fast_path, _global_batch_optimizer
    if _global_fast_path:
        _global_fast_path.cleanup()
        _global_fast_path = None
    if _global_batch_optimizer:
        _global_batch_optimizer = None
