# RunSim 数据看板使用指南

## 📊 功能概述

数据看板是RunSim GUI的核心功能之一，提供仿真执行记录的可视化管理和数据分析功能。通过数据看板，用户可以：

- 📈 实时监控仿真执行状态
- 📋 管理仿真记录和统计数据
- 📊 查看数据可视化图表
- 📤 导出Excel报告
- 📥 导入TestPlan测试计划

## 🚀 快速开始

### 1. 打开数据看板

在RunSim GUI主界面中：
1. 点击菜单栏中的 **"数据看板"**
2. 选择 **"打开数据看板"**
3. 数据看板窗口将会打开

### 2. 演示模式

如果您想快速体验数据看板功能，可以运行演示程序：

```bash
python demo_dashboard.py
```

演示程序会自动创建示例数据并打开数据看板窗口。

## 📋 主要功能

### 仿真记录管理

#### 记录表格
- **序号**：记录的唯一编号
- **用例名称**：从runsim命令的-case参数解析获得
- **用例状态**：
  - `Pending`：等待执行
  - `On-Going`：正在执行
  - `PASS`：执行成功
  - `FAIL`：执行失败
- **开始时间**：仿真开始时间
- **结束时间**：仿真结束时间
- **编译耗时**：编译时间（分钟）
- **仿真耗时**：仿真时间（分钟）
- **仿真阶段**：DVR1/DVR2/DVR3/DVS1/DVS2

#### 自动监控
数据看板会自动监控仿真执行过程：
1. 当点击"执行仿真和编译"按钮时，自动创建记录
2. 实时监控日志文件变化
3. 自动解析编译和仿真时间
4. 根据日志内容判断执行结果

### 数据过滤和搜索

#### 状态过滤
使用状态下拉框过滤记录：
- **全部**：显示所有记录
- **Pending**：仅显示等待执行的记录
- **On-Going**：仅显示正在执行的记录
- **PASS**：仅显示成功的记录
- **FAIL**：仅显示失败的记录

#### 搜索功能
在搜索框中输入用例名称关键字，实时过滤匹配的记录。

### 📊 数据可视化

数据看板提供多种图表类型（需要安装matplotlib）：

#### 1. 每日用例通过数柱状图
- 显示每天通过的用例数量
- 帮助了解每日测试进度

#### 2. 每日通过用例总数折线图
- 显示累计通过用例数量趋势
- 反映整体测试进展

#### 3. 用例状态分布饼图
- 显示各状态用例的比例分布
- 快速了解测试完成情况

#### 4. 编译时间趋势图
- 显示平均编译时间变化趋势
- 帮助识别性能问题

#### 图表控制
- **图表类型**：选择要显示的图表类型
- **时间范围**：选择数据时间范围（7天/30天/90天）
- **更新图表**：手动刷新图表数据

### 📤 数据导出

#### Excel导出功能
1. 点击 **"导出Excel"** 按钮
2. 选择保存位置和文件名
3. 系统会生成包含以下内容的Excel文件：
   - **仿真记录**工作表：所有仿真记录详细信息
   - **每日统计**工作表：每日汇总统计数据

#### 导出内容
- 完整的仿真记录数据
- 格式化的时间信息
- 彩色状态标识
- 自动调整的列宽

### 📥 TestPlan导入

#### 支持的格式
- Excel文件（.xlsx, .xls）
- 包含TP或TestPlan工作表
- 符合标准TestPlan格式

#### 导入步骤
1. 点击 **"导入TestPlan"** 按钮
2. 选择TestPlan Excel文件
3. 系统自动解析用例信息
4. 将用例添加到数据看板

#### TestPlan格式要求
- 包含用例名称列（TestCase Name或类似标题）
- 用例名称不能为空
- 支持注释行（以#开头的行会被跳过）

## ⚙️ 配置和设置

### 数据库配置
- 数据库文件位置：`work/runsim_dashboard.db`
- 自动创建表结构
- 支持SQLite数据库

### 自动刷新
- 数据每30秒自动刷新一次
- 日志监控每5秒检查一次
- 可手动点击"刷新数据"按钮

### 数据清理
通过菜单 **"数据看板" -> "清除历史数据"** 可以清理历史记录。

## 🔧 依赖安装

### 必需依赖
数据看板的基本功能需要以下Python包：
- PyQt5（GUI框架）
- sqlite3（数据库，Python内置）

### 可选依赖
图表功能需要额外安装：

```bash
# 安装图表依赖
python install_dashboard_dependencies.py

# 或手动安装
pip install matplotlib pandas numpy openpyxl
```

### 依赖说明
- **matplotlib**：图表绘制
- **pandas**：数据处理
- **numpy**：数值计算
- **openpyxl**：Excel文件处理

## 🐛 故障排除

### 常见问题

#### 1. 图表不显示
**原因**：matplotlib未安装
**解决**：运行 `python install_dashboard_dependencies.py`

#### 2. Excel导出失败
**原因**：openpyxl未安装
**解决**：运行 `pip install openpyxl`

#### 3. 数据库错误
**原因**：数据库文件权限问题
**解决**：检查work目录的读写权限

#### 4. 仿真监控不工作
**原因**：日志路径不正确
**解决**：确保仿真在正确的目录下执行

### 日志调试
程序运行时会在控制台输出调试信息，可以通过这些信息定位问题。

## 📈 最佳实践

### 1. 数据管理
- 定期导出重要数据
- 适时清理历史记录
- 备份数据库文件

### 2. 性能优化
- 避免同时监控过多用例
- 定期清理过期数据
- 合理设置刷新间隔

### 3. 使用建议
- 使用状态过滤提高查找效率
- 利用图表分析测试趋势
- 定期导出报告进行分析

## 🔄 版本更新

### 当前版本功能
- ✅ 基础记录管理
- ✅ 自动状态监控
- ✅ 数据可视化图表
- ✅ Excel导入导出
- ✅ TestPlan导入

### 计划功能
- 🔄 更多图表类型
- 🔄 高级数据分析
- 🔄 自定义报告模板
- 🔄 数据同步功能

## 📞 技术支持

如果在使用过程中遇到问题：

1. 查看本使用指南
2. 检查控制台错误信息
3. 运行演示程序验证功能
4. 联系开发团队获取支持

---

**最后更新**：2024年12月
**版本**：1.0.0
**兼容性**：RunSim GUI v2.0+
