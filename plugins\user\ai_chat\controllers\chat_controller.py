"""
聊天控制器

管理聊天逻辑和数据流
"""

import os
from PyQt5.QtCore import QObject, pyqtSlot, QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtWidgets import QDialog
from ..models.chat_model import ChatModel
from ..models.config_model import ConfigModel
from ..models.document_model import DocumentModel
from ..utils.api_client import APIClient
from ..utils.translator import Translator
from ..utils.document_parser import DocumentParser


class ChatController(QObject):
    """聊天控制器"""
    
    def __init__(self, chat_window):
        super().__init__()
        self.chat_window = chat_window
        
        # 初始化模型
        self.chat_model = ChatModel()
        self.config_model = ConfigModel()
        self.document_model = DocumentModel()
        
        # 初始化工具
        self.api_client = APIClient()
        self.translator = Translator(self.api_client)

        # 初始化主题管理器
        from ..utils.theme_manager import ThemeManager
        self.theme_manager = ThemeManager()

        # 初始化性能监控器
        from ..utils.model_monitor import ModelPerformanceMonitor
        self.performance_monitor = None  # 延迟初始化
        
        # 当前流式请求工作线程
        self.current_stream_worker = None
        
        # 连接信号
        self._connect_signals()
        
        # 初始化API客户端配置
        self._update_api_config()

        # 初始化界面数据
        self._initialize_ui_data()

        # 应用当前主题
        self._apply_current_theme()

        # 延迟加载重型组件
        self._init_heavy_components_async()

    def _init_heavy_components_async(self):
        """异步初始化重型组件"""
        import threading
        from PyQt5.QtCore import QTimer

        def init_worker():
            try:
                # 预加载文档解析器
                from ..utils.document_parser import DocumentParser
                DocumentParser._load_parsers()

                # 预加载其他可能的重型组件
                print("[AI聊天助手] 后台组件加载完成")
            except Exception as e:
                print(f"[ERROR] 初始化重型组件失败: {e}")

        def init_performance_monitor():
            """在主线程中初始化性能监控器"""
            try:
                from ..utils.model_monitor import ModelPerformanceMonitor
                self.performance_monitor = ModelPerformanceMonitor()
                self.performance_monitor.start_monitoring()
                print("[AI聊天助手] 性能监控器已启动")
            except Exception as e:
                print(f"[ERROR] 初始化性能监控器失败: {e}")

        # 创建并启动后台线程处理非Qt组件
        thread = threading.Thread(target=init_worker, name="AIChatHeavyInit")
        thread.daemon = True
        thread.start()

        # 使用QTimer在主线程中初始化性能监控器
        QTimer.singleShot(1000, init_performance_monitor)

    def _initialize_ui_data(self):
        """初始化界面数据"""
        try:
            # 加载所有历史会话到界面
            all_sessions = self.chat_model.get_all_sessions()

            for session in all_sessions:
                # 添加会话到界面（避免重复添加）
                if not self.chat_window.chat_list.has_session(session.session_id):
                    self.chat_window.add_chat_session(session)

            # 如果有当前会话，切换到当前会话并加载消息
            current_session = self.chat_model.current_session
            if current_session:
                self.chat_window.switch_to_session(current_session.session_id)
                self.chat_window.load_chat_messages(current_session.messages)
            elif all_sessions:
                # 如果没有当前会话但有历史会话，选择最新的会话
                latest_session = max(all_sessions, key=lambda s: s.updated_at)
                self.chat_model.set_current_session(latest_session.session_id)
                self.chat_window.switch_to_session(latest_session.session_id)
                self.chat_window.load_chat_messages(latest_session.messages)
            else:
                # 如果没有任何会话，创建一个新会话
                session = self.chat_model.create_new_session("新对话")
                self.chat_window.switch_to_session(session.session_id)

        except Exception as e:
            print(f"[ERROR] 初始化界面数据失败: {e}")
            # 创建一个新会话作为备用
            try:
                session = self.chat_model.create_new_session("新对话")
                self.chat_window.switch_to_session(session.session_id)
            except Exception as e2:
                print(f"[ERROR] 创建备用会话失败: {e2}")

    def _apply_current_theme(self):
        """应用当前主题"""
        try:
            current_theme = self.theme_manager.get_current_theme()
            self.theme_manager.set_theme(current_theme)
        except Exception as e:
            print(f"[ERROR] 应用主题失败: {e}")

    def _connect_signals(self):
        """连接信号和槽"""
        # 聊天窗口信号
        self.chat_window.message_sent.connect(self.send_message)
        self.chat_window.new_chat_requested.connect(self.create_new_chat)
        self.chat_window.chat_selected.connect(self.switch_chat)
        self.chat_window.chat_deleted.connect(self.delete_chat)
        self.chat_window.clear_all_chats_requested.connect(self.clear_all_chats)
        self.chat_window.settings_requested.connect(self.show_settings)
        self.chat_window.document_uploaded.connect(self.upload_document)
        self.chat_window.document_removed.connect(self.remove_document)
        self.chat_window.translation_requested.connect(self.translate_text)
        self.chat_window.stop_generation_requested.connect(self.stop_generation)
        
        # 配置模型信号
        self.config_model.config_changed.connect(self._on_config_changed)
        
        # 聊天模型信号
        self.chat_model.session_added.connect(self._on_session_added)
        self.chat_model.session_updated.connect(self._on_session_updated)
        self.chat_model.session_deleted.connect(self._on_session_deleted)
        self.chat_model.message_added.connect(self._on_message_added)
        
        # 文档模型信号
        self.document_model.document_added.connect(self._on_document_added)
        self.document_model.document_processed.connect(self._on_document_processed)
        self.document_model.document_removed.connect(self._on_document_removed)
        self.document_model.processing_error.connect(self._on_document_error)
        
        # 翻译器信号
        self.translator.translation_started.connect(self._on_translation_started)
        self.translator.translation_finished.connect(self._on_translation_finished)
        self.translator.translation_error.connect(self._on_translation_error)
    
    def _update_api_config(self):
        """更新API配置"""
        config = self.config_model.get_all()
        self.api_client.set_config(config)
    
    @pyqtSlot(str)
    def send_message(self, message: str):
        """发送消息"""
        if not message.strip():
            return
        
        # 检查配置
        if not self.config_model.is_valid():
            self.chat_window.show_error("请先在设置中配置API信息")
            return
        
        # 添加用户消息
        user_message = self.chat_model.add_message('user', message)
        
        # 准备API请求
        self._send_api_request()
    
    def _send_api_request(self):
        """发送API请求"""
        try:
            # 获取当前会话
            current_session = self.chat_model.current_session
            if not current_session:
                return

            # 准备消息列表
            messages = current_session.get_messages_for_api()

            # 清理和验证消息格式
            cleaned_messages = self._clean_and_validate_messages(messages)

            # 添加文档上下文（只包含选中的文档）
            doc_context = self.document_model.get_content_for_context(selected_only=True)
            if doc_context:
                system_message = {
                    "role": "system",
                    "content": f"以下是用户上传的文档内容，请在回答问题时参考这些内容：\n\n{doc_context}"
                }
                cleaned_messages.insert(0, system_message)

            # 检查是否启用流式响应
            if self.config_model.get('stream', True):
                self._send_stream_request(cleaned_messages)
            else:
                self._send_normal_request(cleaned_messages)

        except Exception as e:
            self.chat_window.show_error(f"发送请求失败: {str(e)}")

    def _clean_and_validate_messages(self, messages):
        """清理和验证消息格式"""
        cleaned_messages = []

        for msg in messages:
            # 确保消息格式正确
            if not isinstance(msg, dict):
                print(f"[WARNING] 跳过非字典消息: {msg}")
                continue

            role = msg.get('role', '').strip()
            content = msg.get('content', '').strip()

            # 验证角色
            if role not in ['user', 'assistant', 'system']:
                print(f"[WARNING] 跳过无效角色的消息: {role}")
                continue

            # 验证内容
            if not content:
                print(f"[WARNING] 跳过空内容的消息")
                continue

            # 清理内容中可能的格式问题
            content = self._clean_message_content(content)

            cleaned_messages.append({
                'role': role,
                'content': content
            })

        return cleaned_messages

    def _clean_message_content(self, content):
        """清理消息内容"""
        # 移除可能的重复前缀
        prefixes_to_remove = [
            "User says: '",
            "Assistant says: '",
            "user: ",
            "assistant: ",
            "User: ",
            "Assistant: "
        ]

        for prefix in prefixes_to_remove:
            if content.startswith(prefix):
                content = content[len(prefix):]
                # 移除可能的结尾引号
                if content.endswith("'"):
                    content = content[:-1]
                break

        # 移除多余的空白字符
        content = content.strip()

        # 移除重复的换行符
        while '\n\n\n' in content:
            content = content.replace('\n\n\n', '\n\n')

        return content
    
    def _send_stream_request(self, messages):
        """发送流式请求"""
        # 取消当前请求
        self.stop_generation()
        
        # 添加助手消息占位符
        assistant_message = self.chat_model.add_message('assistant', '')
        
        # 创建流式工作线程
        self.current_stream_worker = self.api_client.stream_chat_completion_async(messages)
        
        # 连接信号
        self.current_stream_worker.chunk_received.connect(
            lambda chunk: self._on_stream_chunk_received(assistant_message, chunk)
        )
        self.current_stream_worker.finished.connect(
            lambda success, result: self._on_stream_finished(assistant_message, success, result)
        )
        self.current_stream_worker.error_occurred.connect(
            lambda error: self._on_stream_error(assistant_message, error)
        )
        
        # 启动请求
        self.current_stream_worker.start()
        
        # 更新UI状态
        self.chat_window.set_generating(True)
    
    def _send_normal_request(self, messages):
        """发送普通请求（异步）"""
        # 显示加载状态
        self.chat_window.set_generating(True)

        # 添加助手消息占位符
        assistant_message = self.chat_model.add_message('assistant', '')

        # 创建异步请求工作器
        self.current_request_worker = ChatRequestWorker(self.api_client, messages)

        # 连接信号
        self.current_request_worker.request_finished.connect(
            lambda success, result: self._on_normal_request_finished(assistant_message, success, result),
            Qt.QueuedConnection
        )
        self.current_request_worker.error_occurred.connect(
            lambda error: self._on_normal_request_error(assistant_message, error),
            Qt.QueuedConnection
        )

        # 启动异步请求
        self.current_request_worker.start()

    def _on_normal_request_finished(self, message, success, result):
        """普通请求完成回调"""
        try:
            if success:
                # 更新消息内容
                message.content = result
                self.chat_window.update_message_content(message.id, result)
            else:
                # 处理失败
                self.chat_window.show_error(f"API请求失败: {result}")
                # 移除占位符消息
                self.chat_model.remove_message(message.id)

            # 隐藏加载状态
            self.chat_window.set_generating(False)
            self.current_request_worker = None

        except Exception as e:
            print(f"普通请求完成回调错误: {e}")
            self.chat_window.show_error(f"处理响应失败: {str(e)}")
            self.chat_window.set_generating(False)

    def _on_normal_request_error(self, message, error):
        """普通请求错误回调"""
        try:
            self.chat_window.show_error(f"API请求错误: {error}")
            # 移除占位符消息
            self.chat_model.remove_message(message.id)

            # 隐藏加载状态
            self.chat_window.set_generating(False)
            self.current_request_worker = None

        except Exception as e:
            print(f"普通请求错误回调失败: {e}")
    
    def _on_stream_chunk_received(self, message, chunk):
        """处理流式响应块"""
        # 确保chunk是字符串类型并正确处理中文
        if isinstance(chunk, bytes):
            chunk = chunk.decode('utf-8', errors='ignore')
        elif not isinstance(chunk, str):
            chunk = str(chunk)

        message.content += chunk
        self.chat_window.update_message_content(message.id, message.content)
    
    def _on_stream_finished(self, message, success, result):
        """流式响应完成"""
        if success:
            # 确保result是正确的字符串格式
            if isinstance(result, bytes):
                result = result.decode('utf-8', errors='ignore')
            elif not isinstance(result, str):
                result = str(result)
            message.content = result
        else:
            self.chat_window.show_error(f"生成失败: {result}")

        self.chat_window.set_generating(False)
        self.current_stream_worker = None
    
    def _on_stream_error(self, message, error):
        """流式响应错误"""
        self.chat_window.show_error(f"生成错误: {error}")
        self.chat_window.set_generating(False)
        self.current_stream_worker = None
    
    @pyqtSlot()
    def stop_generation(self):
        """停止生成"""
        # 停止流式请求
        if self.current_stream_worker:
            self.current_stream_worker.cancel()
            self.current_stream_worker = None

        # 停止普通请求
        if hasattr(self, 'current_request_worker') and self.current_request_worker:
            self.current_request_worker.cancel()
            self.current_request_worker = None

        self.chat_window.set_generating(False)
    
    @pyqtSlot()
    def create_new_chat(self):
        """创建新聊天"""
        # 总是创建新会话，不复用空会话
        session = self.chat_model.create_new_session("新对话")

        # 切换到新会话
        self.chat_window.switch_to_session(session.session_id)
        self.chat_window.load_chat_messages(session.messages)  # 加载空消息列表
    
    @pyqtSlot(str)
    def switch_chat(self, session_id: str):
        """切换聊天"""
        # 设置当前会话
        self.chat_model.set_current_session(session_id)
        session = self.chat_model.get_session(session_id)

        if session:
            # 确保界面选中正确的会话
            self.chat_window.switch_to_session(session_id)
            # 加载消息
            self.chat_window.load_chat_messages(session.messages)
        else:
            print(f"[ERROR] 找不到会话: {session_id}")
            self.chat_window.show_error(f"找不到会话: {session_id}")
    
    @pyqtSlot(str)
    def delete_chat(self, session_id: str):
        """删除聊天"""
        self.chat_model.delete_session(session_id)

    @pyqtSlot()
    def clear_all_chats(self):
        """清空所有聊天记录"""
        try:
            # 清空所有数据
            self.chat_model.clear_all_data()

            # 清空界面显示
            self.chat_window.load_chat_messages([])

            # 创建一个新的默认会话
            session = self.chat_model.create_new_session("新对话")
            self.chat_window.switch_to_session(session.session_id)

        except Exception as e:
            print(f"[ERROR] 清空聊天记录失败: {e}")
            self.chat_window.show_error(f"清空聊天记录失败: {str(e)}")
    
    @pyqtSlot()
    def show_settings(self):
        """显示设置"""
        from ..views.settings_dialog import SettingsDialog
        
        dialog = SettingsDialog(self.config_model, self.chat_window)
        if dialog.exec_() == dialog.Accepted:
            # 配置已更新，重新加载API配置
            self._update_api_config()
    
    @pyqtSlot(str)
    def upload_document(self, file_path: str):
        """上传文档"""
        try:
            # 预先验证文件
            if not self._validate_document_file(file_path):
                return

            # 添加文档到模型
            doc_info = self.document_model.add_document(file_path)
            if not doc_info:
                self.chat_window.show_error("文档添加失败")
                return

            # 显示上传进度
            self.chat_window.show_document_upload_progress(doc_info.doc_id, "正在解析文档...")

            # 异步解析文档
            self._process_document_async(doc_info)

        except Exception as e:
            self.chat_window.show_error(f"文档上传失败: {str(e)}")

    def _validate_document_file(self, file_path: str) -> bool:
        """验证文档文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.chat_window.show_error("文件不存在")
                return False

            # 检查文件大小（限制为50MB）
            file_size = os.path.getsize(file_path)
            max_size = 50 * 1024 * 1024  # 50MB
            if file_size > max_size:
                self.chat_window.show_error(f"文件过大，最大支持{max_size // (1024*1024)}MB")
                return False

            # 检查文件格式
            allowed_extensions = {'.pdf', '.docx', '.doc', '.txt', '.md', '.rtf'}
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in allowed_extensions:
                self.chat_window.show_error(f"不支持的文件格式: {file_ext}")
                return False

            # 检查文件是否可读
            try:
                with open(file_path, 'rb') as f:
                    f.read(1024)  # 尝试读取前1KB
            except Exception:
                self.chat_window.show_error("文件无法读取")
                return False

            return True

        except Exception as e:
            self.chat_window.show_error(f"文件验证失败: {str(e)}")
            return False

    def _process_document_async(self, doc_info):
        """异步处理文档"""
        try:
            # 创建文档处理工作器
            worker = DocumentProcessingWorker(doc_info)

            # 连接信号到主线程的槽函数
            worker.progress_updated.connect(
                self.chat_window.update_document_upload_progress,
                Qt.QueuedConnection
            )
            worker.processing_finished.connect(
                self._on_document_processing_finished,
                Qt.QueuedConnection
            )
            worker.error_occurred.connect(
                self._on_document_processing_error,
                Qt.QueuedConnection
            )

            # 存储工作器引用，防止被垃圾回收
            if not hasattr(self, '_document_workers'):
                self._document_workers = {}
            self._document_workers[doc_info.doc_id] = worker

            # 启动处理
            worker.start_processing()

        except Exception as e:
            self.chat_window.show_error(f"启动文档处理失败: {str(e)}")
            self.chat_window.hide_document_upload_progress(doc_info.doc_id)

    def _on_document_processing_finished(self, doc_id, success, content, summary):
        """文档处理完成回调"""
        try:
            if success:
                # 更新文档内容
                self.document_model.update_document_content(doc_id, content, summary)
                self.chat_window.hide_document_upload_progress(doc_id)
                self.chat_window.status_bar.showMessage(f"文档处理完成", 3000)
            else:
                # 处理失败但没有具体错误信息
                self.document_model.set_document_error(doc_id, "文档处理失败")
                self.chat_window.hide_document_upload_progress(doc_id)
                self.chat_window.show_error("文档处理失败")

            # 清理工作器引用
            if hasattr(self, '_document_workers') and doc_id in self._document_workers:
                del self._document_workers[doc_id]

        except Exception as e:
            print(f"文档处理完成回调错误: {e}")
            self.chat_window.show_error(f"文档处理回调失败: {str(e)}")

    def _on_document_processing_error(self, doc_id, error_message):
        """文档处理错误回调"""
        try:
            # 设置错误状态
            self.document_model.set_document_error(doc_id, error_message)
            self.chat_window.hide_document_upload_progress(doc_id)
            self.chat_window.show_error(f"文档处理失败: {error_message}")

            # 清理工作器引用
            if hasattr(self, '_document_workers') and doc_id in self._document_workers:
                del self._document_workers[doc_id]

        except Exception as e:
            print(f"文档处理错误回调失败: {e}")
    
    @pyqtSlot(str)
    def remove_document(self, doc_id: str):
        """移除文档"""
        self.document_model.remove_document(doc_id)
    
    @pyqtSlot(str, str)
    def translate_text(self, text: str, target_lang: str = None):
        """翻译文本"""
        if not self.config_model.get('translation_enabled', True):
            self.chat_window.show_error("翻译功能已禁用")
            return
        
        self.translator.translate_text(text, target_lang)
    
    # 信号处理方法
    def _on_config_changed(self, key, value):
        """配置变更处理"""
        if key in ['api_key', 'api_url', 'model', 'model_provider']:
            self._update_api_config()
    
    def _on_session_added(self, session):
        """会话添加处理"""
        # 只有当会话不存在时才添加，避免重复
        if not self.chat_window.chat_list.has_session(session.session_id):
            self.chat_window.add_chat_session(session)
    
    def _on_session_updated(self, session):
        """会话更新处理"""
        self.chat_window.update_chat_session(session)
    
    def _on_session_deleted(self, session_id):
        """会话删除处理"""
        self.chat_window.remove_chat_session(session_id)

        # 如果删除后有新的当前会话，切换到新会话
        current_session = self.chat_model.current_session
        if current_session:
            self.chat_window.switch_to_session(current_session.session_id)
            self.chat_window.load_chat_messages(current_session.messages)
        else:
            # 如果没有会话了，清空消息显示
            self.chat_window.load_chat_messages([])
    
    def _on_message_added(self, message):
        """消息添加处理"""
        self.chat_window.add_message(message)
    
    def _on_document_added(self, doc_info):
        """文档添加处理"""
        self.chat_window.add_document(doc_info)
    
    def _on_document_processed(self, doc_info):
        """文档处理完成"""
        self.chat_window.update_document_status(doc_info)
    
    def _on_document_removed(self, doc_id):
        """文档移除处理"""
        self.chat_window.remove_document(doc_id)
    
    def _on_document_error(self, doc_id, error):
        """文档处理错误"""
        self.chat_window.show_document_error(doc_id, error)
    
    def _on_translation_started(self, text):
        """翻译开始"""
        self.chat_window.show_translation_status("正在翻译...")
    
    def _on_translation_finished(self, original, translated, target_lang):
        """翻译完成"""
        # 检查是否有打开的翻译对话框
        for widget in self.chat_window.findChildren(QDialog):
            if hasattr(widget, 'show_translation_result'):
                widget.show_translation_result(original, translated, target_lang)
                return

        # 如果没有翻译对话框，使用原来的方式显示
        self.chat_window.show_translation_result(original, translated, target_lang)
    
    def _on_translation_error(self, error):
        """翻译错误"""
        self.chat_window.show_error(f"翻译失败: {error}")
    
    def cleanup(self):
        """清理资源"""
        # 停止当前生成
        self.stop_generation()

        # 清理性能监控器
        if hasattr(self, 'performance_monitor') and self.performance_monitor:
            try:
                self.performance_monitor.stop_monitoring()
                self.performance_monitor = None
                print("[AI聊天助手] 性能监控器已停止")
            except Exception as e:
                print(f"[ERROR] 停止性能监控器失败: {e}")

        # 清理翻译器
        if self.translator:
            self.translator.cleanup()

        # 清理文档处理工作器
        if hasattr(self, '_document_workers'):
            for worker in self._document_workers.values():
                worker.stop_processing()
            self._document_workers.clear()

        # 清理请求工作器
        if hasattr(self, 'current_request_worker') and self.current_request_worker:
            self.current_request_worker.cancel()
            self.current_request_worker = None

        # 保存数据
        self.chat_model.save_data()
        self.config_model.save_config()


class DocumentProcessingWorker(QObject):
    """安全的文档处理工作器"""

    # 信号定义
    progress_updated = pyqtSignal(str, str)  # doc_id, status
    processing_finished = pyqtSignal(str, bool, str, str)  # doc_id, success, content, summary
    error_occurred = pyqtSignal(str, str)  # doc_id, error_message

    def __init__(self, doc_info):
        super().__init__()
        self.doc_info = doc_info
        self.thread = None
        self.is_cancelled = False

    def start_processing(self):
        """启动文档处理"""
        try:
            # 创建工作线程
            self.thread = QThread()
            self.moveToThread(self.thread)

            # 连接信号
            self.thread.started.connect(self._process_document)
            self.thread.finished.connect(self.thread.deleteLater)

            # 启动线程
            self.thread.start()

        except Exception as e:
            self.error_occurred.emit(self.doc_info.doc_id, f"启动处理线程失败: {str(e)}")

    def stop_processing(self):
        """停止文档处理"""
        self.is_cancelled = True
        if self.thread and self.thread.isRunning():
            self.thread.quit()
            self.thread.wait(5000)  # 等待最多5秒

    def _process_document(self):
        """处理文档（在工作线程中执行）"""
        try:
            if self.is_cancelled:
                return

            # 发送进度更新
            self.progress_updated.emit(self.doc_info.doc_id, "正在读取文档...")

            # 检查文件是否仍然存在
            if not os.path.exists(self.doc_info.file_path):
                self.error_occurred.emit(self.doc_info.doc_id, "文件不存在")
                return

            if self.is_cancelled:
                return

            # 解析文档内容
            try:
                success, content, error = DocumentParser.parse_document(self.doc_info.file_path)
            except Exception as e:
                self.error_occurred.emit(self.doc_info.doc_id, f"文档解析失败: {str(e)}")
                return

            if self.is_cancelled:
                return

            if success and content:
                # 发送进度更新
                self.progress_updated.emit(self.doc_info.doc_id, "正在清理文档内容...")

                try:
                    # 清理内容
                    clean_content = DocumentParser.clean_text(content)

                    if self.is_cancelled:
                        return

                    # 生成摘要
                    if len(clean_content) > 1000:
                        self.progress_updated.emit(self.doc_info.doc_id, "正在生成文档摘要...")
                        # 简单截取前500字符作为摘要，避免API调用
                        summary = clean_content[:500] + "..." if len(clean_content) > 500 else clean_content
                    else:
                        summary = clean_content

                    if self.is_cancelled:
                        return

                    # 发送完成信号
                    self.processing_finished.emit(self.doc_info.doc_id, True, clean_content, summary)

                except Exception as e:
                    self.error_occurred.emit(self.doc_info.doc_id, f"内容处理失败: {str(e)}")
            else:
                # 解析失败
                error_msg = error if error else "文档解析失败"
                self.error_occurred.emit(self.doc_info.doc_id, error_msg)

        except Exception as e:
            # 捕获所有未处理的异常
            self.error_occurred.emit(self.doc_info.doc_id, f"处理过程中发生错误: {str(e)}")

        finally:
            # 确保线程正确退出
            if self.thread:
                self.thread.quit()


class ChatRequestWorker(QObject):
    """聊天请求工作器（异步处理普通API请求）"""

    # 信号定义
    request_finished = pyqtSignal(bool, str)  # success, result
    error_occurred = pyqtSignal(str)  # error_message

    def __init__(self, api_client, messages):
        super().__init__()
        self.api_client = api_client
        self.messages = messages
        self.thread = None
        self.is_cancelled = False

    def start(self):
        """启动异步请求"""
        try:
            # 创建工作线程
            self.thread = QThread()
            self.moveToThread(self.thread)

            # 连接信号
            self.thread.started.connect(self._make_request)
            self.thread.finished.connect(self.thread.deleteLater)

            # 启动线程
            self.thread.start()

        except Exception as e:
            self.error_occurred.emit(f"启动请求线程失败: {str(e)}")

    def cancel(self):
        """取消请求"""
        self.is_cancelled = True
        if self.thread and self.thread.isRunning():
            self.thread.quit()
            self.thread.wait(5000)  # 等待最多5秒

    def _make_request(self):
        """执行API请求（在工作线程中）"""
        try:
            if self.is_cancelled:
                return

            # 发送API请求
            response = self.api_client.chat_completion(self.messages)

            if self.is_cancelled:
                return

            # 发送结果信号
            if response.success:
                self.request_finished.emit(True, response.data)
            else:
                self.request_finished.emit(False, response.error)

        except Exception as e:
            if not self.is_cancelled:
                self.error_occurred.emit(f"API请求异常: {str(e)}")

        finally:
            # 确保线程正确退出
            if self.thread:
                self.thread.quit()
