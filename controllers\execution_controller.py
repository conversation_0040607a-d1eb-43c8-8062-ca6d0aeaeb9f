"""
执行控制器
"""
import os
import sys
import subprocess
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal

from views.execution_panel import ExecutionPanel
from utils.path_resolver import PathResolver
from utils.event_bus import EventBus
from utils.task_queue_manager import TaskQueueManager
from utils.task_queue_core import TaskPriority, TaskMetadata
from views.task_monitor_panel import TaskMonitorPanel

class ExecutionController(QObject):
    """执行控制器，负责管理命令执行和日志显示"""

    # 定义信号
    execution_requested = pyqtSignal(str, str)  # command, case_name

    def __init__(self, main_window, config_model, history_model):
        """
        初始化执行控制器

        Args:
            main_window (MainWindow): 主窗口
            config_model (ConfigModel): 配置模型
            history_model (HistoryModel): 历史记录模型
        """
        super().__init__()
        self.main_window = main_window
        self.config_model = config_model
        self.history_model = history_model
        self.path_resolver = PathResolver()

        # 获取事件总线实例
        self.event_bus = EventBus.instance()

        # 创建执行面板
        self.execution_panel = ExecutionPanel()

        # 创建任务队列管理器
        self.task_queue_manager = TaskQueueManager()

        # 创建任务监控面板
        self.task_monitor_panel = TaskMonitorPanel(self.task_queue_manager)

        # 队列模式标志
        self.queue_mode_enabled = False

        # 配置控制器引用（稍后设置）
        self.config_controller = None

        # 连接信号
        self.connect_signals()

    def set_config_controller(self, config_controller):
        """
        设置配置控制器引用

        Args:
            config_controller: 配置控制器实例
        """
        self.config_controller = config_controller

    def connect_signals(self):
        """连接信号和槽"""
        # 执行面板信号
        self.execution_panel.open_verdi_requested.connect(self.open_verdi)
        self.execution_panel.open_verisium_requested.connect(self.open_verisium)
        self.execution_panel.open_compile_log_requested.connect(self.open_compile_log)
        self.execution_panel.open_sim_log_requested.connect(self.open_sim_log)
        self.execution_panel.open_asm_file_requested.connect(self.open_asm_file)
        self.execution_panel.close_tab_requested.connect(self.close_tab)

        # 执行请求信号
        self.execution_requested.connect(self.execute_command)

        # 使用事件总线连接命令执行信号
        try:
            self.event_bus.command_executed.connect(self.execute_command)
        except Exception as e:
            print(f"警告: 连接事件总线命令执行信号时出错: {str(e)}")

    def on_config_loaded(self, config):
        """
        配置加载后的处理

        Args:
            config (dict): 配置数据
        """
        # 这里可以处理与执行相关的配置
        pass

    def _launch_tool_with_nohup(self, case_dir, tool_name, command, script_name=None):
        """
        使用nohup启动外部工具，确保在终端关闭后工具仍能继续运行

        Args:
            case_dir (str): 用例目录
            tool_name (str): 工具名称，用于显示消息
            command (str): 要执行的命令
            script_name (str, optional): 脚本文件名，默认为"launch_{tool_name}.sh"
        """
        if script_name is None:
            script_name = f"launch_{tool_name.lower()}.sh"

        # 创建启动脚本
        script_path = os.path.join(case_dir, script_name)
        with open(script_path, "w") as f:
            f.write("#!/bin/csh\n")
            f.write("cd $(dirname \"$0\")\n")  # 切换到脚本所在目录

            # 检查命令是否已经是脚本（如run_verdi, run_verdi_vcs, run_verisium）
            if command.startswith("run_"):
                # 如果是已有脚本，直接执行它（不使用nohup，因为脚本内部可能已经处理了后台运行）
                f.write(f"./{command} > {tool_name.lower()}_launch.log 2>&1\n")
            else:
                # 否则直接执行命令
                f.write(f"{command} > {tool_name.lower()}_launch.log 2>&1\n")

        # 设置脚本可执行权限
        os.chmod(script_path, 0o755)

        # 直接在后台执行脚本，不依赖终端
        try:
            # 使用nohup直接在后台执行脚本，确保即使终端关闭也能继续运行
            subprocess.Popen(
                ['nohup', 'csh', script_path],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                shell=False,
                start_new_session=True  # 创建新会话，与父进程完全分离
            )

            # 显示启动消息
            print(f"{tool_name}已在后台启动，可以在{case_dir}目录查看{tool_name.lower()}_launch.log日志")

        except Exception as e:
            print(f"启动工具时出错: {str(e)}")
            # 尝试其他方法启动
            try:
                # 直接执行命令，不使用终端
                full_cmd = f"cd {case_dir} && {command}"
                subprocess.Popen(
                    ['csh', '-c', full_cmd],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    shell=False,
                    start_new_session=True  # 创建新会话，与父进程完全分离
                )
            except Exception as e2:
                print(f"备用启动方法也失败: {str(e2)}")

    @pyqtSlot(str, str)
    def execute_command(self, command, case_name):
        """
        执行命令

        Args:
            command (str): 命令字符串
            case_name (str): 用例名称
        """
        # 检查是否启用队列模式
        if self.queue_mode_enabled:
            self.execute_command_with_queue(command, case_name)
        else:
            self.execute_command_direct(command, case_name)

    def execute_command_direct(self, command, case_name):
        """直接执行命令（原有方式）"""
        # 导入命令解析器
        from utils.command_generator import CommandParser

        # 如果没有指定用例名称，尝试从命令中提取
        if not case_name:
            case_name = CommandParser.parse_case_from_command(command)

        # 如果仍然没有用例名称，使用时间戳或其他标识
        if not case_name:
            import time
            case_name = f"执行_{int(time.time())}"

        # 确定标签页名称：
        # 1. 对于回归命令，使用回归列表文件名（去掉路径和后缀）
        # 2. 否则优先使用rundir
        # 3. 如果没有则使用case_name
        regr_file = CommandParser.parse_regr_file_from_command(command)
        if regr_file:
            import os
            # 去掉路径和后缀，只保留文件名
            tab_name = os.path.splitext(os.path.basename(regr_file))[0]
        else:
            rundir = CommandParser.parse_rundir_from_command(command)
            tab_name = rundir if rundir else case_name

        # 添加日志标签页
        success = self.execution_panel.add_log_tab(tab_name, command)

        if success:
            # 获取日志面板并开始执行
            log_panel = self.execution_panel.get_log_panel(tab_name)
            if log_panel:
                # 连接选项变更信号，实现与配置面板的同步
                log_panel.option_changed.connect(self._on_log_panel_option_changed)

                # 从配置面板同步初始状态到LogPanel
                if hasattr(self, 'config_controller') and hasattr(self.config_controller, 'config_panel'):
                    log_panel.update_from_config_panel(self.config_controller.config_panel)

                log_panel.start_execution()

                # 注意：历史记录已经在config_controller.execute_command中添加，这里不需要重复添加
        else:
            self.main_window.show_warning(
                "标签页已满",
                "已达到最大标签页数量，请关闭一些标签页后再试"
            )

    def execute_command_with_queue(self, command, case_name, priority=TaskPriority.NORMAL):
        """使用队列执行命令"""
        # 导入命令解析器
        from utils.command_generator import CommandParser
        import time

        # 如果没有指定用例名称，尝试从命令中提取
        if not case_name:
            case_name = CommandParser.parse_case_from_command(command)

        # 如果仍然没有用例名称，使用时间戳或其他标识
        if not case_name:
            case_name = f"执行_{int(time.time())}"

        # 解析工作目录
        rundir = CommandParser.parse_rundir_from_command(command)
        work_directory = rundir if rundir else os.getcwd()

        # 创建任务元数据
        metadata = TaskMetadata(
            case_name=case_name,
            work_directory=work_directory,
            tags=['runsim']
        )

        # 确定任务名称
        regr_file = CommandParser.parse_regr_file_from_command(command)
        if regr_file:
            task_name = os.path.splitext(os.path.basename(regr_file))[0]
        else:
            task_name = case_name

        # 添加任务到队列
        task_id = self.task_queue_manager.add_task(
            name=task_name,
            command=command,
            priority=priority,
            metadata=metadata,
            execution_callback=self._on_queue_task_execution
        )

        if task_id:
            self.main_window.show_message(f"任务 '{task_name}' 已添加到队列")
        else:
            self.main_window.show_warning("添加任务失败", "无法将任务添加到队列")

    def _on_queue_task_execution(self, task):
        """队列任务执行回调"""
        # 当任务从队列开始执行时，创建对应的日志标签页
        try:
            tab_name = task.name
            success = self.execution_panel.add_log_tab(tab_name, task.command)

            if success:
                log_panel = self.execution_panel.get_log_panel(tab_name)
                if log_panel:
                    # 启动日志面板的执行
                    log_panel.start_execution()

                    # 连接日志面板的进程信号到任务进度更新
                    def on_process_started():
                        print(f"进程启动，设置任务 {task.name} 进度为10%")
                        task.set_progress(0.1)  # 进程启动时设置10%进度

                    def on_process_finished(exit_code):
                        if exit_code == 0:
                            print(f"进程成功完成，设置任务 {task.name} 进度为100%")
                            task.set_progress(1.0)  # 成功完成时设置100%进度
                        else:
                            print(f"进程失败，重置任务 {task.name} 进度")
                            task.set_progress(0.0)  # 失败时重置进度

                    def on_output_ready(data):
                        # 根据输出内容估算进度
                        output_text = data.decode('utf-8', errors='ignore')
                        current_progress = task.progress
                        new_progress = current_progress

                        if 'compile' in output_text.lower() and current_progress < 0.3:
                            new_progress = 0.3  # 编译阶段30%
                        elif ('simulation' in output_text.lower() or 'sim' in output_text.lower()) and current_progress < 0.6:
                            new_progress = 0.6  # 仿真阶段60%
                        elif ('finish' in output_text.lower() or 'done' in output_text.lower()) and current_progress < 0.9:
                            new_progress = 0.9  # 接近完成90%

                        if new_progress > current_progress:
                            print(f"根据输出更新任务 {task.name} 进度: {current_progress:.1%} -> {new_progress:.1%}")
                            task.set_progress(new_progress)

                    # 连接信号
                    if hasattr(log_panel.process_manager, 'process_started'):
                        log_panel.process_manager.process_started.connect(on_process_started)
                    if hasattr(log_panel.process_manager, 'process_finished'):
                        log_panel.process_manager.process_finished.connect(on_process_finished)
                    if hasattr(log_panel.process_manager, 'output_ready'):
                        log_panel.process_manager.output_ready.connect(on_output_ready)

                    # 保存日志面板引用，用于状态同步
                    task.metadata.user_data['log_panel'] = log_panel
                    task.metadata.user_data['tab_name'] = tab_name

        except Exception as e:
            print(f"队列任务执行回调失败: {e}")

    def enable_queue_mode(self, enabled=True):
        """启用/禁用队列模式"""
        self.queue_mode_enabled = enabled

        if enabled:
            # 启动任务队列管理器
            self.task_queue_manager.start()
            self.main_window.show_message("任务队列模式已启用")
        else:
            # 停止任务队列管理器
            self.task_queue_manager.stop()
            self.main_window.show_message("任务队列模式已禁用")

    def get_task_monitor_panel(self):
        """获取任务监控面板"""
        return self.task_monitor_panel

    def get_task_queue_manager(self):
        """获取任务队列管理器"""
        return self.task_queue_manager

    @pyqtSlot(int)
    def close_tab(self, index):
        """
        关闭标签页

        Args:
            index (int): 标签页索引
        """
        self.execution_panel.close_tab(index)

    @pyqtSlot(str)
    def open_verdi(self, case_name):
        """
        打开 Verdi

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        try:
            # 检查是否存在VCS仿真结果
            vcs_result_exists = os.path.exists(os.path.join(case_dir, "simv.daidir")) or \
                               os.path.exists(os.path.join(case_dir, "vcdplus.vpd"))

            # 根据仿真类型选择合适的命令
            if vcs_result_exists:
                # VCS仿真，使用run_verdi_vcs脚本
                verdi_cmd = "run_verdi_vcs"
                tool_name = "Verdi(VCS)"
            else:
                # XRUN仿真，使用run_verdi脚本
                verdi_cmd = "run_verdi comp_load"
                tool_name = "Verdi(XRUN)"

            # 构建完整命令
            full_cmd = f"cd {case_dir} && {verdi_cmd}"

            # 参考旧版本实现，使用CaseTab方式启动Verdi
            # 创建新的标签页并执行命令
            from views.log_panel import LogPanel

            # 创建标签页名称
            tab_name = f"Verdi_{os.path.basename(case_dir)}"

            # 添加日志标签页
            success = self.execution_panel.add_log_tab(tab_name, full_cmd)

            if success:
                # 获取日志面板并开始执行
                log_panel = self.execution_panel.get_log_panel(tab_name)
                if log_panel:
                    log_panel.start_execution()
                    self.main_window.show_message(f"正在打开 {tool_name}: {case_dir}")
            else:
                self.main_window.show_warning(
                    "标签页已满",
                    "已达到最大标签页数量，请关闭一些标签页后再试"
                )

        except Exception as e:
            self.main_window.show_error(
                "启动 Verdi 失败",
                f"无法启动 Verdi: {str(e)}"
            )

    @pyqtSlot(str)
    def open_verisium(self, case_name):
        """
        打开 Verisium

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        try:
            # 使用run_verisium脚本启动Verisium
            verisium_cmd = "run_verisium"

            # 构建完整命令
            full_cmd = f"cd {case_dir} && {verisium_cmd}"

            # 参考旧版本实现，使用CaseTab方式启动Verisium
            # 创建新的标签页并执行命令
            from views.log_panel import LogPanel

            # 创建标签页名称
            tab_name = f"Verisium_{os.path.basename(case_dir)}"

            # 添加日志标签页
            success = self.execution_panel.add_log_tab(tab_name, full_cmd)

            if success:
                # 获取日志面板并开始执行
                log_panel = self.execution_panel.get_log_panel(tab_name)
                if log_panel:
                    log_panel.start_execution()
                    self.main_window.show_message(f"正在打开 Verisium: {case_dir}")
            else:
                self.main_window.show_warning(
                    "标签页已满",
                    "已达到最大标签页数量，请关闭一些标签页后再试"
                )

        except Exception as e:
            self.main_window.show_error(
                "启动 Verisium 失败",
                f"无法启动 Verisium: {str(e)}"
            )

    def _on_log_panel_option_changed(self, option_name, enabled):
        """
        处理LogPanel中的选项变更，同步到配置面板

        Args:
            option_name (str): 选项名称 ("fsdb" 或 "R")
            enabled (bool): 是否启用
        """
        try:
            # 获取配置控制器和配置面板
            if not hasattr(self, 'config_controller') or not hasattr(self.config_controller, 'config_panel'):
                return

            config_panel = self.config_controller.config_panel

            # 根据选项名称更新配置面板
            if option_name == "fsdb" and hasattr(config_panel, 'fsdb_check'):
                config_panel.fsdb_check.blockSignals(True)
                config_panel.fsdb_check.setChecked(enabled)
                config_panel.fsdb_check.blockSignals(False)

                # 触发配置面板的更新
                if hasattr(config_panel, 'on_checkbox_changed'):
                    config_panel.on_checkbox_changed()

            elif option_name == "R" and hasattr(config_panel, 'sim_only_check'):
                config_panel.sim_only_check.blockSignals(True)
                config_panel.sim_only_check.setChecked(enabled)
                config_panel.sim_only_check.blockSignals(False)

                # 触发配置面板的更新
                if hasattr(config_panel, 'on_checkbox_changed'):
                    config_panel.on_checkbox_changed()

            # 触发命令预览更新
            if hasattr(self.config_controller, 'generate_command_preview'):
                self.config_controller.generate_command_preview()

        except Exception as e:
            print(f"同步LogPanel选项到配置面板时出错: {str(e)}")

    @pyqtSlot(str)
    def open_compile_log(self, case_name):
        """
        打开编译日志

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 首先尝试直接使用tab名称找到对应的日志文件
        case_dir = None
        tab_name = self.get_tab_name(case_name)
        
        if self.execution_panel.has_tab(tab_name):
            # 如果tab_name存在，直接查找对应的日志文件
            case_dir = tab_name

        if not case_dir or not os.path.exists(case_dir):
            # 如果无法直接找到日志文件，则搜索用例目录
            case_dirs = self.path_resolver.get_case_directories()
            matching_dirs = [d for d in case_dirs if case_name in d]

            if not matching_dirs:
                self.main_window.show_warning(
                    "未找到用例目录",
                    f"无法找到与 '{case_name}' 匹配的用例目录"
                )
                return

            # 如果有多个匹配的目录，弹出对话框让用户选择
            if len(matching_dirs) > 1:
                from PyQt5.QtWidgets import QInputDialog
                selected_dir, ok = QInputDialog.getItem(
                    self.main_window,
                    "选择用例目录",
                    "请选择要打开的用例目录：",
                    matching_dirs,
                    0,  # 默认选择第一项
                    False  # 不可编辑
                )
                if ok and selected_dir:
                    case_dir = selected_dir
                else:
                    return  # 用户取消选择
            else:
                case_dir = matching_dirs[0]

        # 获取日志文件路径
        log_file = self.path_resolver.get_log_file_path(case_dir, "compile")

        if not log_file or not os.path.exists(log_file):
            # 如果找不到irun_compile.log，尝试搜索目录下所有的.log文件
            log_files = []
            for root, _, files in os.walk(case_dir):
                for file in files:
                    if file.endswith('.log'):
                        log_files.append(os.path.join(root, file))
            
            if not log_files:
                self.main_window.show_warning(
                    "未找到日志文件",
                    f"在目录 '{case_dir}' 中未找到任何日志文件"
                )
                return

            # 如果有多个日志文件，让用户选择
            if len(log_files) > 1:
                file_path, ok = QFileDialog.getOpenFileName(
                    self.main_window,
                    "选择日志文件",
                    case_dir,
                    "日志文件 (*.log);;所有文件 (*.*)"
                )
                if ok and file_path:
                    log_file = file_path
                else:
                    return  # 用户取消选择
            else:
                log_file = log_files[0]

        # 打开日志文件
        try:
            if sys.platform == 'win32':
                os.startfile(log_file)
            else:
                subprocess.Popen(['gvim', log_file])

            self.main_window.show_message(f"正在打开编译日志: {log_file}")

        except Exception as e:
            self.main_window.show_error(
                "打开日志文件失败",
                f"无法打开编译日志文件: {str(e)}"
            )

    @pyqtSlot(str)
    def open_sim_log(self, case_name):
        """
        打开仿真日志

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 首先尝试直接使用tab名称找到对应的日志文件
        case_dir = None
        tab_name = self.get_tab_name(case_name)
        
        if self.execution_panel.has_tab(tab_name):
            # 如果tab_name存在，直接查找对应的日志文件
            case_dir = tab_name

        if not case_dir or not os.path.exists(case_dir):
            # 如果无法直接找到日志文件，则搜索用例目录
            case_dirs = self.path_resolver.get_case_directories()
            matching_dirs = [d for d in case_dirs if case_name in d]

            if not matching_dirs:
                self.main_window.show_warning(
                    "未找到用例目录",
                    f"无法找到与 '{case_name}' 匹配的用例目录"
                )
                return

            # 如果有多个匹配的目录，弹出对话框让用户选择
            if len(matching_dirs) > 1:
                from PyQt5.QtWidgets import QInputDialog
                selected_dir, ok = QInputDialog.getItem(
                    self.main_window,
                    "选择用例目录",
                    "请选择要打开的用例目录：",
                    matching_dirs,
                    0,  # 默认选择第一项
                    False  # 不可编辑
                )
                if ok and selected_dir:
                    case_dir = selected_dir
                else:
                    return  # 用户取消选择
            else:
                case_dir = matching_dirs[0]

        # 获取日志文件路径
        log_file = self.path_resolver.get_log_file_path(case_dir, "sim")
        
        if not log_file or not os.path.exists(log_file):
            # 如果找不到irun_sim.log，尝试搜索目录下所有的.log文件
            log_files = []
            for root, _, files in os.walk(case_dir):
                for file in files:
                    if file.endswith('.log'):
                        log_files.append(os.path.join(root, file))
            
            if not log_files:
                self.main_window.show_warning(
                    "未找到日志文件",
                    f"在目录 '{case_dir}' 中未找到任何日志文件"
                )
                return
                
            # 如果有多个日志文件，让用户选择
            if len(log_files) > 1:
                file_path, ok = QFileDialog.getOpenFileName(
                    self.main_window,
                    "选择日志文件",
                    case_dir,
                    "日志文件 (*.log);;所有文件 (*.*)"
                )
                if ok and file_path:
                    log_file = file_path
                else:
                    return  # 用户取消选择
            else:
                log_file = log_files[0]

        # 打开日志文件
        try:
            if sys.platform == 'win32':
                os.startfile(log_file)
            else:
                subprocess.Popen(['gvim', log_file])

            self.main_window.show_message(f"正在打开仿真日志: {log_file}")

        except Exception as e:
            self.main_window.show_error(
                "打开日志文件失败",
                f"无法打开仿真日志文件: {str(e)}"
            )

    @pyqtSlot(str)
    def open_asm_file(self, case_name):
        """
        打开反汇编文件

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        # 查找反汇编文件
        asm_files = self.path_resolver.find_asm_files(case_dir)

        if not asm_files:
            self.main_window.show_warning(
                "未找到反汇编文件",
                f"在 '{case_dir}' 中未找到反汇编文件"
            )
            return

        # 如果只有一个文件，直接打开
        if len(asm_files) == 1:
            asm_file = asm_files[0]
            try:
                if sys.platform == 'win32':
                    os.startfile(asm_file)
                else:
                    subprocess.Popen(['gvim', asm_file])

                self.main_window.show_message(f"正在打开反汇编文件: {asm_file}")

            except Exception as e:
                self.main_window.show_error(
                    "打开反汇编文件失败",
                    f"无法打开反汇编文件: {str(e)}"
                )
        else:
            # 如果有多个文件，让用户选择
            file_path, _ = QFileDialog.getOpenFileName(
                self.main_window,
                "选择反汇编文件",
                case_dir,
                "反汇编文件 (*.asm);;所有文件 (*.*)"
            )

            if file_path:
                try:
                    if sys.platform == 'win32':
                        os.startfile(file_path)
                    else:
                        subprocess.Popen(['gvim', file_path])

                    self.main_window.show_message(f"正在打开反汇编文件: {file_path}")

                except Exception as e:
                    self.main_window.show_error(
                        "打开反汇编文件失败",
                        f"无法打开反汇编文件: {str(e)}"
                    )

    def get_tab_name(self, case_name):
        """
        获取标签页名称，保持与execute_command中相同的逻辑

        Args:
            case_name (str): 用例名称

        Returns:
            str: 标签页名称（优先使用regr文件名，其次是rundir，如果都没有则使用case_name）
        """
        # 导入命令解析器
        from utils.command_generator import CommandParser
        
        # 获取当前标签页对应的日志面板
        log_panel = self.execution_panel.get_current_log_panel()
        if log_panel and hasattr(log_panel, 'command'):
            # 从命令中解析regr文件
            regr_file = CommandParser.parse_regr_file_from_command(log_panel.command)
            if regr_file:
                import os
                # 去掉路径和后缀，只保留文件名
                return os.path.splitext(os.path.basename(regr_file))[0]
                
            # 从命令中解析rundir
            rundir = CommandParser.parse_rundir_from_command(log_panel.command)
            if rundir:
                return rundir

        return case_name
