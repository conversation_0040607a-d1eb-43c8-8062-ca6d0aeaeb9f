"""
优化的虚拟滚动核心算法
提供更高效的可见区域计算和渲染优化
"""
import time
import bisect
from typing import List, Tuple, Optional, Dict, Any
from collections import OrderedDict
import threading
from utils.string_pool import intern_string


class TextDataManager:
    """
    文本数据管理器 - 优化文本存储和访问
    """
    
    def __init__(self, max_lines: int = 100000):
        self.max_lines = max_lines
        self._lines: List[str] = []
        self._line_heights: List[int] = []
        self._cumulative_heights: List[int] = []  # 累积高度，用于快速查找
        self._total_height = 0
        self._default_line_height = 20
        self._lock = threading.RLock()
        
        # 性能统计
        self._stats = {
            'total_lines_added': 0,
            'total_lines_removed': 0,
            'height_calculations': 0,
            'binary_searches': 0
        }
    
    def set_default_line_height(self, height: int):
        """设置默认行高"""
        self._default_line_height = height
        self._recalculate_all_heights()
    
    def append_lines(self, lines: List[str]) -> bool:
        """
        批量添加行

        Args:
            lines: 要添加的行列表

        Returns:
            是否成功添加
        """
        if not lines:
            return True

        with self._lock:
            try:
                # 使用字符串池优化内存，并提取公共部分
                optimized_lines = []
                for line in lines:
                    # 尝试提取公共前缀和后缀进行优化
                    optimized_line = self._optimize_line_storage(line)
                    optimized_lines.append(optimized_line)

                # 批量计算行高（使用固定高度减少内存）
                line_heights = [self._default_line_height] * len(optimized_lines)

                # 添加到数据结构
                self._lines.extend(optimized_lines)
                self._line_heights.extend(line_heights)

                # 更新累积高度
                self._update_cumulative_heights(len(line_heights))

                # 检查是否超过最大行数
                if len(self._lines) > self.max_lines:
                    self._remove_old_lines(len(self._lines) - self.max_lines)

                self._stats['total_lines_added'] += len(lines)
                return True

            except Exception as e:
                print(f"添加行时出错: {e}")
                return False
    
    def _optimize_line_storage(self, line: str) -> str:
        """优化行存储，提取公共部分"""
        # 使用字符串池存储整行
        return intern_string(line)

    def _calculate_line_height(self, line: str) -> int:
        """计算行高（可以根据内容优化）"""
        self._stats['height_calculations'] += 1
        # 简单实现：固定高度
        # 可以扩展为根据文本长度、换行等计算实际高度
        return self._default_line_height
    
    def _update_cumulative_heights(self, new_lines_count: int):
        """更新累积高度数组"""
        start_index = len(self._cumulative_heights)
        
        for i in range(new_lines_count):
            line_index = start_index + i
            if line_index < len(self._line_heights):
                if self._cumulative_heights:
                    cumulative = self._cumulative_heights[-1] + self._line_heights[line_index]
                else:
                    cumulative = self._line_heights[line_index]
                self._cumulative_heights.append(cumulative)
        
        self._total_height = self._cumulative_heights[-1] if self._cumulative_heights else 0
    
    def _remove_old_lines(self, count: int):
        """移除旧行"""
        if count <= 0:
            return
        
        # 移除行数据
        self._lines = self._lines[count:]
        self._line_heights = self._line_heights[count:]
        
        # 重新计算累积高度
        self._recalculate_cumulative_heights()
        
        self._stats['total_lines_removed'] += count
    
    def _recalculate_cumulative_heights(self):
        """重新计算所有累积高度"""
        self._cumulative_heights = []
        cumulative = 0
        
        for height in self._line_heights:
            cumulative += height
            self._cumulative_heights.append(cumulative)
        
        self._total_height = cumulative
    
    def _recalculate_all_heights(self):
        """重新计算所有行高"""
        self._line_heights = [self._calculate_line_height(line) for line in self._lines]
        self._recalculate_cumulative_heights()
    
    def get_line_count(self) -> int:
        """获取行数"""
        return len(self._lines)
    
    def get_total_height(self) -> int:
        """获取总高度"""
        return self._total_height
    
    def get_line(self, index: int) -> Optional[str]:
        """获取指定行"""
        if 0 <= index < len(self._lines):
            return self._lines[index]
        return None
    
    def get_line_height(self, index: int) -> int:
        """获取指定行高度"""
        if 0 <= index < len(self._line_heights):
            return self._line_heights[index]
        return self._default_line_height
    
    def get_lines_range(self, start: int, end: int) -> List[str]:
        """获取行范围"""
        start = max(0, start)
        end = min(len(self._lines), end)
        return self._lines[start:end]
    
    def clear(self):
        """清空所有数据"""
        with self._lock:
            self._lines.clear()
            self._line_heights.clear()
            self._cumulative_heights.clear()
            self._total_height = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'line_count': len(self._lines),
            'total_height': self._total_height,
            **self._stats
        }


class VirtualScrollManager:
    """
    虚拟滚动管理器 - 优化可见区域计算
    """
    
    def __init__(self, data_manager: TextDataManager):
        self.data_manager = data_manager
        self._render_margin = 5  # 额外渲染的行数
        
        # 性能统计
        self._stats = {
            'visible_calculations': 0,
            'binary_search_time': 0.0,
            'binary_searches': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 缓存最近的可见区域计算
        self._visible_cache = {}
        self._cache_max_size = 10
    
    def get_visible_range(self, scroll_y: int, viewport_height: int) -> Optional[Tuple[int, int, int]]:
        """
        获取可见行范围（优化版本）
        
        Args:
            scroll_y: 滚动位置
            viewport_height: 视口高度
            
        Returns:
            (start_line, end_line, y_offset) 或 None
        """
        self._stats['visible_calculations'] += 1
        
        # 检查缓存
        cache_key = f"{scroll_y//10}_{viewport_height}"  # 降低缓存精度
        if cache_key in self._visible_cache:
            self._stats['cache_hits'] += 1
            return self._visible_cache[cache_key]
        
        self._stats['cache_misses'] += 1
        
        line_count = self.data_manager.get_line_count()
        if line_count == 0:
            return None
        
        # 使用二分查找快速定位起始行
        start_time = time.perf_counter()
        start_line = self._find_line_at_y(scroll_y)
        search_time = time.perf_counter() - start_time
        self._stats['binary_search_time'] += search_time
        
        if start_line is None:
            return None
        
        # 计算起始行的Y偏移
        y_offset = self._get_line_y_position(start_line) - scroll_y
        
        # 计算结束行
        end_line = self._find_end_line(start_line, y_offset, viewport_height)
        
        # 添加渲染边距
        start_line_with_margin = max(0, start_line - self._render_margin)
        end_line_with_margin = min(line_count, end_line + self._render_margin)
        
        result = (start_line_with_margin, end_line_with_margin, y_offset)
        
        # 缓存结果
        self._cache_visible_range(cache_key, result)
        
        return result
    
    def _find_line_at_y(self, y: int) -> Optional[int]:
        """使用二分查找定位Y位置对应的行"""
        cumulative_heights = self.data_manager._cumulative_heights
        if not cumulative_heights:
            return None
        
        self._stats['binary_searches'] += 1
        
        # 二分查找第一个累积高度大于y的行
        index = bisect.bisect_right(cumulative_heights, y)
        
        # 如果y超出了所有行的范围
        if index >= len(cumulative_heights):
            return len(cumulative_heights) - 1
        
        return index
    
    def _get_line_y_position(self, line_index: int) -> int:
        """获取行的Y位置"""
        if line_index <= 0:
            return 0
        
        cumulative_heights = self.data_manager._cumulative_heights
        if line_index - 1 < len(cumulative_heights):
            return cumulative_heights[line_index - 1]
        
        return self.data_manager.get_total_height()
    
    def _find_end_line(self, start_line: int, y_offset: int, viewport_height: int) -> int:
        """计算结束行"""
        current_y = y_offset
        line_index = start_line
        line_count = self.data_manager.get_line_count()
        
        while line_index < line_count and current_y < viewport_height:
            line_height = self.data_manager.get_line_height(line_index)
            current_y += line_height
            line_index += 1
        
        return line_index
    
    def _cache_visible_range(self, cache_key: str, result: Tuple[int, int, int]):
        """缓存可见范围结果"""
        if len(self._visible_cache) >= self._cache_max_size:
            # 移除最旧的缓存项
            oldest_key = next(iter(self._visible_cache))
            del self._visible_cache[oldest_key]
        
        self._visible_cache[cache_key] = result
    
    def clear_cache(self):
        """清空缓存"""
        self._visible_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = self._stats['cache_hits'] + self._stats['cache_misses']
        hit_rate = (self._stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_hit_rate': hit_rate,
            'avg_binary_search_time_ms': self._stats['binary_search_time'] * 1000 / max(1, self._stats['binary_searches']),
            **self._stats
        }


class TextRenderEngine:
    """
    文本渲染引擎 - 优化渲染性能
    """

    def __init__(self):
        # 渲染缓存
        self._render_cache = OrderedDict()
        self._cache_max_size = 100
        self._cache_lock = threading.RLock()

        # 性能统计
        self._stats = {
            'render_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_render_time': 0.0
        }

    def generate_render_key(self, line_text: str, line_index: int, has_selection: bool) -> str:
        """生成渲染缓存键"""
        # 使用字符串池优化键的内存使用
        return intern_string(f"{hash(line_text)}_{line_index}_{has_selection}")

    def get_cached_render_data(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的渲染数据"""
        with self._cache_lock:
            if cache_key in self._render_cache:
                # 移动到末尾（LRU）
                self._render_cache.move_to_end(cache_key)
                self._stats['cache_hits'] += 1
                return self._render_cache[cache_key]

            self._stats['cache_misses'] += 1
            return None

    def cache_render_data(self, cache_key: str, render_data: Dict[str, Any]):
        """缓存渲染数据"""
        with self._cache_lock:
            # 如果缓存已满，移除最旧的项
            if len(self._render_cache) >= self._cache_max_size:
                self._render_cache.popitem(last=False)

            self._render_cache[cache_key] = render_data

    def clear_cache(self):
        """清空渲染缓存"""
        with self._cache_lock:
            self._render_cache.clear()

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = self._stats['cache_hits'] + self._stats['cache_misses']
        hit_rate = (self._stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        avg_render_time = (self._stats['total_render_time'] / max(1, self._stats['render_calls'])) * 1000

        return {
            'cache_size': len(self._render_cache),
            'cache_hit_rate': hit_rate,
            'avg_render_time_ms': avg_render_time,
            **self._stats
        }


class OptimizedVirtualScrollController:
    """
    优化的虚拟滚动控制器 - 整合所有优化组件
    """

    def __init__(self, max_lines: int = 100000):
        self.data_manager = TextDataManager(max_lines)
        self.scroll_manager = VirtualScrollManager(self.data_manager)
        self.render_engine = TextRenderEngine()

        # 批处理配置
        self._batch_size = 100
        self._pending_lines = []

        # 性能监控
        self._performance_stats = {
            'total_appends': 0,
            'total_render_calls': 0,
            'avg_batch_process_time': 0.0
        }

    def append_text(self, text: str):
        """添加文本（支持批处理）"""
        if not text:
            return

        # 分割成行
        lines = text.split('\n')
        lines = [line for line in lines if line.strip()]  # 过滤空行

        if not lines:
            return

        self._pending_lines.extend(lines)
        self._performance_stats['total_appends'] += 1

        # 如果达到批处理大小，立即处理
        if len(self._pending_lines) >= self._batch_size:
            self._process_pending_lines()

    def _process_pending_lines(self):
        """处理待处理的行"""
        if not self._pending_lines:
            return

        start_time = time.perf_counter()

        # 批量添加到数据管理器
        self.data_manager.append_lines(self._pending_lines)
        self._pending_lines.clear()

        # 清空相关缓存
        self.scroll_manager.clear_cache()
        self.render_engine.clear_cache()

        # 更新性能统计
        process_time = time.perf_counter() - start_time
        self._performance_stats['avg_batch_process_time'] = (
            (self._performance_stats['avg_batch_process_time'] * (self._performance_stats['total_appends'] - 1) + process_time) /
            self._performance_stats['total_appends']
        )

    def force_flush(self):
        """强制处理所有待处理的行"""
        self._process_pending_lines()

    def get_visible_range(self, scroll_y: int, viewport_height: int):
        """获取可见行范围"""
        return self.scroll_manager.get_visible_range(scroll_y, viewport_height)

    def get_line_data(self, start_line: int, end_line: int) -> List[str]:
        """获取行数据"""
        return self.data_manager.get_lines_range(start_line, end_line)

    def get_total_height(self) -> int:
        """获取总高度"""
        return self.data_manager.get_total_height()

    def get_line_count(self) -> int:
        """获取行数"""
        return self.data_manager.get_line_count()

    def clear(self):
        """清空所有数据"""
        self.data_manager.clear()
        self.scroll_manager.clear_cache()
        self.render_engine.clear_cache()
        self._pending_lines.clear()

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合性能统计"""
        return {
            'controller': self._performance_stats,
            'data_manager': self.data_manager.get_stats(),
            'scroll_manager': self.scroll_manager.get_stats(),
            'render_engine': self.render_engine.get_stats()
        }
