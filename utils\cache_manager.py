import os
import json
import hashlib
from datetime import datetime

class CacheManager:
    """缓存管理器"""
    def __init__(self, cache_dir=".cache"):
        self.cache_dir = cache_dir
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
            
    def get_cache_key(self, file_path):
        """获取缓存键"""
        file_stat = os.stat(file_path)
        key_data = f"{file_path}_{file_stat.st_mtime}_{file_stat.st_size}"
        return hashlib.md5(key_data.encode()).hexdigest()
        
    def get_cached_data(self, file_path):
        """获取缓存数据"""
        cache_key = self.get_cache_key(file_path)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)
                    # 验证缓存是否过期(24小时)
                    if (datetime.now() - datetime.fromisoformat(cache_data['timestamp'])).days < 1:
                        return cache_data['data']
            except Exception:
                pass
        return None
        
    def save_cache(self, file_path, data):
        """保存数据到缓存"""
        cache_key = self.get_cache_key(file_path)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")

        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'data': data
            }
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f)
        except Exception as e:
            print(f"保存缓存失败: {str(e)}")

    def remove_cache(self, file_path):
        """删除特定文件的缓存"""
        try:
            if not os.path.exists(file_path):
                # 如果文件不存在，尝试清理所有可能的缓存文件
                self._remove_cache_by_path_pattern(file_path)
                return

            cache_key = self.get_cache_key(file_path)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")

            if os.path.exists(cache_file):
                os.remove(cache_file)
                print(f"已清理缓存文件: {cache_file}")
        except Exception as e:
            print(f"清理缓存失败: {str(e)}")

    def _remove_cache_by_path_pattern(self, file_path):
        """根据文件路径模式清理缓存（用于文件已删除的情况）"""
        try:
            # 获取文件路径的基本信息用于匹配
            file_name = os.path.basename(file_path)

            # 遍历缓存目录，查找可能匹配的缓存文件
            for cache_file in os.listdir(self.cache_dir):
                if cache_file.endswith('.json'):
                    cache_path = os.path.join(self.cache_dir, cache_file)
                    try:
                        with open(cache_path, 'r') as f:
                            cache_data = json.load(f)
                            # 检查缓存数据中是否包含相关文件信息
                            if 'data' in cache_data and isinstance(cache_data['data'], dict):
                                if cache_data['data'].get('name') == file_name:
                                    os.remove(cache_path)
                                    print(f"已清理匹配的缓存文件: {cache_path}")
                    except Exception:
                        # 忽略无法读取的缓存文件
                        pass
        except Exception as e:
            print(f"按模式清理缓存失败: {str(e)}")

    def clear_all_cache(self):
        """清空所有缓存"""
        try:
            for cache_file in os.listdir(self.cache_dir):
                if cache_file.endswith('.json'):
                    cache_path = os.path.join(self.cache_dir, cache_file)
                    os.remove(cache_path)
            print("已清空所有缓存")
        except Exception as e:
            print(f"清空缓存失败: {str(e)}")
