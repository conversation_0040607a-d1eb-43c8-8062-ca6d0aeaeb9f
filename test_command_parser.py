#!/usr/bin/env python3
"""
测试命令解析和修改功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.command_generator import CommandParser

def test_command_parsing():
    """测试命令解析功能"""
    print("=== 测试命令解析功能 ===")
    
    test_commands = [
        "runsim -base test_base -block test_block -case test_case1 -rundir ./run1",
        "runsim -base test_base -block test_block -case test_case2 -fsdb -rundir ./run2",
        "runsim -base test_base -block test_block -case test_case3 -R -rundir ./run3",
        "runsim -base test_base -block test_block -case test_case4 -fsdb -R -rundir ./run4",
        "runsim -base test_base -block test_block -case base_case -rundir ./base ; runsim -base test_base -block test_block -case target_case -R -rundir ./target"
    ]
    
    for i, cmd in enumerate(test_commands, 1):
        print(f"\n测试用例 {i}:")
        print(f"原始命令: {cmd}")
        
        # 检查是否包含选项
        has_fsdb = CommandParser.has_fsdb_option(cmd)
        has_r = CommandParser.has_r_option(cmd)
        print(f"包含-fsdb: {has_fsdb}")
        print(f"包含-R: {has_r}")
        
        # 测试添加选项
        cmd_with_fsdb = CommandParser.modify_command_options(cmd, fsdb_enabled=True)
        print(f"添加-fsdb后: {cmd_with_fsdb}")
        
        cmd_with_r = CommandParser.modify_command_options(cmd, r_enabled=True)
        print(f"添加-R后: {cmd_with_r}")
        
        # 测试移除选项
        cmd_without_fsdb = CommandParser.modify_command_options(cmd, fsdb_enabled=False)
        print(f"移除-fsdb后: {cmd_without_fsdb}")
        
        cmd_without_r = CommandParser.modify_command_options(cmd, r_enabled=False)
        print(f"移除-R后: {cmd_without_r}")

def test_option_modification():
    """测试选项修改功能"""
    print("\n\n=== 测试选项修改功能 ===")
    
    # 测试从无选项到有选项
    cmd1 = "runsim -base test_base -block test_block -case test_case1"
    print(f"\n原始命令: {cmd1}")
    
    cmd1_modified = CommandParser.modify_command_options(cmd1, fsdb_enabled=True, r_enabled=True)
    print(f"添加-fsdb和-R: {cmd1_modified}")
    
    # 验证添加的选项
    print(f"验证包含-fsdb: {CommandParser.has_fsdb_option(cmd1_modified)}")
    print(f"验证包含-R: {CommandParser.has_r_option(cmd1_modified)}")
    
    # 测试从有选项到无选项
    cmd2 = "runsim -base test_base -fsdb -block test_block -case test_case2 -R"
    print(f"\n原始命令: {cmd2}")
    
    cmd2_modified = CommandParser.modify_command_options(cmd2, fsdb_enabled=False, r_enabled=False)
    print(f"移除-fsdb和-R: {cmd2_modified}")
    
    # 验证移除的选项
    print(f"验证不包含-fsdb: {not CommandParser.has_fsdb_option(cmd2_modified)}")
    print(f"验证不包含-R: {not CommandParser.has_r_option(cmd2_modified)}")

def test_batch_run_commands():
    """测试BATCH RUN命令"""
    print("\n\n=== 测试BATCH RUN命令 ===")
    
    batch_cmd = "runsim -base test_base -block test_block -case base_case -rundir ./base ; runsim -base test_base -block test_block -case target_case -R -rundir ./target"
    print(f"原始BATCH RUN命令: {batch_cmd}")
    
    # 检查选项
    has_fsdb = CommandParser.has_fsdb_option(batch_cmd)
    has_r = CommandParser.has_r_option(batch_cmd)
    print(f"包含-fsdb: {has_fsdb}")
    print(f"包含-R: {has_r}")
    
    # 添加-fsdb到所有命令
    cmd_with_fsdb = CommandParser.modify_command_options(batch_cmd, fsdb_enabled=True)
    print(f"添加-fsdb后: {cmd_with_fsdb}")
    
    # 移除-R从所有命令
    cmd_without_r = CommandParser.modify_command_options(batch_cmd, r_enabled=False)
    print(f"移除-R后: {cmd_without_r}")

if __name__ == "__main__":
    test_command_parsing()
    test_option_modification()
    test_batch_run_commands()
    print("\n=== 测试完成 ===")
