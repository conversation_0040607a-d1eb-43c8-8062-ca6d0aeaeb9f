"""
任务队列核心组件
提供任务队列管理的基础数据结构和功能
"""
import uuid
import time
from datetime import datetime
from enum import Enum
from typing import List, Dict, Optional, Any, Callable
from dataclasses import dataclass, field
from PyQt5.QtCore import QObject, pyqtSignal
import threading
import heapq
import json


class ReadWriteLock:
    """读写锁实现 - 允许多个读者同时访问，但写者独占访问"""

    def __init__(self):
        self._read_ready = threading.Condition(threading.RLock())
        self._readers = 0

    def acquire_read(self):
        """获取读锁"""
        self._read_ready.acquire()
        try:
            self._readers += 1
        finally:
            self._read_ready.release()

    def release_read(self):
        """释放读锁"""
        self._read_ready.acquire()
        try:
            self._readers -= 1
            if self._readers == 0:
                self._read_ready.notifyAll()
        finally:
            self._read_ready.release()

    def acquire_write(self):
        """获取写锁"""
        self._read_ready.acquire()
        while self._readers > 0:
            self._read_ready.wait()

    def release_write(self):
        """释放写锁"""
        self._read_ready.release()

    def __enter__(self):
        """上下文管理器入口 - 默认使用写锁"""
        self.acquire_write()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release_write()
        return False


class LockManager:
    """锁管理器 - 提供细粒度的锁控制"""

    def __init__(self):
        self.main_lock = ReadWriteLock()  # 主要的读写锁
        self.queue_lock = threading.RLock()  # 队列操作锁
        self.stats_lock = threading.RLock()  # 统计信息锁
        self.task_locks = {}  # 单个任务的锁
        self.task_locks_lock = threading.RLock()  # 任务锁字典的锁

    def get_task_lock(self, task_id: str) -> threading.RLock:
        """获取特定任务的锁"""
        with self.task_locks_lock:
            if task_id not in self.task_locks:
                self.task_locks[task_id] = threading.RLock()
            return self.task_locks[task_id]

    def remove_task_lock(self, task_id: str):
        """移除任务锁"""
        with self.task_locks_lock:
            if task_id in self.task_locks:
                del self.task_locks[task_id]


class TaskPriority(Enum):
    """任务优先级枚举"""
    URGENT = 1      # 紧急任务
    HIGH = 2        # 高优先级  
    NORMAL = 3      # 普通优先级
    LOW = 4         # 低优先级
    BACKGROUND = 5  # 后台任务
    
    def __str__(self):
        return self.name
    
    def __lt__(self, other):
        """支持优先级比较，数值越小优先级越高"""
        if isinstance(other, TaskPriority):
            return self.value < other.value
        return NotImplemented


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"         # 等待执行
    QUEUED = "queued"          # 已排队
    RUNNING = "running"        # 正在执行
    COMPLETED = "completed"    # 执行完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"    # 已取消
    PAUSED = "paused"          # 已暂停
    
    def __str__(self):
        return self.value


@dataclass
class TaskMetadata:
    """任务元数据"""
    case_name: str = ""
    work_directory: str = ""
    log_file: str = ""
    estimated_duration: int = 0  # 预估执行时间（秒）
    tags: List[str] = field(default_factory=list)
    user_data: Dict[str, Any] = field(default_factory=dict)


class Task:
    """任务对象类"""
    
    def __init__(self, 
                 name: str,
                 command: str,
                 priority: TaskPriority = TaskPriority.NORMAL,
                 dependencies: Optional[List[str]] = None,
                 max_retries: int = 3,
                 metadata: Optional[TaskMetadata] = None):
        """
        初始化任务对象
        
        Args:
            name: 任务名称
            command: 执行命令
            priority: 任务优先级
            dependencies: 依赖任务ID列表
            max_retries: 最大重试次数
            metadata: 任务元数据
        """
        self.task_id = str(uuid.uuid4())
        self.name = name
        self.command = command
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.dependencies = dependencies or []
        self.max_retries = max_retries
        self.metadata = metadata or TaskMetadata()
        
        # 时间戳
        self.created_time = datetime.now()
        self.queued_time: Optional[datetime] = None
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        
        # 执行相关
        self.retry_count = 0
        self.exit_code: Optional[int] = None
        self.error_message: str = ""
        self.progress: float = 0.0  # 执行进度 0.0-1.0
        
        # 回调函数
        self.on_status_changed: Optional[Callable] = None
        self.on_progress_updated: Optional[Callable] = None
        
    def __lt__(self, other):
        """支持优先级队列排序"""
        if not isinstance(other, Task):
            return NotImplemented
        
        # 首先按优先级排序
        if self.priority != other.priority:
            return self.priority < other.priority
        
        # 优先级相同时按创建时间排序
        return self.created_time < other.created_time
    
    def set_status(self, status: TaskStatus, error_message: str = ""):
        """设置任务状态"""
        old_status = self.status
        self.status = status
        
        # 更新时间戳
        if status == TaskStatus.QUEUED and not self.queued_time:
            self.queued_time = datetime.now()
        elif status == TaskStatus.RUNNING and not self.start_time:
            self.start_time = datetime.now()
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            if not self.end_time:
                self.end_time = datetime.now()
        
        # 设置错误信息
        if error_message:
            self.error_message = error_message
        
        # 调用状态变化回调
        if self.on_status_changed:
            try:
                self.on_status_changed(self, old_status, status)
            except Exception as e:
                print(f"任务状态变化回调执行失败: {e}")
    
    def set_progress(self, progress: float):
        """设置任务进度"""
        old_progress = self.progress
        self.progress = max(0.0, min(1.0, progress))

        # 添加调试信息
        if old_progress != self.progress:
            print(f"任务 {self.name} 进度更新: {old_progress:.1%} -> {self.progress:.1%}")

        # 调用进度更新回调
        if self.on_progress_updated:
            try:
                self.on_progress_updated(self, self.progress)
            except Exception as e:
                print(f"任务进度更新回调执行失败: {e}")
        else:
            print(f"警告: 任务 {self.name} 没有设置进度更新回调")
    
    def can_execute(self, completed_tasks: set) -> bool:
        """检查任务是否可以执行（依赖是否满足）"""
        if not self.dependencies:
            return True
        
        return all(dep_id in completed_tasks for dep_id in self.dependencies)
    
    def should_retry(self) -> bool:
        """检查是否应该重试"""
        return (self.status == TaskStatus.FAILED and 
                self.retry_count < self.max_retries)
    
    def get_duration(self) -> Optional[float]:
        """获取任务执行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    def get_wait_time(self) -> Optional[float]:
        """获取任务等待时长（秒）"""
        if self.queued_time and self.start_time:
            return (self.start_time - self.queued_time).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于序列化）"""
        # 过滤user_data中不可序列化的对象
        serializable_user_data = {}
        for key, value in self.metadata.user_data.items():
            try:
                # 尝试序列化该值，如果失败则跳过
                import json
                json.dumps(value)
                serializable_user_data[key] = value
            except (TypeError, ValueError):
                # 跳过不可序列化的对象（如LogPanel等PyQt对象）
                print(f"跳过不可序列化的user_data项: {key} (类型: {type(value).__name__})")
                continue

        # 安全地获取优先级和状态值
        try:
            priority_value = self.priority.name if hasattr(self.priority, 'name') else str(self.priority)
        except:
            priority_value = 'NORMAL'  # 默认优先级

        try:
            status_value = self.status.value if hasattr(self.status, 'value') else str(self.status)
        except:
            status_value = 'pending'  # 默认状态

        return {
            'task_id': self.task_id,
            'name': self.name,
            'command': self.command,
            'priority': priority_value,
            'status': status_value,
            'dependencies': self.dependencies,
            'max_retries': self.max_retries,
            'retry_count': self.retry_count,
            'created_time': self.created_time.isoformat(),
            'queued_time': self.queued_time.isoformat() if self.queued_time else None,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'exit_code': self.exit_code,
            'error_message': self.error_message,
            'progress': self.progress,
            'metadata': {
                'case_name': self.metadata.case_name,
                'work_directory': self.metadata.work_directory,
                'log_file': self.metadata.log_file,
                'estimated_duration': self.metadata.estimated_duration,
                'tags': self.metadata.tags,
                'user_data': serializable_user_data
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """从字典创建任务对象（用于反序列化）"""
        metadata = TaskMetadata(
            case_name=data['metadata'].get('case_name', ''),
            work_directory=data['metadata'].get('work_directory', ''),
            log_file=data['metadata'].get('log_file', ''),
            estimated_duration=data['metadata'].get('estimated_duration', 0),
            tags=data['metadata'].get('tags', []),
            user_data=data['metadata'].get('user_data', {})
        )

        # 安全地恢复优先级
        try:
            priority = TaskPriority[data['priority']]
        except (KeyError, TypeError):
            print(f"警告: 无效的优先级值 '{data.get('priority')}', 使用默认值")
            priority = TaskPriority.NORMAL

        task = cls(
            name=data['name'],
            command=data['command'],
            priority=priority,
            dependencies=data['dependencies'],
            max_retries=data['max_retries'],
            metadata=metadata
        )

        # 恢复状态
        task.task_id = data['task_id']

        # 安全地恢复状态
        try:
            task.status = TaskStatus(data['status'])
        except (ValueError, TypeError):
            print(f"警告: 无效的状态值 '{data.get('status')}', 使用默认值")
            task.status = TaskStatus.PENDING

        task.retry_count = data['retry_count']
        task.exit_code = data['exit_code']
        task.error_message = data['error_message']
        task.progress = data['progress']

        # 恢复时间戳
        try:
            task.created_time = datetime.fromisoformat(data['created_time'])
            if data['queued_time']:
                task.queued_time = datetime.fromisoformat(data['queued_time'])
            if data['start_time']:
                task.start_time = datetime.fromisoformat(data['start_time'])
            if data['end_time']:
                task.end_time = datetime.fromisoformat(data['end_time'])
        except (ValueError, TypeError) as e:
            print(f"警告: 恢复时间戳失败: {e}")
            # 使用当前时间作为创建时间
            task.created_time = datetime.now()

        return task


class TaskQueue(QObject):
    """任务队列类"""
    
    # 信号定义
    task_added = pyqtSignal(object)  # 任务添加
    task_removed = pyqtSignal(str)   # 任务移除
    task_status_changed = pyqtSignal(object, str, str)  # 任务状态变化
    queue_changed = pyqtSignal()     # 队列变化
    
    def __init__(self, parent=None):
        """初始化任务队列"""
        super().__init__(parent)
        self._tasks: Dict[str, Task] = {}  # 所有任务
        self._pending_queue: List[Task] = []  # 待执行队列（优先级堆）
        self._running_tasks: Dict[str, Task] = {}  # 正在执行的任务
        self._completed_tasks: set = set()  # 已完成任务ID集合

        # 使用优化的锁管理器
        self._lock_manager = LockManager()
        # 保持向后兼容性
        self._lock = self._lock_manager.main_lock
        
    def add_task(self, task: Task) -> bool:
        """添加任务到队列"""
        # 使用写锁，因为要修改队列状态
        self._lock_manager.main_lock.acquire_write()
        try:
            if task.task_id in self._tasks:
                return False

            # 设置状态变化回调
            task.on_status_changed = self._on_task_status_changed

            # 添加到任务字典
            self._tasks[task.task_id] = task

            # 如果可以执行，添加到待执行队列
            if task.can_execute(self._completed_tasks):
                with self._lock_manager.queue_lock:
                    task.set_status(TaskStatus.QUEUED)
                    heapq.heappush(self._pending_queue, task)

            # 发出信号
            self.task_added.emit(task)
            self.queue_changed.emit()

            return True
        finally:
            self._lock_manager.main_lock.release_write()

    def remove_task(self, task_id: str) -> bool:
        """从队列中移除任务"""
        self._lock_manager.main_lock.acquire_write()
        try:
            if task_id not in self._tasks:
                return False

            task = self._tasks[task_id]

            # 如果任务正在运行，不能直接移除
            if task.status == TaskStatus.RUNNING:
                return False

            # 从各个队列中移除
            with self._lock_manager.queue_lock:
                if task in self._pending_queue:
                    self._pending_queue.remove(task)
                    heapq.heapify(self._pending_queue)

            if task_id in self._running_tasks:
                del self._running_tasks[task_id]

            if task_id in self._completed_tasks:
                self._completed_tasks.remove(task_id)

            # 从任务字典中移除
            del self._tasks[task_id]

            # 清理任务锁
            self._lock_manager.remove_task_lock(task_id)

            # 发出信号
            self.task_removed.emit(task_id)
            self.queue_changed.emit()

            return True
        finally:
            self._lock_manager.main_lock.release_write()

    def get_next_task(self) -> Optional[Task]:
        """获取下一个可执行的任务"""
        # 使用队列锁，因为要修改待执行队列
        with self._lock_manager.queue_lock:
            while self._pending_queue:
                task = heapq.heappop(self._pending_queue)

                # 检查任务是否仍然可以执行（使用读锁检查完成任务）
                self._lock_manager.main_lock.acquire_read()
                try:
                    can_execute = task.can_execute(self._completed_tasks)
                finally:
                    self._lock_manager.main_lock.release_read()

                if can_execute:
                    return task
                else:
                    # 依赖未满足，重新加入队列
                    heapq.heappush(self._pending_queue, task)
                    break

            return None

    def start_task(self, task_id: str) -> bool:
        """标记任务开始执行"""
        self._lock_manager.main_lock.acquire_write()
        try:
            if task_id not in self._tasks:
                return False

            task = self._tasks[task_id]
            if task.status != TaskStatus.QUEUED:
                return False

            # 移动到运行队列
            self._running_tasks[task_id] = task

            # 任务开始时，如果进度还是0，设置一个小的初始进度
            if task.progress == 0.0:
                task.set_progress(0.01)  # 设置1%表示已开始

            task.set_status(TaskStatus.RUNNING)

            return True
        finally:
            self._lock_manager.main_lock.release_write()

    def finish_task(self, task_id: str, success: bool, exit_code: int = 0, error_message: str = ""):
        """标记任务完成"""
        self._lock_manager.main_lock.acquire_write()
        try:
            if task_id not in self._running_tasks:
                return False

            task = self._running_tasks[task_id]
            task.exit_code = exit_code

            if success:
                # 成功完成时，确保进度设置为100%
                task.set_progress(1.0)
                task.set_status(TaskStatus.COMPLETED)
                self._completed_tasks.add(task_id)

                # 检查是否有依赖此任务的任务可以执行
                self._check_dependencies()
            else:
                # 失败时，进度保持当前值或设置为0
                if task.progress == 0.0:
                    # 如果进度还是0，说明任务可能在启动阶段就失败了
                    pass  # 保持0%
                # 如果有进度，保持当前进度，表示执行到某个阶段失败了

                task.set_status(TaskStatus.FAILED, error_message)

                # 检查是否需要重试
                if task.should_retry():
                    task.retry_count += 1
                    task.set_status(TaskStatus.QUEUED)
                    with self._lock_manager.queue_lock:
                        heapq.heappush(self._pending_queue, task)

            # 从运行队列中移除
            del self._running_tasks[task_id]

            return True
        finally:
            self._lock_manager.main_lock.release_write()

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        self._lock_manager.main_lock.acquire_write()
        try:
            if task_id not in self._tasks:
                return False

            task = self._tasks[task_id]

            # 正在运行的任务需要外部停止进程
            if task.status == TaskStatus.RUNNING:
                task.set_status(TaskStatus.CANCELLED)
                if task_id in self._running_tasks:
                    del self._running_tasks[task_id]
            elif task.status == TaskStatus.QUEUED:
                # 从待执行队列中移除
                with self._lock_manager.queue_lock:
                    if task in self._pending_queue:
                        self._pending_queue.remove(task)
                        heapq.heapify(self._pending_queue)
                task.set_status(TaskStatus.CANCELLED)

            return True
        finally:
            self._lock_manager.main_lock.release_write()

    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        self._lock_manager.main_lock.acquire_write()
        try:
            if task_id not in self._tasks:
                return False

            task = self._tasks[task_id]

            if task.status == TaskStatus.QUEUED:
                # 从待执行队列中移除
                with self._lock_manager.queue_lock:
                    if task in self._pending_queue:
                        self._pending_queue.remove(task)
                        heapq.heapify(self._pending_queue)
                task.set_status(TaskStatus.PAUSED)
                return True

            return False
        finally:
            self._lock_manager.main_lock.release_write()

    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        self._lock_manager.main_lock.acquire_write()
        try:
            if task_id not in self._tasks:
                return False

            task = self._tasks[task_id]

            if task.status == TaskStatus.PAUSED:
                if task.can_execute(self._completed_tasks):
                    task.set_status(TaskStatus.QUEUED)
                    with self._lock_manager.queue_lock:
                        heapq.heappush(self._pending_queue, task)
                    return True

            return False
        finally:
            self._lock_manager.main_lock.release_write()

    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务对象 - 使用读锁"""
        self._lock_manager.main_lock.acquire_read()
        try:
            return self._tasks.get(task_id)
        finally:
            self._lock_manager.main_lock.release_read()

    def get_all_tasks(self) -> Dict[str, Task]:
        """获取所有任务 - 使用读锁"""
        self._lock_manager.main_lock.acquire_read()
        try:
            return self._tasks.copy()
        finally:
            self._lock_manager.main_lock.release_read()

    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """根据状态获取任务 - 使用读锁"""
        self._lock_manager.main_lock.acquire_read()
        try:
            return [task for task in self._tasks.values() if task.status == status]
        finally:
            self._lock_manager.main_lock.release_read()

    def get_queue_stats(self) -> Dict[str, int]:
        """获取队列统计信息 - 使用专用统计锁"""
        with self._lock_manager.stats_lock:
            # 使用读锁获取任务数据
            self._lock_manager.main_lock.acquire_read()
            try:
                stats = {
                    'total': len(self._tasks),
                    'pending': len([t for t in self._tasks.values() if t.status == TaskStatus.PENDING]),
                    'queued': len([t for t in self._tasks.values() if t.status == TaskStatus.QUEUED]),
                    'running': len(self._running_tasks),
                    'completed': len([t for t in self._tasks.values() if t.status == TaskStatus.COMPLETED]),
                    'failed': len([t for t in self._tasks.values() if t.status == TaskStatus.FAILED]),
                    'cancelled': len([t for t in self._tasks.values() if t.status == TaskStatus.CANCELLED]),
                    'paused': len([t for t in self._tasks.values() if t.status == TaskStatus.PAUSED])
                }
                return stats
            finally:
                self._lock_manager.main_lock.release_read()

    def clear_completed_tasks(self):
        """清除已完成的任务"""
        self._lock_manager.main_lock.acquire_read()
        try:
            completed_task_ids = [
                task_id for task_id, task in self._tasks.items()
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
            ]
        finally:
            self._lock_manager.main_lock.release_read()

        # 在读锁外执行删除操作，避免死锁
        for task_id in completed_task_ids:
            self.remove_task(task_id)

    def get_running_count(self) -> int:
        """获取正在运行的任务数量 - 使用读锁"""
        self._lock_manager.main_lock.acquire_read()
        try:
            return len(self._running_tasks)
        finally:
            self._lock_manager.main_lock.release_read()

    def get_pending_count(self) -> int:
        """获取待执行任务数量 - 使用队列锁"""
        with self._lock_manager.queue_lock:
            return len(self._pending_queue)

    def has_pending_tasks(self) -> bool:
        """检查是否有待执行的任务 - 使用队列锁"""
        with self._lock_manager.queue_lock:
            return len(self._pending_queue) > 0

    def _check_dependencies(self):
        """检查依赖关系，将可执行的任务加入队列"""
        # 注意：此方法应该在已获取写锁的情况下调用
        for task in self._tasks.values():
            if (task.status == TaskStatus.PENDING and
                task.can_execute(self._completed_tasks)):
                task.set_status(TaskStatus.QUEUED)
                with self._lock_manager.queue_lock:
                    heapq.heappush(self._pending_queue, task)

    def _on_task_status_changed(self, task: Task, old_status: TaskStatus, new_status: TaskStatus):
        """任务状态变化回调"""
        self.task_status_changed.emit(task, old_status.value, new_status.value)
        self.queue_changed.emit()
