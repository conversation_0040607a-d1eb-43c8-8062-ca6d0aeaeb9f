"""
智能刷新管理器
负责优化UI刷新策略，避免频繁刷新导致的性能问题
"""
import time
from typing import Dict, Set, Optional, Callable
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
from PyQt5.QtWidgets import QWidget


class SmartRefreshManager(QObject):
    """智能刷新管理器"""
    
    # 信号定义
    refresh_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 防抖配置
        self.debounce_interval = 300  # 防抖间隔（毫秒），减少延迟
        self.debounce_timer = QTimer()
        self.debounce_timer.setSingleShot(True)
        self.debounce_timer.timeout.connect(self._execute_refresh)
        
        # 刷新状态
        self.last_refresh_time = 0
        self.pending_refresh = False
        self.is_visible = True
        
        # 增量更新缓存
        self.last_update_time = None
        self.cached_records = {}  # {record_id: record_data}
        self.dirty_records = set()  # 需要更新的记录ID
        
        # 性能监控
        self.refresh_count = 0
        self.total_refresh_time = 0
        
    def request_refresh(self, force: bool = False, immediate: bool = False):
        """请求刷新

        Args:
            force (bool): 是否强制刷新，忽略防抖
            immediate (bool): 是否立即执行，用于分页等需要快速响应的操作
        """
        current_time = time.time() * 1000  # 转换为毫秒

        if force or immediate:
            # 强制刷新或立即刷新，立即执行
            if self.debounce_timer.isActive():
                self.debounce_timer.stop()  # 停止防抖定时器
            self._execute_refresh()
            return

        # 检查是否在防抖间隔内
        if current_time - self.last_refresh_time < self.debounce_interval:
            # 在防抖间隔内，延迟执行
            self.pending_refresh = True
            if not self.debounce_timer.isActive():
                remaining_time = self.debounce_interval - (current_time - self.last_refresh_time)
                self.debounce_timer.start(max(50, int(remaining_time)))  # 减少最小延迟
        else:
            # 超出防抖间隔，可以执行刷新
            self._execute_refresh()
    
    def _execute_refresh(self):
        """执行刷新"""
        start_time = time.time()
        
        self.last_refresh_time = time.time() * 1000
        self.pending_refresh = False
        self.refresh_count += 1
        
        # 发送刷新信号
        self.refresh_requested.emit()
        
        # 记录性能数据
        refresh_time = (time.time() - start_time) * 1000
        self.total_refresh_time += refresh_time
        
        # 调整防抖间隔（基于性能）
        self._adjust_debounce_interval(refresh_time)
    
    def _adjust_debounce_interval(self, refresh_time: float):
        """根据刷新性能调整防抖间隔
        
        Args:
            refresh_time (float): 刷新耗时（毫秒）
        """
        if refresh_time > 1000:  # 刷新耗时超过1秒
            self.debounce_interval = min(2000, self.debounce_interval * 1.2)
        elif refresh_time < 100:  # 刷新很快
            self.debounce_interval = max(200, self.debounce_interval * 0.9)
    
    def set_visibility(self, visible: bool):
        """设置可见性状态
        
        Args:
            visible (bool): 是否可见
        """
        self.is_visible = visible
        
        # 调整防抖间隔
        if visible:
            self.debounce_interval = 500  # 可见时较短间隔
        else:
            self.debounce_interval = 2000  # 隐藏时较长间隔
    
    def mark_record_dirty(self, record_id: str):
        """标记记录为需要更新
        
        Args:
            record_id (str): 记录ID
        """
        self.dirty_records.add(record_id)
    
    def clear_dirty_records(self):
        """清除脏记录标记"""
        self.dirty_records.clear()
    
    def get_dirty_records(self) -> Set[str]:
        """获取需要更新的记录ID
        
        Returns:
            Set[str]: 脏记录ID集合
        """
        return self.dirty_records.copy()
    
    def update_cached_record(self, record_id: str, record_data: Dict):
        """更新缓存记录
        
        Args:
            record_id (str): 记录ID
            record_data (Dict): 记录数据
        """
        self.cached_records[record_id] = record_data
        self.mark_record_dirty(record_id)
    
    def get_cached_record(self, record_id: str) -> Optional[Dict]:
        """获取缓存记录
        
        Args:
            record_id (str): 记录ID
            
        Returns:
            Optional[Dict]: 记录数据，如果不存在返回None
        """
        return self.cached_records.get(record_id)
    
    def set_last_update_time(self, update_time: str):
        """设置最后更新时间
        
        Args:
            update_time (str): 更新时间（ISO格式）
        """
        self.last_update_time = update_time
    
    def get_last_update_time(self) -> Optional[str]:
        """获取最后更新时间

        Returns:
            Optional[str]: 最后更新时间，如果没有返回None
        """
        return self.last_update_time

    def clear_last_update_time(self):
        """清除最后更新时间，强制下次进行全量刷新"""
        self.last_update_time = None
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计
        
        Returns:
            Dict: 性能统计数据
        """
        avg_refresh_time = 0
        if self.refresh_count > 0:
            avg_refresh_time = self.total_refresh_time / self.refresh_count
        
        return {
            'refresh_count': self.refresh_count,
            'total_refresh_time': self.total_refresh_time,
            'avg_refresh_time': avg_refresh_time,
            'current_debounce_interval': self.debounce_interval,
            'cached_records_count': len(self.cached_records),
            'dirty_records_count': len(self.dirty_records)
        }
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.refresh_count = 0
        self.total_refresh_time = 0


class IncrementalTableUpdater:
    """增量表格更新器"""
    
    def __init__(self, table_widget):
        self.table = table_widget
        self.row_map = {}  # {record_id: row_index}
        self.record_map = {}  # {row_index: record_id}
        
    def update_records_incremental(self, records: list, dirty_record_ids: Set[str] = None):
        """增量更新表格记录
        
        Args:
            records (list): 所有记录列表
            dirty_record_ids (Set[str]): 需要更新的记录ID，如果为None则全量更新
        """
        if dirty_record_ids is None:
            # 全量更新
            self._full_update(records)
        else:
            # 增量更新
            self._incremental_update(records, dirty_record_ids)
    
    def _full_update(self, records: list):
        """全量更新表格"""
        self.table.setRowCount(len(records))
        self.row_map.clear()
        self.record_map.clear()
        
        for row, record in enumerate(records):
            record_id = str(record.get('id', ''))
            self.row_map[record_id] = row
            self.record_map[row] = record_id
            self._update_row(row, record)
    
    def _incremental_update(self, records: list, dirty_record_ids: Set[str]):
        """增量更新表格"""
        # 创建记录ID到记录的映射
        record_dict = {str(r.get('id', '')): r for r in records}
        
        # 更新脏记录
        for record_id in dirty_record_ids:
            if record_id in record_dict:
                record = record_dict[record_id]
                
                if record_id in self.row_map:
                    # 更新现有行
                    row = self.row_map[record_id]
                    self._update_row(row, record)
                else:
                    # 添加新行
                    row = self.table.rowCount()
                    self.table.setRowCount(row + 1)
                    self.row_map[record_id] = row
                    self.record_map[row] = record_id
                    self._update_row(row, record)
        
        # 检查是否有记录被删除
        current_record_ids = set(str(r.get('id', '')) for r in records)
        deleted_record_ids = set(self.row_map.keys()) - current_record_ids
        
        if deleted_record_ids:
            # 有记录被删除，需要重新构建表格
            self._full_update(records)
    
    def _update_row(self, row: int, record: Dict):
        """更新单行数据（需要子类实现）"""
        raise NotImplementedError("子类必须实现_update_row方法")
