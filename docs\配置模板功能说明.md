# RunSim GUI 配置模板功能说明

## 📋 功能概述

配置模板功能是RunSim GUI的智能工作流优化功能，允许用户保存常用的参数组合为模板，并可以一键应用这些模板，大大减少重复配置的工作量。

## ✨ 主要特性

### 1. 模板管理
- **保存模板**：将当前配置保存为可重用的模板
- **应用模板**：一键应用已保存的配置模板
- **编辑模板**：修改现有模板的名称、描述和配置
- **删除模板**：删除不再需要的模板
- **使用统计**：跟踪模板的使用频率

### 2. 智能界面
- **快速选择**：在配置面板直接选择和应用模板
- **详细管理**：通过专门的管理界面进行高级操作
- **配置预览**：在应用前预览模板包含的所有配置项
- **工具提示**：鼠标悬停显示模板详细信息
- **选择性应用**：只应用模板中包含的配置项，保留其他现有配置

### 3. 数据持久化
- **自动保存**：模板数据自动保存到本地文件
- **版本兼容**：支持模板数据的向后兼容
- **导入导出**：支持模板的备份和共享（未来版本）

## 🚀 使用方法

### 基本操作

#### 1. 保存配置模板
1. 在RunSim GUI中配置好所需的参数
2. 点击配置面板中的"管理"按钮
3. 在弹出的模板管理器中点击"保存当前配置为模板"
4. 输入模板名称和描述（可选）
5. 点击保存

#### 2. 应用配置模板
**方法一：快速应用**
1. 在配置面板的"配置模板"下拉框中选择模板
2. 点击"应用"按钮

**方法二：通过管理器应用**
1. 点击"管理"按钮打开模板管理器
2. 在左侧列表中选择要应用的模板
3. 在右侧预览配置内容
4. 点击"应用选中模板"按钮

#### 3. 管理配置模板
1. 点击配置面板中的"管理"按钮
2. 在模板管理器中可以：
   - 查看所有已保存的模板
   - 编辑模板信息和配置
   - 删除不需要的模板
   - 查看模板使用统计

### 高级功能

#### 模板编辑
1. 在模板管理器中选择要编辑的模板
2. 修改右侧的名称和描述
3. 点击"更新模板"按钮保存修改

#### 模板删除
1. 在模板管理器中选择要删除的模板
2. 点击"删除"按钮
3. 确认删除操作

## 📊 支持的配置项

配置模板支持保存以下所有配置项：

### 基础参数
- **BASE参数**：base环境配置
- **BLOCK路径**：block路径配置
- **运行目录**：rundir路径
- **提交服务器**：bq服务器名称
- **其他选项**：其他runsim选项

### 波形和调试选项
- **FSDB波形**：是否生成FSDB波形文件
- **VWDB波形**：是否生成VWDB波形文件
- **CL选项**：是否启用CL选项
- **Dump SVA断言**：是否转储SVA断言
- **收集覆盖率**：是否收集覆盖率数据
- **UPF仿真**：是否启用UPF仿真

### 执行模式
- **仅仿真**：只执行仿真（-R选项）
- **仅编译**：只执行编译（-C选项）

### 高级参数
- **内存转储**：dump_mem配置
- **波形时间**：wdd时间配置
- **种子号**：随机种子设置
- **仿真参数**：simarg参数
- **CFG定义**：cfg_def配置
- **后仿参数**：post仿真配置

### 回归测试参数
- **FM检查**：是否启用FM检查
- **标签**：回归测试标签
- **NT参数**：NT配置
- **看板参数**：dashboard配置

## ✨ 智能特性

### 选择性应用
配置模板具有智能的选择性应用功能：
- **只保存有意义的值**：模板只保存非空的配置项
- **只应用包含的配置**：应用模板时只修改模板中包含的配置项
- **保留现有配置**：模板中未包含的配置项保持不变

**示例场景**：
1. 当前配置：BASE="my_base", BLOCK="my_block", FSDB=true
2. 应用模板：只包含FSDB=false, COV=true
3. 结果配置：BASE="my_base", BLOCK="my_block", FSDB=false, COV=true

这样可以避免意外清空重要的配置项，如已选择的用例的BASE和BLOCK参数。

## 💡 使用技巧

### 1. 模板命名建议
- 使用描述性的名称，如"基础功能验证"、"回归测试配置"
- 包含关键信息，如"DVR1_覆盖率_调试模式"
- 避免使用特殊字符和过长的名称

### 2. 模板分类管理
- 按功能分类：基础测试、回归测试、调试配置等
- 按项目分类：不同项目使用不同的模板前缀
- 按阶段分类：开发阶段、验证阶段、发布阶段等

### 3. 最佳实践
- **定期清理**：删除不再使用的过时模板
- **描述完整**：为模板添加详细的描述信息
- **版本管理**：在模板名称中包含版本信息
- **团队共享**：与团队成员分享常用模板（未来版本支持）

## 🔧 技术实现

### 数据存储
- 模板数据存储在`runsim_config_templates.json`文件中
- 使用JSON格式，便于阅读和编辑
- 支持UTF-8编码，兼容中文字符

### 数据结构
```json
{
  "id": "唯一标识符",
  "name": "模板名称",
  "description": "模板描述",
  "config": {
    "base": "配置值",
    "block": "配置值",
    ...
  },
  "created_at": "创建时间",
  "updated_at": "更新时间",
  "usage_count": "使用次数"
}
```

### 安全性
- 自动备份：每次保存前自动备份现有数据
- 错误恢复：保存失败时自动恢复到之前状态
- 数据验证：加载时验证数据完整性

## 🐛 故障排除

### 常见问题

#### 1. 模板保存失败
**可能原因**：
- 磁盘空间不足
- 文件权限问题
- 模板名称已存在

**解决方法**：
- 检查磁盘空间
- 确保有写入权限
- 使用不同的模板名称

#### 2. 模板应用后配置不正确
**可能原因**：
- 模板数据损坏
- 界面组件状态异常

**解决方法**：
- 重新保存模板
- 重启应用程序
- 手动检查配置项

#### 3. 模板列表为空
**可能原因**：
- 模板文件不存在
- 文件格式错误
- 读取权限问题

**解决方法**：
- 创建新模板
- 检查文件格式
- 确保有读取权限

## 📈 未来规划

### 计划功能
1. **模板导入导出**：支持模板的备份和共享
2. **模板分组**：支持模板的分类管理
3. **模板搜索**：支持按名称和描述搜索模板
4. **模板版本控制**：支持模板的版本管理
5. **团队协作**：支持团队模板的共享和同步

### 性能优化
1. **延迟加载**：大量模板时的性能优化
2. **缓存机制**：提高模板加载速度
3. **增量更新**：只更新变化的配置项

---

**版本**：1.0.0  
**更新时间**：2024年12月  
**作者**：RunSim GUI开发团队
