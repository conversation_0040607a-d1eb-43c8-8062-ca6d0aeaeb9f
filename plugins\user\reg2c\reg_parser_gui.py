import sys
import os
import re
import pandas as pd
import jinja2
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QFileDialog, QMessageBox, QProgressDialog)
from PyQt5.QtCore import Qt

# 从我们的parser_engine模块导入函数
from .parser_engine import TemplateManager, parse_register_file

def clean_column_name(col):
    """清理列名，移除空格、下划线和斜杠"""
    return re.sub('[ _/]', '', str(col)).lower()

class RegParserApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.excel_path = ''
        self.output_dir = '.'
        
        # 初始化UI
        self._ui_initialized = False
        self._init_minimal_ui()
    
    def _init_minimal_ui(self):
        """初始化最小化UI，延迟初始化完整界面"""
        self.setWindowTitle('寄存器解析工具')
        self.setGeometry(200, 200, 500, 100)  # 调整初始窗口尺寸更紧凑
        
        # 创建一个等待加载的提示标签
        loading_widget = QWidget()
        self.setCentralWidget(loading_widget)
        loading_layout = QVBoxLayout(loading_widget)
        loading_label = QLabel("正在初始化界面，请稍候...")
        loading_label.setAlignment(Qt.AlignCenter)
        loading_layout.addWidget(loading_label)
    
    def showEvent(self, event):
        """窗口显示时完成完整UI初始化"""
        super().showEvent(event)
        
        if not self._ui_initialized:
            # 延迟初始化完整UI
            self.initUI()
            self.init_ui_styles()
            self._ui_initialized = True

    def init_ui_styles(self):
        """统一管理界面样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            QLabel[error="true"] {
                color: #D32F2F;
                font-weight: bold;
            }
        """)

    def initUI(self):
        # 创建主容器和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(5, 10, 5, 10)  # 紧凑边距
        main_layout.setSpacing(8)  # 减小控件间距

        # 文件选择区域
        file_group = QWidget()
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(8)
        
        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover { background-color: #45a049; }
        """

        # 第一行：Excel文件选择
        excel_layout = QHBoxLayout()
        self.btn_open = QPushButton('选择Excel文件')
        self.btn_open.setStyleSheet(button_style)
        self.btn_open.setMinimumWidth(100)
        excel_layout.addWidget(self.btn_open)
        
        self.lbl_excel = QLabel('未选择文件')
        self.lbl_excel.setStyleSheet("color: #666; font-size: 12px;")
        self.lbl_excel.setWordWrap(True)  # 允许自动换行
        excel_layout.addWidget(self.lbl_excel, 1)
        file_layout.addLayout(excel_layout)

        # 第二行：输出目录选择
        output_layout = QHBoxLayout()
        self.btn_output = QPushButton('选择输出目录')
        self.btn_output.setStyleSheet(button_style)
        self.btn_output.setMinimumWidth(100)
        output_layout.addWidget(self.btn_output)
        
        self.lbl_output = QLabel('当前输出目录: .')
        self.lbl_output.setStyleSheet("color: #666; font-size: 12px;")
        output_layout.addWidget(self.lbl_output, 1)
        file_layout.addLayout(output_layout)

        main_layout.addWidget(file_group)
        

        # 生成按钮
        self.btn_generate = QPushButton('生成驱动代码')
        self.btn_generate.setStyleSheet("""
            background-color: #2196F3;
            font-size: 16px;
            padding: 12px;
            border-radius: 6px;
        """)
        main_layout.addWidget(self.btn_generate)

        # 连接信号
        self.btn_open.clicked.connect(self.open_excel)
        self.btn_output.clicked.connect(self.select_output)
        self.btn_generate.clicked.connect(self.generate_code)

    def open_excel(self):
        path, _ = QFileDialog.getOpenFileName(self, '选择Excel文件', '', 'Excel Files (*.xlsx *.xls)')
        if path:
            self.excel_path = path
            self.lbl_excel.setText(f'已选择: {path}')

    def select_output(self):
        path = QFileDialog.getExistingDirectory(self, '选择输出目录')
        if path:
            self.output_dir = path
            self.lbl_output.setText(f'当前输出目录: {path}')

    def parse_reset_value(self, val):
        try:
            cleaned_val = str(val).strip()
            if 'b' in cleaned_val:  # 二进制格式处理
                return int(cleaned_val.replace('_', '').split('b')[-1], 2)
            elif "'h" in cleaned_val:  # Verilog格式
                return int(cleaned_val.split('h')[-1], 16)
            elif cleaned_val.startswith('0x'):  # 标准十六进制
                return int(cleaned_val, 16)
            return int(cleaned_val)
        except ValueError as e:
            raise ValueError(f"无效复位值格式: {val}") from e

    def generate_code(self):
        progress = None
        success = False  # 初始化状态变量
        try:
            if not self.excel_path:
                QMessageBox.warning(self, '错误', '请先选择Excel文件！')
                return
                
            # 创建进度对话框
            progress = QProgressDialog("正在生成代码...", "取消", 0, 100, self)
            progress.setWindowTitle("处理中")
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            
            # 更新进度
            progress.setValue(20)
            QApplication.processEvents()
            
            try:
                # 解析寄存器数据
                reg_data = parse_register_file(self.excel_path)
                registers = reg_data['registers']
                
                # 更新进度
                progress.setValue(50)
                QApplication.processEvents()
                
                # 创建模板管理器
                template_manager = TemplateManager()
                
                # 渲染各部分模板
                try:
                    c_struct = template_manager.render_template('c_struct.j2', 
                        {'registers': registers, 'module_name': reg_data['module_name']})
                    c_macros = template_manager.render_template('c_macros.j2',
                        {'registers': registers, 'base_addr': reg_data['base_addr'], 'module_name': reg_data['module_name']})
                    c_functions = template_manager.render_template('c_functions.j2',
                        {'registers': registers, 'module_name': reg_data['module_name']})
                    
                    # 更新进度
                    progress.setValue(80)
                    QApplication.processEvents()
                    
                except jinja2.exceptions.TemplateSyntaxError as e:
                    raise ValueError(f"模板语法错误: {e.message} (行{e.lineno})")
                except Exception as e:
                    raise ValueError(f"模板渲染失败: {str(e)}")
                
                # 创建输出目录（如果不存在）
                if not os.path.exists(self.output_dir):
                    os.makedirs(self.output_dir)
                
                # 生成文件名并写入文件
                filename = f"{reg_data['module_name']}_reg.h"
                file_path = os.path.join(self.output_dir, filename)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(c_macros + '\n\n' + c_struct + '\n\n' + c_functions)
                
                # 更新进度并标记成功
                progress.setValue(100)
                success = True
                
            except Exception as e:
                raise ValueError(f"处理失败: {str(e)}")
                
        except Exception as e:
            import traceback
            error_message = f'生成失败: {str(e)}\n{traceback.format_exc()}'
            print(error_message)
            QMessageBox.critical(self, '错误', error_message)
            
        finally:
            # 安全关闭进度条
            if progress is not None:
                progress.close()
            
            # 显示结果信息
            if success:
                QMessageBox.information(self, '完成', '驱动代码生成成功！')

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = RegParserApp()
    window.show()
    sys.exit(app.exec_())
