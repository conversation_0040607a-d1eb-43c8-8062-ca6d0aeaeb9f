import sys
import os
import re
import json
import time
from functools import partial
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QTabWidget, QListWidget, QListWidgetItem,
                             QPushButton, QFileDialog, QComboBox, QHBoxLayout, QVBoxLayout, QSplitter,
                             QLabel, QStyleFactory, QAbstractItemView, QMessageBox, QInputDialog, QProgressBar,
                             QCheckBox, QTextEdit, QMenu, QAction)
from PyQt5.QtCore import Qt, QProcess, QThread, pyqtSignal, QSettings, QPoint
from PyQt5.QtGui import QColor, QBrush, QFont, QIcon, QCursor

# 预编译正则表达式以提高性能
FORCE_PATTERN = re.compile(r'(?!//.*)(\bforce\b|sprd_hld_force|uvm_hld_force)')
WAIT_PATTERN = re.compile(r'(?!//.*)\bwait\b')
ENV_VAR_PATTERN = re.compile(r'\$\{([^}]+)\}|\$([a-zA-Z_][a-zA-Z0-9_]*)')

# 支持的编辑器配置
EDITORS = {
    'win32': {
        'gvim': ['gvim', '--remote-tab-silent'],
        'notepad++': ['C:\\Program Files\\Notepad++\\notepad++.exe', '-multiInst'],
        'vscode': ['code.cmd'],
        'ultraedit': ['C:\\Program Files\\IDM Computer Solutions\\UltraEdit\\uedit64.exe']
    },
    'linux': {
        'gvim': ['gvim', '--remote-tab-silent'],
        'vscode': ['code'],
        'sublime': ['subl'],
        'gedit': ['gedit']
    },
    'darwin': {
        'vscode': ['code'],
        'sublime': ['subl'],
        'textmate': ['mate']
    }
}


class ScannerThread(QThread):
    progress_updated = pyqtSignal(int)
    file_scanned = pyqtSignal(str, dict)  # 发射单个文件的结果
    scan_finished = pyqtSignal(dict)

    def __init__(self, proj_env, subsys, filters, max_workers=4):
        super().__init__()
        self.proj_env = proj_env
        self.subsys = subsys
        self.filters = filters
        self.max_workers = max_workers
        self.stopped = False

    def stop(self):
        self.stopped = True

    def run(self):
        start_time = time.time()
        # 收集检查路径
        check_paths = [
            os.path.join(self.proj_env, self.subsys),
            os.path.join(self.proj_env, 'udtb', self.subsys)
        ]
        if self.subsys in {'apcpu_sys', 'ch_sys', 'sp_sys', 'aon_sys',
                        'spch_sys', 'ps_cp_sys', 'phy_cp_sys'}:
            check_paths.append(os.path.join(self.proj_env, 'udtb', 'usvp'))
        
        # 收集所有需要处理的文件
        all_files = []
        for path in check_paths:
            if os.path.exists(path):
                for root, _, files in os.walk(path):
                    for f in files:
                        if f.split('.')[-1] in ('v', 'sv', 'svi', 'svh'):
                            all_files.append(os.path.join(root, f))
        
        total_files = len(all_files)
        if total_files == 0:
            self.scan_finished.emit({})
            return
            
        # 使用线程池并行处理文件
        current_files = {'force': {}, 'wait': {}}
        processed_count = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {executor.submit(self.check_file, file_path): file_path 
                             for file_path in all_files}
            
            for future in as_completed(future_to_file):
                if self.stopped:
                    break
                    
                file_path = future_to_file[future]
                results = future.result()
                
                if results['force'] > 0 and not self.is_filtered(file_path, 'force'):
                    current_files['force'][file_path] = {
                        'count': results['force'],
                        'lines': results['force_lines']
                    }
                    self.file_scanned.emit(file_path, {'type': 'force', 'count': results['force'], 'lines': results['force_lines']})
                    
                if results['wait'] > 0 and not self.is_filtered(file_path, 'wait'):
                    current_files['wait'][file_path] = {
                        'count': results['wait'],
                        'lines': results['wait_lines']
                    }
                    self.file_scanned.emit(file_path, {'type': 'wait', 'count': results['wait'], 'lines': results['wait_lines']})
                
                processed_count += 1
                self.progress_updated.emit(int(100 * processed_count / total_files))
        
        if not self.stopped:
            print(f"扫描完成，耗时: {time.time() - start_time:.2f}秒")
            self.scan_finished.emit(current_files)

    def check_file(self, file_path):
        comment_block = False
        results = {'force': 0, 'wait': 0, 'force_lines': [], 'wait_lines': []}
        
        try:
            with open(file_path, 'r', errors='ignore') as f:
                # 添加多行语句处理的变量
                current_statement = ""
                statement_start_line = 0
                
                for line_no, line in enumerate(f, 1):
                    # 跳过注释块
                    if '/*' in line and '*/' not in line:
                        comment_block = True
                    if '*/' in line:
                        comment_block = False
                        if '/*' not in line:
                            continue
                    if comment_block:
                        continue

                    # 跳过注释和空行
                    if line.strip().startswith('//') or not line.strip():
                        continue
                    
                    # 处理多行语句
                    # 如果是新语句的开始，记录起始行号
                    if not current_statement:
                        statement_start_line = line_no
                    
                    # 添加当前行到语句缓冲区
                    current_statement += " " + line.strip()
                    
                    # 如果语句未结束(没有分号)，继续读取下一行
                    if ";" not in line:
                        continue
                    
                    # 到这里说明语句已完整(有分号)，检查整个语句
                    # 检查force模式
                    if FORCE_PATTERN.search(current_statement):
                        if "FORCE_CHECK" not in current_statement:
                            results['force'] += 1
                            results['force_lines'].append((statement_start_line, current_statement.strip()))
                    
                    # 检查wait模式
                    if WAIT_PATTERN.search(current_statement):
                        if "WAIT_CHECK" not in current_statement:
                            results['wait'] += 1
                            results['wait_lines'].append((statement_start_line, current_statement.strip()))
                    
                    # 重置语句缓冲区
                    current_statement = ""
                
                # 处理文件末尾可能的未结束语句
                if current_statement and ";" not in current_statement:
                    # 未完成的语句，可以选择报告或忽略
                    pass
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
        
        return results

    def is_filtered(self, file_path, check_type):
        rel_path = os.path.relpath(file_path, self.proj_env)
        return rel_path in self.filters[check_type]


class EnvChecker(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # 加载设置
        self.settings = QSettings("SPRD", "EnvChecker")
        self.load_settings()
        
        self.setup_connections()
        self.current_files = {'force': {}, 'wait': {}}
        self.filters = {'force': set(), 'wait': set()}
        self.special_subsys = {'apcpu_sys', 'ch_sys', 'sp_sys', 'aon_sys',
                              'spch_sys', 'ps_cp_sys', 'phy_cp_sys'}
        self.editor_process = None
        self.scanner_thread = None

    def setup_ui(self):
        self.setWindowTitle("验证环境自动化检查工具 v3.0")
        self.setGeometry(100, 100, 1280, 800)

        # 定义与 Runsim GUI 主题一致的浅色主题样式
        runsim_theme_style = """
            /* 主窗口样式 - 与 Runsim GUI 保持一致 */
            QMainWindow {
                background-color: #f5f5f5;
                color: #444;
            }

            /* 列表控件样式 - 使用浅色主题 */
            QListWidget {
                background-color: white;
                color: #333;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                font-family: "Microsoft YaHei", Consolas, Courier, monospace;
                selection-background-color: #4a9eff;
                selection-color: white;
                padding: 5px;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #e0e0e0;
                border-radius: 2px;
                margin: 1px;
            }
            QListWidget::item:hover {
                background-color: #f0f8ff;
                border: 1px solid #4a9eff;
            }
            QListWidget::item:selected {
                background-color: #4a9eff;
                color: white;
                border: 1px solid #3d8ced;
            }

            /* 标签页样式 */
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                background-color: white;
                border-radius: 4px;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                color: #444;
                padding: 8px 20px;
                margin-right: 2px;
                border: 1px solid #d0d0d0;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-family: "Microsoft YaHei";
                font-weight: bold;
                min-width: 80px;
                max-width: 150px;
            }
            QTabBar::tab:selected {
                background-color: #4a9eff;
                color: white;
                border-color: #3d8ced;
            }
            QTabBar::tab:hover {
                background-color: #e6f3ff;
                color: #3d8ced;
            }

            /* 按钮样式 - 与 Runsim GUI 保持一致 */
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3d8ced;
            }
            QPushButton:pressed {
                background-color: #3274bf;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }

            /* 下拉框样式 */
            QComboBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
                color: #333;
                min-width: 100px;
                font-family: "Microsoft YaHei";
            }
            QComboBox:focus {
                border: 2px solid #4a9eff;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #f0f0f0;
                width: 20px;
                border-left: 1px solid #ccc;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin: 0px;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                color: #333;
                border: 1px solid #ccc;
                selection-background-color: #4a9eff;
                selection-color: white;
            }

            /* 标签样式 */
            QLabel {
                color: #444;
                font-family: "Microsoft YaHei";
                font-weight: normal;
            }

            /* 分割器样式 */
            QSplitter::handle {
                background-color: #d0d0d0;
                width: 3px;
                height: 3px;
            }
            QSplitter::handle:hover {
                background-color: #4a9eff;
            }

            /* 文本编辑器样式 */
            QTextEdit {
                background-color: white;
                color: #333;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px;
                font-family: "Microsoft YaHei", Consolas, Courier, monospace;
                selection-background-color: #4a9eff;
                selection-color: white;
            }
            QTextEdit:focus {
                border: 2px solid #4a9eff;
            }

            /* 进度条样式 */
            QProgressBar {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                text-align: center;
                color: #333;
                font-family: "Microsoft YaHei";
            }
            QProgressBar::chunk {
                background-color: #4a9eff;
                border-radius: 3px;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #f5f5f5;
                color: #444;
                border-top: 1px solid #d0d0d0;
                font-family: "Microsoft YaHei";
            }

            /* 分组框样式 */
            QGroupBox {
                font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }

            /* 复选框样式 */
            QCheckBox {
                font-family: "Microsoft YaHei";
                spacing: 5px;
                color: #444;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #ccc;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4a9eff;
                border-color: #4a9eff;
            }
            QCheckBox::indicator:hover {
                border-color: #4a9eff;
            }
        """

        # 设置样式表，与 Runsim GUI 主题保持一致
        self.setStyleSheet(runsim_theme_style)

        # 强制设置窗口属性，确保主题生效
        self.setAttribute(Qt.WA_StyledBackground, True)

        # 设置应用程序字体，与主程序保持一致
        from PyQt5.QtGui import QFont
        app_font = QFont("Microsoft YaHei", 9)
        self.setFont(app_font)

        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)

        # Header Section
        header_layout = QHBoxLayout()
        
        # 项目路径选择部分
        path_section = QHBoxLayout()
        self.path_label = QLabel("项目路径:")
        self.path_value = QLabel("未设置")
        self.select_path_btn = QPushButton("选择路径")
        path_section.addWidget(self.path_label)
        path_section.addWidget(self.path_value)
        path_section.addWidget(self.select_path_btn)
        
        # 子系统选择部分
        subsys_section = QHBoxLayout()
        subsys_label = QLabel("子系统:")
        self.subsys_combo = QComboBox()
        self.subsys_combo.setMinimumWidth(200)
        self.scan_button = QPushButton("扫描")
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.scan_button.setEnabled(False)  # 初始禁用扫描按钮
        subsys_section.addWidget(subsys_label)
        subsys_section.addWidget(self.subsys_combo)
        subsys_section.addWidget(self.scan_button)
        subsys_section.addWidget(self.stop_button)

        header_layout.addLayout(path_section)
        header_layout.addLayout(subsys_section)

        # Editor Selection
        self.editor_combo = QComboBox()
        editor_options = EDITORS.get(sys.platform, EDITORS['linux']).keys()
        self.editor_combo.addItems(editor_options)
        header_layout.addWidget(QLabel("编辑器:"))
        header_layout.addWidget(self.editor_combo)

        # 主分割器
        self.main_splitter = QSplitter(Qt.Vertical)
        
        # 结果标签页
        self.tab_widget = QTabWidget()
        self.force_tab = QListWidget()
        self.wait_tab = QListWidget()
        self.force_tab.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.wait_tab.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.force_tab.setContextMenuPolicy(Qt.CustomContextMenu)
        self.wait_tab.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tab_widget.addTab(self.force_tab, "Force语句 (0)")
        self.tab_widget.addTab(self.wait_tab, "Wait语句 (0)")

        # 设置标签页的最小尺寸，确保文字能完整显示
        self.tab_widget.setMinimumWidth(300)
        # 设置标签页栏的展开模式，让标签页有足够空间
        self.tab_widget.tabBar().setExpanding(True)
        # 设置标签页的最小宽度
        self.tab_widget.tabBar().setMinimumWidth(200)
        
        # 代码预览区域
        self.preview_widget = QWidget()
        preview_layout = QVBoxLayout(self.preview_widget)
        preview_label = QLabel("代码预览:")
        self.code_preview = QTextEdit()
        self.code_preview.setReadOnly(True)
        self.code_preview.setFont(QFont("Consolas", 10))
        preview_layout.addWidget(preview_label)
        preview_layout.addWidget(self.code_preview)
        
        self.main_splitter.addWidget(self.tab_widget)
        self.main_splitter.addWidget(self.preview_widget)
        self.main_splitter.setSizes([500, 300])
        
        # 操作按钮
        button_layout = QHBoxLayout()
        self.open_button = QPushButton("打开文件")
        self.confirm_button = QPushButton("确认标记")
        self.batch_confirm_button = QPushButton("批量确认")
        self.export_button = QPushButton("导出报告")
        self.open_button.setEnabled(False)
        self.confirm_button.setEnabled(False)
        self.batch_confirm_button.setEnabled(False)
        self.export_button.setEnabled(False)
        button_layout.addWidget(self.open_button)
        button_layout.addWidget(self.confirm_button)
        button_layout.addWidget(self.batch_confirm_button)
        button_layout.addWidget(self.export_button)

        main_layout.addLayout(header_layout)
        main_layout.addWidget(self.main_splitter)
        main_layout.addLayout(button_layout)

        # 状态栏
        self.statusBar().showMessage("就绪")
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)

    def setup_connections(self):
        self.select_path_btn.clicked.connect(self.select_project_path)
        self.scan_button.clicked.connect(self.start_check)
        self.stop_button.clicked.connect(self.stop_scan)
        self.force_tab.itemSelectionChanged.connect(self.update_button_state)
        self.wait_tab.itemSelectionChanged.connect(self.update_button_state)
        self.force_tab.itemClicked.connect(self.preview_file)
        self.wait_tab.itemClicked.connect(self.preview_file)
        self.force_tab.customContextMenuRequested.connect(self.show_context_menu)
        self.wait_tab.customContextMenuRequested.connect(self.show_context_menu)
        self.open_button.clicked.connect(self.open_selected_file)
        self.confirm_button.clicked.connect(self.mark_confirmed)
        self.batch_confirm_button.clicked.connect(self.batch_confirm)
        self.export_button.clicked.connect(self.export_report)
        self.subsys_combo.currentIndexChanged.connect(self.on_subsys_changed)

    def load_settings(self):
        proj_env = self.settings.value("proj_env", os.environ.get('PROJ_ENV', ''))
        if proj_env and os.path.exists(proj_env):
            self.proj_env = proj_env
            self.path_value.setText(proj_env)
            os.environ['PROJ_ENV'] = proj_env
            self.scan_subsys_dirs()
        
        editor = self.settings.value("editor", "")
        if editor and self.editor_combo.findText(editor) >= 0:
            self.editor_combo.setCurrentText(editor)

    def save_settings(self):
        if hasattr(self, 'proj_env'):
            self.settings.setValue("proj_env", self.proj_env)
        if self.editor_combo.currentText():
            self.settings.setValue("editor", self.editor_combo.currentText())

    def select_project_path(self):
        selected_path = QFileDialog.getExistingDirectory(self, "选择项目根目录")
        if selected_path:
            self.proj_env = selected_path
            os.environ['PROJ_ENV'] = selected_path
            self.path_value.setText(selected_path)
            self.save_settings()
            self.scan_subsys_dirs()

    def scan_subsys_dirs(self):
        if not hasattr(self, 'proj_env'):
            proj_env = os.environ.get('PROJ_ENV')
            if not proj_env:
                self.select_project_path()
                return
            self.proj_env = proj_env
            self.path_value.setText(proj_env)
        
        sys_dirs = []
        # 只扫描直接子目录，而不是递归地浏览
        try:
            for item in os.listdir(self.proj_env):
                item_path = os.path.join(self.proj_env, item)
                if os.path.isdir(item_path) and (item.endswith('_sys') or item == 'top'):
                    sys_dirs.append(item)
        except FileNotFoundError:
            pass
        
        self.subsys_combo.clear()
        self.subsys_combo.addItems(sorted(sys_dirs))
        self.scan_button.setEnabled(len(sys_dirs) > 0)

    def on_subsys_changed(self, index):
        if index >= 0:
            self.scan_button.setEnabled(True)

    def start_check(self):
        subsys = self.subsys_combo.currentText()
        if not subsys:
            return
        
        self.force_tab.clear()
        self.wait_tab.clear()
        self.tab_widget.setTabText(0, "Force语句 (0)")
        self.tab_widget.setTabText(1, "Wait语句 (0)")
        self.code_preview.clear()
        self.current_files = {'force': {}, 'wait': {}}
        
        # 加载过滤器
        self.filters = self.load_filter_files(subsys)
        
        # 更新UI状态
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)
        self.scan_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.statusBar().showMessage("扫描中...")
        
        # 启动扫描线程
        self.scanner_thread = ScannerThread(self.proj_env, subsys, self.filters)
        self.scanner_thread.progress_updated.connect(self.update_progress)
        self.scanner_thread.file_scanned.connect(self.add_file_result)
        self.scanner_thread.scan_finished.connect(self.scan_completed)
        self.scanner_thread.start()

    def stop_scan(self):
        if self.scanner_thread and self.scanner_thread.isRunning():
            self.scanner_thread.stop()
            self.statusBar().showMessage("正在停止扫描...")
            self.stop_button.setEnabled(False)

    def expand_env_vars(self, path):
        """展开路径中的环境变量"""
        def replace_env_var(match):
            var_name = match.group(1) or match.group(2)
            return os.environ.get(var_name, '')
        
        return ENV_VAR_PATTERN.sub(replace_env_var, path)

    def load_filter_files(self, subsys):
        filters = {'force': set(), 'wait': set()}
        filter_dirs = [
            os.path.join(self.proj_env, subsys, 'env_checker'),
            os.path.join(self.proj_env, 'udtb', subsys, 'env_checker')
        ]
        
        for d in filter_dirs:
            if os.path.exists(d):
                for f in os.listdir(d):
                    if f.endswith('.force_filter'):
                        with open(os.path.join(d, f)) as ff:
                            # 处理每一行可能包含的环境变量
                            filters['force'].update(
                                self.expand_env_vars(line.strip()) 
                                for line in ff.read().splitlines() if line.strip()
                            )
                    elif f.endswith('.wait.filter'):
                        with open(os.path.join(d, f)) as wf:
                            # 处理每一行可能包含的环境变量
                            filters['wait'].update(
                                self.expand_env_vars(line.strip())
                                for line in wf.read().splitlines() if line.strip()
                            )
        
        return filters

    def add_file_result(self, file_path, result):
        """动态添加扫描结果"""
        check_type = result['type']
        count = result['count']
        lines = result.get('lines', [])
        
        item = QListWidgetItem(f"{file_path} ({count}处)")
        item.setData(Qt.UserRole, {'path': file_path, 'lines': lines})
        
        if check_type == 'force':
            self.force_tab.addItem(item)
            self.current_files['force'][file_path] = {'count': count, 'lines': lines}
            self.tab_widget.setTabText(0, f"Force语句 ({self.force_tab.count()})")
        else:
            self.wait_tab.addItem(item)
            self.current_files['wait'][file_path] = {'count': count, 'lines': lines}
            self.tab_widget.setTabText(1, f"Wait语句 ({self.wait_tab.count()})")
        
        self.export_button.setEnabled(self.force_tab.count() > 0 or self.wait_tab.count() > 0)
        self.batch_confirm_button.setEnabled(self.force_tab.count() > 0 or self.wait_tab.count() > 0)

    def scan_completed(self, current_files):
        self.progress_bar.setVisible(False)
        self.scan_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        force_count = len(current_files.get('force', {}))
        wait_count = len(current_files.get('wait', {}))
        
        self.statusBar().showMessage(f"扫描完成：发现 Force 语句 {force_count} 个文件，Wait 语句 {wait_count} 个文件")

    def preview_file(self, item):
        """预览选中的文件和行"""
        data = item.data(Qt.UserRole)
        if isinstance(data, dict) and 'path' in data:
            file_path = data['path']
            lines = data.get('lines', [])
            
            try:
                with open(file_path, 'r', errors='ignore') as f:
                    content = f.readlines()
                
                preview_text = ""
                for line_no, line_content in lines:
                    context_start = max(0, line_no - 3)
                    context_end = min(len(content), line_no + 2)
                    
                    preview_text += f"===== 行 {line_no} =====\n"
                    for i in range(context_start, context_end):
                        if i == line_no - 1:  # 因为行号是1-based，但列表索引是0-based
                            preview_text += f"> {i+1}: {content[i]}"
                        else:
                            preview_text += f"  {i+1}: {content[i]}"
                    preview_text += "\n"
                
                self.code_preview.setPlainText(preview_text)
            except Exception as e:
                self.code_preview.setPlainText(f"无法预览文件: {e}")
        elif isinstance(data, str):  # 兼容旧版本数据格式
            file_path = data
            try:
                with open(file_path, 'r', errors='ignore') as f:
                    self.code_preview.setPlainText(f.read())
            except Exception as e:
                self.code_preview.setPlainText(f"无法预览文件: {e}")

    def show_context_menu(self, position):
        """显示右键菜单"""
        current_tab = self.tab_widget.currentWidget()
        if not current_tab.selectedItems():
            return
            
        menu = QMenu()
        open_action = menu.addAction("打开文件")
        mark_action = menu.addAction("标记确认")
        
        # 如果选中了多个项目，添加批量操作选项
        if len(current_tab.selectedItems()) > 1:
            batch_mark_action = menu.addAction("批量标记确认")
            action = menu.exec_(QCursor.pos())
            
            if action == batch_mark_action:
                self.batch_confirm()
                return
        else:
            action = menu.exec_(QCursor.pos())
        
        if action == open_action:
            self.open_selected_file()
        elif action == mark_action:
            self.mark_confirmed()

    def open_selected_file(self):
        current_tab = self.tab_widget.currentWidget()
        selected = current_tab.selectedItems()
        if not selected:
            return
        
        data = selected[0].data(Qt.UserRole)
        if isinstance(data, dict):
            file_path = data['path']
        else:  # 兼容旧版本数据格式
            file_path = data
            
        self.open_in_editor(file_path)

    def open_in_editor(self, file_path):
        editor = self.editor_combo.currentText().lower()
        platform_editors = EDITORS.get(sys.platform, EDITORS['linux'])
        
        if editor in platform_editors:
            command = platform_editors[editor] + [file_path]
            self.editor_process = QProcess()
            self.editor_process.finished.connect(self.update_button_state)
            self.editor_process.startDetached(command[0], command[1:])
        else:
            self.show_styled_message("编辑器错误", f"不支持的编辑器: {editor}", 'warning')

    def mark_confirmed(self):
        current_tab = self.tab_widget.currentWidget()
        selected = current_tab.selectedItems()
        if not selected:
            return

        data = selected[0].data(Qt.UserRole)
        if isinstance(data, dict):
            file_path = data['path']
        else:  # 兼容旧版本数据格式
            file_path = data

        check_type = 'force' if current_tab is self.force_tab else 'wait'

        # 创建样式化的输入对话框
        user_input, ok = self.get_styled_input('确认信息', '请输入确认信息(如:Confirmed by xxx):')

        if ok:
            self.add_check_comment(file_path, check_type, user_input)
            selected[0].setForeground(QBrush(QColor('#888888')))
            self.confirm_button.setEnabled(False)
            self.statusBar().showMessage(f"已标记 {file_path}")

    def batch_confirm(self):
        """批量确认选中的文件"""
        current_tab = self.tab_widget.currentWidget()
        selected = current_tab.selectedItems()
        if not selected:
            return

        check_type = 'force' if current_tab is self.force_tab else 'wait'

        # 创建样式化的输入对话框
        user_input, ok = self.get_styled_input('批量确认', '请输入确认信息(如:Confirmed by xxx):')

        if ok:
            for item in selected:
                data = item.data(Qt.UserRole)
                if isinstance(data, dict):
                    file_path = data['path']
                else:  # 兼容旧版本数据格式
                    file_path = data

                self.add_check_comment(file_path, check_type, user_input)
                item.setForeground(QBrush(QColor('#888888')))

            self.statusBar().showMessage(f"批量标记完成: {len(selected)}个文件")

    def get_styled_input(self, title, label_text, default_text=''):
        """创建样式化的输入对话框"""
        dialog = QInputDialog(self)
        dialog.setWindowTitle(title)
        dialog.setLabelText(label_text)
        dialog.setTextValue(default_text)

        # 应用与 Runsim GUI 一致的浅色主题样式
        dialog.setStyleSheet("""
            QInputDialog {
                background-color: #f5f5f5;
                color: #444;
                min-width: 400px;
                min-height: 150px;
                font-family: "Microsoft YaHei";
            }
            QInputDialog QLabel {
                color: #444;
                font-size: 12px;
                padding: 5px;
                font-family: "Microsoft YaHei";
            }
            QInputDialog QLineEdit {
                background-color: white;
                color: #333;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
                min-width: 300px;
                font-family: "Microsoft YaHei";
                selection-background-color: #4a9eff;
            }
            QInputDialog QLineEdit:focus {
                border: 2px solid #4a9eff;
            }
            QInputDialog QPushButton {
                background-color: #4a9eff;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
                border: none;
                font-size: 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }
            QInputDialog QPushButton:hover {
                background-color: #3d8ced;
            }
            QInputDialog QPushButton:pressed {
                background-color: #3274bf;
            }
        """)

        ok = dialog.exec_() == QInputDialog.Accepted
        return dialog.textValue(), ok

    def show_styled_message(self, title, message, msg_type='information'):
        """显示样式化的消息框"""
        # 创建消息框
        box = QMessageBox(self)
        box.setWindowTitle(title)
        box.setText(message)

        if msg_type == 'warning':
            box.setIcon(QMessageBox.Warning)
        elif msg_type == 'critical':
            box.setIcon(QMessageBox.Critical)
        else:
            box.setIcon(QMessageBox.Information)

        # 应用与 Runsim GUI 一致的浅色主题样式
        box.setStyleSheet("""
            QMessageBox {
                background-color: #f5f5f5;
                color: #444;
                min-width: 300px;
                font-family: "Microsoft YaHei";
            }
            QMessageBox QLabel {
                color: #444;
                font-size: 12px;
                padding: 10px;
                font-family: "Microsoft YaHei";
            }
            QMessageBox QPushButton {
                background-color: #4a9eff;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
                border: none;
                font-size: 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }
            QMessageBox QPushButton:hover {
                background-color: #3d8ced;
            }
            QMessageBox QPushButton:pressed {
                background-color: #3274bf;
            }
        """)

        return box.exec_()

    def add_check_comment(self, file_path, check_type, user_input=''):
        try:
            with open(file_path, 'r+', errors='ignore') as f:
                lines = f.readlines()
                modified = False
                comment_block = False
                statement_buffer = ""
                statement_lines = []
                
                i = 0
                while i < len(lines):
                    line = lines[i]
                    
                    # 跳过注释块
                    if '/*' in line and '*/' not in line:
                        comment_block = True
                    if '*/' in line:
                        comment_block = False
                        if '/*' not in line:
                            i += 1
                            continue
                    if comment_block:
                        i += 1
                        continue

                    # 跳过注释和空行
                    if line.strip().startswith('//') or not line.strip():
                        i += 1
                        continue
                    
                    # 收集当前语句行
                    statement_buffer += " " + line.strip()
                    statement_lines.append(i)
                    
                    # 如果语句未结束，继续读取下一行
                    if ";" not in line:
                        i += 1
                        continue
                    
                    # 语句结束，检查并处理
                    if check_type == 'force' and FORCE_PATTERN.search(statement_buffer):
                        if "FORCE_CHECK" not in statement_buffer:
                            # 在最后一行添加标记
                            last_line_idx = statement_lines[-1]
                            last_line = lines[last_line_idx]
                            if ';' in last_line:  # 安全检查
                                lines[last_line_idx] = last_line.rstrip() + f' // FORCE_CHECK({user_input})\n' if user_input else last_line.rstrip() + ' // FORCE_CHECK\n'
                                modified = True
                    
                    elif check_type == 'wait' and WAIT_PATTERN.search(statement_buffer):
                        if "WAIT_CHECK" not in statement_buffer:
                            # 在最后一行添加标记
                            last_line_idx = statement_lines[-1]
                            last_line = lines[last_line_idx]
                            if ';' in last_line:  # 安全检查
                                lines[last_line_idx] = last_line.rstrip() + f' // WAIT_CHECK({user_input})\n' if user_input else last_line.rstrip() + ' // WAIT_CHECK\n'
                                modified = True
                    
                    # 重置状态
                    statement_buffer = ""
                    statement_lines = []
                    i += 1
                
                if modified:
                    f.seek(0)
                    f.writelines(lines)
                    f.truncate()
        except Exception as e:
            self.show_styled_message("文件操作错误", f"标记文件时出错：{e}", 'warning')

    def update_button_state(self):
        current_tab = self.tab_widget.currentWidget()
        has_selection = bool(current_tab.selectedItems())
        self.open_button.setEnabled(has_selection)
        self.confirm_button.setEnabled(has_selection)

    def update_progress(self, progress):
        self.progress_bar.setValue(progress)

    def export_report(self):
        """导出检查结果报告"""
        if not (self.current_files['force'] or self.current_files['wait']):
            self.show_styled_message("导出报告", "没有发现问题，无需导出报告。")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(self, "保存报告", 
                                                os.path.join(os.getcwd(), f"{self.subsys_combo.currentText()}_report.html"),
                                                "HTML Files (*.html)")
        if not file_path:
            return
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>验证环境检查报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2 {{ color: #333; }}
        .section {{ margin-bottom: 30px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
    </style>
</head>
<body>
    <h1>验证环境检查报告</h1>
    <p>子系统: {self.subsys_combo.currentText()}</p>
    <p>生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <div class="section">
        <h2>Force语句检查 ({len(self.current_files['force'])}个文件)</h2>
        <table>
            <tr>
                <th>文件</th>
                <th>问题数量</th>
            </tr>""")
                
                for path, info in self.current_files['force'].items():
                    f.write(f"""
            <tr>
                <td>{path}</td>
                <td>{info.get('count', 0)}</td>
            </tr>""")
                    
                f.write(f"""
        </table>
    </div>
    
    <div class="section">
        <h2>Wait语句检查 ({len(self.current_files['wait'])}个文件)</h2>
        <table>
            <tr>
                <th>文件</th>
                <th>问题数量</th>
            </tr>""")
                
                for path, info in self.current_files['wait'].items():
                    f.write(f"""
            <tr>
                <td>{path}</td>
                <td>{info.get('count', 0)}</td>
            </tr>""")
                    
                f.write("""
        </table>
    </div>
</body>
</html>""")
                
            self.show_styled_message("导出成功", f"报告已成功导出到\n{file_path}")

        except Exception as e:
            self.show_styled_message("导出失败", f"报告导出失败：{e}", 'warning')

    def closeEvent(self, event):
        """程序关闭时保存设置"""
        self.save_settings()
        if self.scanner_thread and self.scanner_thread.isRunning():
            self.scanner_thread.stop()
            self.scanner_thread.wait()
        super().closeEvent(event)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyle(QStyleFactory.create('Fusion'))
    window = EnvChecker()
    window.show()
    sys.exit(app.exec_())
