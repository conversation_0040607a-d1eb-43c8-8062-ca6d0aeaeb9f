"""
终端检测模块
用于检测Linux环境下打开的终端窗口和标签页
"""
import subprocess
import re
import os
from typing import List, Dict, Optional


class TerminalInfo:
    """终端信息类"""
    
    def __init__(self, window_id: str, title: str, terminal_type: str, pid: Optional[str] = None):
        self.window_id = window_id
        self.title = title
        self.terminal_type = terminal_type
        self.pid = pid
        
    def __str__(self):
        return f"{self.terminal_type}: {self.title}"
        
    def __repr__(self):
        return f"TerminalInfo(id={self.window_id}, title='{self.title}', type={self.terminal_type})"


class TerminalDetector:
    """终端检测器"""
    
    # 支持的终端类型及其识别模式
    TERMINAL_PATTERNS = {
        'gnome-terminal': [
            r'.*Terminal.*',
            r'.*gnome-terminal.*',
            r'.*@.*:.*',  # 用户@主机:路径 格式
        ],
        'konsole': [
            r'.*Konsole.*',
            r'.*konsole.*',
        ],
        'xterm': [
            r'.*xterm.*',
            r'.*XTerm.*',
        ],
        'terminator': [
            r'.*Terminator.*',
            r'.*terminator.*',
        ],
        'tilix': [
            r'.*Tilix.*',
            r'.*tilix.*',
        ],
        'alacritty': [
            r'.*Alacritty.*',
            r'.*alacritty.*',
        ],
        'kitty': [
            r'.*kitty.*',
            r'.*Kitty.*',
        ]
    }
    
    @classmethod
    def check_dependencies(cls) -> Dict[str, bool]:
        """检查依赖工具是否可用"""
        dependencies = {}
        
        # 检查wmctrl
        try:
            subprocess.run(['wmctrl', '-l'], capture_output=True, check=True)
            dependencies['wmctrl'] = True
        except (subprocess.CalledProcessError, FileNotFoundError):
            dependencies['wmctrl'] = False
            
        # 检查xdotool
        try:
            subprocess.run(['xdotool', '--version'], capture_output=True, check=True)
            dependencies['xdotool'] = True
        except (subprocess.CalledProcessError, FileNotFoundError):
            dependencies['xdotool'] = False
            
        # 检查ps
        try:
            subprocess.run(['ps', '--version'], capture_output=True, check=True)
            dependencies['ps'] = True
        except (subprocess.CalledProcessError, FileNotFoundError):
            dependencies['ps'] = False
            
        return dependencies
    
    @classmethod
    def detect_terminals(cls) -> List[TerminalInfo]:
        """检测所有打开的终端窗口"""
        terminals = []
        
        # 检查依赖
        deps = cls.check_dependencies()
        if not deps.get('wmctrl', False):
            print("警告: wmctrl 未安装，无法检测终端窗口")
            return terminals
            
        try:
            # 使用wmctrl获取所有窗口
            result = subprocess.run(['wmctrl', '-l'], capture_output=True, text=True, check=True)
            windows = result.stdout.strip().split('\n')
            
            for window_line in windows:
                if not window_line.strip():
                    continue
                    
                # 解析窗口信息: window_id desktop_id hostname title
                parts = window_line.split(None, 3)
                if len(parts) < 4:
                    continue
                    
                window_id = parts[0]
                title = parts[3]
                
                # 检查是否为终端窗口
                terminal_type = cls._identify_terminal_type(title)
                if terminal_type:
                    # 尝试获取PID
                    pid = cls._get_window_pid(window_id)
                    terminals.append(TerminalInfo(window_id, title, terminal_type, pid))
                    
        except subprocess.CalledProcessError as e:
            print(f"检测终端时出错: {e}")
        except Exception as e:
            print(f"检测终端时发生未知错误: {e}")
            
        return terminals
    
    @classmethod
    def _identify_terminal_type(cls, title: str) -> Optional[str]:
        """根据窗口标题识别终端类型"""
        for terminal_type, patterns in cls.TERMINAL_PATTERNS.items():
            for pattern in patterns:
                if re.match(pattern, title, re.IGNORECASE):
                    return terminal_type
        return None
    
    @classmethod
    def _get_window_pid(cls, window_id: str) -> Optional[str]:
        """获取窗口对应的进程ID"""
        try:
            # 使用xprop获取窗口的PID
            result = subprocess.run(
                ['xprop', '-id', window_id, '_NET_WM_PID'],
                capture_output=True, text=True, check=True
            )
            
            # 解析输出: _NET_WM_PID(CARDINAL) = 12345
            match = re.search(r'_NET_WM_PID\(CARDINAL\)\s*=\s*(\d+)', result.stdout)
            if match:
                return match.group(1)
                
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
            
        return None
    
    @classmethod
    def get_terminal_tabs(cls, terminal_info: TerminalInfo) -> List[str]:
        """获取指定终端的标签页列表（如果支持）"""
        # 注意：大多数终端不提供直接的API来列出标签页
        # 这里返回基本信息，实际实现可能需要特定终端的支持
        tabs = []
        
        if terminal_info.terminal_type == 'gnome-terminal':
            # gnome-terminal的标签页检测比较复杂，需要特殊处理
            tabs = cls._get_gnome_terminal_tabs(terminal_info)
        elif terminal_info.terminal_type == 'konsole':
            # Konsole可能有不同的处理方式
            tabs = cls._get_konsole_tabs(terminal_info)
        else:
            # 对于其他终端，返回窗口本身作为单个"标签页"
            tabs = [terminal_info.title]
            
        return tabs
    
    @classmethod
    def _get_gnome_terminal_tabs(cls, terminal_info: TerminalInfo) -> List[str]:
        """获取gnome-terminal的标签页"""
        # gnome-terminal的标签页检测比较复杂
        # 简化实现：返回窗口标题作为单个标签页
        return [terminal_info.title]
    
    @classmethod
    def _get_konsole_tabs(cls, terminal_info: TerminalInfo) -> List[str]:
        """获取Konsole的标签页"""
        # Konsole的标签页检测
        # 简化实现：返回窗口标题作为单个标签页
        return [terminal_info.title]
    
    @classmethod
    def is_terminal_available(cls) -> bool:
        """检查终端检测功能是否可用"""
        deps = cls.check_dependencies()
        return deps.get('wmctrl', False) and deps.get('xdotool', False)


if __name__ == "__main__":
    # 测试代码
    print("检查依赖工具...")
    deps = TerminalDetector.check_dependencies()
    for tool, available in deps.items():
        status = "✓" if available else "✗"
        print(f"  {tool}: {status}")
    
    print("\n检测终端窗口...")
    terminals = TerminalDetector.detect_terminals()
    
    if terminals:
        print(f"找到 {len(terminals)} 个终端窗口:")
        for i, terminal in enumerate(terminals, 1):
            print(f"  {i}. {terminal}")
            print(f"     窗口ID: {terminal.window_id}")
            if terminal.pid:
                print(f"     进程ID: {terminal.pid}")
    else:
        print("未找到终端窗口")
