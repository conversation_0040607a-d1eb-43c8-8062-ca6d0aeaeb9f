"""
配置控制器
"""
import os
import random
import re
import time
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal

from views.config_panel import ConfigPanel
from models.command_model import CommandModel
from utils.event_bus import EventBus

class ConfigController(QObject):
    """配置控制器，负责管理配置相关操作"""

    # 定义信号
    execution_requested = pyqtSignal(str, str)  # command, case_name

    def __init__(self, main_window, config_model, history_model, app_controller=None):
        """
        初始化配置控制器

        Args:
            main_window (MainWindow): 主窗口
            config_model (ConfigModel): 配置模型
            history_model (HistoryModel): 历史记录模型
            app_controller (AppController, optional): 应用控制器引用
        """
        super().__init__()
        self.main_window = main_window
        self.config_model = config_model
        self.history_model = history_model
        self.command_model = CommandModel(config_model)
        self.execution_controller = None  # 将在 set_execution_controller 方法中设置
        self.app_controller = app_controller  # 添加app_controller引用

        # 获取事件总线实例
        self.event_bus = EventBus.instance()

        # 创建配置面板
        self.config_panel = ConfigPanel()

        # 添加执行状态管理
        self.is_executing = False
        self.execution_start_time = 0

        # 连接信号
        self.connect_signals()

        # 连接历史记录模型的信号
        self.history_model.history_changed.connect(self.update_history_view)

    def set_execution_controller(self, execution_controller):
        """
        设置执行控制器引用

        Args:
            execution_controller (ExecutionController): 执行控制器
        """
        self.execution_controller = execution_controller

    def connect_signals(self):
        """连接信号和槽"""
        # 配置面板信号
        # 使用事件总线处理配置变更信号，避免无限递归
        self.config_panel.config_changed.connect(self.handle_config_changed)
        self.config_panel.execute_command_requested.connect(self.execute_command)
        self.config_panel.terminal_execute_requested.connect(self.execute_command_in_terminal)
        self.config_panel.apply_history_requested.connect(self.apply_history)
        self.config_panel.re_run_history_requested.connect(self.re_run_history)
        self.config_panel.select_fsdb_file_requested.connect(self.select_fsdb_file)
        self.config_panel.clear_fsdb_file_requested.connect(self.clear_fsdb_file)
        self.config_panel.select_regr_file_requested.connect(self.select_regr_file)
        self.config_panel.clear_regr_file_requested.connect(self.clear_regr_file)
        self.config_panel.get_seed_requested.connect(self.get_seed)
        self.config_panel.parse_regr_command_requested.connect(self.parse_regr_command)
        self.config_panel.template_applied.connect(self.on_template_applied)

        # 命令模型信号
        self.command_model.command_generated.connect(self.update_command_preview)

    def handle_config_changed(self, config):
        """
        处理配置面板发出的配置变更信号

        Args:
            config (dict): 新的配置数据
        """
        # 在执行期间，减少配置更新的频率
        if self.is_executing:
            # 如果正在执行命令，延迟处理配置变更
            import time
            if time.time() - self.execution_start_time < 2.0:  # 执行开始后2秒内减少更新
                return

        # 更新配置模型，但不触发事件总线的配置变更信号
        self.config_model.config.update(config)

        # 使用事件总线发射配置变更信号
        self.event_bus.emit_config_changed(config)

        # 生成命令预览
        self.generate_command_preview()

    def on_config_loaded(self, config):
        """
        配置加载后的处理

        Args:
            config (dict): 配置数据
        """
        # 更新配置面板
        self.config_panel.update_config(config)

        # 生成命令预览
        self.generate_command_preview()

    @pyqtSlot(dict)
    def on_config_changed(self, config):
        """
        处理配置变更事件

        Args:
            config (dict): 新的配置数据
        """
        # 防止无限递归
        if hasattr(self, '_handling_config_change') and self._handling_config_change:
            return

        self._handling_config_change = True
        try:
            # 获取当前配置模型状态
            current_model_config = self.config_model.get_config()

            # 比较配置是否真正发生变化
            config_needs_update = False
            panel_needs_update = False

            for key, value in config.items():
                if current_model_config.get(key) != value:
                    config_needs_update = True
                    break

            # 只在配置真正发生变化时才更新
            if config_needs_update:
                # 更新配置模型，但不触发事件总线的配置变更信号
                # 这里直接更新配置模型的内部状态，避免无限递归
                self.config_model.config.update(config)

                # 优先检查BASE和BLOCK参数是否需要更新
                base_update_needed = 'base' in config and config['base'] != self.config_panel.get_current_config().get('base')
                block_update_needed = 'block' in config and config['block'] != self.config_panel.get_current_config().get('block')
                
                # 如果BASE或BLOCK需要更新，立即更新这些字段
                if base_update_needed or block_update_needed:
                    # 阻止信号传递
                    self.config_panel.blockSignals(True)
                    
                    # 更新BASE和BLOCK输入框
                    if base_update_needed and hasattr(self.config_panel, 'base_input'):
                        self.config_panel.base_input.setText(config['base'])
                    
                    if block_update_needed and hasattr(self.config_panel, 'block_input'):
                        self.config_panel.block_input.setText(config['block'])
                    
                    # 恢复信号传递
                    self.config_panel.blockSignals(False)

                # 检查配置面板是否需要更新
                current_panel_config = self.config_panel.get_current_config()
                for key, value in config.items():
                    if key not in ['base', 'block'] and current_panel_config.get(key) != value:
                        panel_needs_update = True
                        break

                # 只在配置面板状态与新配置不一致时才更新面板
                if panel_needs_update:
                    self.config_panel.blockSignals(True)  # 阻止信号传递
                    # 使用增量更新，但不更新已经处理的BASE和BLOCK
                    config_to_update = {k: v for k, v in config.items() if k not in ['base', 'block'] or (k == 'base' and not base_update_needed) or (k == 'block' and not block_update_needed)}
                    if config_to_update:
                        self.config_panel.update_config(config_to_update, force_update=False)
                    self.config_panel.blockSignals(False)  # 恢复信号传递

                # 生成命令预览
                self.generate_command_preview()
        finally:
            self._handling_config_change = False

    def generate_command_preview(self):
        """生成命令预览"""
        # 防止无限递归
        if hasattr(self, '_generating_preview') and self._generating_preview:
            return

        self._generating_preview = True
        try:
            # 获取当前配置面板的所有配置
            current_config = self.config_panel.get_current_config()

            # 比较配置是否真正发生变化，避免不必要的更新
            current_model_config = self.config_model.get_config()
            config_changed = False

            # 检查关键配置字段是否发生变化
            key_fields = ['base', 'block', 'case', 'rundir', 'bq_server', 'other_options',
                         'fsdb', 'vwdb', 'cl', 'dump_sva', 'cov', 'upf', 'sim_only',
                         'compile_only', 'dump_mem', 'wdd', 'seed', 'simarg', 'cfg_def',
                         'post', 'fm_checked', 'tag', 'nt', 'dashboard']

            for field in key_fields:
                if current_config.get(field) != current_model_config.get(field):
                    config_changed = True
                    break

            # 只在配置真正发生变化时才更新配置模型
            if config_changed:
                # 直接更新配置模型的内部状态，避免触发信号
                self.config_model.config.update(current_config)

            # 获取当前模式 - 直接从当前配置获取，确保与UI状态一致
            mode = "normal"
            if current_config.get("sim_only", False):
                mode = "R"
            elif current_config.get("compile_only", False):
                mode = "C"

            # 获取当前用例名称
            case_name = None
            if hasattr(self.config_panel, 'case_input'):
                case_name = self.config_panel.case_input.text().strip()
                if case_name and case_name.startswith("已选择"):
                    case_name = None
                elif case_name and case_name != current_model_config.get("case", ""):
                    # 只在用例名称真正发生变化时才更新配置模型
                    self.config_model.config["case"] = case_name

            # 获取 BATCH RUN 信息
            batch_run_info = self.get_batch_run_info(case_name)

            # 生成命令
            command = self.command_model.generate_command(mode, case_name, batch_run_info)

            # 确保命令预览更新到UI
            self.update_command_preview(command)
        finally:
            self._generating_preview = False

    def update_command_preview(self, command):
        """
        更新命令预览

        Args:
            command (str or list): 命令字符串或命令列表（BATCH RUN模式）
        """
        if isinstance(command, list):
            # BATCH RUN 模式：显示命令序列
            # 检查是否是多次执行模式
            sim_commands = [cmd for cmd, desc in command if "执行" in desc and "/" in desc]

            if len(sim_commands) > 1:
                # 多次执行模式
                preview_text = f"BATCH RUN 模式 - 多次执行 ({len(sim_commands)} 次)：\n"
                preview_text += "将先执行编译（如需要），然后并行执行多个仿真任务\n\n"
            else:
                # 普通BATCH RUN模式
                preview_text = "BATCH RUN 模式 - 将按顺序执行以下命令：\n"

            for i, (cmd, desc) in enumerate(command, 1):
                preview_text += f"步骤 {i}: {desc}\n{cmd}\n\n"
            self.config_panel.set_preview_text(preview_text.strip())
        else:
            # 检查是否启用了BATCH RUN但实际使用普通模式
            batch_run_enabled = (hasattr(self.config_panel, 'batch_run_check') and
                                self.config_panel.batch_run_check.isChecked())

            if batch_run_enabled:
                # BATCH RUN已启用但使用普通模式（说明选中的是根节点用例）
                preview_text = f"BATCH RUN 模式 - 检测到根节点用例，使用普通执行：\n{command}"
                self.config_panel.set_preview_text(preview_text)
            else:
                # 普通模式：显示单个命令
                self.config_panel.set_preview_text(command)

    @pyqtSlot(str, str)
    def execute_command(self, mode, case_name):
        """
        执行命令

        Args:
            mode (str): 执行模式，例如 "normal", "R", "C" 等
            case_name (str): 用例名称
        """
        # 设置执行状态，避免执行期间的不必要配置更新
        self.is_executing = True
        self.execution_start_time = time.time()

        try:
            # 检查是否是多选用例（格式为"已选择 N 个用例"）
            if case_name and case_name.startswith("已选择 "):
                # 执行多个用例
                self.execute_multiple_cases(mode)
                return

            # 验证必填字段
            config = self.config_model.get_config()

            # 检查必填字段
            # 如果选择了回归列表文件，则不检查BLOCK参数
            if not config.get("regr_file") and not config.get("block"):
                self.main_window.show_warning("缺少必填参数", "BLOCK参数不能为空")
                return

            # 如果没有使用回归文件，则case参数也是必填的
            if not config.get("regr_file") and not case_name and not config.get("case"):
                self.main_window.show_warning("缺少必填参数", "CASE参数不能为空")
                return

            # 验证种子号格式
            if seed := config.get("seed"):
                if not seed.isdigit():
                    self.main_window.show_warning("输入格式错误", "种子号应为数字")
                    return

            # 获取 BATCH RUN 信息
            batch_run_info = self.get_batch_run_info(case_name)

            # 生成命令
            command = self.command_model.generate_command(mode, case_name, batch_run_info)

            # 处理命令执行
            if isinstance(command, list):
                # BATCH RUN 模式：处理命令列表
                execution_strategy = self.combine_batch_commands(command)

                if isinstance(execution_strategy, dict) and execution_strategy.get('type') == 'parallel_execution':
                    # 多次执行模式：使用任务队列并行执行
                    self._execute_parallel_batch_commands(execution_strategy, case_name)
                    return  # 并行执行不需要继续后续流程
                else:
                    # 串行执行模式
                    command = execution_strategy

            # 添加到历史记录
            command_for_history = command if isinstance(command, str) else str(command)
            self.history_model.add_command(command_for_history)

            # 更新历史记录视图
            history = self.history_model.get_history()
            self.update_history_view(history)

            # 使用事件总线发射历史记录更新信号
            self.event_bus.emit_history_updated(history)

            # 标记命令已执行
            self.command_model.mark_command_executed()

            # 使用事件总线发射命令执行信号
            self.event_bus.emit_command_executed(command, case_name)

            # 保持向后兼容
            self.execution_requested.emit(command, case_name)

        finally:
            # 重置执行状态
            self.is_executing = False
            self.execution_start_time = 0

    def _execute_parallel_batch_commands(self, execution_strategy, case_name):
        """
        执行并行BATCH RUN命令

        Args:
            execution_strategy (dict): 执行策略字典
            case_name (str): 用例名称
        """
        # 导入任务队列相关模块
        from plugins.builtin.batch_execution_plugin import BatchExecutionDialog

        # 获取编译和仿真命令
        compile_commands = execution_strategy.get('compile_commands', [])
        sim_commands = execution_strategy.get('sim_commands', [])
        total_count = execution_strategy.get('total_count', len(sim_commands))

        # 创建批量执行对话框
        batch_dialog = BatchExecutionDialog(self.main_window, f"BATCH RUN 多次执行 - {case_name}")

        # 如果有编译命令，需要先串行执行编译，再并行执行仿真
        if compile_commands:
            # 编译阶段：串行执行
            batch_dialog.process_manager.set_max_parallel(1)

            # 添加编译命令
            for cmd, desc in compile_commands:
                row = batch_dialog.add_case_to_table(case_name, cmd, desc)
                batch_dialog.process_manager.add_command(row, cmd, case_name)

            # 添加仿真命令
            for i, (cmd, desc) in enumerate(sim_commands):
                row = batch_dialog.add_case_to_table(case_name, cmd, desc)
                batch_dialog.process_manager.add_command(row, cmd, case_name)

            # 显示对话框
            batch_dialog.show()

            # 连接编译完成信号，在编译完成后切换到并行模式
            def on_compile_finished():
                # 切换到并行执行模式
                batch_dialog.process_manager.set_max_parallel(min(total_count, 5))  # 最多5个并行任务

            # 监听第一个任务完成（编译任务）
            batch_dialog.process_manager.execution_finished.connect(
                lambda row, exit_code: on_compile_finished() if row == 0 and exit_code == 0 else None
            )

            # 开始执行
            batch_dialog.start_execution()
        else:
            # 没有编译命令，直接并行执行仿真
            batch_dialog.process_manager.set_max_parallel(min(total_count, 5))  # 最多5个并行任务

            # 添加仿真命令
            for cmd, desc in sim_commands:
                row = batch_dialog.add_case_to_table(case_name, cmd, desc)
                batch_dialog.process_manager.add_command(row, cmd, case_name)

            # 显示对话框并开始执行
            batch_dialog.show()
            batch_dialog.start_execution()

        # 添加到历史记录
        history_text = f"BATCH RUN 多次执行: {case_name} ({total_count} 次)"
        self.history_model.add_command(history_text)

        # 更新历史记录视图
        history = self.history_model.get_history()
        self.update_history_view(history)

        # 使用事件总线发射历史记录更新信号
        self.event_bus.emit_history_updated(history)

        # 标记命令已执行
        self.command_model.mark_command_executed()

    def get_batch_run_info(self, case_name):
        """
        获取 BATCH RUN 模式信息

        Args:
            case_name (str): 当前用例名

        Returns:
            dict: BATCH RUN 信息字典
        """
        if not hasattr(self.config_panel, 'batch_run_check'):
            return None

        batch_run_enabled = self.config_panel.batch_run_check.isChecked()
        if not batch_run_enabled:
            return None

        force_update = self.config_panel.force_update_check.isChecked()

        # 获取执行次数
        execution_count = 1
        if hasattr(self.config_panel, 'execution_count_spin'):
            execution_count = self.config_panel.execution_count_spin.value()

        # 获取基础用例名
        base_case_name = case_name

        try:
            # 优先通过app_controller获取case_controller
            case_panel = None
            if self.app_controller and hasattr(self.app_controller, 'case_controller'):
                case_panel = self.app_controller.case_controller.case_panel
            elif hasattr(self.main_window, 'case_controller') and self.main_window.case_controller:
                case_panel = self.main_window.case_controller.case_panel

            if case_panel and hasattr(case_panel, 'get_base_case_name'):
                base_case_name = case_panel.get_base_case_name(case_name)

        except Exception as e:
            print(f"警告: 获取基础用例名时出错: {str(e)}")
            base_case_name = case_name

        # 判断是否真正需要BATCH RUN处理
        # 如果执行次数大于1，无论是否有base_case都启用BATCH RUN
        # 如果执行次数为1且基础用例名与当前用例名相同，说明选中的是根节点用例，不需要BATCH RUN
        if execution_count == 1 and base_case_name == case_name:
            return None  # 返回None表示不启用BATCH RUN

        return {
            'enabled': True,
            'force_update': force_update,
            'base_case_name': base_case_name,
            'execution_count': execution_count
        }



    def combine_batch_commands(self, command_list):
        """
        将BATCH RUN命令列表处理为执行策略

        Args:
            command_list (list): 命令列表，每个元素是 (command, description) 元组

        Returns:
            str or dict: 单次执行返回命令字符串，多次执行返回执行策略字典
        """
        if not command_list:
            return ""

        # 检查是否有多个仿真命令（执行次数 > 1）
        sim_commands = []
        compile_commands = []

        for cmd, desc in command_list:
            if "编译基础用例" in desc:
                compile_commands.append((cmd, desc))
            else:
                sim_commands.append((cmd, desc))

        # 如果有多个仿真命令，说明是多次执行模式，使用任务队列
        if len(sim_commands) > 1:
            execution_strategy = {
                'type': 'parallel_execution',
                'compile_commands': compile_commands,
                'sim_commands': sim_commands,
                'total_count': len(sim_commands)
            }
            print(f"BATCH RUN: 多次执行模式 - {len(sim_commands)} 个仿真任务")
            return execution_strategy
        else:
            # 单次执行，使用串行执行
            commands = [cmd for cmd, desc in command_list]
            combined_command = " ; ".join(commands)
            print(f"BATCH RUN: 串行执行模式 - {combined_command}")
            return combined_command

    @pyqtSlot(str, str)
    def execute_command_in_terminal(self, mode, case_name):
        """
        在终端中执行命令

        Args:
            mode (str): 执行模式，例如 "normal", "R", "C" 等
            case_name (str): 用例名称
        """
        # 检查是否是多选用例（格式为"已选择 N 个用例"）
        if case_name and case_name.startswith("已选择 "):
            self.main_window.show_warning("不支持的操作", "终端执行模式不支持多用例执行")
            return

        # 验证必填字段
        config = self.config_model.get_config()

        # 检查必填字段
        # 如果选择了回归列表文件，则不检查BLOCK参数
        if not config.get("regr_file") and not config.get("block"):
            self.main_window.show_warning("缺少必填参数", "BLOCK参数不能为空")
            return

        # 如果没有使用回归文件，则case参数也是必填的
        if not config.get("regr_file") and not case_name and not config.get("case"):
            self.main_window.show_warning("缺少必填参数", "CASE参数不能为空")
            return

        # 验证种子号格式
        if seed := config.get("seed"):
            if not seed.isdigit():
                self.main_window.show_warning("输入格式错误", "种子号应为数字")
                return

        # 生成命令
        command = self.command_model.generate_command(mode, case_name)

        # 显示终端选择对话框
        self.show_terminal_selector(command, case_name)

    def show_terminal_selector(self, command, case_name):
        """
        显示终端选择对话框

        Args:
            command (str): 要执行的命令
            case_name (str): 用例名称
        """
        try:
            # 导入终端选择对话框
            from views.terminal_selector_dialog import TerminalSelectorDialog

            # 创建并显示对话框
            dialog = TerminalSelectorDialog(command, self.main_window)

            # 连接信号
            dialog.terminal_selected.connect(
                lambda terminal_info, cmd: self.on_terminal_command_sent(terminal_info, cmd, case_name)
            )

            # 显示对话框
            result = dialog.exec_()

            if result == dialog.Accepted:
                # 命令已发送，添加到历史记录
                self.history_model.add_command(command)

                # 更新历史记录视图
                history = self.history_model.get_history()
                self.update_history_view(history)

                # 使用事件总线发射历史记录更新信号
                self.event_bus.emit_history_updated(history)

        except ImportError as e:
            self.main_window.show_error("导入错误", f"无法导入终端选择对话框: {str(e)}")
        except Exception as e:
            self.main_window.show_error("终端执行错误", f"显示终端选择对话框时出错: {str(e)}")

    def on_terminal_command_sent(self, terminal_info, command, case_name):
        """
        处理终端命令发送完成事件

        Args:
            terminal_info: 终端信息
            command (str): 发送的命令
            case_name (str): 用例名称
        """
        # 标记命令已执行
        self.command_model.mark_command_executed()

        # 使用事件总线发射命令执行信号（终端模式）
        self.event_bus.emit_command_executed(command, case_name)

        # 显示成功消息
        self.main_window.show_message(f"命令已发送到终端: {terminal_info.title}")

    def execute_multiple_cases(self, mode):
        """
        执行多个用例

        Args:
            mode (str): 执行模式，例如 "normal", "R", "C" 等
        """
        from PyQt5.QtCore import QObject
        from PyQt5.QtWidgets import QWidget

        # 验证必填字段
        config = self.config_model.get_config()

        # 检查必填字段
        # 如果选择了回归列表文件，则不检查BLOCK参数
        if not config.get("regr_file") and not config.get("block"):
            self.main_window.show_warning("缺少必填参数", "BLOCK参数不能为空")
            return

        # 获取用例面板
        case_panel = None
        for child in self.main_window.findChildren(QWidget):
            if hasattr(child, 'case_tree') and isinstance(child, QWidget):
                case_panel = child
                break

        if not case_panel:
            self.main_window.show_warning("错误", "无法获取用例面板")
            return

        # 获取选中的用例
        selected_items = case_panel.case_tree.selectedItems()

        # 过滤掉文件名节点（顶层节点），只处理用例节点
        valid_items = [item for item in selected_items if item.parent() is not None]

        if not valid_items:
            self.main_window.show_warning("选择错误", "请选择要执行的用例")
            return

        # 执行每个选中的用例
        for item in valid_items:
            case_name = item.text(0)

            # 生成命令
            command = self.command_model.generate_command(mode, case_name)

            # 添加到历史记录
            self.history_model.add_command(command)

            # 使用事件总线发射命令执行信号
            self.event_bus.emit_command_executed(command, case_name)

            # 保持向后兼容
            self.execution_requested.emit(command, case_name)

        # 更新历史记录视图
        history = self.history_model.get_history()
        self.update_history_view(history)

        # 使用事件总线发射历史记录更新信号
        self.event_bus.emit_history_updated(history)

        # 显示完成消息
        self.main_window.show_message(f"已开始执行 {len(valid_items)} 个用例")

    def _parse_command_params(self, parts, smart_reset=True):
        """
        解析命令参数

        Args:
            parts (list): 命令参数列表
            smart_reset (bool): 是否使用智能重置，默认为True

        Returns:
            None: 解析结果直接修改UI控件
        """
        i = 0

        # 智能重置：先解析命令参数，确定哪些字段需要重置
        if smart_reset:
            # 预解析命令，确定涉及的字段
            involved_fields = self._pre_parse_command_fields(parts)
            # 只重置涉及的字段
            self.smart_reset_inputs(involved_fields)
        else:
            # 传统方式：重置所有输入框和选项
            self.reset_all_inputs()

        # 记录已处理的选项，避免重复解析
        processed_options = set()

        # 首先判断是否是回归命令
        is_regr_command = False
        for j, part in enumerate(parts):
            if part == "-regr" and j + 1 < len(parts):
                is_regr_command = True
                break

        while i < len(parts):
            part = parts[i]
            if part.startswith("-"):
                # 移除开头的横杠以便匹配
                option = part[1:]

                # 处理基本参数
                if option == "base" and i + 1 < len(parts):
                    self.config_panel.base_input.setText(parts[i+1])
                    processed_options.add("base")
                    i += 2
                elif option == "block" and i + 1 < len(parts):
                    self.config_panel.block_input.setText(parts[i+1])
                    processed_options.add("block")
                    i += 2
                elif option == "case" and i + 1 < len(parts):
                    self.config_panel.case_input.setText(parts[i+1])
                    processed_options.add("case")
                    i += 2
                elif option == "R":
                    # 检查是否有路径参数
                    if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                        # 有路径参数的-R选项
                        r_path = parts[i+1]

                        # 检查是否是BATCH_RUN模式生成的命令（通过检查是否启用了BATCH_RUN模式）
                        is_batch_run_mode = (hasattr(self.config_panel, 'batch_run_check') and
                                           self.config_panel.batch_run_check.isChecked())

                        if not is_batch_run_mode:
                            # 非BATCH_RUN模式，将-R参数添加到其他选项中
                            current_other_options = self.config_panel.other_options_input.text().strip()
                            r_option = f"-R {r_path}"

                            if current_other_options:
                                # 检查是否已经包含相同的-R选项，避免重复
                                if r_option not in current_other_options:
                                    self.config_panel.other_options_input.setText(f"{current_other_options} {r_option}")
                            else:
                                self.config_panel.other_options_input.setText(r_option)
                        # BATCH_RUN模式下，不将-R参数添加到其他选项中，因为它是自动生成的
                        i += 2
                    else:
                        # 没有路径参数，只是简单的-R选项，勾选复选框
                        self.config_panel.sim_only_check.setChecked(True)
                        i += 1
                elif option.startswith("lsf="):
                    i += 1
                elif option == "C":
                    self.config_panel.compile_only_check.setChecked(True)
                    i += 1
                elif option == "cl":
                    self.config_panel.cl_check.setChecked(True)
                    i += 1
                elif option == "fsdb":
                    self.config_panel.fsdb_check.setChecked(True)
                    if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                        # 直接更新配置模型，避免触发信号
                        self.config_model.config["fsdb_file"] = parts[i+1]
                        # 更新界面
                        self.config_panel.fsdb_label.setText(os.path.basename(parts[i+1]))
                        self.config_panel.fsdb_clear_btn.setEnabled(True)
                        i += 2
                    else:
                        i += 1
                elif option == "vwdb":
                    self.config_panel.vwdb_check.setChecked(True)
                    if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                        # 直接更新配置模型，避免触发信号
                        self.config_model.config["fsdb_file"] = parts[i+1]
                        # 更新界面
                        self.config_panel.fsdb_label.setText(os.path.basename(parts[i+1]))
                        self.config_panel.fsdb_clear_btn.setEnabled(True)
                        i += 2
                    else:
                        i += 1
                elif option == "dump_sva":
                    self.config_panel.sva_check.setChecked(True)
                    i += 1
                elif option == "cov":
                    self.config_panel.cov_check.setChecked(True)
                    i += 1
                elif option == "upf":
                    self.config_panel.upf_check.setChecked(True)
                    i += 1
                elif option == "seed" and i + 1 < len(parts):
                    self.config_panel.seed_input.setText(parts[i+1])
                    processed_options.add("seed")
                    i += 2
                elif option == "dump_mem" and i + 1 < len(parts):
                    # 处理dump_mem的多选设置，支持带引号的参数
                    if parts[i+1].startswith('"'):
                        # 处理带引号的dump_mem参数
                        dump_mem_value = parts[i+1][1:]  # 移除开始引号
                        j = i + 2
                        # 收集所有部分直到找到结束引号
                        while j < len(parts) and not parts[j].endswith('"'):
                            dump_mem_value += " " + parts[j]
                            j += 1
                        if j < len(parts):
                            dump_mem_value += " " + parts[j][:-1]  # 移除结束引号
                            i = j + 1
                        else:
                            i = j
                    else:
                        # 处理不带引号的dump_mem参数
                        dump_mem_value = parts[i+1]
                        i += 2

                    if dump_mem_value:
                        # 将字符串分割为选项列表
                        options = [opt.strip() for opt in dump_mem_value.split() if opt.strip()]
                        self.config_panel.dump_mem_input.set_selected_options(options)
                elif option == "wdd" and i + 1 < len(parts):
                    self.config_panel.wdd_input.setText(parts[i+1])
                    processed_options.add("wdd")
                    i += 2
                elif option == "cfg_def":
                    # 收集所有后续的非选项参数作为 cfg_def 的值
                    cfg_def_values = []
                    j = i + 1
                    while j < len(parts) and not parts[j].startswith('-'):
                        cfg_def_values.append(parts[j])
                        j += 1

                    # 如果找到了值
                    if cfg_def_values:
                        current = self.config_panel.cfg_def_input.text()
                        new_values = ' '.join(cfg_def_values)
                        if current:
                            # 如果已有值，则追加新值
                            self.config_panel.cfg_def_input.setText(f"{current} {new_values}")
                        else:
                            # 如果没有现有值，直接设置
                            self.config_panel.cfg_def_input.setText(new_values)
                        processed_options.add("cfg_def")
                    i = j  # 更新索引到下一个选项
                elif option == "simarg" and i + 1 < len(parts):
                    # 处理带引号的参数
                    if parts[i+1].startswith('"'):
                        # 初始化参数值，去掉开头的引号
                        value = parts[i+1][1:]  # 移除开始引号
                        # 检查当前部分是否已经包含结束引号
                        if '"' in value:
                            # 当前部分已经包含结束引号，直接截取到引号前
                            simarg = value[:value.find('"')]
                            i += 2
                        else:
                            # 需要继续查找结束引号
                            simarg = value
                            j = i + 2
                            # 查找结束引号或下一个参数（以-开头）
                            while j < len(parts):
                                # 如果找到以-开头的参数，说明simarg参数值到此结束
                                if parts[j].startswith('-'):
                                    break
                                # 如果找到带结束引号的部分
                                if '"' in parts[j]:
                                    quote_pos = parts[j].find('"')
                                    simarg += " " + parts[j][:quote_pos]
                                    j += 1  # 跳过当前部分
                                    break
                                simarg += " " + parts[j]
                                j += 1
                            
                            # 更新索引到下一个选项
                            if j < len(parts) and parts[j].startswith('-'):
                                i = j  # 下一个是新参数，直接从新参数开始
                            else:
                                i = j  # 否则跳过当前处理的部分

                        # 将值设置到输入框，只显示EXEC_CPU=0（不带额外引号）
                        self.config_panel.simarg_input.setText(simarg)
                        processed_options.add("simarg")
                    else:
                        self.config_panel.simarg_input.setText(parts[i+1])
                        processed_options.add("simarg")
                        i += 2
                elif option == "post" and i + 1 < len(parts):
                    self.config_panel.post_input.setCurrentText(parts[i+1])
                    processed_options.add("post")
                    i += 2
                elif option == "regr" and i + 1 < len(parts):
                    # 直接更新配置模型，避免触发信号
                    self.config_model.config["regr_file"] = parts[i+1]
                    # 更新界面
                    self.config_panel.regr_label.setText(os.path.basename(parts[i+1]))
                    self.config_panel.clear_regr_btn.setEnabled(True)
                    processed_options.add("regr")
                    i += 2
                elif option == "fm":
                    self.config_panel.fm_check.setChecked(True)
                    i += 1
                elif option == "bq" and i + 1 < len(parts):
                    self.config_panel.bq_input.setText(parts[i+1])
                    processed_options.add("bq")
                    i += 2
                elif option == "rundir" and i + 1 < len(parts):
                    self.config_panel.rundir_input.setText(parts[i+1])
                    processed_options.add("rundir")
                    i += 2
                elif option == "tag" and i + 1 < len(parts) and is_regr_command:
                    # 对于回归命令，解析tag参数
                    self.config_panel.tag_input.setText(parts[i+1])
                    processed_options.add("tag")
                    i += 2
                elif option == "nt" and i + 1 < len(parts) and is_regr_command:
                    # 对于回归命令，解析nt参数
                    self.config_panel.nt_input.setText(parts[i+1])
                    processed_options.add("nt")
                    i += 2
                elif option == "m" and i + 1 < len(parts) and is_regr_command:
                    # 对于回归命令，解析dashboard参数
                    self.config_panel.dashboard_input.setText(parts[i+1])
                    processed_options.add("dashboard")
                    i += 2
                else:
                    # 处理包含等号的选项，如 -simarg=xxx
                    if "=" in part and not part.startswith("-simarg="):
                        param_parts = part[1:].split("=", 1)
                        if len(param_parts) == 2:
                            param_name, param_value = param_parts
                            if param_name == "post" and not "post" in processed_options:
                                self.config_panel.post_input.setCurrentText(param_value.strip("'"))
                                processed_options.add("post")
                                i += 1
                                continue
                    
                    # 处理特殊情况
                    simarg_value = part.split("=")[1].strip("'") if "=" in part else ""
                    if simarg_value in ["-nolock", "-nohistory"]:
                        i += 1
                        continue
                        
                    # 其他选项作为额外选项
                    self.config_panel.other_options_input.setText(part + " " + " ".join(parts[i+1:]))
                    break
            else:
                i += 1

    @pyqtSlot(int)
    def apply_history(self, index):
        """
        应用历史记录

        Args:
            index (int): 历史记录索引
        """
        if index < 0:
            return

        history = self.history_model.get_history()
        if index < len(history):
            record = history[index]
            command = record.get('command', '')

            # 解析命令，更新配置
            try:
                # 先处理引号不匹配的问题
                # 计算命令中的引号数量，如果是奇数，添加一个引号到末尾以避免解析错误
                quote_count = command.count('"')
                if quote_count % 2 != 0:
                    command += '"'

                # 分割命令参数并解析，使用智能重置
                parts = command.split()
                self._parse_command_params(parts, smart_reset=True)

                # 生成命令预览
                self.generate_command_preview()

                self.main_window.show_message(f"已应用历史命令: {command[:30]}...")
            except Exception as e:
                self.main_window.show_error("解析历史命令失败", str(e))

    @pyqtSlot()
    def re_run_history(self):
        """重新执行历史命令"""
        index = self.config_panel.history_combo.currentIndex()
        if index < 0:
            return

        # 先应用历史命令到界面上
        self.apply_history(index)

        # 获取当前模式
        mode = "normal"
        config = self.config_model.get_config()
        if config.get("sim_only", False):
            mode = "R"
        elif config.get("compile_only", False):
            mode = "C"

        # 获取当前用例名称
        case_name = None
        if hasattr(self.config_panel, 'case_input'):
            case_name = self.config_panel.case_input.text().strip()
            if case_name and case_name.startswith("已选择"):
                case_name = None

        # 执行命令
        self.execute_command(mode, case_name)

        # 获取最新生成的命令
        command = self.command_model.get_current_command()
        self.main_window.show_message(f"重新执行历史命令: {command[:30]}...")

    @pyqtSlot(dict)
    def on_template_applied(self, template_config):
        """
        处理配置模板应用事件

        Args:
            template_config (dict): 模板配置数据
        """
        # 更新配置模型
        self.config_model.config.update(template_config)

        # 使用事件总线发射配置变更信号
        self.event_bus.emit_config_changed(template_config)

        # 生成命令预览
        self.generate_command_preview()

        # 保存配置
        self.config_model.save_config()

    @pyqtSlot()
    def select_fsdb_file(self):
        """选择 FSDB tcl 文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择 FSDB tcl 文件",
            "",
            "TCL 文件 (*.tcl);;所有文件 (*.*)"
        )

        if file_path:
            # 检查是否与当前文件相同，避免不必要的更新
            current_fsdb_file = self.config_model.get_config().get("fsdb_file", "")
            if current_fsdb_file == file_path:
                return  # 文件相同，不需要更新

            # 直接更新配置模型，避免触发信号
            self.config_model.config["fsdb_file"] = file_path

            # 更新界面
            self.config_panel.fsdb_label.setText(os.path.basename(file_path))
            self.config_panel.fsdb_clear_btn.setEnabled(True)

            # 生成命令预览
            self.generate_command_preview()

    @pyqtSlot()
    def clear_fsdb_file(self):
        """清除 FSDB tcl 文件"""
        # 检查当前是否已经是空的，避免不必要的更新
        current_fsdb_file = self.config_model.get_config().get("fsdb_file", "")
        if not current_fsdb_file:
            return  # 已经是空的，不需要更新

        # 直接更新配置模型，避免触发信号
        self.config_model.config["fsdb_file"] = ""

        # 更新界面
        self.config_panel.fsdb_label.setText("未选择TCL文件")
        self.config_panel.fsdb_clear_btn.setEnabled(False)

        # 生成命令预览
        self.generate_command_preview()

    @pyqtSlot()
    def select_regr_file(self):
        """选择回归列表文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择回归列表文件",
            "",
            "回归列表文件 (*.list *.txt);;所有文件 (*.*)"
        )

        if file_path:
            # 检查是否与当前文件相同，避免不必要的更新
            current_regr_file = self.config_model.get_config().get("regr_file", "")
            if current_regr_file == file_path:
                return  # 文件相同，不需要更新

            # 直接更新配置模型，避免触发信号
            self.config_model.config["regr_file"] = file_path

            # 更新界面
            self.config_panel.regr_label.setText(os.path.basename(file_path))
            self.config_panel.clear_regr_btn.setEnabled(True)

            # 生成命令预览
            self.generate_command_preview()

    @pyqtSlot()
    def clear_regr_file(self):
        """清除回归列表文件"""
        # 检查当前是否已经是空的，避免不必要的更新
        current_config = self.config_model.get_config()
        if (not current_config.get("regr_file") and
            not current_config.get("fm_checked") and
            not current_config.get("tag") and
            not current_config.get("nt") and
            not current_config.get("dashboard")):
            return  # 已经是空的，不需要更新

        # 批量直接更新配置模型，避免触发多次信号
        self.config_model.config.update({
            "regr_file": "",
            "fm_checked": False,
            "tag": "",
            "nt": "",
            "dashboard": ""
        })

        # 更新界面（只在需要时更新）
        if self.config_panel.regr_label.text() != "未选择回归文件":
            self.config_panel.regr_label.setText("未选择回归文件")
        if self.config_panel.clear_regr_btn.isEnabled():
            self.config_panel.clear_regr_btn.setEnabled(False)

        # 生成命令预览
        self.generate_command_preview()

    @pyqtSlot()
    def get_seed(self):
        """从仿真日志文件中获取种子号，并更新到种子号输入框"""
        # 检查执行控制器是否已设置
        if not self.execution_controller:
            # 尝试查找执行控制器
            for controller in self.main_window.findChildren(QObject):
                if hasattr(controller, 'execution_panel') and hasattr(controller.execution_panel, 'tab_widget'):
                    self.execution_controller = controller
                    break

        # 尝试从当前选中的case tab获取种子号
        current_tab = None
        case_name = None

        if self.execution_controller:
            current_tab = self.execution_controller.execution_panel.get_current_log_panel()
            if current_tab and hasattr(current_tab, 'case_name'):
                case_name = current_tab.case_name

        # 如果有选中的case tab，使用原有逻辑
        if case_name:
            self._get_seed_from_case_tab(case_name)
        else:
            # 如果没有选中的case tab，搜索$PROJ_DIR/work目录
            self._get_seed_from_work_directory()

    def _get_seed_from_case_tab(self, case_name):
        """从选中的case tab获取种子号"""
        log_file = f"{case_name}/log/irun_sim.log"  # 构建仿真日志文件路径

        if not os.path.exists(log_file):
            # 尝试其他可能的日志文件名
            alternative_logs = [
                f"{case_name}/log/vcs_sim.log",
                f"{case_name}/log/sim.log",
                f"{case_name}/irun_sim.log",
                f"{case_name}/vcs_sim.log",
                f"{case_name}/sim.log"
            ]

            for alt_log in alternative_logs:
                if os.path.exists(alt_log):
                    log_file = alt_log
                    break
            else:
                QMessageBox.warning(self.main_window, "错误", f"找不到仿真日志文件：{log_file}")
                return

        self._parse_seed_from_log(log_file, f"用例 {case_name}")

    def _get_seed_from_work_directory(self):
        """从$PROJ_DIR/work目录搜索种子号"""
        # 获取当前配置中的用例名称
        config = self.config_model.get_config()
        current_case = config.get("case", "").strip()

        if not current_case:
            QMessageBox.warning(self.main_window, "错误", "请先在配置面板中设置用例名称，或选择一个用例标签页")
            return

        # 获取PROJ_DIR环境变量
        proj_dir = os.environ.get("PROJ_DIR", "")
        if not proj_dir:
            # 如果环境变量不存在，使用当前工作目录
            proj_dir = os.getcwd()
            self.main_window.show_message(f"PROJ_DIR环境变量未设置，使用当前目录：{proj_dir}", 3000)

        work_dir = os.path.join(proj_dir, "work")

        if not os.path.exists(work_dir):
            QMessageBox.warning(self.main_window, "错误", f"工作目录不存在：{work_dir}")
            return

        # 搜索匹配的用例目录
        found_logs = []

        try:
            for item in os.listdir(work_dir):
                case_path = os.path.join(work_dir, item)
                if not os.path.isdir(case_path):
                    continue

                # 检查目录名是否包含当前用例名称
                if current_case.lower() in item.lower():
                    log_dir = os.path.join(case_path, "log")
                    if os.path.exists(log_dir):
                        # 查找仿真日志文件
                        possible_logs = [
                            os.path.join(log_dir, "irun_sim.log"),
                            os.path.join(log_dir, "vcs_sim.log"),
                            os.path.join(log_dir, "sim.log")
                        ]

                        for log_file in possible_logs:
                            if os.path.exists(log_file):
                                # 获取文件修改时间
                                mtime = os.path.getmtime(log_file)
                                found_logs.append((log_file, mtime, item))
                                break

            if not found_logs:
                QMessageBox.warning(self.main_window, "错误",
                                  f"在目录 {work_dir} 中未找到用例 '{current_case}' 的仿真日志文件")
                return

            # 按修改时间排序，选择最新的日志文件
            found_logs.sort(key=lambda x: x[1], reverse=True)
            latest_log, _, case_dir = found_logs[0]

            # 如果找到多个日志文件，提示用户
            if len(found_logs) > 1:
                self.main_window.show_message(
                    f"找到 {len(found_logs)} 个匹配的日志文件，使用最新的：{case_dir}", 5000)

            self._parse_seed_from_log(latest_log, f"用例 {current_case} (来自 {case_dir})")

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"搜索日志文件失败：{str(e)}")

    def _parse_seed_from_log(self, log_file, source_info):
        """从日志文件中解析种子号"""
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                # 使用正则表达式查找种子号，匹配 -seed 后面的数字
                if match := re.search(r'-seed\s+(\d+)', content):
                    seed = match.group(1)
                    # 更新配置
                    self.config_model.update_config({"seed": seed})
                    # 更新界面
                    self.config_panel.seed_input.setText(seed)
                    # 生成命令预览
                    self.generate_command_preview()
                    self.main_window.show_message(f"已从 {source_info} 获取种子号：{seed}", 3000)
                else:
                    QMessageBox.warning(self.main_window, "错误", f"在 {source_info} 的仿真日志中未找到种子号")
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"读取日志文件失败：{str(e)}")

    @pyqtSlot()
    def parse_regr_command(self):
        """解析回归指令"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, QPushButton

        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("解析回归指令")
        dialog.setMinimumWidth(600)

        layout = QVBoxLayout()

        # 添加说明标签
        hint_label = QLabel("请输入回归用例指令:")
        hint_label.setStyleSheet("color: #666;")
        layout.addWidget(hint_label)

        # 添加文本输入框
        text_edit = QTextEdit()
        text_edit.setPlaceholderText("示例: runsim -base top -block udtb/usvp -case apcpu_hello_world ...")
        text_edit.setMinimumHeight(100)
        layout.addWidget(text_edit)

        # 按钮区域
        btn_layout = QHBoxLayout()
        parse_btn = QPushButton("解析")
        cancel_btn = QPushButton("取消")

        btn_layout.addWidget(parse_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)

        # 连接信号
        parse_btn.clicked.connect(lambda: self.do_parse_command(text_edit.toPlainText(), dialog))
        cancel_btn.clicked.connect(dialog.reject)

        dialog.exec_()

    def do_parse_command(self, command_text, dialog):
        """解析回归指令并填充到界面"""
        from PyQt5.QtWidgets import QMessageBox

        if not command_text.strip():
            QMessageBox.warning(dialog, "错误", "请输入回归指令")
            return

        self.config_panel.fsdb_check.setChecked(True)
        try:
            # 分割命令参数并解析，使用智能重置
            parts = command_text.split()
            self._parse_command_params(parts, smart_reset=True)

            dialog.accept()
            self.main_window.show_message("回归指令解析完成", 3000)

            # 生成命令预览
            self.generate_command_preview()

        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"解析指令失败: {str(e)}")

    def _pre_parse_command_fields(self, parts):
        """
        预解析命令参数，确定涉及的字段

        Args:
            parts (list): 命令参数列表

        Returns:
            set: 涉及的字段名称集合
        """
        involved_fields = set()

        # 定义参数到字段的映射
        param_to_field = {
            'base': 'base',
            'block': 'block',
            'case': 'case',
            'rundir': 'rundir',
            'bq': 'bq_server',
            'other': 'other_options',
            'fsdb': 'fsdb',
            'vwdb': 'vwdb',
            'cl': 'cl',
            'sva': 'dump_sva',
            'cov': 'cov',
            'upf': 'upf',
            'R': 'sim_only',
            'C': 'compile_only',
            'dump_mem': 'dump_mem',
            'wdd': 'wdd',
            'seed': 'seed',
            'simarg': 'simarg',
            'cfg_def': 'cfg_def',
            'post': 'post',
            'fm': 'fm_checked',
            'tag': 'tag',
            'nt': 'nt',
            'm': 'dashboard',
            'regr': 'regr_file'
        }

        # 扫描命令参数
        for part in parts:
            if part.startswith('-'):
                param = part[1:]  # 移除开头的横杠
                if param in param_to_field:
                    involved_fields.add(param_to_field[param])

        return involved_fields

    def smart_reset_inputs(self, involved_fields):
        """
        智能重置：只重置涉及的输入框和选项

        Args:
            involved_fields (set): 需要重置的字段名称集合
        """
        # 暂停定时器，防止重置过程中触发预览更新
        timer_was_active = self.pause_config_panel_timer()

        try:
            # 设置更新状态标记，防止信号触发
            self.config_panel._updating_config = True

            # 文本输入框映射
            text_field_mapping = {
                'base': self.config_panel.base_input,
                'block': self.config_panel.block_input,
                'case': self.config_panel.case_input,
                'rundir': self.config_panel.rundir_input,
                'bq_server': self.config_panel.bq_input,
                'other_options': self.config_panel.other_options_input,
                'wdd': self.config_panel.wdd_input,
                'seed': self.config_panel.seed_input,
                'simarg': self.config_panel.simarg_input,
                'cfg_def': self.config_panel.cfg_def_input,
                'tag': self.config_panel.tag_input,
                'nt': self.config_panel.nt_input,
                'dashboard': self.config_panel.dashboard_input
            }

            # 复选框映射
            checkbox_field_mapping = {
                'fsdb': self.config_panel.fsdb_check,
                'vwdb': self.config_panel.vwdb_check,
                'cl': self.config_panel.cl_check,
                'dump_sva': self.config_panel.sva_check,
                'cov': self.config_panel.cov_check,
                'upf': self.config_panel.upf_check,
                'sim_only': self.config_panel.sim_only_check,
                'compile_only': self.config_panel.compile_only_check,
                'fm_checked': self.config_panel.fm_check
            }

            # 只重置涉及的文本输入框
            for field in involved_fields:
                if field in text_field_mapping:
                    text_field_mapping[field].clear()
                elif field in checkbox_field_mapping:
                    checkbox_field_mapping[field].setChecked(False)
                elif field == 'post':
                    self.config_panel.post_input.setCurrentText("")
                elif field == 'dump_mem':
                    self.config_panel.dump_mem_input.clear()
                elif field == 'regr_file':
                    self.clear_regr_file()

        finally:
            # 恢复状态标记
            self.config_panel._updating_config = False

            # 恢复定时器（如果之前是活跃的）
            if timer_was_active:
                self.resume_config_panel_timer()

    def pause_config_panel_timer(self):
        """暂停配置面板的定时器"""
        if hasattr(self.config_panel, 'preview_timer') and self.config_panel.preview_timer.isActive():
            self.config_panel.preview_timer.stop()
            return True
        return False

    def resume_config_panel_timer(self):
        """恢复配置面板的定时器"""
        if hasattr(self.config_panel, 'preview_timer'):
            self.config_panel.preview_timer.start(2000)

    def reset_all_inputs(self):
        """重置所有输入框和选项，确保不影响多值参数的处理"""
        # 暂停定时器，防止重置过程中触发预览更新
        timer_was_active = self.pause_config_panel_timer()

        try:
            # 设置更新状态标记，防止信号触发
            self.config_panel._updating_config = True

            # 清空输入框，但保留值分隔符
            self.config_panel.base_input.clear()
            self.config_panel.block_input.clear()
            self.config_panel.case_input.clear()
            self.config_panel.rundir_input.clear()
            self.config_panel.bq_input.clear()
            self.config_panel.other_options_input.clear()
            self.config_panel.dump_mem_input.clear()
            self.config_panel.wdd_input.clear()
            self.config_panel.seed_input.clear()
            self.config_panel.simarg_input.clear()
            self.config_panel.cfg_def_input.clear()  # 清空时不会影响多个值的处理逻辑
            self.config_panel.post_input.setCurrentText("")
            self.config_panel.tag_input.clear()
            self.config_panel.nt_input.clear()
            self.config_panel.dashboard_input.clear()

            # 取消所有复选框
            self.config_panel.fsdb_check.setChecked(False)
            self.config_panel.vwdb_check.setChecked(False)
            self.config_panel.cl_check.setChecked(False)
            self.config_panel.sva_check.setChecked(False)
            self.config_panel.sim_only_check.setChecked(False)
            self.config_panel.compile_only_check.setChecked(False)
            self.config_panel.cov_check.setChecked(False)
            self.config_panel.upf_check.setChecked(False)
            self.config_panel.fm_check.setChecked(False)

            # 清除文件选择
            self.clear_fsdb_file()
            self.clear_regr_file()

        finally:
            # 恢复状态标记
            self.config_panel._updating_config = False

            # 恢复定时器（如果之前是活跃的）
            if timer_was_active:
                self.resume_config_panel_timer()

    def update_history_view(self, history):
        """
        更新历史记录视图

        Args:
            history (list): 历史记录列表
        """
        self.config_panel.update_history_combo(history)
