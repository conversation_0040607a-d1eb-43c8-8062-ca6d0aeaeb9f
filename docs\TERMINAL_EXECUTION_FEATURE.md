# RunSim GUI 终端执行功能

## 功能概述

RunSim GUI 新增了"发射到终端执行"功能，允许用户将仿真编译命令发送到指定的终端标签页中执行，而不是在RunSim的内置执行日志窗口中执行。

## 主要特性

### 1. 终端检测
- 自动检测当前系统中所有打开的终端窗口
- 支持多种终端类型：
  - gnome-terminal
  - konsole
  - xterm
  - terminator
  - tilix
  - alacritty
  - kitty

### 2. 用户界面
- 在"执行仿真和编译"按钮旁边添加了"发射到终端执行"按钮
- 蓝色按钮样式，易于识别
- 仅在Linux环境下启用，其他环境下显示为禁用状态

### 3. 终端选择对话框
- 显示所有可用的终端窗口列表
- 实时命令预览
- 支持刷新终端列表
- 支持打开新终端窗口
- 现代化的用户界面设计

### 4. 命令发送机制
- 使用 `xdotool` 直接向终端发送按键
- 支持剪贴板备用方案
- 自动激活目标终端窗口
- 智能处理复杂命令

## 系统要求

### Linux环境依赖
```bash
# Ubuntu/Debian
sudo apt-get install wmctrl xdotool xclip

# CentOS/RHEL
sudo yum install wmctrl xdotool xclip

# 或者使用 xsel 替代 xclip
sudo apt-get install xsel
```

### 支持的操作系统
- **主要支持**: Linux (Ubuntu, CentOS, RHEL, SUSE等)
- **不支持**: Windows, macOS

## 使用方法

### 1. 基本使用流程
1. 在RunSim GUI中配置好仿真参数
2. 点击"发射到终端执行"按钮
3. 在弹出的对话框中选择目标终端
4. 点击"发送命令"按钮
5. 命令将在选定的终端中执行

### 2. 终端选择对话框操作
- **选择终端**: 单击列表中的终端项目
- **快速执行**: 双击终端项目直接发送命令
- **刷新列表**: 点击"刷新终端列表"按钮
- **新建终端**: 点击"打开新终端"按钮
- **取消操作**: 点击"取消"按钮

### 3. 错误处理
- 如果没有找到终端，会提示用户打开新终端
- 如果依赖工具缺失，会显示安装提示
- 如果命令发送失败，会提供错误信息

## 技术实现

### 1. 架构设计
```
views/config_panel.py           # 添加终端执行按钮
controllers/config_controller.py # 处理终端执行逻辑
utils/terminal_detector.py      # 终端检测模块
utils/terminal_manager.py       # 终端管理模块
views/terminal_selector_dialog.py # 终端选择对话框
```

### 2. 核心组件

#### TerminalDetector
- 使用 `wmctrl` 检测窗口
- 通过窗口标题识别终端类型
- 获取窗口ID和进程ID

#### TerminalManager
- 使用 `wmctrl` 激活窗口
- 使用 `xdotool` 发送按键
- 支持剪贴板备用方案

#### TerminalSelectorDialog
- PyQt5 对话框界面
- 实时终端列表更新
- 命令预览和发送

### 3. 信号流程
```
ConfigPanel.terminal_execute_requested
    ↓
ConfigController.execute_command_in_terminal
    ↓
ConfigController.show_terminal_selector
    ↓
TerminalSelectorDialog.exec_()
    ↓
TerminalManager.send_command_to_terminal
```

## 使用场景

### 1. 资源管理
- 当前终端资源紧张时，可以将命令发送到其他空闲终端
- 避免在RunSim GUI中创建过多的执行标签页

### 2. 环境隔离
- 在不同的终端环境中执行命令
- 利用不同终端的环境变量设置

### 3. 并行执行
- 同时在多个终端中执行不同的仿真任务
- 提高系统资源利用效率

### 4. 调试便利
- 在熟悉的终端环境中执行和调试命令
- 利用终端的历史记录和快捷键功能

## 限制和注意事项

### 1. 平台限制
- 仅支持Linux环境
- 需要X11窗口系统支持

### 2. 依赖要求
- 必须安装 `wmctrl` 和 `xdotool`
- 建议安装 `xclip` 或 `xsel`

### 3. 功能限制
- 不支持多用例同时执行
- 无法直接获取命令执行结果
- 依赖于终端窗口的可见性

### 4. 安全考虑
- 命令通过剪贴板传输时可能被其他程序访问
- 建议在安全环境中使用

## 故障排除

### 1. 依赖工具检查
```bash
# 检查工具是否安装
which wmctrl xdotool xclip

# 测试工具功能
wmctrl -l
xdotool --version
```

### 2. 权限问题
```bash
# 确保用户有访问X11的权限
echo $DISPLAY
xhost +local:
```

### 3. 终端检测问题
- 确保终端窗口可见且未最小化
- 检查终端标题是否包含识别信息
- 尝试刷新终端列表

### 4. 命令发送问题
- 检查目标终端是否仍然打开
- 确认终端窗口处于活动状态
- 尝试手动激活终端窗口

## 测试方法

### 1. 功能测试
```bash
# 运行测试脚本
python test_terminal_functionality.py
```

### 2. 手动测试
1. 打开多个终端窗口
2. 启动RunSim GUI
3. 配置仿真参数
4. 点击"发射到终端执行"
5. 验证命令是否正确发送

## 未来改进

### 1. 功能增强
- 支持终端标签页的精确识别
- 添加命令执行状态监控
- 支持批量命令发送

### 2. 用户体验
- 记住用户的终端选择偏好
- 添加终端窗口预览
- 支持自定义终端启动参数

### 3. 平台扩展
- 研究Windows Terminal支持
- 探索macOS Terminal集成
- 支持远程终端连接
