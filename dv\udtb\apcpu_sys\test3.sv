class test;

	force tb.aaa = 1; // FORCE_CHECK(Confirmed by jiadong.he2)
	
	// force tb.bbb = 0;
	
	/*
	force tb.ccc = 0;
	force tb.ddd = 0;
	wait(tb.bbb == 1'b1);
	*/
	
	wait(tb.ccc == 1'b0);
	
	force tb.ddd = 32'h0; // FORCE_CHECK(Confirmed by jiadong.he2)
	
	wait(tb.ddd == 'h4);
	
	void'(sprd_hld_force("tb.ccc", 1)); // FORCE_CHECK(Confirmed by jiadong.he2)
	
	uvm_hld_force("tb.ddd", 0); // FORCE_CHECK(Confirmed by jiadong.he2)
	
	force tb.aaa = 0; // FORCE_CHECK(Confirmed by jiadong.he2)
	
	force tb.ccc = 0; // FORCE_CHECK(Confirmed by jiadong.he2)

endclass