"""
路径解析器模块
"""
import os
import re

class PathResolver:
    """路径解析器，用于处理各种路径相关的操作"""
    
    @staticmethod
    def get_case_directories(current_dir=None):
        """
        获取当前工作目录下的所有用例目录
        
        Args:
            current_dir (str, optional): 当前工作目录，默认为None，表示使用os.getcwd()
            
        Returns:
            list: 用例目录列表
        """
        try:
            # 获取当前工作目录
            if current_dir is None:
                current_dir = os.getcwd()
                
            # 获取所有子目录
            directories = [d for d in os.listdir(current_dir) 
                          if os.path.isdir(os.path.join(current_dir, d)) and 
                          (os.path.exists(os.path.join(current_dir, d, "INCA_libs")) or 
                           os.path.exists(os.path.join(current_dir, d, "log")))]
            return directories
        except Exception as e:
            print(f"获取用例目录失败: {str(e)}")
            return []
    
    @staticmethod
    def get_log_file_path(case_dir, log_type="compile"):
        """
        获取日志文件路径
        
        Args:
            case_dir (str): 用例目录
            log_type (str): 日志类型，可选值为 "compile" 或 "sim"
            
        Returns:
            str: 日志文件路径
        """
        if log_type == "compile":
            return os.path.join(case_dir, "log", "irun_compile.log")
        elif log_type == "sim":
            return os.path.join(case_dir, "log", "irun_sim.log")
        else:
            return None
    
    @staticmethod
    def find_asm_files(case_dir):
        """
        查找用例目录下的所有反汇编文件
        
        Args:
            case_dir (str): 用例目录
            
        Returns:
            list: 反汇编文件路径列表
        """
        asm_files = []
        try:
            # 扫描所有sw_build目录
            sw_build_dirs = []
            for root, dirs, _ in os.walk(case_dir):
                for dir_name in dirs:
                    if dir_name.endswith("_sw_build"):
                        sw_build_dirs.append(os.path.join(root, dir_name))
            
            # 收集所有.asm文件
            for sw_dir in sw_build_dirs:
                for root, _, files in os.walk(sw_dir):
                    for file in files:
                        if file.endswith(".asm"):
                            asm_files.append(os.path.join(root, file))
        except Exception as e:
            print(f"查找反汇编文件时出错: {str(e)}")
        
        return asm_files
