# RunSim GUI仪表盘自动状态更新功能使用指南

## 功能概述

RunSim GUI仪表盘自动状态更新功能实现了仿真执行过程中的实时状态监控和数据库自动更新，主要包括：

1. **仿真执行时间记录**：自动记录仿真开始时间，更新状态为"On-Going"
2. **仿真结果自动检测**：监控日志文件，检测"SPRD_PASSED"标志，自动更新最终状态
3. **智能状态列映射**：根据命令参数自动判断用例类型，更新正确的数据库列
4. **用例通过率统计**：提供按日/按周的用例通过率统计图表和累计统计信息

## 核心功能

### 1. 自动状态更新

当用户在RunSim GUI中点击"执行仿真和编译"按钮时：

- **开始阶段**：
  - 自动记录当前时间戳到Start Time字段（I列）
  - 将用例状态更新为"On-Going"
  - 根据命令参数确定更新的状态列

- **结束阶段**：
  - 监控`irun_sim.log`文件的最后50行
  - 检测"SPRD_PASSED"字符串：
    - 找到 → 状态更新为"PASS"，记录End Time（J列）
    - 未找到 → 状态更新为"FAIL"，不记录结束时间

### 2. 状态列映射规则

系统根据仿真命令中的参数自动确定更新哪个状态列：

```
解析命令参数：-case、-base、-block、-post

判断逻辑：
如果-post参数为空或不存在：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新P列（TOP级用例状态）
    否则：
        更新N列（Subsys级用例状态）

如果-post参数不为空：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新T列（TOP级后仿用例状态）
    否则：
        更新R列（Subsys级后仿用例状态）
```

### 3. 用例通过率统计图表

仪表盘新增用例通过率统计功能：

- **时间维度切换**：支持按天/按周统计
- **显示模式切换**：支持数量模式和通过率模式
- **累计统计信息**：显示总用例数、已执行、已通过、总通过率
- **实时更新**：与仿真执行状态联动，自动刷新数据

## 使用方法

### 1. 启动仪表盘服务

```bash
# 在RunSim GUI中启动仪表盘
# 或者独立启动仪表盘服务
cd plugins/builtin/dashboard_web
python app.py
```

### 2. 执行仿真

在RunSim GUI中正常执行仿真：

1. 选择用例
2. 配置参数
3. 点击"执行仿真和编译"
4. 系统自动监控和更新状态

### 3. 查看统计

访问仪表盘页面查看：

- 实时用例状态更新
- 用例通过率趋势图
- 累计统计信息

## 技术架构

### 核心模块

1. **仿真监听器** (`utils/simulation_monitor.py`)
   - `SimulationMonitor`：主监听器类
   - `CommandParser`：命令参数解析器
   - `LogMonitor`：日志文件监控器

2. **状态更新器** (`utils/dashboard_updater.py`)
   - `DashboardUpdater`：仪表盘状态更新器
   - 支持API更新和直接数据库更新

3. **执行控制器集成** (`controllers/execution_controller.py`)
   - 集成仿真监听器到执行流程
   - 连接执行事件到状态更新

4. **仪表盘API** (`plugins/builtin/dashboard_web/routes/`)
   - `testplan.py`：用例状态更新API
   - `api.py`：用例通过率统计API

### 数据流

```
用户执行仿真
    ↓
执行控制器捕获事件
    ↓
仿真监听器解析命令参数
    ↓
状态更新器调用API/数据库更新
    ↓
仪表盘实时显示更新结果
```

## 配置说明

### 环境变量

- `PROJ_DIR`：项目根目录，用于解析相对路径

### 数据库配置

仪表盘数据库文件默认位置：
- 当前工作目录：`dashboard.db`
- 插件目录：`plugins/builtin/dashboard_web/data/dashboard.db`

### API端点

- 状态更新：`POST /api/testplan/update_from_runsim`
- 通过率统计：`GET /api/dashboard/case_pass_rate`
- 健康检查：`GET /api/health`

## 故障排除

### 常见问题

1. **状态更新失败**
   - 检查仪表盘服务是否运行
   - 检查数据库文件权限
   - 查看日志文件中的错误信息

2. **日志监控不工作**
   - 确认`irun_sim.log`文件路径正确
   - 检查文件读取权限
   - 验证"SPRD_PASSED"字符串格式

3. **图表不显示**
   - 检查浏览器控制台错误
   - 确认API返回数据格式正确
   - 验证Chart.js库加载

### 调试方法

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **测试API连接**
   ```bash
   python test_dashboard_integration.py
   ```

3. **手动测试状态更新**
   ```bash
   curl -X POST http://127.0.0.1:5001/api/testplan/update_from_runsim \
        -H "Content-Type: application/json" \
        -d '{"case_name":"test_case","status":"PASS"}'
   ```

## 性能优化

### 监控优化

- 日志文件检查间隔：10秒
- 最大监控时间：1小时
- 日志读取行数：最后50行

### 数据库优化

- 使用事务确保数据一致性
- 建立适当的索引
- 定期清理历史数据

### 前端优化

- 图表数据缓存
- 按需加载数据
- 防抖动更新机制

## 扩展功能

### 自定义状态检测

可以扩展日志检测逻辑，支持更多的成功/失败标志：

```python
def check_simulation_result(self, log_file_path):
    # 添加自定义检测逻辑
    custom_success_patterns = ['CUSTOM_PASSED', 'TEST_SUCCESS']
    custom_fail_patterns = ['CUSTOM_FAILED', 'TEST_ERROR']
```

### 通知机制

可以添加仿真完成通知：

```python
def on_simulation_finished(self, case_name, success, message):
    # 发送邮件通知
    # 发送企业微信消息
    # 更新外部系统状态
```

## 版本历史

- **v1.0**：基础自动状态更新功能
- **v1.1**：添加用例通过率统计图表
- **v1.2**：优化性能和错误处理

## 联系支持

如有问题或建议，请联系开发团队或提交Issue。
