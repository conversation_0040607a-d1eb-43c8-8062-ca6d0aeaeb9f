"""
智能自动补全引擎
提供模糊匹配、前缀匹配和智能排序功能
"""
import re
import difflib
from typing import List, Tuple, Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QTimer


class AutoCompleteEngine(QObject):
    """智能自动补全引擎"""
    
    # 信号定义
    suggestions_ready = pyqtSignal(str, list)  # 建议准备就绪 (query, suggestions)
    
    def __init__(self, parent=None):
        """初始化补全引擎"""
        super().__init__(parent)
        
        # 防抖动定时器
        self._debounce_timer = QTimer()
        self._debounce_timer.setSingleShot(True)
        self._debounce_timer.timeout.connect(self._process_pending_query)
        self._debounce_delay = 150  # 150ms防抖动
        
        # 待处理的查询
        self._pending_query = None
        self._pending_callback = None
    
    def get_suggestions(self, query: str, candidates: List[str], 
                       max_results: int = 10, 
                       min_score: float = 0.1) -> List[Tuple[str, float]]:
        """
        获取智能建议列表
        
        Args:
            query (str): 查询字符串
            candidates (List[str]): 候选项列表
            max_results (int): 最大返回结果数
            min_score (float): 最小匹配分数
            
        Returns:
            List[Tuple[str, float]]: 建议列表，每项包含(建议文本, 匹配分数)
        """
        if not query or not candidates:
            return [(candidate, 1.0) for candidate in candidates[:max_results]]
        
        query = query.strip().lower()
        if not query:
            return [(candidate, 1.0) for candidate in candidates[:max_results]]
        
        # 计算所有候选项的匹配分数
        scored_candidates = []
        
        for candidate in candidates:
            score = self._calculate_match_score(query, candidate)
            if score >= min_score:
                scored_candidates.append((candidate, score))
        
        # 按分数排序
        scored_candidates.sort(key=lambda x: x[1], reverse=True)
        
        return scored_candidates[:max_results]
    
    def _calculate_match_score(self, query: str, candidate: str) -> float:
        """
        计算匹配分数
        
        Args:
            query (str): 查询字符串
            candidate (str): 候选项
            
        Returns:
            float: 匹配分数 (0.0 - 1.0)
        """
        if not query or not candidate:
            return 0.0
        
        query_lower = query.lower()
        candidate_lower = candidate.lower()
        
        # 1. 完全匹配
        if query_lower == candidate_lower:
            return 1.0
        
        # 2. 前缀匹配
        if candidate_lower.startswith(query_lower):
            # 前缀匹配分数基于匹配长度比例
            return 0.9 + 0.1 * (len(query) / len(candidate))
        
        # 3. 包含匹配
        if query_lower in candidate_lower:
            # 包含匹配分数基于位置和长度
            index = candidate_lower.find(query_lower)
            position_score = 1.0 - (index / len(candidate_lower))  # 越靠前分数越高
            length_score = len(query) / len(candidate)  # 匹配长度比例
            return 0.6 + 0.2 * position_score + 0.2 * length_score
        
        # 4. 模糊匹配
        fuzzy_score = self._fuzzy_match_score(query_lower, candidate_lower)
        if fuzzy_score > 0.3:
            return 0.3 + 0.3 * fuzzy_score
        
        # 5. 子序列匹配
        subsequence_score = self._subsequence_match_score(query_lower, candidate_lower)
        if subsequence_score > 0.5:
            return 0.2 + 0.3 * subsequence_score
        
        return 0.0
    
    def _fuzzy_match_score(self, query: str, candidate: str) -> float:
        """
        模糊匹配分数计算
        
        Args:
            query (str): 查询字符串
            candidate (str): 候选项
            
        Returns:
            float: 模糊匹配分数 (0.0 - 1.0)
        """
        try:
            # 使用difflib计算相似度
            similarity = difflib.SequenceMatcher(None, query, candidate).ratio()
            return similarity
        except:
            return 0.0
    
    def _subsequence_match_score(self, query: str, candidate: str) -> float:
        """
        子序列匹配分数计算
        
        Args:
            query (str): 查询字符串
            candidate (str): 候选项
            
        Returns:
            float: 子序列匹配分数 (0.0 - 1.0)
        """
        if not query or not candidate:
            return 0.0
        
        # 检查query的字符是否按顺序出现在candidate中
        query_index = 0
        matched_chars = 0
        
        for char in candidate:
            if query_index < len(query) and char == query[query_index]:
                matched_chars += 1
                query_index += 1
        
        if matched_chars == 0:
            return 0.0
        
        # 计算匹配比例
        match_ratio = matched_chars / len(query)
        
        # 考虑候选项长度，越短越好
        length_penalty = 1.0 - min(0.5, (len(candidate) - len(query)) / (len(candidate) * 2))
        
        return match_ratio * length_penalty
    
    def get_suggestions_async(self, query: str, candidates: List[str], 
                            callback, max_results: int = 10):
        """
        异步获取建议列表（带防抖动）
        
        Args:
            query (str): 查询字符串
            candidates (List[str]): 候选项列表
            callback: 回调函数
            max_results (int): 最大返回结果数
        """
        # 设置待处理的查询
        self._pending_query = (query, candidates, max_results)
        self._pending_callback = callback
        
        # 重启防抖动定时器
        self._debounce_timer.stop()
        self._debounce_timer.start(self._debounce_delay)
    
    def _process_pending_query(self):
        """处理待处理的查询"""
        if self._pending_query and self._pending_callback:
            query, candidates, max_results = self._pending_query
            
            # 获取建议
            suggestions = self.get_suggestions(query, candidates, max_results)
            
            # 调用回调函数
            self._pending_callback([item[0] for item in suggestions])
            
            # 发送信号
            self.suggestions_ready.emit(query, [item[0] for item in suggestions])
            
            # 清理
            self._pending_query = None
            self._pending_callback = None
    
    def highlight_match(self, text: str, query: str) -> str:
        """
        高亮匹配的文本
        
        Args:
            text (str): 原始文本
            query (str): 查询字符串
            
        Returns:
            str: 带HTML高亮标记的文本
        """
        if not query or not text:
            return text
        
        query_lower = query.lower()
        text_lower = text.lower()
        
        # 查找匹配位置
        if query_lower in text_lower:
            # 直接包含匹配
            start_index = text_lower.find(query_lower)
            end_index = start_index + len(query)
            
            highlighted = (
                text[:start_index] + 
                f'<b style="color: #0066cc;">{text[start_index:end_index]}</b>' + 
                text[end_index:]
            )
            return highlighted
        else:
            # 子序列匹配高亮
            return self._highlight_subsequence(text, query)
    
    def _highlight_subsequence(self, text: str, query: str) -> str:
        """
        高亮子序列匹配
        
        Args:
            text (str): 原始文本
            query (str): 查询字符串
            
        Returns:
            str: 带HTML高亮标记的文本
        """
        if not query:
            return text
        
        result = []
        query_index = 0
        query_lower = query.lower()
        
        for i, char in enumerate(text):
            if (query_index < len(query_lower) and 
                char.lower() == query_lower[query_index]):
                # 匹配的字符高亮
                result.append(f'<b style="color: #0066cc;">{char}</b>')
                query_index += 1
            else:
                result.append(char)
        
        return ''.join(result)
    
    def set_debounce_delay(self, delay_ms: int):
        """
        设置防抖动延迟
        
        Args:
            delay_ms (int): 延迟时间（毫秒）
        """
        self._debounce_delay = max(50, min(1000, delay_ms))  # 限制在50-1000ms之间
