#!/usr/bin/env python3
"""
演示ExecutionPanel仿真选项控制功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.command_generator import CommandParser

def demo_command_modification():
    """演示命令修改功能"""
    print("=" * 60)
    print("RunSim GUI 执行日志面板仿真选项控制功能演示")
    print("=" * 60)
    
    # 演示用例1：基本命令
    print("\n【演示用例1：基本命令】")
    cmd1 = "runsim -base test_base -block test_block -case test_case1 -rundir ./run1"
    print(f"原始命令: {cmd1}")
    
    # 添加-fsdb选项
    cmd1_fsdb = CommandParser.modify_command_options(cmd1, fsdb_enabled=True)
    print(f"添加-fsdb: {cmd1_fsdb}")
    
    # 添加-R选项
    cmd1_r = CommandParser.modify_command_options(cmd1_fsdb, r_enabled=True)
    print(f"添加-R:    {cmd1_r}")
    
    # 移除-fsdb选项
    cmd1_no_fsdb = CommandParser.modify_command_options(cmd1_r, fsdb_enabled=False)
    print(f"移除-fsdb: {cmd1_no_fsdb}")
    
    # 演示用例2：已有选项的命令
    print("\n【演示用例2：已有选项的命令】")
    cmd2 = "runsim -base test_base -fsdb -block test_block -case test_case2 -R -rundir ./run2"
    print(f"原始命令: {cmd2}")
    print(f"包含-fsdb: {CommandParser.has_fsdb_option(cmd2)}")
    print(f"包含-R:   {CommandParser.has_r_option(cmd2)}")
    
    # 移除所有选项
    cmd2_clean = CommandParser.modify_command_options(cmd2, fsdb_enabled=False, r_enabled=False)
    print(f"移除选项: {cmd2_clean}")
    
    # 演示用例3：BATCH RUN命令
    print("\n【演示用例3：BATCH RUN命令】")
    cmd3 = "runsim -base test_base -block test_block -case base_case -rundir ./base ; runsim -base test_base -block test_block -case target_case -R -rundir ./target"
    print(f"原始命令: {cmd3}")
    print(f"包含-fsdb: {CommandParser.has_fsdb_option(cmd3)}")
    print(f"包含-R:   {CommandParser.has_r_option(cmd3)}")
    
    # 添加-fsdb到所有子命令
    cmd3_fsdb = CommandParser.modify_command_options(cmd3, fsdb_enabled=True)
    print(f"添加-fsdb: {cmd3_fsdb}")
    
    # 移除-R从所有子命令
    cmd3_no_r = CommandParser.modify_command_options(cmd3_fsdb, r_enabled=False)
    print(f"移除-R:   {cmd3_no_r}")

def demo_typical_workflow():
    """演示典型的调试工作流程"""
    print("\n" + "=" * 60)
    print("典型调试工作流程演示")
    print("=" * 60)
    
    # 初始仿真命令
    initial_cmd = "runsim -base cpu_base -block cpu_core -case basic_test -rundir ./cpu_test"
    print(f"\n步骤1：初始仿真")
    print(f"命令: {initial_cmd}")
    print("结果: 仿真失败 (FAIL)")
    
    # 添加波形输出进行调试
    debug_cmd = CommandParser.modify_command_options(initial_cmd, fsdb_enabled=True)
    print(f"\n步骤2：添加波形输出调试")
    print(f"操作: 勾选-fsdb复选框")
    print(f"命令: {debug_cmd}")
    print("结果: 生成波形文件，发现问题")
    
    # 修复问题后快速重新仿真
    rerun_cmd = CommandParser.modify_command_options(debug_cmd, r_enabled=True)
    print(f"\n步骤3：快速重新仿真")
    print(f"操作: 勾选-R复选框")
    print(f"命令: {rerun_cmd}")
    print("结果: 跳过编译，快速验证修复")
    
    # 最终验证（移除调试选项）
    final_cmd = CommandParser.modify_command_options(rerun_cmd, fsdb_enabled=False, r_enabled=False)
    print(f"\n步骤4：最终验证")
    print(f"操作: 取消所有调试选项")
    print(f"命令: {final_cmd}")
    print("结果: 完整流程验证通过")

def demo_ui_interaction():
    """演示UI交互流程"""
    print("\n" + "=" * 60)
    print("UI交互流程演示")
    print("=" * 60)
    
    print("\n【界面布局】")
    print("执行日志面板 > 用例标签页 > 状态栏")
    print("├── 状态标签: '状态: 准备执行'")
    print("├── 控制按钮: [重新执行] [暂停] [继续] [停止]")
    print("└── 选项控制: [-fsdb☐] [-R☐] [实时模式☐]")
    
    print("\n【交互步骤】")
    print("1. 用户执行仿真 -> 创建新标签页")
    print("2. 仿真失败 -> 用户勾选[-fsdb☑]")
    print("3. 命令自动更新 -> 预览区显示新命令")
    print("4. 用户点击[重新执行] -> 使用新命令运行")
    print("5. 需要快速重试 -> 用户勾选[-R☑]")
    print("6. 再次重新执行 -> 跳过编译直接仿真")
    
    print("\n【状态同步】")
    print("✓ 复选框状态 ↔ 命令内容")
    print("✓ 命令预览 ↔ 实际执行")
    print("✓ 历史记录 ↔ 当前配置")

def demo_advanced_features():
    """演示高级功能"""
    print("\n" + "=" * 60)
    print("高级功能演示")
    print("=" * 60)
    
    print("\n【智能选项管理】")
    # 演示重复选项处理
    cmd_with_fsdb = "runsim -base test -fsdb -case test1"
    print(f"已有-fsdb的命令: {cmd_with_fsdb}")
    
    # 再次添加-fsdb（应该不重复）
    cmd_fsdb_again = CommandParser.modify_command_options(cmd_with_fsdb, fsdb_enabled=True)
    print(f"再次添加-fsdb:   {cmd_fsdb_again}")
    print("✓ 智能避免重复选项")
    
    print("\n【选项位置优化】")
    # 演示选项插入位置
    cmd_basic = "runsim -base test -case test1"
    cmd_with_options = CommandParser.modify_command_options(cmd_basic, fsdb_enabled=True, r_enabled=True)
    print(f"基础命令: {cmd_basic}")
    print(f"添加选项: {cmd_with_options}")
    print("✓ -fsdb插入到runsim后，-R添加到末尾")
    
    print("\n【错误处理】")
    # 演示异常命令处理
    invalid_cmd = ""
    safe_result = CommandParser.modify_command_options(invalid_cmd, fsdb_enabled=True)
    print(f"空命令处理: '{invalid_cmd}' -> '{safe_result}'")
    print("✓ 优雅处理异常情况")

if __name__ == "__main__":
    demo_command_modification()
    demo_typical_workflow()
    demo_ui_interaction()
    demo_advanced_features()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("\n功能特点总结：")
    print("✓ 直接在执行页面修改仿真选项")
    print("✓ 支持-fsdb和-R选项的灵活控制")
    print("✓ 实时命令预览和状态同步")
    print("✓ 支持普通命令和BATCH RUN命令")
    print("✓ 智能选项管理，避免重复和冲突")
    print("✓ 简化调试流程，提高工作效率")
    
    print("\n使用建议：")
    print("• 仿真失败时勾选-fsdb生成波形")
    print("• 调试时勾选-R跳过编译节省时间")
    print("• 根据需要灵活组合选项")
    print("• 利用实时预览确认命令正确性")
