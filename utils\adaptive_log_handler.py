"""
自适应日志处理器
"""
import time
from collections import deque
from threading import Lock

class AdaptiveLogHandler:
    """
    自适应日志处理器，根据日志生成速率动态调整刷新频率

    优化点：
    1. 根据日志生成速率动态调整刷新间隔
    2. 在高速日志生成时降低刷新频率，避免UI阻塞
    3. 在低速日志生成时提高刷新频率，保持实时性
    4. 使用多级缓冲区，分离收集和显示逻辑
    """

    # 常量定义
    MIN_REFRESH_INTERVAL = 100   # 最小刷新间隔(ms)
    MAX_REFRESH_INTERVAL = 2000  # 最大刷新间隔(ms)
    RATE_HISTORY_SIZE = 10       # 速率历史记录大小
    HIGH_RATE_THRESHOLD = 5000   # 高速率阈值（字符/秒）
    LOW_RATE_THRESHOLD = 500     # 低速率阈值（字符/秒）

    def __init__(self):
        """初始化自适应日志处理器"""
        self._buffer = []  # 主缓冲区
        self._batch = []   # 批处理缓冲区
        self._lock = Lock()  # 线程锁

        # 速率计算相关
        self._rate_history = deque(maxlen=self.RATE_HISTORY_SIZE)  # 速率历史记录
        self._last_append_time = time.time()  # 上次追加时间
        self._last_append_size = 0  # 上次追加大小
        self._total_chars = 0  # 总字符数

        # 刷新相关
        self._current_refresh_interval = 300  # 当前刷新间隔(ms)
        self._last_flush_time = time.time()  # 上次刷新时间
        self._is_visible = True  # 是否可见

    def append(self, text):
        """
        追加文本到缓冲区

        Args:
            text (str): 要追加的文本
        """
        if not text:
            return

        with self._lock:
            # 添加到批处理缓冲区
            self._batch.append(text)

            # 计算当前速率
            current_time = time.time()
            time_diff = current_time - self._last_append_time

            if time_diff > 0.1:  # 至少间隔0.1秒计算一次速率
                chars_added = sum(len(t) for t in self._batch) - self._last_append_size
                rate = chars_added / time_diff  # 字符/秒

                # 更新速率历史
                self._rate_history.append(rate)

                # 更新计数器
                self._last_append_time = current_time
                self._last_append_size = sum(len(t) for t in self._batch)
                self._total_chars += chars_added

                # 调整刷新间隔
                self._adjust_refresh_interval()

    def flush(self, force=False):
        """
        刷新缓冲区

        Args:
            force (bool): 是否强制刷新

        Returns:
            str: 刷新的文本，如果没有刷新则返回空字符串
        """
        current_time = time.time()
        refresh_interval = self._current_refresh_interval / 1000.0  # 转换为秒

        # 修复：即使在隐藏状态也要定期刷新，只是间隔更长
        # 如果强制刷新或者已经到了刷新时间，就进行刷新
        if not force and (current_time - self._last_flush_time < refresh_interval):
            # 如果有大量日志积累，即使没到刷新时间也要刷新
            with self._lock:
                total_size = sum(len(text) for text in self._batch) + sum(len(text) for text in self._buffer)
                if total_size < 10000:  # 如果缓冲区不大，就等待下一次刷新
                    return ""

        with self._lock:
            # 合并批处理缓冲区到主缓冲区
            if self._batch:
                batch_text = ''.join(self._batch)
                if batch_text:  # 只有在有内容时才添加
                    self._buffer.append(batch_text)
                self._batch = []

            # 获取主缓冲区内容
            if not self._buffer:
                return ""

            # 合并主缓冲区内容
            result = ''.join(self._buffer)

            # 始终清空缓冲区，避免重复内容
            self._buffer = []

            # 更新刷新时间
            self._last_flush_time = current_time

            return result

    def set_visible(self, visible):
        """
        设置是否可见

        Args:
            visible (bool): 是否可见
        """
        self._is_visible = visible

        # 如果变为可见，立即刷新
        if visible:
            self._current_refresh_interval = self.MIN_REFRESH_INTERVAL
        else:
            self._current_refresh_interval = self.MAX_REFRESH_INTERVAL

    def get_refresh_interval(self):
        """
        获取当前刷新间隔

        Returns:
            int: 当前刷新间隔(ms)
        """
        return self._current_refresh_interval

    def get_log_rate(self):
        """
        获取当前日志生成速率

        Returns:
            float: 当前日志生成速率（字符/秒）
        """
        if not self._rate_history:
            return 0.0

        return sum(self._rate_history) / len(self._rate_history)

    def clear(self):
        """清空缓冲区"""
        with self._lock:
            self._buffer = []
            self._batch = []
            self._rate_history.clear()
            self._last_append_time = time.time()
            self._last_append_size = 0
            self._total_chars = 0

    def _adjust_refresh_interval(self):
        """根据日志生成速率调整刷新间隔"""
        if not self._rate_history:
            return

        # 计算平均速率
        avg_rate = sum(self._rate_history) / len(self._rate_history)

        # 根据速率调整刷新间隔
        if avg_rate > self.HIGH_RATE_THRESHOLD:
            # 高速率，降低刷新频率
            target_interval = min(self.MAX_REFRESH_INTERVAL,
                                 self._current_refresh_interval * 1.2)
        elif avg_rate < self.LOW_RATE_THRESHOLD:
            # 低速率，提高刷新频率
            target_interval = max(self.MIN_REFRESH_INTERVAL,
                                 self._current_refresh_interval * 0.8)
        else:
            # 中等速率，保持当前刷新频率
            return

        # 平滑过渡
        self._current_refresh_interval = int(0.7 * self._current_refresh_interval + 0.3 * target_interval)

        # 确保在范围内
        self._current_refresh_interval = max(self.MIN_REFRESH_INTERVAL,
                                           min(self.MAX_REFRESH_INTERVAL,
                                              self._current_refresh_interval))
