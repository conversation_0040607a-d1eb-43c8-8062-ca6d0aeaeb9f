# 数据看板
为RunSim GUI增加看板功能，用于实时监控用仿真状态，形成用例表格以及一些图片展示。

# 具体要求
 - 数据看板位于主GUI “插件”右边
 - 即使没有打开数据看板，依然能够实现对正在仿真的用例状态进行监控
 - 每次执行用例都会形成一条记录并通过表格形式展示，表格格式如下：
    - 序号
    - 用例名称：解析runsim命令，从-case选项获取
    - 用例状态：默认状态为Pending，点击“执行仿真和编译按钮”后更新用例状态为On-Going，当用例执行完毕，自动读取用例目录下的irun_sim.log文件的最后50行内容，检测是否包含"SPRD_PASSED"字符串，如果找到"SPRD_PASSED"：判定为PASS，更新用例状态为PASS，否则为FAIL，更新用例状态为FAIL
    - 开始时间：只有第一次执行该用例时，点击“执行仿真和编译按钮”后才更新开始时间，后续重复执行相同case不更新开始时间
    - 结束时间：当用例执行完毕后，自动读取用例目录下的irun_sim.log文件的最后50行内容，检测是否包含"SPRD_PASSED"字符串，如果找到"SPRD_PASSED"，则更新结束时间，否则不更新。而且同一条用例反复仿真，只更新第一次该用例PASS时的结束时间，后续PASS不再更新结束时间
    - 编译耗时：当用例执行完毕后，读取irun_compile.log，获取编译时间，具体参考plugins/builtin/time_analyzer.py中的get_time_from_log函数
    - 仿真耗时：当用例执行完毕后，读取irun_sim.log，获取编译时间，具体参考plugins/builtin/time_analyzer.py中的get_time_from_log函数
    - 仿真阶段：下拉列表，由用户选择，合法值是DVR1、DVR2、DVR2、DVS1、DVS2
 - 表格支持导出为Excel格式
 - 表格支持过滤和排序
 - 表格支持列宽调整
 - 表格支持行列冻结
 - 支持导入TestPlan功能，TestPlan表格格式参考TestPlan表格说明.md
 - 图表展示功能，具体包括：
    - 每日/每周用例通过数柱状图
    - 每日/每周通过用例总数折线图

# 技术要求
 - 数据库使用splite3
 - 图表使用matplotlib或者其他，请你推荐

