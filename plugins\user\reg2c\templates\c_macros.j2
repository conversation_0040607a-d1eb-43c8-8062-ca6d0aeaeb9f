#ifndef {{ module_name|upper }}_H
#define {{ module_name|upper }}_H

#include <stdint.h>

#define {{ module_name | upper }}_BASE_ADDR 0x{{ "%08X" % base_addr }}

// Register offset macro definitions
{% for reg in registers %}
#define {{ reg.name|upper }}_OFFSET 0x{{ "%04X" % reg.offset }}
{% endfor %}

// Register and field definitions
{% for reg in registers %}
/* {{ reg.name }} - {{ reg.short_desc }} */
typedef union {
    struct {
{% for field in reg.fields %}
        uint32_t {{ field.name }} : {{ field.bit_width }};  // {{ field.bits }} {{ field.rw }}
{% endfor %}
    } bits;
    uint32_t value;
} {{ reg.name }}_t;
{% for field in reg.fields %}
{% if field.name %}
#define {{ reg.name|upper }}_{{ field.name|upper }}_WIDTH {{ field.bit_width }}
#define {{ reg.name|upper }}_{{ field.name|upper }}_MASK ((1UL << {{ reg.name|upper }}_{{ field.name|upper }}_WIDTH) - 1UL)
#define {{ reg.name|upper }}_{{ field.name|upper }}_POS  {{ field.bit_end }}
{% endif %}
{% endfor %}

{% endfor %}
// Bit field operation macros
{% for reg in registers %}
/* {{ reg.name }} access macros */
#define {{ reg.name|upper }}_READ() \
    (*((volatile uint32_t*)({{ module_name|upper }}_BASE_ADDR + {{ reg.offset }})))
#define {{ reg.name|upper }}_WRITE(value) \
    (*((volatile uint32_t*)({{ module_name|upper }}_BASE_ADDR + {{ reg.offset }})) = (value))
{% for field in reg.fields %}
{% if field.name %}
/* {{ field.name }} bit field operations */
#define {{ field.name|upper }}_{{ field.name|upper }}_GET() \
    (({{ field.name|upper }}_READ() >> {{ reg.name|upper }}_{{ field.name|upper }}_POS) & {{ reg.name|upper }}_{{ field.name|upper }}_MASK)
{% if field.rw == "RW" %}
#define {{ field.name|upper }}_{{ field.name|upper }}_SET(value) \
    {{ field.name|upper }}_WRITE( \
        ({{ field.name|upper }}_READ() & ~({{ reg.name|upper }}_{{ field.name|upper }}_MASK << {{ reg.name|upper }}_{{ field.name|upper }}_POS) \
        | ((value) << {{ reg.name|upper }}_{{ field.name|upper }}_POS) \
    )
{% endif %}
{% endif %}
{% endfor %}

{% endfor %}