"""
定时器管理器 - 统一管理和协调多个定时器
从LogPanel中提取的定时器管理逻辑
"""
import time
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from .signal_safety import safe_disconnect


class TimerManager(QObject):
    """
    定时器管理器，统一管理和协调多个定时器
    
    职责：
    1. 创建和管理多个命名定时器
    2. 自适应定时器间隔调整
    3. 定时器生命周期管理
    4. 性能优化和资源管理
    """
    
    # 信号定义
    timer_triggered = pyqtSignal(str, object)  # 定时器触发，参数为定时器名称和数据
    
    def __init__(self, parent=None):
        """
        初始化定时器管理器
        
        Args:
            parent (QObject): 父对象
        """
        super().__init__(parent)
        
        # 定时器存储
        self._timers = {}  # 定时器信息字典
        self._active_timers = set()  # 活跃定时器集合
        
        # 自适应管理
        self._adaptive_manager = AdaptiveTimerManager()
        
        # 统计信息
        self._stats = {
            'total_timers_created': 0,
            'total_triggers': 0,
            'avg_interval': 0
        }

        # 清理状态标志
        self._cleaned_up = False
    
    def create_timer(self, name, interval, callback, single_shot=False, adaptive=False):
        """
        创建定时器
        
        Args:
            name (str): 定时器名称
            interval (int): 间隔时间（毫秒）
            callback (callable): 回调函数
            single_shot (bool): 是否单次触发
            adaptive (bool): 是否启用自适应间隔
            
        Returns:
            bool: 创建是否成功
        """
        if name in self._timers:
            print(f"定时器 '{name}' 已存在")
            return False
        
        try:
            # 创建QTimer对象
            timer = QTimer(self)
            timer.setSingleShot(single_shot)
            
            # 连接信号
            if adaptive:
                timer.timeout.connect(lambda: self._adaptive_timeout(name))
            else:
                timer.timeout.connect(lambda: self._normal_timeout(name))
            
            # 存储定时器信息
            timer_info = {
                'timer': timer,
                'callback': callback,
                'interval': interval,
                'current_interval': interval,
                'single_shot': single_shot,
                'adaptive': adaptive,
                'created_time': time.time(),
                'trigger_count': 0,
                'last_trigger_time': None
            }
            
            self._timers[name] = timer_info
            self._stats['total_timers_created'] += 1
            
            return True
            
        except Exception as e:
            print(f"创建定时器 '{name}' 时出错: {str(e)}")
            return False
    
    def start_timer(self, name):
        """
        启动定时器
        
        Args:
            name (str): 定时器名称
            
        Returns:
            bool: 启动是否成功
        """
        if name not in self._timers:
            print(f"定时器 '{name}' 不存在")
            return False
        
        try:
            timer_info = self._timers[name]
            timer = timer_info['timer']
            
            if not timer.isActive():
                timer.start(timer_info['current_interval'])
                self._active_timers.add(name)
                
                # 如果是自适应定时器，注册到自适应管理器
                if timer_info['adaptive']:
                    self._adaptive_manager.register_timer(name, timer_info)
            
            return True
            
        except Exception as e:
            print(f"启动定时器 '{name}' 时出错: {str(e)}")
            return False
    
    def stop_timer(self, name):
        """
        停止定时器
        
        Args:
            name (str): 定时器名称
            
        Returns:
            bool: 停止是否成功
        """
        if name not in self._timers:
            return False
        
        try:
            timer_info = self._timers[name]
            timer = timer_info['timer']
            
            if timer.isActive():
                timer.stop()
                self._active_timers.discard(name)
                
                # 从自适应管理器注销
                if timer_info['adaptive']:
                    self._adaptive_manager.unregister_timer(name)
            
            return True
            
        except Exception as e:
            print(f"停止定时器 '{name}' 时出错: {str(e)}")
            return False
    
    def adjust_timer_interval(self, name, new_interval):
        """
        调整定时器间隔
        
        Args:
            name (str): 定时器名称
            new_interval (int): 新的间隔时间（毫秒）
            
        Returns:
            bool: 调整是否成功
        """
        if name not in self._timers:
            return False
        
        try:
            timer_info = self._timers[name]
            timer = timer_info['timer']
            
            # 更新间隔
            timer_info['current_interval'] = new_interval
            
            # 如果定时器正在运行，重新设置间隔
            if timer.isActive():
                timer.setInterval(new_interval)
            
            return True
            
        except Exception as e:
            print(f"调整定时器 '{name}' 间隔时出错: {str(e)}")
            return False
    
    def is_timer_active(self, name):
        """
        检查定时器是否活跃
        
        Args:
            name (str): 定时器名称
            
        Returns:
            bool: 是否活跃
        """
        return name in self._active_timers
    
    def get_timer_info(self, name):
        """
        获取定时器信息
        
        Args:
            name (str): 定时器名称
            
        Returns:
            dict: 定时器信息
        """
        if name in self._timers:
            timer_info = self._timers[name].copy()
            # 移除QTimer对象，避免序列化问题
            timer_info.pop('timer', None)
            timer_info.pop('callback', None)
            return timer_info
        return None
    
    def get_all_timers_info(self):
        """
        获取所有定时器信息
        
        Returns:
            dict: 所有定时器信息
        """
        info = {}
        for name in self._timers:
            info[name] = self.get_timer_info(name)
        return info
    
    def stop_all_timers(self):
        """停止所有定时器"""
        for name in list(self._active_timers):
            self.stop_timer(name)
    
    def remove_timer(self, name):
        """
        移除定时器
        
        Args:
            name (str): 定时器名称
            
        Returns:
            bool: 移除是否成功
        """
        if name not in self._timers:
            return False
        
        try:
            # 先停止定时器
            self.stop_timer(name)
            
            # 删除定时器对象
            timer_info = self._timers[name]
            timer_info['timer'].deleteLater()
            
            # 从字典中移除
            del self._timers[name]
            
            return True
            
        except Exception as e:
            print(f"移除定时器 '{name}' 时出错: {str(e)}")
            return False
    
    def _normal_timeout(self, name):
        """普通定时器超时处理"""
        if name in self._timers:
            timer_info = self._timers[name]
            
            # 更新统计
            timer_info['trigger_count'] += 1
            timer_info['last_trigger_time'] = time.time()
            self._stats['total_triggers'] += 1
            
            # 调用回调函数
            try:
                timer_info['callback']()
                self.timer_triggered.emit(name, None)
            except Exception as e:
                print(f"定时器 '{name}' 回调函数执行出错: {str(e)}")
    
    def _adaptive_timeout(self, name):
        """自适应定时器超时处理"""
        if name in self._timers:
            timer_info = self._timers[name]
            
            # 更新统计
            timer_info['trigger_count'] += 1
            timer_info['last_trigger_time'] = time.time()
            self._stats['total_triggers'] += 1
            
            # 获取自适应间隔
            new_interval = self._adaptive_manager.get_adaptive_interval(name)
            if new_interval != timer_info['current_interval']:
                self.adjust_timer_interval(name, new_interval)
            
            # 调用回调函数
            try:
                timer_info['callback']()
                self.timer_triggered.emit(name, {'new_interval': new_interval})
            except Exception as e:
                print(f"自适应定时器 '{name}' 回调函数执行出错: {str(e)}")
    
    def get_stats(self):
        """获取统计信息"""
        active_count = len(self._active_timers)
        total_count = len(self._timers)
        
        # 计算平均间隔
        if self._timers:
            total_interval = sum(info['current_interval'] for info in self._timers.values())
            avg_interval = total_interval / len(self._timers)
        else:
            avg_interval = 0
        
        return {
            'total_timers': total_count,
            'active_timers': active_count,
            'avg_interval': avg_interval,
            **self._stats
        }
    
    def cleanup(self):
        """清理所有资源 - Linux兼容版本"""
        # 防止重复清理
        if hasattr(self, '_cleaned_up') and self._cleaned_up:
            return

        try:
            # 标记为已清理，防止重复调用
            self._cleaned_up = True

            # 首先安全地断开信号
            self._safe_disconnect_timer_signals()

            # 检查应用程序是否正在关闭
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import QThread

            app_instance = QApplication.instance()
            # 检查应用实例是否存在或正在关闭
            if app_instance is None or app_instance.closingDown():
                # 如果应用实例不存在或正在关闭，直接在当前线程执行清理，避免延迟
                self._cleanup_in_main_thread()
                return

            if QThread.currentThread() != app_instance.thread():
                # 如果不在主线程且应用程序未关闭，使用QTimer.singleShot在主线程中执行
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(0, self._cleanup_in_main_thread)
                return

            self._cleanup_in_main_thread()

        except Exception as e:
            print(f"清理定时器管理器时出错: {str(e)}")
            # 确保无论如何都尝试清理
            try:
                self._cleanup_in_main_thread()
            except Exception as e2:
                print(f"应急清理定时器管理器时出错: {str(e2)}")

    def _cleanup_in_main_thread(self):
        """在主线程中执行清理操作"""
        try:
            # 停止所有定时器
            self.stop_all_timers()

            # 断开所有信号连接并删除定时器
            for name in list(self._timers.keys()):
                try:
                    timer_info = self._timers[name]
                    timer = timer_info['timer']

                    # 安全地断开信号连接
                    try:
                        timer.timeout.disconnect()
                    except (TypeError, RuntimeError):
                        pass

                    # 停止并删除定时器
                    if timer.isActive():
                        timer.stop()
                    timer.deleteLater()

                except Exception as e:
                    print(f"清理定时器 '{name}' 时出错: {str(e)}")

            # 清空字典
            self._timers.clear()
            self._active_timers.clear()

            # 清理自适应管理器
            if hasattr(self, '_adaptive_manager') and self._adaptive_manager:
                self._adaptive_manager.cleanup()
                self._adaptive_manager = None

            # 安全地断开自身信号连接
            self._safe_disconnect_timer_signals()

        except Exception as e:
            print(f"在主线程清理定时器管理器时出错: {str(e)}")

    def _safe_disconnect_timer_signals(self):
        """
        安全地断开定时器信号连接 - Linux兼容版本
        使用统一的信号安全工具
        """
        # 使用安全的信号断开工具
        safe_disconnect(self.timer_triggered, 'timer_triggered')


class AdaptiveTimerManager:
    """自适应定时器管理器，根据系统状态动态调整定时器间隔"""
    
    def __init__(self):
        self._registered_timers = {}
        self._system_load_factor = 1.0
        self._last_adjustment_time = time.time()
        
    def register_timer(self, name, timer_info):
        """注册自适应定时器"""
        self._registered_timers[name] = {
            'base_interval': timer_info['interval'],
            'current_load': 0.0,
            'adjustment_history': []
        }
    
    def unregister_timer(self, name):
        """注销自适应定时器"""
        self._registered_timers.pop(name, None)
    
    def get_adaptive_interval(self, name):
        """获取自适应间隔"""
        if name not in self._registered_timers:
            return 1000  # 默认间隔
        
        timer_data = self._registered_timers[name]
        base_interval = timer_data['base_interval']
        
        # 根据系统负载调整间隔
        adjusted_interval = int(base_interval * self._system_load_factor)
        
        # 限制调整范围
        min_interval = base_interval // 2
        max_interval = base_interval * 3
        
        adjusted_interval = max(min_interval, min(max_interval, adjusted_interval))
        
        # 记录调整历史
        timer_data['adjustment_history'].append({
            'time': time.time(),
            'interval': adjusted_interval,
            'load_factor': self._system_load_factor
        })
        
        # 限制历史记录长度
        if len(timer_data['adjustment_history']) > 10:
            timer_data['adjustment_history'].pop(0)
        
        return adjusted_interval
    
    def update_system_load(self, cpu_usage, memory_usage):
        """更新系统负载信息"""
        # 简单的负载因子计算
        load_factor = 1.0
        
        if cpu_usage > 80 or memory_usage > 80:
            load_factor = 2.0  # 高负载时增加间隔
        elif cpu_usage > 60 or memory_usage > 60:
            load_factor = 1.5  # 中等负载时适度增加间隔
        
        self._system_load_factor = load_factor
        self._last_adjustment_time = time.time()
    
    def cleanup(self):
        """清理资源"""
        self._registered_timers.clear()
