"""
字体管理器 - 跨平台字体检测和配置
提供自动字体检测、配置和matplotlib中文字体支持
"""
import platform
import os
import sys
from typing import List, Optional, Dict
import warnings

class FontManager:
    """字体管理器 - 处理跨平台字体配置"""
    
    def __init__(self):
        self.system = platform.system()
        self.available_fonts = []
        self.selected_font = None
        
        # 预定义的字体映射
        self.font_mapping = {
            "Windows": [
                'Microsoft YaHei',
                'SimHei', 
                'SimSun',
                'KaiTi',
                'FangSong'
            ],
            "Darwin": [  # macOS
                'PingFang SC',
                'Hiragino Sans GB',
                'STHeiti',
                'STSong',
                'Arial Unicode MS'
            ],
            "Linux": [
                'WenQuanYi Micro Hei',
                'WenQuanYi Zen Hei',
                'Noto Sans CJK SC',
                'Source Han Sans SC',
                'Droid Sans Fallback',
                'AR PL UMing CN',
                'AR PL UKai CN'
            ]
        }
        
        # 通用后备字体
        self.fallback_fonts = [
            'DejaVu Sans',
            'Liberation Sans',
            'Arial',
            'Helvetica',
            'sans-serif'
        ]
    
    def detect_available_fonts(self) -> List[str]:
        """检测系统可用的中文字体"""
        try:
            import matplotlib.font_manager as fm
            
            # 获取系统所有字体
            font_list = fm.findSystemFonts()
            font_names = []
            
            # 获取字体名称
            for font_path in font_list:
                try:
                    font_prop = fm.FontProperties(fname=font_path)
                    font_name = font_prop.get_name()
                    if font_name not in font_names:
                        font_names.append(font_name)
                except:
                    continue
            
            # 过滤出可能的中文字体
            system_fonts = self.font_mapping.get(self.system, [])
            available = []
            
            for font in system_fonts:
                if font in font_names:
                    available.append(font)
            
            self.available_fonts = available
            return available
            
        except ImportError:
            # matplotlib不可用时的简化检测
            return self._simple_font_detection()
        except Exception as e:
            print(f"字体检测失败: {e}")
            return self._simple_font_detection()
    
    def _simple_font_detection(self) -> List[str]:
        """简化的字体检测（不依赖matplotlib）"""
        system_fonts = self.font_mapping.get(self.system, [])
        
        if self.system == "Linux":
            # Linux系统的字体路径检测
            font_paths = [
                '/usr/share/fonts/',
                '/usr/local/share/fonts/',
                '~/.fonts/',
                '~/.local/share/fonts/'
            ]
            
            available = []
            for font in system_fonts:
                # 简单的文件名检测
                font_found = False
                for path in font_paths:
                    expanded_path = os.path.expanduser(path)
                    if os.path.exists(expanded_path):
                        for root, dirs, files in os.walk(expanded_path):
                            for file in files:
                                if any(keyword in file.lower() for keyword in 
                                      font.lower().split()):
                                    available.append(font)
                                    font_found = True
                                    break
                            if font_found:
                                break
                    if font_found:
                        break
            
            return available
        else:
            # Windows和macOS假设字体可用
            return system_fonts
    
    def get_best_font_list(self) -> List[str]:
        """获取最佳字体列表"""
        # 检测可用字体
        available = self.detect_available_fonts()
        
        # 构建字体列表
        font_list = []
        
        # 添加可用的系统字体
        font_list.extend(available)
        
        # 添加系统默认字体（即使检测不到也尝试使用）
        system_defaults = self.font_mapping.get(self.system, [])
        for font in system_defaults:
            if font not in font_list:
                font_list.append(font)
        
        # 添加后备字体
        font_list.extend(self.fallback_fonts)
        
        return font_list
    
    def configure_matplotlib_fonts(self) -> bool:
        """配置matplotlib字体"""
        try:
            from matplotlib import rcParams
            import matplotlib.font_manager
            
            # 获取最佳字体列表
            font_list = self.get_best_font_list()
            
            # 设置字体
            rcParams['font.sans-serif'] = font_list
            rcParams['axes.unicode_minus'] = False
            
            # 设置字体大小
            rcParams['font.size'] = 10
            rcParams['axes.titlesize'] = 12
            rcParams['axes.labelsize'] = 10
            rcParams['xtick.labelsize'] = 9
            rcParams['ytick.labelsize'] = 9
            rcParams['legend.fontsize'] = 9
            
            # 刷新字体缓存（抑制警告）
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                try:
                    matplotlib.font_manager._rebuild()
                except:
                    pass
            
            # 记录选择的字体
            if font_list:
                self.selected_font = font_list[0]
                print(f"字体配置完成，使用字体: {self.selected_font}")
            
            return True
            
        except ImportError:
            print("matplotlib未安装，跳过字体配置")
            return False
        except Exception as e:
            print(f"配置matplotlib字体失败: {e}")
            return False
    
    def configure_qt_fonts(self) -> bool:
        """配置Qt字体"""
        try:
            from PyQt5.QtGui import QFont, QFontDatabase
            from PyQt5.QtWidgets import QApplication
            
            app = QApplication.instance()
            if not app:
                return False
            
            # 获取最佳字体
            font_list = self.get_best_font_list()
            
            # 尝试设置应用程序字体
            font_db = QFontDatabase()
            for font_name in font_list:
                font = QFont(font_name, 9)
                # 检查字体是否可用
                families = font_db.families()
                if font_name in families:
                    app.setFont(font)
                    print(f"Qt字体设置为: {font_name}")
                    return True
            
            # 如果没有找到合适字体，使用系统默认
            print("使用系统默认Qt字体")
            return True
            
        except ImportError:
            print("PyQt5未安装，跳过Qt字体配置")
            return False
        except Exception as e:
            print(f"配置Qt字体失败: {e}")
            return False
    
    def get_font_info(self) -> Dict[str, any]:
        """获取字体配置信息"""
        return {
            'system': self.system,
            'available_fonts': self.available_fonts,
            'selected_font': self.selected_font,
            'font_list': self.get_best_font_list()
        }
    
    def print_font_info(self):
        """打印字体配置信息"""
        info = self.get_font_info()
        print("=" * 50)
        print("字体配置信息")
        print("=" * 50)
        print(f"操作系统: {info['system']}")
        print(f"检测到的中文字体: {len(info['available_fonts'])} 个")
        for font in info['available_fonts']:
            print(f"  - {font}")
        print(f"当前使用字体: {info['selected_font'] or '系统默认'}")
        print("=" * 50)


# 全局字体管理器实例
font_manager = FontManager()


def setup_fonts():
    """设置应用程序字体（便捷函数）"""
    print("正在配置应用程序字体...")
    
    # 配置matplotlib字体
    matplotlib_ok = font_manager.configure_matplotlib_fonts()
    
    # 配置Qt字体
    qt_ok = font_manager.configure_qt_fonts()
    
    # 打印配置信息
    if matplotlib_ok or qt_ok:
        font_manager.print_font_info()
    
    return matplotlib_ok or qt_ok


if __name__ == "__main__":
    # 测试字体管理器
    setup_fonts()
