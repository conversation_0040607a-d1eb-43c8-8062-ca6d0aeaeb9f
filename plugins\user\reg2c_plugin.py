"""
寄存器表格转C驱动头文件工具插件

提供将寄存器配置表格转换生成对应的C语言驱动头文件的插件。
支持GUI界面操作，包含Excel解析、Jinja2模板引擎、复杂的数据处理逻辑等功能。
"""

import os
import sys
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from plugins.base import PluginBase

class Reg2CPlugin(PluginBase):
    """寄存器表格转C驱动头文件工具插件主类"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.reg_parser_window = None
        self.main_window = None
        self.menu_action = None
        self._app_loaded = False  # 标记是否已加载应用
        
    @property
    def name(self) -> str:
        """插件名称"""
        return "寄存器表格转C驱动头文件工具"
    
    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """插件描述"""
        return "将寄存器配置表格转换生成对应的C语言驱动头文件"
    
    def initialize(self, main_window):
        """初始化插件
        
        Args:
            main_window: 主窗口实例
        """
        try:
            self.main_window = main_window
            
            # 不再在初始化时创建寄存器解析窗口，改为延迟加载
            # self.reg_parser_window = RegParserApp()
            
            # 添加菜单项
            self._add_menu_item()
            
            print(f"插件 {self.name} 初始化成功")
            
        except Exception as e:
            print(f"插件 {self.name} 初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def cleanup(self):
        """清理插件资源"""
        try:
            # 移除菜单项
            if self.menu_action and self.main_window:
                tools_menu = getattr(self.main_window, 'tools_menu', None)
                if tools_menu:
                    tools_menu.removeAction(self.menu_action)
            
            # 关闭寄存器解析窗口
            if self.reg_parser_window:
                self.reg_parser_window.close()
                self.reg_parser_window = None
            
            self.main_window = None
            self.menu_action = None
            
            print(f"插件 {self.name} 清理完成")
            
        except Exception as e:
            print(f"插件 {self.name} 清理失败: {str(e)}")
    
    def _add_menu_item(self):
        """添加菜单项"""
        try:
            if not self.main_window:
                return
            
            # 获取工具菜单
            tools_menu = getattr(self.main_window, 'tools_menu', None)
            if not tools_menu:
                print("未找到工具菜单")
                return
            
            # 创建菜单动作
            self.menu_action = QAction("寄存器表格转C驱动头文件工具", self.main_window)
            self.menu_action.setStatusTip("打开寄存器表格转C驱动头文件工具")
            self.menu_action.triggered.connect(self._on_menu_triggered)
            
            # 添加到工具菜单
            tools_menu.addAction(self.menu_action)
            
        except Exception as e:
            print(f"添加菜单项失败: {str(e)}")
    
    def _on_menu_triggered(self):
        """菜单项被点击时的处理函数"""
        try:
            # 如果应用尚未加载，先加载应用
            if not self._app_loaded:
                # 导入模块
                from reg2c.reg_parser_gui import RegParserApp
                self.reg_parser_window = RegParserApp()
                self._app_loaded = True
                print(f"已加载寄存器表格转C驱动头文件工具模块")
            
            self._show_reg_parser_window()
            
        except Exception as e:
            print(f"加载寄存器表格转C驱动头文件工具失败: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开寄存器表格转C驱动头文件工具: {str(e)}"
            )
    
    def _show_reg_parser_window(self):
        """显示寄存器解析窗口"""
        try:
            if self.reg_parser_window:
                self.reg_parser_window.show()
                self.reg_parser_window.raise_()
                self.reg_parser_window.activateWindow()
        except Exception as e:
            print(f"显示寄存器解析窗口失败: {str(e)}")
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开寄存器表格转C驱动头文件工具: {str(e)}"
            )


# 插件实例
plugin_instance = Reg2CPlugin()
