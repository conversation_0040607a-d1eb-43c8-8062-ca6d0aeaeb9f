"""
本地模型配置对话框

提供本地AI模型的添加、配置和管理界面。
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
    QTableWidget, QTableWidgetItem, QFileDialog, QMessageBox,
    QTextEdit, QProgressBar, QGroupBox, QFormLayout,
    QCheckBox, QSlider
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QObject
from PyQt5.QtGui import QFont

from ..utils.local_model_client import LocalModelManager, LocalModelInfo


class ModelInstallWorker(QObject):
    """模型安装工作线程"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, model_path: str, model_name: str, model_type: str):
        super().__init__()
        self.model_path = model_path
        self.model_name = model_name
        self.model_type = model_type
    
    def install_model(self):
        """安装模型"""
        try:
            self.status_updated.emit("正在验证模型文件...")
            self.progress_updated.emit(20)
            
            # 检查文件是否存在
            if not os.path.exists(self.model_path):
                self.finished.emit(False, "模型文件不存在")
                return
            
            self.status_updated.emit("正在检查模型格式...")
            self.progress_updated.emit(40)
            
            # 检查文件格式
            file_ext = os.path.splitext(self.model_path)[1].lower()
            if self.model_type == 'gguf' and file_ext != '.gguf':
                self.finished.emit(False, "GGUF模型文件扩展名应为.gguf")
                return
            
            self.status_updated.emit("正在配置模型...")
            self.progress_updated.emit(60)
            
            # 这里可以添加更多的模型验证逻辑
            
            self.status_updated.emit("安装完成")
            self.progress_updated.emit(100)
            
            self.finished.emit(True, "模型安装成功")
            
        except Exception as e:
            self.finished.emit(False, f"安装失败: {str(e)}")


class LocalModelDialog(QDialog):
    """本地模型配置对话框"""
    
    def __init__(self, model_manager: LocalModelManager, parent=None):
        super().__init__(parent)
        self.model_manager = model_manager
        self.setup_ui()
        self.load_models()
        
        # 连接信号
        self.model_manager.model_loaded.connect(self.on_model_loaded)
        self.model_manager.model_unloaded.connect(self.on_model_unloaded)
        self.model_manager.error_occurred.connect(self.on_error_occurred)
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("本地模型管理")
        self.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 模型列表标签页
        self.create_model_list_tab()
        
        # 添加模型标签页
        self.create_add_model_tab()
        
        # 模型配置标签页
        self.create_config_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def create_model_list_tab(self):
        """创建模型列表标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 标题
        title_label = QLabel("已安装的模型")
        title_label.setFont(QFont("", 14, QFont.Bold))
        layout.addWidget(title_label)
        
        # 模型表格
        self.model_table = QTableWidget()
        self.model_table.setColumnCount(5)
        self.model_table.setHorizontalHeaderLabels([
            "模型名称", "类型", "状态", "路径", "操作"
        ])
        self.model_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.model_table)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_models)
        button_layout.addWidget(self.refresh_btn)
        
        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self.load_selected_model)
        button_layout.addWidget(self.load_model_btn)
        
        self.unload_model_btn = QPushButton("卸载模型")
        self.unload_model_btn.clicked.connect(self.unload_selected_model)
        button_layout.addWidget(self.unload_model_btn)
        
        self.remove_model_btn = QPushButton("删除模型")
        self.remove_model_btn.clicked.connect(self.remove_selected_model)
        button_layout.addWidget(self.remove_model_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.tab_widget.addTab(tab, "模型列表")
    
    def create_add_model_tab(self):
        """创建添加模型标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 标题
        title_label = QLabel("添加新模型")
        title_label.setFont(QFont("", 14, QFont.Bold))
        layout.addWidget(title_label)
        
        # 表单
        form_group = QGroupBox("模型信息")
        form_layout = QFormLayout(form_group)
        
        self.model_name_edit = QLineEdit()
        self.model_name_edit.setPlaceholderText("输入模型名称")
        form_layout.addRow("模型名称:", self.model_name_edit)
        
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItems(["gguf", "ollama", "onnx", "pytorch"])
        form_layout.addRow("模型类型:", self.model_type_combo)
        
        # 文件路径选择
        path_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText("选择模型文件路径")
        path_layout.addWidget(self.model_path_edit)
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_model_file)
        path_layout.addWidget(self.browse_btn)
        
        form_layout.addRow("文件路径:", path_layout)
        
        layout.addWidget(form_group)
        
        # 高级配置
        config_group = QGroupBox("高级配置")
        config_layout = QFormLayout(config_group)
        
        self.context_size_spin = QSpinBox()
        self.context_size_spin.setRange(512, 32768)
        self.context_size_spin.setValue(2048)
        config_layout.addRow("上下文大小:", self.context_size_spin)
        
        self.threads_spin = QSpinBox()
        self.threads_spin.setRange(1, 16)
        self.threads_spin.setValue(4)
        config_layout.addRow("线程数:", self.threads_spin)
        
        self.llama_cpp_path_edit = QLineEdit()
        self.llama_cpp_path_edit.setPlaceholderText("llama.cpp可执行文件路径（可选）")
        config_layout.addRow("llama.cpp路径:", self.llama_cpp_path_edit)
        
        layout.addWidget(config_group)
        
        # 安装进度
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel()
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)
        
        # 添加按钮
        add_layout = QHBoxLayout()
        add_layout.addStretch()
        
        self.add_model_btn = QPushButton("添加模型")
        self.add_model_btn.clicked.connect(self.add_model)
        add_layout.addWidget(self.add_model_btn)
        
        layout.addLayout(add_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "添加模型")
    
    def create_config_tab(self):
        """创建配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 标题
        title_label = QLabel("全局配置")
        title_label.setFont(QFont("", 14, QFont.Bold))
        layout.addWidget(title_label)
        
        # 配置表单
        config_group = QGroupBox("默认设置")
        config_layout = QFormLayout(config_group)
        
        self.auto_load_checkbox = QCheckBox()
        self.auto_load_checkbox.setChecked(True)
        config_layout.addRow("启动时自动加载模型:", self.auto_load_checkbox)
        
        self.default_port_spin = QSpinBox()
        self.default_port_spin.setRange(1024, 65535)
        self.default_port_spin.setValue(11434)
        config_layout.addRow("默认端口:", self.default_port_spin)
        
        layout.addWidget(config_group)
        
        # 帮助信息
        help_group = QGroupBox("使用说明")
        help_layout = QVBoxLayout(help_group)
        
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setMaximumHeight(200)
        help_text.setPlainText("""
本地模型支持说明：

1. GGUF模型：
   - 需要安装llama.cpp
   - 支持Llama、Mistral等模型
   - 推荐使用量化版本以节省内存

2. Ollama模型：
   - 需要安装Ollama
   - 支持多种开源模型
   - 自动管理模型下载和运行

3. 模型推荐：
   - 轻量级：Llama 3.2 3B
   - 平衡性：Llama 3.1 8B
   - 高性能：Llama 3.1 70B（需要大内存）

4. 注意事项：
   - 确保有足够的内存和存储空间
   - 首次加载可能需要较长时间
   - 建议使用SSD以提高加载速度
        """)
        help_layout.addWidget(help_text)
        
        layout.addWidget(help_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "配置与帮助")
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #FAFAFA;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }
            QTabWidget::pane {
                border: 1px solid #E1E8ED;
                border-radius: 8px;
                background-color: #FFFFFF;
            }
            QTabBar::tab {
                background-color: #F7F9FA;
                color: #2C3E50;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background-color: #FFFFFF;
                color: #1DA1F2;
                border-bottom: 2px solid #1DA1F2;
            }
            QGroupBox {
                font-weight: 600;
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2C3E50;
            }
            QPushButton {
                background-color: #1DA1F2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #1991DB;
            }
            QPushButton:pressed {
                background-color: #1681C4;
            }
            QLineEdit, QComboBox, QSpinBox {
                border: 2px solid #E1E8ED;
                border-radius: 6px;
                padding: 6px 8px;
                background-color: #FFFFFF;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
                border-color: #1DA1F2;
            }
            QTableWidget {
                border: 1px solid #E1E8ED;
                border-radius: 8px;
                background-color: #FFFFFF;
                gridline-color: #F0F0F0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
            }
        """)
    
    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择模型文件",
            "",
            "模型文件 (*.gguf *.bin *.onnx *.pt *.pth);;所有文件 (*)"
        )
        
        if file_path:
            self.model_path_edit.setText(file_path)
            
            # 自动设置模型名称
            if not self.model_name_edit.text():
                name = os.path.splitext(os.path.basename(file_path))[0]
                self.model_name_edit.setText(name)
    
    def add_model(self):
        """添加模型"""
        name = self.model_name_edit.text().strip()
        path = self.model_path_edit.text().strip()
        model_type = self.model_type_combo.currentText()
        
        if not name or not path:
            QMessageBox.warning(self, "警告", "请填写模型名称和文件路径")
            return
        
        # 构建配置
        config = {
            'context_size': self.context_size_spin.value(),
            'threads': self.threads_spin.value(),
            'llama_cpp_path': self.llama_cpp_path_edit.text().strip()
        }
        
        # 显示进度
        self.progress_bar.setVisible(True)
        self.status_label.setVisible(True)
        self.add_model_btn.setEnabled(False)
        
        # 创建安装线程
        self.install_thread = QThread()
        self.install_worker = ModelInstallWorker(path, name, model_type)
        self.install_worker.moveToThread(self.install_thread)
        
        # 连接信号
        self.install_worker.progress_updated.connect(self.progress_bar.setValue)
        self.install_worker.status_updated.connect(self.status_label.setText)
        self.install_worker.finished.connect(self.on_install_finished)
        self.install_thread.started.connect(self.install_worker.install_model)
        
        # 启动线程
        self.install_thread.start()
        
        # 添加到模型管理器
        if self.model_manager.add_model(name, path, model_type, config):
            self.load_models()
    
    def on_install_finished(self, success: bool, message: str):
        """安装完成回调"""
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
        self.add_model_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "成功", message)
            # 清空表单
            self.model_name_edit.clear()
            self.model_path_edit.clear()
            self.load_models()
        else:
            QMessageBox.critical(self, "错误", message)
        
        # 清理线程
        self.install_thread.quit()
        self.install_thread.wait()
    
    def load_models(self):
        """加载模型列表"""
        models = self.model_manager.get_model_list()
        self.model_table.setRowCount(len(models))
        
        for i, model in enumerate(models):
            # 模型名称
            self.model_table.setItem(i, 0, QTableWidgetItem(model.name))
            
            # 模型类型
            self.model_table.setItem(i, 1, QTableWidgetItem(model.model_type.upper()))
            
            # 状态
            status = "已加载" if model.is_loaded else "未加载"
            status_item = QTableWidgetItem(status)
            if model.is_loaded:
                status_item.setBackground(Qt.green)
            self.model_table.setItem(i, 2, status_item)
            
            # 路径
            self.model_table.setItem(i, 3, QTableWidgetItem(model.path))
            
            # 操作按钮（这里简化处理）
            self.model_table.setItem(i, 4, QTableWidgetItem(""))
    
    def load_selected_model(self):
        """加载选中的模型"""
        current_row = self.model_table.currentRow()
        if current_row >= 0:
            model_name = self.model_table.item(current_row, 0).text()
            if self.model_manager.load_model(model_name):
                QMessageBox.information(self, "成功", f"模型 {model_name} 加载成功")
                self.load_models()
            else:
                QMessageBox.warning(self, "失败", f"模型 {model_name} 加载失败")
    
    def unload_selected_model(self):
        """卸载选中的模型"""
        current_row = self.model_table.currentRow()
        if current_row >= 0:
            model_name = self.model_table.item(current_row, 0).text()
            if self.model_manager.unload_model(model_name):
                QMessageBox.information(self, "成功", f"模型 {model_name} 卸载成功")
                self.load_models()
            else:
                QMessageBox.warning(self, "失败", f"模型 {model_name} 卸载失败")
    
    def remove_selected_model(self):
        """删除选中的模型"""
        current_row = self.model_table.currentRow()
        if current_row >= 0:
            model_name = self.model_table.item(current_row, 0).text()
            
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除模型 {model_name} 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if self.model_manager.remove_model(model_name):
                    QMessageBox.information(self, "成功", f"模型 {model_name} 删除成功")
                    self.load_models()
                else:
                    QMessageBox.warning(self, "失败", f"模型 {model_name} 删除失败")
    
    def on_model_loaded(self, model_name: str):
        """模型加载完成"""
        self.load_models()
    
    def on_model_unloaded(self, model_name: str):
        """模型卸载完成"""
        self.load_models()
    
    def on_error_occurred(self, model_name: str, error: str):
        """错误发生"""
        QMessageBox.critical(self, "错误", f"模型 {model_name}: {error}")
