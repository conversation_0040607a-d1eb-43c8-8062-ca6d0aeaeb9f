"""
优化的文档处理器

支持大文档的高效处理，包括：
- 分块处理
- 并行解析
- 缓存机制
- 进度回调
- 内存优化
"""

import os
import json
import hashlib
import threading
import concurrent.futures
from typing import Tuple, List, Dict, Any, Optional, Callable
from dataclasses import dataclass
import time
from PyQt5.QtCore import QObject, pyqtSignal, QThread


@dataclass
class DocumentChunk:
    """文档块"""
    chunk_id: str
    content: str
    start_pos: int
    end_pos: int
    page_num: Optional[int] = None


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    content: str
    chunks: List[DocumentChunk]
    metadata: Dict[str, Any]
    error_message: str = ""
    processing_time: float = 0.0


class OptimizedDocumentProcessor(QObject):
    """优化的文档处理器"""
    
    # 信号
    progress_updated = pyqtSignal(str, int, str)  # doc_id, progress, status
    chunk_processed = pyqtSignal(str, object)  # doc_id, chunk
    processing_completed = pyqtSignal(str, object)  # doc_id, result
    error_occurred = pyqtSignal(str, str)  # doc_id, error
    
    def __init__(self, cache_dir: str = "document_cache", max_workers: int = 4):
        super().__init__()
        self.cache_dir = cache_dir
        self.max_workers = max_workers
        self.chunk_size = 1024 * 1024  # 1MB per chunk
        self.max_file_size = 100 * 1024 * 1024  # 100MB limit
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
        
        # 处理器映射
        self.processors = {
            '.txt': self._process_text_file,
            '.md': self._process_markdown_file,
            '.pdf': self._process_pdf_file,
            '.docx': self._process_docx_file,
            '.doc': self._process_doc_file,
            '.rtf': self._process_rtf_file
        }
    
    def process_document_async(self, file_path: str, doc_id: str, 
                             progress_callback: Optional[Callable] = None) -> QThread:
        """异步处理文档"""
        thread = QThread()
        worker = DocumentProcessingWorker(
            file_path, doc_id, self, progress_callback
        )
        worker.moveToThread(thread)
        
        # 连接信号
        thread.started.connect(worker.process)
        worker.finished.connect(thread.quit)
        worker.finished.connect(worker.deleteLater)
        thread.finished.connect(thread.deleteLater)
        
        # 转发信号
        worker.progress_updated.connect(self.progress_updated.emit)
        worker.chunk_processed.connect(self.chunk_processed.emit)
        worker.processing_completed.connect(self.processing_completed.emit)
        worker.error_occurred.connect(self.error_occurred.emit)
        
        thread.start()
        return thread
    
    def process_document(self, file_path: str, doc_id: str) -> ProcessingResult:
        """同步处理文档"""
        start_time = time.time()
        
        try:
            # 检查文件
            if not os.path.exists(file_path):
                return ProcessingResult(
                    success=False,
                    content="",
                    chunks=[],
                    metadata={},
                    error_message="文件不存在"
                )
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                return ProcessingResult(
                    success=False,
                    content="",
                    chunks=[],
                    metadata={},
                    error_message=f"文件过大: {file_size / (1024*1024):.1f}MB > {self.max_file_size / (1024*1024):.1f}MB"
                )
            
            # 检查缓存
            cache_key = self._get_cache_key(file_path)
            cached_result = self._load_from_cache(cache_key)
            if cached_result:
                cached_result.processing_time = time.time() - start_time
                return cached_result
            
            # 获取文件扩展名
            extension = os.path.splitext(file_path)[1].lower()
            if extension not in self.processors:
                return ProcessingResult(
                    success=False,
                    content="",
                    chunks=[],
                    metadata={},
                    error_message=f"不支持的文件格式: {extension}"
                )
            
            # 处理文档
            processor = self.processors[extension]
            result = processor(file_path, doc_id)
            
            # 保存到缓存
            if result.success:
                self._save_to_cache(cache_key, result)
            
            result.processing_time = time.time() - start_time
            return result
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message=f"处理文档时出错: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def _get_cache_key(self, file_path: str) -> str:
        """生成缓存键"""
        # 基于文件路径、大小和修改时间生成唯一键
        stat = os.stat(file_path)
        key_data = f"{file_path}_{stat.st_size}_{stat.st_mtime}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _load_from_cache(self, cache_key: str) -> Optional[ProcessingResult]:
        """从缓存加载结果"""
        try:
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                chunks = [
                    DocumentChunk(**chunk_data) 
                    for chunk_data in data.get('chunks', [])
                ]
                
                return ProcessingResult(
                    success=data['success'],
                    content=data['content'],
                    chunks=chunks,
                    metadata=data['metadata'],
                    error_message=data.get('error_message', '')
                )
        except Exception as e:
            print(f"加载缓存失败: {e}")
        
        return None
    
    def _save_to_cache(self, cache_key: str, result: ProcessingResult):
        """保存结果到缓存"""
        try:
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            
            # 转换chunks为可序列化的格式
            chunks_data = []
            for chunk in result.chunks:
                chunks_data.append({
                    'chunk_id': chunk.chunk_id,
                    'content': chunk.content,
                    'start_pos': chunk.start_pos,
                    'end_pos': chunk.end_pos,
                    'page_num': chunk.page_num
                })
            
            data = {
                'success': result.success,
                'content': result.content,
                'chunks': chunks_data,
                'metadata': result.metadata,
                'error_message': result.error_message,
                'cached_time': time.time()
            }
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def _process_text_file(self, file_path: str, doc_id: str) -> ProcessingResult:
        """处理文本文件"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'ascii']
            content = ""
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if not content:
                return ProcessingResult(
                    success=False,
                    content="",
                    chunks=[],
                    metadata={},
                    error_message="无法识别文件编码"
                )
            
            # 分块处理
            chunks = self._split_into_chunks(content, doc_id)
            
            metadata = {
                'file_type': 'text',
                'encoding': encoding,
                'char_count': len(content),
                'chunk_count': len(chunks)
            }
            
            return ProcessingResult(
                success=True,
                content=content,
                chunks=chunks,
                metadata=metadata
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message=f"处理文本文件失败: {str(e)}"
            )
    
    def _process_markdown_file(self, file_path: str, doc_id: str) -> ProcessingResult:
        """处理Markdown文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 清理Markdown标记
            import re
            clean_content = re.sub(r'!\[.*?\]\(.*?\)', '', content)
            clean_content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', clean_content)
            clean_content = re.sub(r'```[\s\S]*?```', '[代码块]', clean_content)
            clean_content = re.sub(r'`([^`]+)`', r'\1', clean_content)
            
            chunks = self._split_into_chunks(clean_content, doc_id)
            
            metadata = {
                'file_type': 'markdown',
                'original_size': len(content),
                'cleaned_size': len(clean_content),
                'chunk_count': len(chunks)
            }
            
            return ProcessingResult(
                success=True,
                content=clean_content,
                chunks=chunks,
                metadata=metadata
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message=f"处理Markdown文件失败: {str(e)}"
            )
    
    def _process_pdf_file(self, file_path: str, doc_id: str) -> ProcessingResult:
        """处理PDF文件（优化版）"""
        try:
            import PyPDF2
            
            content = ""
            chunks = []
            
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                total_pages = len(pdf_reader.pages)
                
                # 并行处理页面
                with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    # 提交所有页面处理任务
                    future_to_page = {
                        executor.submit(self._extract_pdf_page, pdf_reader, page_num): page_num
                        for page_num in range(total_pages)
                    }
                    
                    # 收集结果
                    page_contents = {}
                    for future in concurrent.futures.as_completed(future_to_page):
                        page_num = future_to_page[future]
                        try:
                            page_text = future.result()
                            page_contents[page_num] = page_text
                        except Exception as e:
                            print(f"处理PDF页面{page_num}失败: {e}")
                            page_contents[page_num] = ""
                
                # 按页面顺序组合内容
                for page_num in range(total_pages):
                    page_text = page_contents.get(page_num, "")
                    if page_text:
                        content += page_text + "\n"
                        
                        # 创建页面块
                        chunk = DocumentChunk(
                            chunk_id=f"{doc_id}_page_{page_num}",
                            content=page_text,
                            start_pos=len(content) - len(page_text) - 1,
                            end_pos=len(content) - 1,
                            page_num=page_num
                        )
                        chunks.append(chunk)
            
            if not content.strip():
                return ProcessingResult(
                    success=False,
                    content="",
                    chunks=[],
                    metadata={},
                    error_message="PDF文件中没有可提取的文本内容"
                )
            
            metadata = {
                'file_type': 'pdf',
                'page_count': total_pages,
                'char_count': len(content),
                'chunk_count': len(chunks)
            }
            
            return ProcessingResult(
                success=True,
                content=content,
                chunks=chunks,
                metadata=metadata
            )
            
        except ImportError:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message="需要安装PyPDF2库: pip install PyPDF2"
            )
        except Exception as e:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message=f"处理PDF文件失败: {str(e)}"
            )
    
    def _extract_pdf_page(self, pdf_reader, page_num: int) -> str:
        """提取PDF页面文本（线程安全）"""
        try:
            page = pdf_reader.pages[page_num]
            return page.extract_text()
        except Exception as e:
            print(f"提取PDF页面{page_num}失败: {e}")
            return ""

    def _process_docx_file(self, file_path: str, doc_id: str) -> ProcessingResult:
        """处理DOCX文件"""
        try:
            from docx import Document

            doc = Document(file_path)
            content = ""
            chunks = []

            # 处理段落
            for i, paragraph in enumerate(doc.paragraphs):
                para_text = paragraph.text.strip()
                if para_text:
                    content += para_text + "\n"

                    # 创建段落块
                    chunk = DocumentChunk(
                        chunk_id=f"{doc_id}_para_{i}",
                        content=para_text,
                        start_pos=len(content) - len(para_text) - 1,
                        end_pos=len(content) - 1
                    )
                    chunks.append(chunk)

            # 处理表格
            for i, table in enumerate(doc.tables):
                table_content = ""
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    table_content += " | ".join(row_text) + "\n"

                if table_content:
                    content += table_content

                    # 创建表格块
                    chunk = DocumentChunk(
                        chunk_id=f"{doc_id}_table_{i}",
                        content=table_content,
                        start_pos=len(content) - len(table_content),
                        end_pos=len(content) - 1
                    )
                    chunks.append(chunk)

            metadata = {
                'file_type': 'docx',
                'paragraph_count': len(doc.paragraphs),
                'table_count': len(doc.tables),
                'char_count': len(content),
                'chunk_count': len(chunks)
            }

            return ProcessingResult(
                success=True,
                content=content,
                chunks=chunks,
                metadata=metadata
            )

        except ImportError:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message="需要安装python-docx库: pip install python-docx"
            )
        except Exception as e:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message=f"处理DOCX文件失败: {str(e)}"
            )

    def _process_doc_file(self, file_path: str, doc_id: str) -> ProcessingResult:
        """处理DOC文件"""
        try:
            # 尝试使用docx2txt
            try:
                import docx2txt
                content = docx2txt.process(file_path)
                chunks = self._split_into_chunks(content, doc_id)

                metadata = {
                    'file_type': 'doc',
                    'char_count': len(content),
                    'chunk_count': len(chunks),
                    'processor': 'docx2txt'
                }

                return ProcessingResult(
                    success=True,
                    content=content,
                    chunks=chunks,
                    metadata=metadata
                )
            except ImportError:
                pass

            # 尝试使用win32com
            try:
                import win32com.client

                word = win32com.client.Dispatch("Word.Application")
                word.Visible = False

                doc = word.Documents.Open(file_path)
                content = doc.Content.Text
                doc.Close()
                word.Quit()

                chunks = self._split_into_chunks(content, doc_id)

                metadata = {
                    'file_type': 'doc',
                    'char_count': len(content),
                    'chunk_count': len(chunks),
                    'processor': 'win32com'
                }

                return ProcessingResult(
                    success=True,
                    content=content,
                    chunks=chunks,
                    metadata=metadata
                )

            except ImportError:
                pass
            except Exception as e:
                return ProcessingResult(
                    success=False,
                    content="",
                    chunks=[],
                    metadata={},
                    error_message=f"使用Word COM组件失败: {str(e)}"
                )

            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message="需要安装docx2txt库或在Windows上使用Word COM组件"
            )

        except Exception as e:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message=f"处理DOC文件失败: {str(e)}"
            )

    def _process_rtf_file(self, file_path: str, doc_id: str) -> ProcessingResult:
        """处理RTF文件"""
        try:
            from striprtf.striprtf import rtf_to_text

            with open(file_path, 'r', encoding='utf-8') as f:
                rtf_content = f.read()

            content = rtf_to_text(rtf_content)
            chunks = self._split_into_chunks(content, doc_id)

            metadata = {
                'file_type': 'rtf',
                'char_count': len(content),
                'chunk_count': len(chunks)
            }

            return ProcessingResult(
                success=True,
                content=content,
                chunks=chunks,
                metadata=metadata
            )

        except ImportError:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message="需要安装striprtf库: pip install striprtf"
            )
        except Exception as e:
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={},
                error_message=f"处理RTF文件失败: {str(e)}"
            )

    def _split_into_chunks(self, content: str, doc_id: str) -> List[DocumentChunk]:
        """将内容分割成块"""
        chunks = []
        chunk_size = self.chunk_size // 2  # 字符数，大约是字节数的一半

        for i in range(0, len(content), chunk_size):
            chunk_content = content[i:i + chunk_size]

            chunk = DocumentChunk(
                chunk_id=f"{doc_id}_chunk_{i // chunk_size}",
                content=chunk_content,
                start_pos=i,
                end_pos=min(i + chunk_size, len(content))
            )
            chunks.append(chunk)

        return chunks

    def clear_cache(self):
        """清空缓存"""
        try:
            for file in os.listdir(self.cache_dir):
                if file.endswith('.json'):
                    os.remove(os.path.join(self.cache_dir, file))
        except Exception as e:
            print(f"清空缓存失败: {e}")


class DocumentProcessingWorker(QObject):
    """文档处理工作器"""

    # 信号
    progress_updated = pyqtSignal(str, int, str)
    chunk_processed = pyqtSignal(str, object)
    processing_completed = pyqtSignal(str, object)
    error_occurred = pyqtSignal(str, str)
    finished = pyqtSignal()

    def __init__(self, file_path: str, doc_id: str, processor: OptimizedDocumentProcessor,
                 progress_callback: Optional[Callable] = None):
        super().__init__()
        self.file_path = file_path
        self.doc_id = doc_id
        self.processor = processor
        self.progress_callback = progress_callback

    def process(self):
        """处理文档"""
        try:
            # 发送开始信号
            self.progress_updated.emit(self.doc_id, 0, "开始处理文档...")

            # 处理文档
            result = self.processor.process_document(self.file_path, self.doc_id)

            if result.success:
                # 发送块处理信号
                for i, chunk in enumerate(result.chunks):
                    progress = int((i + 1) / len(result.chunks) * 90)
                    self.progress_updated.emit(self.doc_id, progress, f"处理块 {i+1}/{len(result.chunks)}")
                    self.chunk_processed.emit(self.doc_id, chunk)

                # 发送完成信号
                self.progress_updated.emit(self.doc_id, 100, "处理完成")
                self.processing_completed.emit(self.doc_id, result)
            else:
                self.error_occurred.emit(self.doc_id, result.error_message)

        except Exception as e:
            self.error_occurred.emit(self.doc_id, f"处理过程中出错: {str(e)}")

        finally:
            self.finished.emit()
