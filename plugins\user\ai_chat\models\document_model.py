"""
文档处理数据模型

管理文档上传、解析和处理
"""

import os
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal


class DocumentInfo:
    """文档信息类"""
    
    def __init__(self, file_path: str, name: str = None):
        """初始化文档信息
        
        Args:
            file_path: 文件路径
            name: 文档名称（可选）
        """
        self.file_path = file_path
        self.name = name or os.path.basename(file_path)
        self.size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        self.extension = os.path.splitext(file_path)[1].lower()
        self.upload_time = datetime.now()
        self.content = ""
        self.summary = ""
        self.processed = False
        self.error_message = ""
        self.selected = True  # 默认选中
        
        # 生成文档ID（基于文件路径和修改时间的哈希）
        if os.path.exists(file_path):
            mtime = os.path.getmtime(file_path)
            hash_input = f"{file_path}_{mtime}_{self.size}"
            self.doc_id = hashlib.md5(hash_input.encode()).hexdigest()
        else:
            self.doc_id = hashlib.md5(file_path.encode()).hexdigest()
    
    def get_size_str(self) -> str:
        """获取文件大小的字符串表示"""
        if self.size < 1024:
            return f"{self.size} B"
        elif self.size < 1024 * 1024:
            return f"{self.size / 1024:.1f} KB"
        else:
            return f"{self.size / (1024 * 1024):.1f} MB"
    
    def is_valid(self) -> bool:
        """检查文档是否有效"""
        return os.path.exists(self.file_path) and self.size > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'doc_id': self.doc_id,
            'file_path': self.file_path,
            'name': self.name,
            'size': self.size,
            'extension': self.extension,
            'upload_time': self.upload_time.isoformat(),
            'content': self.content,
            'summary': self.summary,
            'processed': self.processed,
            'error_message': self.error_message,
            'selected': getattr(self, 'selected', True)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DocumentInfo':
        """从字典创建文档信息"""
        doc = cls(data['file_path'], data.get('name'))
        doc.doc_id = data['doc_id']
        doc.size = data['size']
        doc.extension = data['extension']
        doc.upload_time = datetime.fromisoformat(data['upload_time'])
        doc.content = data.get('content', '')
        doc.summary = data.get('summary', '')
        doc.processed = data.get('processed', False)
        doc.error_message = data.get('error_message', '')
        doc.selected = data.get('selected', True)
        return doc


class DocumentModel(QObject):
    """文档数据模型"""
    
    # 信号
    document_added = pyqtSignal(DocumentInfo)
    document_processed = pyqtSignal(DocumentInfo)
    document_removed = pyqtSignal(str)  # doc_id
    processing_started = pyqtSignal(str)  # doc_id
    processing_error = pyqtSignal(str, str)  # doc_id, error_message
    
    # 支持的文档格式
    SUPPORTED_FORMATS = {
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.rtf': 'application/rtf'
    }
    
    def __init__(self, max_file_size: int = 10 * 1024 * 1024):
        """初始化文档模型
        
        Args:
            max_file_size: 最大文件大小（字节）
        """
        super().__init__()
        self.documents: Dict[str, DocumentInfo] = {}
        self.max_file_size = max_file_size
    
    def add_document(self, file_path: str, name: str = None) -> Optional[DocumentInfo]:
        """添加文档
        
        Args:
            file_path: 文件路径
            name: 文档名称（可选）
            
        Returns:
            DocumentInfo: 文档信息对象，如果添加失败返回None
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise ValueError(f"文件不存在: {file_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                raise ValueError(f"文件大小超过限制: {file_size / (1024*1024):.1f}MB > {self.max_file_size / (1024*1024):.1f}MB")
            
            # 检查文件格式
            extension = os.path.splitext(file_path)[1].lower()
            if extension not in self.SUPPORTED_FORMATS:
                raise ValueError(f"不支持的文件格式: {extension}")
            
            # 创建文档信息
            doc_info = DocumentInfo(file_path, name)
            
            # 检查是否已存在
            if doc_info.doc_id in self.documents:
                return self.documents[doc_info.doc_id]
            
            # 添加到文档列表
            self.documents[doc_info.doc_id] = doc_info
            
            # 发送信号
            self.document_added.emit(doc_info)
            
            return doc_info
            
        except Exception as e:
            print(f"添加文档失败: {str(e)}")
            return None
    
    def remove_document(self, doc_id: str):
        """移除文档
        
        Args:
            doc_id: 文档ID
        """
        if doc_id in self.documents:
            del self.documents[doc_id]
            self.document_removed.emit(doc_id)
    
    def get_document(self, doc_id: str) -> Optional[DocumentInfo]:
        """获取文档信息
        
        Args:
            doc_id: 文档ID
            
        Returns:
            DocumentInfo: 文档信息对象
        """
        return self.documents.get(doc_id)
    
    def get_all_documents(self) -> List[DocumentInfo]:
        """获取所有文档"""
        return list(self.documents.values())
    
    def get_processed_documents(self) -> List[DocumentInfo]:
        """获取已处理的文档"""
        return [doc for doc in self.documents.values() if doc.processed]
    
    def get_unprocessed_documents(self) -> List[DocumentInfo]:
        """获取未处理的文档"""
        return [doc for doc in self.documents.values() if not doc.processed]
    
    def update_document_content(self, doc_id: str, content: str, summary: str = ""):
        """更新文档内容
        
        Args:
            doc_id: 文档ID
            content: 文档内容
            summary: 文档摘要
        """
        doc = self.get_document(doc_id)
        if doc:
            doc.content = content
            doc.summary = summary
            doc.processed = True
            doc.error_message = ""
            
            self.document_processed.emit(doc)
    
    def set_document_error(self, doc_id: str, error_message: str):
        """设置文档处理错误
        
        Args:
            doc_id: 文档ID
            error_message: 错误信息
        """
        doc = self.get_document(doc_id)
        if doc:
            doc.error_message = error_message
            doc.processed = False
            
            self.processing_error.emit(doc_id, error_message)
    
    def clear_all_documents(self):
        """清空所有文档"""
        doc_ids = list(self.documents.keys())
        self.documents.clear()
        
        for doc_id in doc_ids:
            self.document_removed.emit(doc_id)
    
    def get_total_size(self) -> int:
        """获取所有文档的总大小"""
        return sum(doc.size for doc in self.documents.values())
    
    def get_document_count_by_format(self) -> Dict[str, int]:
        """按格式统计文档数量"""
        count = {}
        for doc in self.documents.values():
            ext = doc.extension
            count[ext] = count.get(ext, 0) + 1
        return count
    
    def is_format_supported(self, file_path: str) -> bool:
        """检查文件格式是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        extension = os.path.splitext(file_path)[1].lower()
        return extension in self.SUPPORTED_FORMATS
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式列表"""
        return list(self.SUPPORTED_FORMATS.keys())
    
    def get_content_for_context(self, max_length: int = 4000, selected_only: bool = False) -> str:
        """获取用于上下文的文档内容

        Args:
            max_length: 最大长度
            selected_only: 是否只获取选中的文档

        Returns:
            str: 合并的文档内容
        """
        # 获取要处理的文档
        if selected_only:
            processed_docs = [doc for doc in self.get_processed_documents() if getattr(doc, 'selected', True)]
        else:
            processed_docs = self.get_processed_documents()

        if not processed_docs:
            return ""

        content_parts = []
        current_length = 0

        # 添加文档数量信息
        doc_count_info = f"用户已上传 {len(processed_docs)} 个文档："
        content_parts.append(doc_count_info)
        current_length += len(doc_count_info)

        for i, doc in enumerate(processed_docs, 1):
            # 使用更清晰的文档分隔符
            doc_header = f"\n\n【文档 {i}：{doc.name}】\n"

            # 优先使用摘要，如果没有摘要则使用内容的前部分
            if doc.summary and len(doc.summary.strip()) > 0:
                doc_content = doc.summary.strip()
            else:
                doc_content = doc.content[:1500].strip()
                if len(doc.content) > 1500:
                    doc_content += "\n[文档内容较长，已截取前部分]"

            part = doc_header + doc_content

            if current_length + len(part) > max_length:
                # 如果添加这个文档会超过限制，截断内容
                remaining = max_length - current_length - len(doc_header)
                if remaining > 200:  # 至少保留200个字符
                    truncated_content = doc_content[:remaining] + "\n[内容已截断]"
                    part = doc_header + truncated_content
                    content_parts.append(part)
                break

            content_parts.append(part)
            current_length += len(part)

        result = "".join(content_parts)

        # 添加使用说明
        if result:
            result += "\n\n请基于以上文档内容回答用户的问题。如果用户询问文档相关内容，请直接引用上述文档信息。"

        return result

    def get_selected_documents(self) -> List[DocumentInfo]:
        """获取选中的文档"""
        return [doc for doc in self.documents.values() if getattr(doc, 'selected', True)]

    def set_document_selected(self, doc_id: str, selected: bool):
        """设置文档选中状态"""
        doc = self.get_document(doc_id)
        if doc:
            doc.selected = selected
